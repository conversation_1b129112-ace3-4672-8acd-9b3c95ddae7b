# This can be mixed in with the training script to enable multi-GPU training

network:
  _target_: torch.nn.parallel.DistributedDataParallel
  module: $@network_def.to(@device)
  device_ids: ['@device']
  find_unused_parameters: true

tsampler:
  _target_: DistributedSampler
  dataset: '@train_ds'
  even_divisible: true
  shuffle: true
train_loader#sampler: '@tsampler'
train_loader#shuffle: false

vsampler:
  _target_: DistributedSampler
  dataset: '@val_ds'
  even_divisible: false
  shuffle: false
val_loader#sampler: '@vsampler'

training:
- $import torch.distributed as dist
- $dist.init_process_group(backend='nccl')
- $torch.cuda.set_device(@device)
- $monai.utils.set_determinism(seed=123),
- $@trainer.run()
- $dist.destroy_process_group()
