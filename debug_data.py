"""
调试数据形状的脚本
"""

import os
# 解决OpenMP库冲突问题
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

import torch
import numpy as np
from torch.utils.data import DataLoader

# 导入我们的模块
from bus_ceus_dataset import BUSCEUSDataset

def debug_data_shapes():
    """调试数据形状"""
    print("Debugging data shapes...")
    
    try:
        # 创建测试数据集
        dataset = BUSCEUSDataset(
            bus_dir="train_mini/bus",
            ceus_dir="train_mini/ceus",
            image_size=(256, 256),
            augmentation=True,
            normalize=True
        )
        
        print(f"Dataset size: {len(dataset)}")
        
        # 测试单个样本
        sample = dataset[0]
        print(f"Sample keys: {sample.keys()}")
        print(f"BUS image shape: {sample['bus'].shape}")
        print(f"BUS image dtype: {sample['bus'].dtype}")
        print(f"BUS image range: [{sample['bus'].min():.3f}, {sample['bus'].max():.3f}]")
        print(f"CEUS image shape: {sample['ceus'].shape}")
        print(f"CEUS image dtype: {sample['ceus'].dtype}")
        print(f"CEUS image range: [{sample['ceus'].min():.3f}, {sample['ceus'].max():.3f}]")
        
        # 测试数据加载器
        dataloader = DataLoader(dataset, batch_size=2, shuffle=True, num_workers=0)
        batch = next(iter(dataloader))
        print(f"\nBatch BUS shape: {batch['bus'].shape}")
        print(f"Batch BUS dtype: {batch['bus'].dtype}")
        print(f"Batch CEUS shape: {batch['ceus'].shape}")
        print(f"Batch CEUS dtype: {batch['ceus'].dtype}")
        
        # 测试连接操作
        bus_images = batch['bus']
        ceus_images = batch['ceus']
        
        print(f"\nBefore concatenation:")
        print(f"BUS images shape: {bus_images.shape}")
        print(f"CEUS images shape: {ceus_images.shape}")
        
        # 模拟训练中的连接操作
        concatenated = torch.cat([ceus_images, bus_images], dim=1)
        print(f"After concatenation shape: {concatenated.shape}")
        
        # 检查每个通道
        print(f"\nChannel analysis:")
        for i in range(concatenated.shape[1]):
            channel = concatenated[:, i:i+1, :, :]
            print(f"Channel {i} shape: {channel.shape}, range: [{channel.min():.3f}, {channel.max():.3f}]")
        
        return True
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        return False


def check_image_files():
    """检查原始图像文件"""
    print("\nChecking original image files...")
    
    try:
        import glob
        from PIL import Image
        
        bus_files = glob.glob("train_mini/bus/*.png")
        ceus_files = glob.glob("train_mini/ceus/*.png")
        
        print(f"Found {len(bus_files)} BUS files")
        print(f"Found {len(ceus_files)} CEUS files")
        
        if bus_files:
            # 检查第一个BUS文件
            bus_img = Image.open(bus_files[0])
            print(f"First BUS image: {bus_files[0]}")
            print(f"  Mode: {bus_img.mode}")
            print(f"  Size: {bus_img.size}")
            print(f"  Bands: {bus_img.getbands()}")
            
            # 转换为numpy数组检查
            bus_array = np.array(bus_img)
            print(f"  Array shape: {bus_array.shape}")
            print(f"  Array dtype: {bus_array.dtype}")
        
        if ceus_files:
            # 检查第一个CEUS文件
            ceus_img = Image.open(ceus_files[0])
            print(f"First CEUS image: {ceus_files[0]}")
            print(f"  Mode: {ceus_img.mode}")
            print(f"  Size: {ceus_img.size}")
            print(f"  Bands: {ceus_img.getbands()}")
            
            # 转换为numpy数组检查
            ceus_array = np.array(ceus_img)
            print(f"  Array shape: {ceus_array.shape}")
            print(f"  Array dtype: {ceus_array.dtype}")
        
        return True
        
    except Exception as e:
        print(f"Error checking image files: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("Data Shape Debug Tool")
    print("=" * 50)
    
    # 检查原始图像文件
    check_image_files()
    
    # 调试数据形状
    debug_data_shapes()


if __name__ == "__main__":
    main()
