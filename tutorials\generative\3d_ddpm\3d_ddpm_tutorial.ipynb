{"cells": [{"cell_type": "code", "execution_count": null, "id": "fa57bdf5", "metadata": {}, "outputs": [], "source": ["# Copyright (c) MONAI Consortium\n", "# Licensed under the Apache License, Version 2.0 (the \"License\");\n", "# you may not use this file except in compliance with the License.\n", "# You may obtain a copy of the License at\n", "#     http://www.apache.org/licenses/LICENSE-2.0\n", "# Unless required by applicable law or agreed to in writing, software\n", "# distributed under the License is distributed on an \"AS IS\" BASIS,\n", "# WITHOUT WAR<PERSON><PERSON>IES OR CONDITIONS OF ANY KIND, either express or implied.\n", "# See the License for the specific language governing permissions and\n", "# limitations under the License."]}, {"cell_type": "markdown", "id": "6286986e", "metadata": {}, "source": ["# Denoising Diffusion Probabilistic Model on 3D data\n", "\n", "This tutorial illustrates how to use MONAI for training a denoising diffusion probabilistic model (DDPM)[1] to create synthetic 3D images.\n", "\n", "[1] - [<PERSON> et al. \"Denoising Diffusion Probabilistic Models\"](https://arxiv.org/abs/2006.11239)\n", "\n", "\n", "## Setup environment"]}, {"cell_type": "code", "execution_count": 1, "id": "f96b6f31", "metadata": {}, "outputs": [], "source": ["!python -c \"import monai\" || pip install -q \"monai-weekly[nibabel, tqdm]\"\n", "!python -c \"import matplotlib\" || pip install -q matplotlib\n", "%matplotlib inline"]}, {"cell_type": "markdown", "id": "cbc01d24", "metadata": {}, "source": ["## Setup imports"]}, {"cell_type": "code", "execution_count": 13, "id": "cdea37d5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["MONAI version: 1.2.dev2304\n", "Numpy version: 1.23.5\n", "Pytorch version: 1.13.1+cu117\n", "MONAI flags: HAS_EXT = False, USE_COMPILED = False, USE_META_DICT = False\n", "MONAI rev id: 9a57be5aab9f2c2a134768c0c146399150e247a0\n", "MONAI __file__: /media/walter/Storage/Projects/GenerativeModels/venv/lib/python3.10/site-packages/monai/__init__.py\n", "\n", "Optional dependencies:\n", "Pytorch Ignite version: 0.4.10\n", "ITK version: 5.3.0\n", "Nibabel version: 4.0.2\n", "scikit-image version: 0.19.3\n", "Pillow version: 9.3.0\n", "Tensorboard version: 2.11.0\n", "gdown version: 4.6.0\n", "TorchVision version: 0.14.1+cu117\n", "tqdm version: 4.64.1\n", "lmdb version: 1.4.0\n", "psutil version: 5.9.4\n", "pandas version: 1.5.3\n", "einops version: 0.6.0\n", "transformers version: 4.21.3\n", "mlflow version: 2.1.1\n", "pynrrd version: 1.0.0\n", "\n", "For details about installing the optional dependencies, please visit:\n", "    https://docs.monai.io/en/latest/installation.html#installing-the-recommended-dependencies\n", "\n"]}], "source": ["import os\n", "import tempfile\n", "import time\n", "\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "import torch\n", "import torch.nn.functional as F\n", "from monai.apps import DecathlonDataset\n", "from monai.config import print_config\n", "from monai.data import DataLoader\n", "from monai.transforms import (\n", "    EnsureChannelFirstd,\n", "    CenterSpatialCropd,\n", "    <PERSON><PERSON><PERSON>,\n", "    Lambdad,\n", "    LoadImaged,\n", "    Resized,\n", "    ScaleIntensityd,\n", ")\n", "from monai.utils import set_determinism\n", "from torch.cuda.amp import GradScaler, autocast\n", "from tqdm import tqdm\n", "\n", "from generative.inferers import DiffusionInferer\n", "from generative.networks.nets import DiffusionModelUNet\n", "from generative.networks.schedulers import DDPMScheduler, DDIMScheduler\n", "\n", "print_config()"]}, {"cell_type": "markdown", "id": "50e37a43", "metadata": {}, "source": ["## Setup data directory\n", "\n", "You can specify a directory with the MONAI_DATA_DIRECTORY environment variable.\n", "\n", "This allows you to save results and reuse downloads.\n", "\n", "If not specified a temporary directory will be used."]}, {"cell_type": "code", "execution_count": 3, "id": "c38b4c33", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/media/walter/Storage/Projects/GTC_2023_presentation/data\n"]}], "source": ["directory = os.environ.get(\"MONAI_DATA_DIRECTORY\")\n", "root_dir = tempfile.mkdtemp() if directory is None else directory\n", "print(root_dir)"]}, {"cell_type": "markdown", "id": "41af1391", "metadata": {}, "source": ["## Set deterministic training for reproducibility"]}, {"cell_type": "code", "execution_count": 4, "id": "515d8583", "metadata": {}, "outputs": [], "source": ["set_determinism(42)"]}, {"cell_type": "markdown", "id": "29d8c601", "metadata": {}, "source": ["## Setup Decathlon Dataset and training and validation data loaders\n", "\n", "In this tutorial, we will use the 3D T1 weighted brain images from the [2016 and 2017 Brain Tumor Segmentation (BraTS) challenges](https://www.med.upenn.edu/sbia/brats2017/data.html). This dataset can be easily downloaded using the [DecathlonDataset](https://docs.monai.io/en/stable/apps.html#monai.apps.DecathlonDataset) from MONAI (`task=\"Task01_BrainTumour\"`). To load the training and validation images, we are using the `data_transform` transformations that are responsible for the following:\n", "\n", "1. `LoadImaged`:  Loads the brain images from files.\n", "2. `Lambdad`: Choose channel 1 of the image, which is the T1-weighted image.\n", "3. `EnsureChannelFirstd`: Add the channel dimension of the input data.\n", "4. `ScaleIntensityd`: Apply a min-max scaling in the intensity values of each image to be in the `[0, 1]` range.\n", "5. `CenterSpatialCropd`: Crop the background of the images using a roi of size `[160, 200, 155]`.\n", "6. `Resized`: Resize the images to a volume with size `[32, 40, 32]`.\n", "\n", "For the data loader, we are using mini-batches of 8 images, which consumes about 21GB of GPU memory during training. Please, reduce this value to run on smaller GPUs."]}, {"cell_type": "code", "execution_count": 5, "id": "f640d7ac", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["<class 'monai.transforms.utility.array.AddChannel'>: Class `AddChannel` has been deprecated since version 0.8. please use MetaTensor data type and monai.transforms.EnsureChannelFirst instead.\n"]}], "source": ["data_transform = Compose(\n", "    [\n", "        LoadImaged(keys=[\"image\"]),\n", "        Lambdad(keys=\"image\", func=lambda x: x[:, :, :, 1]),\n", "        EnsureChannelFirstd(keys=[\"image\"], channel_dim=\"no_channel\"),\n", "        ScaleIntensityd(keys=[\"image\"]),\n", "        CenterSpatialCropd(keys=[\"image\"], roi_size=[160, 200, 155]),\n", "        Resized(keys=[\"image\"], spatial_size=(32, 40, 32)),\n", "    ]\n", ")"]}, {"cell_type": "code", "execution_count": 6, "id": "ddd61e60", "metadata": {"lines_to_next_cell": 2}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2023-03-20 14:39:26,630 - INFO - Verified 'Task01_BrainTumour.tar', md5: 240a19d752f0d9e9101544901065d872.\n", "2023-03-20 14:39:26,630 - INFO - File exists: /media/walter/Storage/Projects/GTC_2023_presentation/data/Task01_BrainTumour.tar, skipped downloading.\n", "2023-03-20 14:39:26,631 - INFO - Non-empty folder exists in /media/walter/Storage/Projects/GTC_2023_presentation/data/Task01_BrainTumour, skipped extracting.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Loading dataset: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 388/388 [03:32<00:00,  1.83it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["2023-03-20 14:43:06,832 - INFO - Verified 'Task01_BrainTumour.tar', md5: 240a19d752f0d9e9101544901065d872.\n", "2023-03-20 14:43:06,832 - INFO - File exists: /media/walter/Storage/Projects/GTC_2023_presentation/data/Task01_BrainTumour.tar, skipped downloading.\n", "2023-03-20 14:43:06,833 - INFO - Non-empty folder exists in /media/walter/Storage/Projects/GTC_2023_presentation/data/Task01_BrainTumour, skipped extracting.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Loading dataset: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 96/96 [00:52<00:00,  1.83it/s]\n"]}], "source": ["train_ds = DecathlonDataset(\n", "    root_dir=root_dir, task=\"Task01_BrainTumour\", transform=data_transform, section=\"training\", download=True\n", ")\n", "\n", "train_loader = DataLoader(train_ds, batch_size=8, shuffle=True, num_workers=8, persistent_workers=True)\n", "\n", "val_ds = DecathlonDataset(\n", "    root_dir=root_dir, task=\"Task01_BrainTumour\", transform=data_transform, section=\"validation\", download=True\n", ")\n", "\n", "val_loader = DataLoader(val_ds, batch_size=8, shuffle=False, num_workers=8, persistent_workers=True)"]}, {"cell_type": "markdown", "id": "50efe5ef", "metadata": {}, "source": ["### Visualization of the training images"]}, {"cell_type": "code", "execution_count": 7, "id": "bffb4abc", "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 1000x600 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.subplots(1, 4, figsize=(10, 6))\n", "for i in range(4):\n", "    plt.subplot(1, 4, i + 1)\n", "    plt.imshow(train_ds[i * 20][\"image\"][0, :, :, 15].detach().cpu(), vmin=0, vmax=1, cmap=\"gray\")\n", "    plt.axis(\"off\")\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "id": "d22296e5", "metadata": {}, "source": ["### Define network, scheduler, optimizer, and inferer\n", "\n", "We will use a DDPM in this example; for that, we need to define a `DiffusionModelUNet` network that will have as input the noisy images and the values for the timestep `t`, and it will predict the noise that is present in the image.\n", "\n", "In this example, we have a network with three levels (with 256, 256, and 512 channels in each). In every level, we will have two residual blocks, and only the last one will have an attention block with a single attention head (with 512 channels)."]}, {"cell_type": "code", "execution_count": 8, "id": "d499f7b1", "metadata": {"lines_to_next_cell": 2}, "outputs": [], "source": ["device = torch.device(\"cuda\")\n", "\n", "model = DiffusionModelUNet(\n", "    spatial_dims=3,\n", "    in_channels=1,\n", "    out_channels=1,\n", "    num_channels=[256, 256, 512],\n", "    attention_levels=[False, False, True],\n", "    num_head_channels=[0, 0, 512],\n", "    num_res_blocks=2,\n", ")\n", "model.to(device)"]}, {"cell_type": "markdown", "id": "47ad91ff", "metadata": {}, "source": ["Together with our U-net, we need to define the Noise Scheduler for the diffusion model. This scheduler is responsible for defining the amount of noise that should be added in each timestep `t` of the diffusion model's Markov chain. Besides that, it has the operations to perform the reverse process, which will remove the noise of the images (a.k.a. denoising process). In this case, we are using a `DDPMScheduler`. Here we are using 1000 timesteps and a `scaled_linear` profile for the beta values (proposed in [<PERSON><PERSON><PERSON> et al. \"High-Resolution Image Synthesis with Latent Diffusion Models\"](https://arxiv.org/abs/2112.10752)). This profile had better results than the `linear, proposed in the original DDPM's paper. In `beta_start` and `beta_end`, we define the limits for the beta values. These are important to determine how accentuated is the addition of noise in the image."]}, {"cell_type": "code", "execution_count": null, "id": "6c1de5ad", "metadata": {}, "outputs": [], "source": ["scheduler = DDPMScheduler(num_train_timesteps=1000, schedule=\"scaled_linear_beta\", beta_start=0.0005, beta_end=0.0195)"]}, {"cell_type": "code", "execution_count": 57, "id": "36d3e99a", "metadata": {}, "outputs": [{"data": {"text/plain": ["Text(0, 0.5, 'alpha cumprod')"]}, "execution_count": 57, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.plot(scheduler.alphas_cumprod.cpu(), color=(2 / 255, 163 / 255, 163 / 255), linewidth=2)\n", "plt.xlabel(\"Timestep [t]\")\n", "plt.ylabel(\"alpha cumprod\")"]}, {"cell_type": "markdown", "id": "9125f7c8", "metadata": {}, "source": ["Finally, we define the Inferer, which contains functions that will help during the training and sampling of the model, and the optimizer."]}, {"cell_type": "code", "execution_count": null, "id": "8685da6e", "metadata": {}, "outputs": [], "source": ["inferer = DiffusionInferer(scheduler)\n", "\n", "optimizer = torch.optim.Adam(params=model.parameters(), lr=5e-5)"]}, {"cell_type": "markdown", "id": "9f371ad8", "metadata": {}, "source": ["## Model training\n", "\n", "In this part, we will train the diffusion model to predict the noise added to the images. For this, we are using an MSE loss between the prediction and the original noise. During the training, we are also sampling brain images to evaluate the evolution of the model. In this training, we use Automatic Mixed Precision to save memory and speed up the training."]}, {"cell_type": "code", "execution_count": 9, "id": "bd10b595", "metadata": {"lines_to_next_cell": 0}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Epoch 0: 100%|████████████| 49/49 [00:54<00:00,  1.12s/it, loss=0.263]\n", "Epoch 1: 100%|███████████| 49/49 [00:54<00:00,  1.12s/it, loss=0.0245]\n", "Epoch 2: 100%|████████████| 49/49 [00:55<00:00,  1.13s/it, loss=0.014]\n", "Epoch 3: 100%|███████████| 49/49 [00:55<00:00,  1.13s/it, loss=0.0103]\n", "Epoch 4: 100%|██████████| 49/49 [00:56<00:00,  1.14s/it, loss=0.00888]\n", "Epoch 5: 100%|███████████| 49/49 [00:56<00:00,  1.15s/it, loss=0.0125]\n", "Epoch 6: 100%|██████████| 49/49 [00:56<00:00,  1.15s/it, loss=0.00897]\n", "Epoch 7: 100%|██████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.00892]\n", "Epoch 8: 100%|██████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.00569]\n", "Epoch 9: 100%|███████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.0075]\n", "Epoch 10: 100%|█████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.00929]\n", "Epoch 11: 100%|█████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.00622]\n", "Epoch 12: 100%|█████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.00633]\n", "Epoch 13: 100%|█████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.00664]\n", "Epoch 14: 100%|█████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.00597]\n", "Epoch 15: 100%|█████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.00646]\n", "Epoch 16: 100%|█████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.00721]\n", "Epoch 17: 100%|█████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.00895]\n", "Epoch 18: 100%|█████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.00681]\n", "Epoch 19: 100%|█████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.00657]\n", "Epoch 20: 100%|█████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.00635]\n", "Epoch 21: 100%|█████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.00578]\n", "Epoch 22: 100%|█████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.00691]\n", "Epoch 23: 100%|█████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.00602]\n", "Epoch 24: 100%|█████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.00668]\n", "100%|██████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 1000/1000 [01:01<00:00, 16.22it/s]\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 200x200 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["Epoch 25: 100%|█████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.00884]\n", "Epoch 26: 100%|█████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.00494]\n", "Epoch 27: 100%|█████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.00529]\n", "Epoch 28: 100%|█████████| 49/49 [00:56<00:00,  1.14s/it, loss=0.00513]\n", "Epoch 29: 100%|█████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.00503]\n", "Epoch 30: 100%|█████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.00852]\n", "Epoch 31: 100%|█████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.00525]\n", "Epoch 32: 100%|█████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.00571]\n", "Epoch 33: 100%|█████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.00495]\n", "Epoch 34: 100%|█████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.00574]\n", "Epoch 35: 100%|██████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.0048]\n", "Epoch 36: 100%|█████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.00593]\n", "Epoch 37: 100%|██████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.0054]\n", "Epoch 38: 100%|█████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.00569]\n", "Epoch 39: 100%|█████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.00641]\n", "Epoch 40: 100%|██████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.0047]\n", "Epoch 41: 100%|█████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.00498]\n", "Epoch 42: 100%|█████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.00431]\n", "Epoch 43: 100%|█████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.00484]\n", "Epoch 44: 100%|█████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.00421]\n", "Epoch 45: 100%|█████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.00462]\n", "Epoch 46: 100%|█████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.00404]\n", "Epoch 47: 100%|█████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.00435]\n", "Epoch 48: 100%|█████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.00626]\n", "Epoch 49: 100%|█████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.00365]\n", "100%|██████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 1000/1000 [01:01<00:00, 16.22it/s]\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 200x200 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["Epoch 50: 100%|██████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.0064]\n", "Epoch 51: 100%|█████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.00488]\n", "Epoch 52: 100%|█████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.00691]\n", "Epoch 53: 100%|█████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.00507]\n", "Epoch 54: 100%|█████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.00373]\n", "Epoch 55: 100%|█████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.00488]\n", "Epoch 56: 100%|█████████| 49/49 [00:56<00:00,  1.14s/it, loss=0.00374]\n", "Epoch 57: 100%|█████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.00416]\n", "Epoch 58: 100%|█████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.00564]\n", "Epoch 59: 100%|█████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.00428]\n", "Epoch 60: 100%|█████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.00456]\n", "Epoch 61: 100%|█████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.00506]\n", "Epoch 62: 100%|██████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.0036]\n", "Epoch 63: 100%|█████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.00306]\n", "Epoch 64: 100%|█████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.00442]\n", "Epoch 65: 100%|█████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.00393]\n", "Epoch 66: 100%|█████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.00361]\n", "Epoch 67: 100%|██████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.0035]\n", "Epoch 68: 100%|█████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.00457]\n", "Epoch 69: 100%|█████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.00455]\n", "Epoch 70: 100%|█████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.00524]\n", "Epoch 71: 100%|█████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.00394]\n", "Epoch 72: 100%|█████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.00577]\n", "Epoch 73: 100%|█████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.00558]\n", "Epoch 74: 100%|█████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.00465]\n", "100%|██████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 1000/1000 [01:01<00:00, 16.23it/s]\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 200x200 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["Epoch 75: 100%|█████████| 49/49 [00:57<00:00,  1.17s/it, loss=0.00369]\n", "Epoch 76: 100%|█████████| 49/49 [00:56<00:00,  1.16s/it, loss=0.00296]\n", "Epoch 77: 100%|█████████| 49/49 [00:57<00:00,  1.17s/it, loss=0.00641]\n", "Epoch 78: 100%|█████████| 49/49 [00:56<00:00,  1.15s/it, loss=0.00384]\n", "Epoch 79: 100%|█████████| 49/49 [00:57<00:00,  1.18s/it, loss=0.00447]\n", "Epoch 80: 100%|█████████| 49/49 [00:57<00:00,  1.17s/it, loss=0.00444]\n", "Epoch 81: 100%|██████████| 49/49 [00:57<00:00,  1.17s/it, loss=0.0046]\n", "Epoch 82: 100%|██████████| 49/49 [00:57<00:00,  1.17s/it, loss=0.0053]\n", "Epoch 83: 100%|██████████| 49/49 [00:57<00:00,  1.17s/it, loss=0.0032]\n", "Epoch 84: 100%|█████████| 49/49 [00:57<00:00,  1.17s/it, loss=0.00467]\n", "Epoch 85: 100%|█████████| 49/49 [00:56<00:00,  1.15s/it, loss=0.00346]\n", "Epoch 86: 100%|█████████| 49/49 [00:57<00:00,  1.17s/it, loss=0.00382]\n", "Epoch 87: 100%|██████████| 49/49 [00:57<00:00,  1.18s/it, loss=0.0034]\n", "Epoch 88: 100%|█████████| 49/49 [00:58<00:00,  1.18s/it, loss=0.00355]\n", "Epoch 89: 100%|█████████| 49/49 [00:56<00:00,  1.15s/it, loss=0.00478]\n", "Epoch 90: 100%|█████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.00405]\n", "Epoch 91: 100%|█████████| 49/49 [00:57<00:00,  1.17s/it, loss=0.00465]\n", "Epoch 92: 100%|█████████| 49/49 [00:57<00:00,  1.16s/it, loss=0.00416]\n", "Epoch 93: 100%|█████████| 49/49 [00:56<00:00,  1.16s/it, loss=0.00372]\n", "Epoch 94: 100%|█████████| 49/49 [00:56<00:00,  1.16s/it, loss=0.00372]\n", "Epoch 95: 100%|█████████| 49/49 [00:57<00:00,  1.18s/it, loss=0.00492]\n", "Epoch 96: 100%|█████████| 49/49 [00:56<00:00,  1.16s/it, loss=0.00495]\n", "Epoch 97: 100%|█████████| 49/49 [00:57<00:00,  1.18s/it, loss=0.00486]\n", "Epoch 98: 100%|█████████| 49/49 [00:56<00:00,  1.16s/it, loss=0.00418]\n", "Epoch 99: 100%|█████████| 49/49 [00:58<00:00,  1.18s/it, loss=0.00563]\n", "100%|██████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 1000/1000 [01:02<00:00, 16.06it/s]\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 200x200 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["Epoch 100: 100%|████████| 49/49 [00:56<00:00,  1.16s/it, loss=0.00425]\n", "Epoch 101: 100%|████████| 49/49 [00:57<00:00,  1.18s/it, loss=0.00327]\n", "Epoch 102: 100%|█████████| 49/49 [00:56<00:00,  1.15s/it, loss=0.0045]\n", "Epoch 103: 100%|████████| 49/49 [00:57<00:00,  1.17s/it, loss=0.00335]\n", "Epoch 104: 100%|████████| 49/49 [00:56<00:00,  1.16s/it, loss=0.00341]\n", "Epoch 105: 100%|████████| 49/49 [00:56<00:00,  1.16s/it, loss=0.00418]\n", "Epoch 106: 100%|█████████| 49/49 [00:56<00:00,  1.16s/it, loss=0.0032]\n", "Epoch 107: 100%|████████| 49/49 [00:57<00:00,  1.18s/it, loss=0.00503]\n", "Epoch 108: 100%|████████| 49/49 [00:56<00:00,  1.16s/it, loss=0.00341]\n", "Epoch 109: 100%|████████| 49/49 [00:56<00:00,  1.16s/it, loss=0.00545]\n", "Epoch 110: 100%|████████| 49/49 [00:57<00:00,  1.18s/it, loss=0.00523]\n", "Epoch 111: 100%|████████| 49/49 [00:59<00:00,  1.21s/it, loss=0.00306]\n", "Epoch 112: 100%|████████| 49/49 [00:56<00:00,  1.15s/it, loss=0.00472]\n", "Epoch 113: 100%|████████| 49/49 [00:59<00:00,  1.21s/it, loss=0.00513]\n", "Epoch 114: 100%|████████| 49/49 [00:59<00:00,  1.21s/it, loss=0.00339]\n", "Epoch 115: 100%|████████| 49/49 [00:57<00:00,  1.18s/it, loss=0.00388]\n", "Epoch 116: 100%|████████| 49/49 [00:56<00:00,  1.16s/it, loss=0.00398]\n", "Epoch 117: 100%|█████████| 49/49 [00:57<00:00,  1.17s/it, loss=0.0026]\n", "Epoch 118: 100%|████████| 49/49 [00:57<00:00,  1.18s/it, loss=0.00355]\n", "Epoch 119: 100%|████████| 49/49 [00:56<00:00,  1.15s/it, loss=0.00315]\n", "Epoch 120: 100%|████████| 49/49 [00:56<00:00,  1.15s/it, loss=0.00346]\n", "Epoch 121: 100%|████████| 49/49 [00:56<00:00,  1.16s/it, loss=0.00506]\n", "Epoch 122: 100%|████████| 49/49 [00:57<00:00,  1.18s/it, loss=0.00301]\n", "Epoch 123: 100%|█████████| 49/49 [00:58<00:00,  1.19s/it, loss=0.0051]\n", "Epoch 124: 100%|█████████| 49/49 [00:57<00:00,  1.18s/it, loss=0.0032]\n", "100%|██████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 1000/1000 [01:03<00:00, 15.74it/s]\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 200x200 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["Epoch 125: 100%|████████| 49/49 [00:57<00:00,  1.17s/it, loss=0.00394]\n", "Epoch 126: 100%|████████| 49/49 [00:57<00:00,  1.18s/it, loss=0.00387]\n", "Epoch 127: 100%|████████| 49/49 [00:58<00:00,  1.18s/it, loss=0.00382]\n", "Epoch 128: 100%|████████| 49/49 [00:57<00:00,  1.18s/it, loss=0.00439]\n", "Epoch 129: 100%|████████| 49/49 [00:56<00:00,  1.15s/it, loss=0.00654]\n", "Epoch 130: 100%|████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.00585]\n", "Epoch 131: 100%|████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.00495]\n", "Epoch 132: 100%|████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.00375]\n", "Epoch 133: 100%|████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.00406]\n", "Epoch 134: 100%|████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.00394]\n", "Epoch 135: 100%|████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.00369]\n", "Epoch 136: 100%|████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.00354]\n", "Epoch 137: 100%|████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.00384]\n", "Epoch 138: 100%|████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.00457]\n", "Epoch 139: 100%|████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.00427]\n", "Epoch 140: 100%|████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.00319]\n", "Epoch 141: 100%|████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.00419]\n", "Epoch 142: 100%|████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.00412]\n", "Epoch 143: 100%|████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.00397]\n", "Epoch 144: 100%|████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.00292]\n", "Epoch 145: 100%|████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.00397]\n", "Epoch 146: 100%|████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.00364]\n", "Epoch 147: 100%|████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.00406]\n", "Epoch 148: 100%|████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.00449]\n", "Epoch 149: 100%|████████| 49/49 [00:55<00:00,  1.14s/it, loss=0.00239]\n", "100%|██████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 1000/1000 [01:01<00:00, 16.37it/s]\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 200x200 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["train completed, total time: 8859.028432130814.\n"]}], "source": ["n_epochs = 150\n", "val_interval = 25\n", "epoch_loss_list = []\n", "val_epoch_loss_list = []\n", "\n", "scaler = GradScaler()\n", "total_start = time.time()\n", "for epoch in range(n_epochs):\n", "    model.train()\n", "    epoch_loss = 0\n", "    progress_bar = tqdm(enumerate(train_loader), total=len(train_loader), ncols=70)\n", "    progress_bar.set_description(f\"Epoch {epoch}\")\n", "    for step, batch in progress_bar:\n", "        images = batch[\"image\"].to(device)\n", "        optimizer.zero_grad(set_to_none=True)\n", "\n", "        with autocast(enabled=True):\n", "            # Generate random noise\n", "            noise = torch.randn_like(images).to(device)\n", "\n", "            # Create timesteps\n", "            timesteps = torch.randint(\n", "                0, inferer.scheduler.num_train_timesteps, (images.shape[0],), device=images.device\n", "            ).long()\n", "\n", "            # Get model prediction\n", "            noise_pred = inferer(inputs=images, diffusion_model=model, noise=noise, timesteps=timesteps)\n", "\n", "            loss = F.mse_loss(noise_pred.float(), noise.float())\n", "\n", "        scaler.scale(loss).backward()\n", "        scaler.step(optimizer)\n", "        scaler.update()\n", "\n", "        epoch_loss += loss.item()\n", "\n", "        progress_bar.set_postfix({\"loss\": epoch_loss / (step + 1)})\n", "    epoch_loss_list.append(epoch_loss / (step + 1))\n", "\n", "    if (epoch + 1) % val_interval == 0:\n", "        model.eval()\n", "        val_epoch_loss = 0\n", "        for step, batch in enumerate(val_loader):\n", "            images = batch[\"image\"].to(device)\n", "            noise = torch.randn_like(images).to(device)\n", "            with torch.no_grad():\n", "                with autocast(enabled=True):\n", "                    timesteps = torch.randint(\n", "                        0, inferer.scheduler.num_train_timesteps, (images.shape[0],), device=images.device\n", "                    ).long()\n", "\n", "                    # Get model prediction\n", "                    noise_pred = inferer(inputs=images, diffusion_model=model, noise=noise, timesteps=timesteps)\n", "                    val_loss = F.mse_loss(noise_pred.float(), noise.float())\n", "\n", "            val_epoch_loss += val_loss.item()\n", "            progress_bar.set_postfix({\"val_loss\": val_epoch_loss / (step + 1)})\n", "        val_epoch_loss_list.append(val_epoch_loss / (step + 1))\n", "\n", "        # Sampling image during training\n", "        image = torch.randn((1, 1, 32, 40, 32))\n", "        image = image.to(device)\n", "        scheduler.set_timesteps(num_inference_steps=1000)\n", "        with autocast(enabled=True):\n", "            image = inferer.sample(input_noise=image, diffusion_model=model, scheduler=scheduler)\n", "\n", "        plt.figure(figsize=(2, 2))\n", "        plt.imshow(image[0, 0, :, :, 15].cpu(), vmin=0, vmax=1, cmap=\"gray\")\n", "        plt.tight_layout()\n", "        plt.axis(\"off\")\n", "        plt.show()\n", "\n", "total_time = time.time() - total_start\n", "print(f\"train completed, total time: {total_time}.\")"]}, {"cell_type": "markdown", "id": "3e263b67", "metadata": {}, "source": ["### Learning curves"]}, {"cell_type": "code", "execution_count": 10, "id": "c7520419", "metadata": {"lines_to_next_cell": 2}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAsQAAAILCAYAAADv64riAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjYuMiwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8o6BhiAAAACXBIWXMAAA9hAAAPYQGoP6dpAAB4FElEQVR4nO3dd1xV9f8H8Ne5i70EBBFwg5l7D1LMkQMty/o2NFdL8+f8+tXKr6Wm3yy1vmbFt1ztZe5cKaW5996IMkRE9r7r/P7Ae7xHQLlykXvk9Xw8fAhn3Q9vEV58eJ/PEURRFEFEREREVE2pqnoARERERERViYGYiIiIiKo1BmIiIiIiqtYYiImIiIioWmMgJiIiIqJqjYGYiIiIiKo1BmIiIiIiqtYYiImIiIioWmMgJiIiIqJqjYGYiMgBrVq1CuHh4QgPD0diYmJVD4eI6KGmqeoBEFH1NnToUBw4cACNGzfG2rVrq3o4DsPLywuNGzcGAGi12ioezf3JycnB2rVrsWfPHpw7dw4ZGRnQ6/Vwd3dHaGgoWrdujaioKDRr1qyqh0pE1ZwgiqJY1YMgouqLgfjh9N133+G///0vsrOzAQBOTk7w9/eHWq3G9evXUVRUJB3bo0cPzJ49G76+vlU1XCKq5jhDTEREdjVjxgz8/PPPAIDWrVvjzTffRPv27aHT6QAAoiji6NGjWLFiBbZs2YLt27fj0qVL+Omnn1CjRo2qHDoRVVPsISYiIrtZsWKFFIaHDRuGH374AREREVIYBgBBENC6dWssWrQIb7/9NgDg6tWr0ttERA8aZ4iJSPHi4+Px9ddfY8+ePbh+/TrMZjP8/PzQrl07DBkyBE2bNi3z3ISEBHz77bfYt28fEhISUFRUBHd3dzRq1Aj9+vXDP/7xD2g0Jb9UhoeHAwAWLlyIunXr4oMPPsCpU6fg4+ODmJgYAMDTTz+N06dP46WXXsKMGTPw999/4+uvv8aZM2eQnZ0NHx8fdOjQAW+++Sbq1asnu/6qVavw1ltvAQC2b9+O4OBgAMCFCxcwYMAAAMDKlSsRHh6OZcuW4ffff0diYiLMZjNCQ0PRr18/jBo1ShZELQwGA7777jusX78ecXFxUKlUqF+/Pp577jkMHjwY33//PWbPng0fHx/s27ev3P8O6enp+OSTTwAAnTt3xltvvQVBEO56zrBhw3Dw4EHs3r0brq6uyM/Ph6urK4Db7TTt27fHt99+W+r5n376KRYvXgwAOH/+vLR9//79ePnllwEAu3btwsGDB/HFF18gLi4OUVFR0Gg0+PXXX+Hq6oq9e/fC2dm5zDH27t0bV69eRUREBJYuXSrbt23bNqxatQonTpxAZmYmXFxcUKdOHURGRuLll1+Gp6dnqdeMjY3Ft99+i4MHD+LatWswGAzw8/NDcHAw+vbtiyeffBLu7u53rR0R2Q8DMREp2rp16/D222/DYDBApVIhNDQUBQUFSExMRGJiItasWYNJkybhtddeK3Hujh07MG7cOBQWFkIQBPj5+aFmzZq4fv06Dh06hEOHDmHjxo1YunRpmYEpJycHr776KrKzsxEaGioLQE5OTtLby5cvx7x58+Dm5oaAgACYzWbcuHED69evx19//YXVq1cjJCTknh+v9TWLioowdOhQHDt2DLVq1UJAQACuXbuGCxcu4MKFCzhx4gS++OIL2flFRUV45ZVXcODAAQCAi4sL/P39cenSJUyfPh27d+9Gy5YtS7xWefz0008oKCgAAEyZMuWeYdhi7ty5cHJysvn1yuvIkSOYNGkS3NzcULduXXh6eqJHjx749ddfkZ+fj507d6J3796lnnvq1ClcvXoVAPDkk09K24uKivDPf/4TW7duBQC4u7sjJCQEKSkpOHnyJE6ePImffvoJS5YskW6OtNi2bRsmTJggfc7WrFkTbm5uSE1NxcGDB3Hw4EF88803+P777+Hn51cpNSEiObZMEJFiHTlyBNOmTYPBYEBUVBT+/vtvbNmyBTt37sSePXswcOBAiKKIBQsWSLO2Frm5uZgyZQoKCwtRv359/P7779i1axe2bNmCw4cPY/LkyQCAQ4cOYcmSJWWO4eeff0bt2rXx559/YuPGjfjpp5+kfWq1GgBw/PhxfPLJJ5g5cyb279+PjRs3Yu/evZg3bx6A4lD91VdfletjVqluf9n+6KOPkJeXh7Vr1+Kvv/7C5s2bsWfPHjz++OMAgJiYGBw7dkx2/pIlS6QwPHLkSOzbtw+bN2/GgQMHMGXKFGzevBm//fabbPzl9ffffwMAwsLC0KRJk3Kf5+npWWlhGACio6Px3HPPYe/evdiwYQPefvtttGvXDoGBgQCALVu2lHnuxo0bAQCurq7o1auXtH3u3LnYunUrnJ2dMWfOHOzfvx+bNm3CkSNH8N1336Fu3bpITU3FmDFjkJeXJ51nMBgwffp0GAwGdO3aFX/99Rd27NiBjRs34sCBA1i6dCn8/f1x5coVfPTRR5VUESK6EwMxESnWhx9+CJPJhLZt2+Kjjz6Szab5+vrio48+QpcuXQAAixYtkp27d+9e5ObmAiiezWzQoIG0T6PR4LXXXkO7du0A3A5FpTl37hw++ugj+Pv7l9hnmSE9deoURo4cKWu/EAQBTz31FNq2bQug+Ff85WE963ry5El8+umnshlId3d3WS+u9XVNJhO+//57AEC7du0wdepUaeZbq9XilVdewRtvvIELFy6Uayx3OnXqFACgRYsW93V+ZcnOzsaMGTNk7SMqlQr9+/cHAPz555+yVS+sbd68GUBx24SLiwsAIC4uTuqTnj59OgYPHixrq2nXrh2WLFkCrVaLpKQk/Prrr9K+CxcuICMjAwAwfvx4BAQESPsEQUBERATee+89dOzYscx2CyKyPwZiIlKkxMREHD16FEBxr6n1zKm1l156CQBw9uxZXLlyRdreq1cvnDp1Cnv27MFjjz1W6rmW9XETEhLKHEezZs1Qt27du45Vo9Fg5MiRpe575JFHAADXr1+/6zVK07NnzxK9xwAQEhIi9Z8mJydL28+ePYu0tDQA8l//W3vllVek4GeLnJwc6PV6AJBmXh1F3759S+0DHzhwIAAgLy9Pmt22duzYMSQlJQEAnnrqKWn7hg0bIIoiPDw8MGjQoFJfMyQkBJGRkQBuh2pA/gPNjRs3Sj23Z8+e+Prrr/HOO+/c/QMjIrthICYiRTpx4oT0dqNGjco8znq28vTp07J9KpUKvr6+ZT74wnJzlyXolebO/tDSBAYGwsPDo9R9lu2FhYX3vM6dwsLCytxnua71zOelS5ekty1B/E7u7u5o3bq1zWOxbgu4n0Bdmcr6N2rcuLH0uVNa24TlNwMBAQHo0KGDtP348eMAgPr165catC2aN28OoPgHEbPZDABo2LAhatasCQD417/+ha+++koK3URUdXhTHREpUkpKivR2v379ynVOamqq7H29Xo9Vq1Zh27ZtuHjxIjIzM20Opj4+Pvc85m4zpmXNbJdHea5r/eyl9PR06W1LKCtN3bp1sXv3bpvGYt0DbGlFcRR3+zcaMGAAFi5ciJiYGOj1etlayZaZ3QEDBsj+nSyfe8ePH5dWG7mbwsJC5ObmwtPTEzqdDh9//DHGjBmDrKwszJ8/H/Pnz0fdunXRoUMHdO3aFY899lil9lQTUUkMxESkSJbVDIDimdLyBEvrmcv09HQMHz5ctlRXQEAAgoODpVm/mzdv4ubNm3e95t1mCC0qEnrteV3rmpW2HJtFWbPZd+Pt7S0tmxYfH2/z+ZXpbv9GUVFR+Pjjj5Gbm4vdu3eje/fuAIDDhw9Lwde6XQK4PZvv7u4uLYd3L9Yz9W3btsX69evxzTff4Pfff0dycjKuXLmCK1eu4Oeff4a3tzfefPNNadk4Iqp8DMREpEhubm7S28uWLSv1pra7+eCDD6QwPHToUIwZM6bEU9Ks17h9GFiHYKPRWOZx1u0P5SUIApo0aYJDhw7h8OHDEEWx3MuuVYT1DPj9qF27Nlq3bo3Dhw9j8+bNUiC2tEs0adKkREuO5XOvZcuWJdYlLq+AgABMmTIFU6ZMwcWLF7Fnzx7s2rULe/fuRWZmJubMmYPCwsJSlwskIvtjDzERKZJ1u4D1jWPlYTAYpJ7RiIgITJ8+vdRHBltWA3hYeHl5SW9bbq4rzf3O8FrCZHJyMnbu3Fnu8/Lz8/Huu+/KepytWfpvS5OVlWXbIEthedBJTEwMDAYDTCaT9PlR2s2Hls+9+7kRsjSNGjXCsGHD8NVXX2H79u1o1aoVgOIfyLKzs+3yGkR0dwzERKRIlhuWAEjr6pZGFMUSs6Hp6enSr70tS6vdyWw2l7rygJJZr4ZR1tJq+fn5OHjw4H1d/+mnn5aWCps3b16ZS5ndaeHChfjpp58wYMAAXL58Wdpu6aO9Wyi03OBWEX369IFWq0V2drb0QJabN29CrVYjKiqqxPGW1UcuX75coi/d2t1uxixrhj4gIEB6QqFer5fVg4gqDwMxESlSrVq10KZNGwDA999/j/z8/FKPW7t2LTp16oR//etfMJlMAG6vHgGUPQv85ZdfypZbK2+4c2TNmjWT1h3etGlTqccsW7aszFreS40aNfDee+8BKH408fjx4+9Zt+joaOmxzM888wzq168v7atVqxaA4nV/S7tRb9++fTh58uR9jdWaj48PIiIiAAA7d+7EH3/8AQDo0qVLqU+Ki4qKgiAIMJvNZbZMiKKI1157DX379sWaNWuk7R9++CE6deqEmTNnljke6xs776efm4hsx0BMRIo1efJkqFQqXLt2Da+99pr0iF2geHbtl19+wbvvvovs7Gz4+vpKT17z8PCQluL67bffZMuxpaSkYPbs2fjss88wduxYafuhQ4ce0EdVeVxcXKQVObZv344vv/wSBoMBQHEbydKlS/H555/LlhizVf/+/fHKK68AKH7gxcCBA/H777/L+pJFUcShQ4fw+uuv4+OPPwZQ3LoyY8YM2bU6duwojW3WrFnSTLHJZMLWrVsxfvx46ZiKsqxJ/Pfff0vtHmWt1VynTh0899xzAIAVK1bgs88+k92wmJycjH/+85/Yu3cvrly5Ilv2rV69ekhPT8evv/6KxYsXl2j5OH/+PP7zn/8AABo0aCB7YAwRVR7eVEdEDiEuLq7MAGLt+eefxwsvvAAAaNOmDebOnYt///vfOHjwIHr37o3atWtDq9Xi+vXr0kxbnz59MH78eNl1Jk2ahDfeeAM5OTl45plnEBQUBFEUcf36dajVarz//vto27YtPvvsM5jNZrzxxhsICgrC119/7XAPnrDFpEmTcODAASQmJmLBggWIjo5GQEAArl+/jvz8fEyaNAl6vb7cT84rzZQpU9C0aVPMmDEDV65cwaRJk6DValGzZk1otVpcu3ZNaifQ6XQYOXIkxo4dW2I96N69e+PRRx/F6dOnsXbtWmzYsAFeXl7Iz89HYWEh+vXrhyeeeAL79u0DUByUbX3ctMXjjz8ONzc3XLx4EUDxjXM9e/Ys8/i3334bN2/exPbt27Fo0SJ8+eWXCAoKQl5eHlJTU2E2m6HT6TBnzhxZIH7mmWewf/9+rF+/Hp9++im++OIL+Pv7w9XVFRkZGdLSeL6+vliwYMF9fSxEZDsGYiJyCEVFRTh37tw9j7tzGbRBgwahTZs2+Prrr7F3714kJyfDbDbD398fjRs3xqBBg/D444+XWPGgW7duWL58Ob788kucOHECN27cgL+/PwYMGIBhw4bh0UcfBVD8aN6vvvoKaWlpcHJyuutyZUrg7++PX3/9FdHR0YiJicH169eRlZWFtm3bYvjw4ejSpQs+/fRTAKjQKhF9+/bFY489hnXr1mHnzp04f/68FBS9vLzQoEEDREREYNCgQWWuiazVavH111/j888/R0xMDJKSkqDX69GoUSMMGjQIL774ouyBGkVFRbJ2GFs4OzujV69eUnvDE088IbWXlHX8559/jm3btmH16tU4ceIEEhIS4ObmhoYNGyIiIgLPPfdciScJqlQqzJ8/H3369MG6detw6tQp3Lx5Ezdu3ICbmxtatWqFrl274qWXXpLdBElElUsQK7pmDRERPVQ++OADLF++HOHh4Vi3bl1VD4eIqNKxh5iIqJoRRRE5OTll7rcsfxYUFPSghkREVKUYiImIqpEZM2agVatWGDx4cKnr+yYnJ0s9ufa6YY2IyNExEBMRVSPt2rVDQUEBrly5grfeeku27FxcXBzefPNNGAwG+Pj4YNCgQVU4UiKiB4c9xERE1cysWbPw/fffAyi+cS0oKAhGoxFJSUkAipel++yzzyq0/BoRkZIwEBMRVUN///03fvzxR1y8eBEpKSlQq9UIDg5Gly5dMGzYMOmhGERE1QEDMRERERFVa+whJiIiIqJqjQ/mqIDU1LKXLaoolUpAjRpuSE/Pg9nMSfx7Yb3Kj7WyDetlG9ar/Fgr27BetmG9bvP397jnMZwhdlAqlQBBEKBS3f+ToqoT1qv8WCvbsF62Yb3Kj7WyDetlG9bLNgzERERERFStMRATERERUbXGQExERERE1RoDMRERERFVawzERERERFStMRATERERUbXGQExERERE1RoDMRERERFVawzERERERFStMRATERERUbXGQExERERE1RoDMRERERFVawzERERERFStMRATERERUbXGQExERERE1RoDMRERERFVa5qqHgDd242MfCzZcBa1fF0xvG9jCIJQ1UMiIiKiO8yZ8x42bdpQrmPffvtd9Os3oMKvGRHRFi1btsbixV9W+FrVGQOxAuw+eR2XkrJwKSkLPduGIKSme1UPiYiIiO4wcuRreOaZ52TbXnnlZdStWw/Tp8+Uba9VK8gur7lkyTdwdXW1y7WqMwZiBdAbTdLbBUXGKhwJERERlaVWraBSg66TkzMaN25y13NFUYTJZIJGY1s0u9d1qXzYQ6wAKqsWCVEUq3AkREREZA9Ll/4PERFtceTIIUyYMAY9enTB1q2bpP2bNm3AG2+MRP/+PdCjRxe8+OIz+PLLz5Gfnye7TkREW4wd+5r0fnT0YkREtEVc3GX897//xVNP9cPjj3fG888Pwg8/fMMcUQbOECuAdc+wmZ/HRERED42vvvoCTZs2x/DhryA4OAQA8OuvP+G//52Pbt26Y+TI16DRaLB799/45ptliI+/ivffn1fm9bRaLQBgwYIPUbt2LUybNh0mk4gVK5bg888XwcvLG/37D3wgH5uSMBArgMpqHt/Mn+yIiIgeGh4ennjzzfGybVlZmejc+TG8++4c6HQ6AEDr1m1x4sQx7NgRg/z8/Hv2DTs7O+PDDz9ERkYejEYzvL19MGrUEOzbt4eBuBQMxAoga5ngFDERESnUwXM3sObvyyjUm+59cClUKgHmSvw+6KxTY9Bj9dG2cc1Ke407derUpcS2V155o9RjQ0NDcfbsaaSkXEe9evXvet2ePXvL3g8Kqg2gOGxTSQzECqBiywQRET0ENu+/iuS0/Koexl1t2h//QANxjRq+JbbduJGC5cuX4NChA7h58wYMBoNsvyia73ldf39/2ftqtRoAYDbf+9zqiIFYAayXHWbLBBERKVXfDnWw2sFniPt2CK2065fmzlUlCgsL8eabr+LGjRS8+OLLaNu2PTw8PCAIApYsicbu3X+X88p8ZoEtGIgVQKViywQRESlf28Y173v2VaNRwcfHTeqJfVgdOnQAycnX8OyzL+D119+U7btzppjsh8uuKYC8ZYKBmIiI6GFlaWnw9vaWbT937gwOHz4IADCZ7m+GncrGGWIFEGTrEFfhQIiIiKhSNW3aDC4urli16leEhNRBjRo1cPLkcWzZshGDBz+Pn3/+Hps3b4RWq0PduvWqergPDQZiBVCxh5iIiKhaqFHDFx98sABffPEp5s59D05OTmjduh3mz/8UarUKhw8fxLp1q6DX6zF58tSqHu5DQxD5yJL7lpqaU2nXtu6V2rTvKn7cdhEA8NqAJuj4aGClva5SVZfeMntgrWzDetmG9So/1so2rJdtWK/b/P097nkMe4gVgD3ERERERJXHoVsmVq5cieXLlyM+Ph4+Pj4YOHAgxo0bJz215U4FBQWIjo7Gxo0bcePGDQQGBmLQoEEYNWqU9CjDoUOH4sCBAyXOdXV1xdGjRyv147lfslUmmIeJiIiI7MphA/GaNWswffp0TJs2DT179sSlS5cwbdo0ZGVlYfbs2aWeM3nyZJw4cQLvvfcewsPDsW/fPsycORP5+fmYNGmSdFzfvn3xzjvvyM5VqRx3sly2DjGXXSMiIiKyK4dNgYsXL0b//v0xfPhwBAcHIzIyEuPHj8fKlSuRnJxc4vjY2Fhs374dkydPRs+ePRESEoJnn30W/fr1w+rVq2XHOjs7w9/fX/bH17fkk2IcBVsmiIiIiCqPQwbiq1evIiEhAd26dZNtj4yMhNlsxu7du0ucU69ePezatQv9+/eXbffz88PNmzeh5HsH+ehmIiIiosrjkC0TcXFxAICQkBDZ9sDAQGi1Wmm/NZVKVeK53UajETt37kSLFi1ka/kqjXU3h5KDPREREZEjcshAnJ2dDQBwd3eXbRcEAW5ubtL+e1mwYAFiY2OxYsUK2fb4+HiMHTsWJ0+ehEajQYsWLTBx4sQSAfxeVCpBdsObPanVKulvjdoqEQvFS6mQnHW96O5YK9uwXrZhvcqPtbIN62Ub1ss2DhmI7zWbe6/9oihi3rx5WLFiBd5991106NBB2ufl5YWrV6/i2WefxdixY5GYmIhPPvkE//jHP7B+/Xqbeolr1HCr9JlnT08XeHg4S+87O+vg4+NWqa+pZJ6eLlU9BMVgrWzDetmG9So/1so2rJdtWK/ycchA7OnpCQDIyZE/+EIUReTl5cHLy6vMcw0GA6ZNm4YtW7Zg3rx5ePLJJ2X7Fy9eLHu/cePGCA8PR8+ePbFq1Sq8+uqr5R5nenpepc4Qe3q6IDu7AAX5eml7Xl4RMjLyKuU1lcy6XiZT9V6A/F5YK9uwXrZhvcqPtbIN62Ub1uu28kwkOmQgbtCgAYDi1obWrVtL2xMTE2EwGKT9dxJFEVOnTsWOHTvw1VdfoVOnTuV6vZCQELi6uiIhIcGmcZrNYqUvg2YymWV9w0aTudo/ceZuTKxPubFWtmG9bMN6lR9rZRvWyzasV/k4ZGNJcHAwGjZsiJiYGNn2mJgYaLVaRERElHreZ599hu3bt2PZsmWlhuG0tDS88847OHz4sGx7bGws8vPzbe4hflCs2zK4DjERERGRfTlkIAaACRMmYOvWrVi2bBmSkpKwbds2LF68GEOHDoWfnx9OnDiBPn364NChQwCA5ORkREdHY/jw4QgKCkJqaqrsj16vh6+vL86dO4cpU6bgr7/+QmJiInbt2oUJEybA398fzzzzTBV/1KXjsmtERERElcchWyYAoFevXpg/fz6io6OxcOFC+Pn5YdiwYRg9ejSA4sc0x8XFIT8/HwCwb98+GAwGREdHIzo6usT1vvnmG3To0AFLlizBf//7X8ycORM3b96Ev78/OnbsiHHjxqFGjRoP9GMsL9mya0zERERERHblsIEYAKKiohAVFVXqvg4dOuD8+fPS+4MGDcKgQYPueU0fHx+899579hriA8En1RERERFVHodtmaDbBLZMEBERObSpUyciIqItzp07e9fjLl48j4iItvjnP8eV67rJydcQEdEWc+a8J20bPHgABg8eUK7zn3qqf7mPvd/xPAwYiBXAemU3PqmOiIjI8Tz99HMAgLVrf7vrcWvXrgIAPPPMc/f9WvPmfYx58z6+7/PLSxRFREX1xMaN66Vtfn7+WLLkG4wc+Vqlv/6DxECsANZrHXOVCSIiIsfTvn1HhISEYtu2LcjLyy31mPz8fGzduhm1awejY8cu9/1aDRo0RIMGDe/7/PK6dOkiMjMzZdu0Wi0aN26CWrWCKv31HySH7iGmYtYtE5wgJiIicjyCIGDQoGexaNECbN68sdQZ4D/+2Iz8/DyMHPkq9Ho9vvtuBf78cztSUq5DrVYhKCgYgwYNxoABT931tSwtECtX3p65PX/+HD777BOcOXMKarUazZo1x7//Pb3U869fv46vv16KQ4f2Iy0tDe7u7qhfvwGGDRuFVq3aAACWLv0fli//CgAwd+5MzJ07E4sWRaNWrSA8++xA9O0bhXfeeU+6ZmzsJSxf/hWOHTuCnJxseHp6oUWLVhgx4lVZeH/99RG4cSMFy5Z9h08//Rj79++BXq9HaGhdjBr1Ojp3Ln1p3crGQKwAvKmOiIjI8fXrNwBfffU51q1bVWogXrt2FZydndGv30DMnDkdf//9F4YOHYH27TuisLAQv/zyI+bNex96vd6mloobN1IwbtzrEAQVxo6diNDQOrh48TzGjBmDgoICuLjcfnxzQUEBxowZhfz8fIwZMw5169ZDamoqvvrqC0yaNBb/+99yhIU1xpNPPg0AWL78K4wY8Sq6dHkMoaF1kJWVVeL1L168gNGjR8LNzQ2vvjoaoaF1kJAQj6VLo/HGGyPw5Zdfo169+gCKZ5gNBgOmTJmAzp0jMGDAh0hPT8OiRQvx1luT8d13vyIkJNS2wtsBA7ECCFaNLWyZICIipTpy4wQ2XN6KIlPRfZ2vUgmV+n3QSe2EqPq90bpm8/s6393dHb1798Xatatw8uRxNGvWQtp39uxpXLhwDgMGPAUnJyc4OTnh2Wefx2uvjZGOadasOfr164FNmzbYFIhXr16JvLw8vP/+PERG9gAAtG/fHo0a1cO4ceNkgTgpKRFNmjyKjh27ICrqSWm7q6srpkwZjz/+2IKwsMbw8/OX2iJq1QpC48ZNAKDUQPzll5+hqKgIS5d+hzp16gIAWrVqg0ceaYIRI17CihVLMHPmXOn4zMwMDBkyDM8/P0Talpp6A4sXf4KDB/czEFPpOENMREQPg21XdyAl/0ZVD+OutsXvuO9ADBTfLLd27SqsWfObLBBbbqZ7+unn4OTkhHfffb/EuW5u7vD19cP168k2vebJk8chCAI6dOgs2969e3doNPKo17BhI7z//oclrhEaWgcAbH5to9GIw4cPoUGDRlIYtmjUKBwBAYE4fPhgifN69Ogtez8oKBgAkJWVadPr2wsDsQKo2ENMREQPgZ51ujn8DHHP0G4Vukb9+g3RsmVr/PnndowfPxmenl7Iy8vF9u1b0axZCzRqFAYAOHXqBL77bgXOnDmNrKxMmEwm6RpeXl42vWZa2k24ubnJZoIBQKfTwcfHp8Txmzf/jnXrVuPy5Vjk5eXKVrASRbNNr52VlQm9vgiBgYGl7q9ZsyZOnjwBo9EoC+d+fv6y49RqNQDAbLbt9e2FgVgBrPIwZ4iJiEixWtdsft+zrxqNCj4+bsjIyIPRWDWhqbyefvo5HDs2DRs3rsfzzw/B5s0bUVBQILVBXLhwDv/3f6/Dw8MTw4aNRKNG4VKY/ec/x8NoNNj0eneLBmaziFtZEwCwatWvWLhwHsLDH8HkyVMRFBQMnU6LmzdTMWXKBFs/VIleX/qYLWOzXiCgtPerGgOxAnDZNSIiIuXo2jUSfn7++P33dXj++SH4/fd18PX1lfp7t2zZBIPBgH/+8y107RopnWc0GpGfnwedTmfT6/n4+CApKQFFRYVwcnKWthcWFiIjIx01awZI2zZsWAuVSoWFCz+Fl5e3tL2o6P5m7b29feDk5IQbN66Xuj8l5Tr8/PylGWBHxXWIFYA9xERERMqh0Wjw5JNPIy7uMrZv/wMXLpzDwIFPSy0DlrYAb29v2XmrVv2CgoICWftEeTz6aDOIoohDh+S9ulu2bCnRgmA2m6HRaODu7iFtE0URP/zwLQDIXtsyi3u38ajVarRv3xFXrsThwoVzsn2nTp1EauoNdOjQyaaPpyowECuA9Qwx8zAREZHjGzhwEDQaDRYs+EAKyBbt23cAAHzxxSIcPLgfBw/uw0cfzcWBA/vQvn1H5ObmYuvWTUhNLd8NiIMGDYaTkxP+85+ZWL9+DY4cOYRvv12BxYsXw9+/puzY9u07QK/XY/78/+D48aPYsSMGEye+ibCwcDg7O+PMmVM4fPgg8vPzpD7frVs3YceOGFy5Elfq67/66hi4uLhiypQJ2LBhDY4ePYzffvsFU6dOgLe3N0aMePV+SvhAMRArAHuIiYiIlMXX1w+RkT2QnZ2Frl27y24i69QpAhMm/BMZGRmYOnUi/vOf2XB1dcOcOR/ixRdfhp+fPz75ZH6pqzOUpnbtYHz88WcICamDjz/+CNOmTcahQwfwxRdfwM/PDwaDXjp25MjXMWjQs9izZxcmThyLJUuiERnZA8OHv4IRI15FQUEh5sx5D6mpqWjTph169OiNs2dPY+HCDxEbe7HU169fvwH+97/laNasBaKjF2PChDH45ptl6Nz5MXz55dcIDKxVsWI+AIIoMmHdr9TUnEq7tvXNA8k38zA1ei8AoP0jNfHGk00r7XWVSkk3W1Q11so2rJdtWK/yY61sw3rZhvW6zd/f457HcIZYAeQ9xFU4ECIiIqKHEAOxAsh7iJmIiYiIiOyJgVgBZD3EnCImIiIisisGYgXgk+qIiIiIKg8DsQLIHszBRExERERkVwzECqDismtERERElYaBWAGsn/ctsoeYiIiIyK4YiBWAy64RERERVR4GYgVQWf0rcZUJIiIiIvtiIFYAWcsEe4iJiIiI7IqBWAHYMkFERERUeRiIFUDgKhNERERElYaBWAEEQZBCMXuIiYiIiOyLgVghLG0TnCAmIiIisi8GYoWw3FjHlgkiIiIi+2IgVgjL0msMxERERET2xUCsEJaWCfYQExEREdkXA7FCsIeYiIiIqHIwECuEtMoEEzERERGRXTEQK4RKxZYJIiIiosrAQKwQt1smGIiJiIiI7ImBWCGkGWLmYSIiIiK7YiBWCPYQExEREVUOBmKFkFomOEVMREREZFcMxAohrUPMPExERERkVwzECiGoeFMdERERUWVgIFYIFXuIiYiIiCoFA7FC3H50cxUPhIiIiOghw0CsEILUQ8wZYiIiIiJ7YiBWCNWtfyn2EBMRERHZFwOxQghsmSAiIiKqFAzECqFiywQRERFRpWAgVgiV1b8UQzERERGR/TAQK4RlhhhgHzERERGRPTEQK4RgFYjZR0xERERkPwzECqG6nYfZMkFERERkRwzECqFSWc8QMxATERER2QsDsULIe4ircCBEREREDxkGYoWQ9RAzERMRERHZDQOxQrCHmIiIiKhyMBArhHUPscgeYiIiIiK7YSBWCJWsZaIKB0JERET0kGEgVgirPMwHcxARERHZEQOxQnDZNSIiIqLKwUCsECquMkFERERUKRiIFULgOsRERERElYKBWCG47BoRERFR5XDoQLxy5Ur0798fzZo1Q9euXTF//nzo9foyjy8oKMDHH3+MXr16oUWLFnjiiScQHR0Ng8EgOy4mJgZPP/00mjdvjk6dOmHGjBnIycmp7A+nQgT2EBMRERFVCocNxGvWrMH06dPx7LPPYtOmTZg1axZWrlyJ2bNnl3nO5MmT8dtvv2Hq1KnYsGEDXnnlFSxevBiffvqpdMz+/fsxduxYdOrUCRs2bMCiRYuwd+9ejB8//kF8WPeNy64RERERVQ6HDcSLFy9G//79MXz4cAQHByMyMhLjx4/HypUrkZycXOL42NhYbN++HZMnT0bPnj0REhKCZ599Fv369cPq1aul4z777DM0b94cU6ZMQWhoKNq1a4d///vf2L17N44cOfIgP0SbyB7MwZYJIiIiIrtxyEB89epVJCQkoFu3brLtkZGRMJvN2L17d4lz6tWrh127dqF///6y7X5+frh58yZEUURhYSEOHz5c4rqdOnWCTqfD33//bf8Pxk4E9hATERERVQpNVQ+gNHFxcQCAkJAQ2fbAwEBotVppvzWVSgV/f3/ZNqPRiJ07d6JFixYQBAEJCQkwGo0lrqvVahEUFFTqdR2FrGXCXIUDISIiInrIOGQgzs7OBgC4u7vLtguCADc3N2n/vSxYsACxsbFYsWLFXa8LwKbrWqhUgqyVwZ7UapXsb4369usIKkCjccjJ/SpzZ72obKyVbVgv27Be5cda2Yb1sg3rZRuHDMTWa+7ez35RFDFv3jysWLEC7777Ljp06GCX696pRg03m8+xlaenCwDAxUUnbXN3d4aPj1ulvq5SWepF98Za2Yb1sg3rVX6slW1YL9uwXuXjkIHY09MTAEoshSaKIvLy8uDl5VXmuQaDAdOmTcOWLVswb948PPnkk9I+y3mlLbGWm5uLunXr2jTO9PS8Sp0h9vR0QXZ2AUwmM/R6o7QvK6sAGRl5lfK6SnVnvahsrJVtWC/bsF7lx1rZhvWyDet1W3kmER0yEDdo0AAAEB8fj9atW0vbExMTYTAYpP13EkURU6dOxY4dO/DVV1+hU6dOsv0hISHQarW4evWqbLter8e1a9cwcOBAm8ZpNouVviawyWSG0WgGrF7GYLy1jUqQ6kX3xFrZhvWyDetVfqyVbVgv27Be5eOQjSXBwcFo2LAhYmJiZNtjYmKg1WoRERFR6nmfffYZtm/fjmXLlpUIwwCg0+nQuXNn/PXXX7LtO3fuhMFgwOOPP263j8HeVFb/UlxlgoiIiMh+HDIQA8CECROwdetWLFu2DElJSdi2bRsWL16MoUOHws/PDydOnECfPn1w6NAhAEBycjKio6MxfPhwBAUFITU1VfbH8oS7sWPH4ty5c5g3bx4SEhKwd+9ezJkzB0888QSaNGlSlR/yXVmvMsF1iImIiIjsxyFbJgCgV69emD9/PqKjo7Fw4UL4+flh2LBhGD16NIDixzTHxcUhPz8fALBv3z4YDAZER0cjOjq6xPW++eYbdOjQAc2bN8f//vc/LFy4EN999x08PT3Rr18/TJo06YF+fLYSuOwaERERUaVw2EAMAFFRUYiKiip1X4cOHXD+/Hnp/UGDBmHQoEHlum6XLl3QpUsXu4zxQVHxwRxERERElcJhWyZITvbo5kq+kY+IiIioOmEgVgjZk+qYh4mIiIjshoFYIQTeVEdERERUKRiIFYI9xERERESVg4FYIQSV9SoTDMRERERE9sJArBDydYircCBEREREDxkGYoVgywQRERFR5WAgVgi2TBARERFVDgZihZAvu8ZATERERGQvDMQKwR5iIiIiosrBQKwQAnuIiYiIiCoFA7FCqNhDTERERFQpGIgVgo9uJiIiIqocDMQKYT1DzEc3ExEREdkPA7FCWLUQs4eYiIiIyI4YiBWCPcRERERElYOBWCHYQ0xERERUORiIFUJl9S/FHmIiIiIi+2EgVghBYMsEERERUWVgIFYItkwQERERVQ4GYoWwuqeOLRNEREREdsRArBCyVSYYiImIiIjshoFYIax7iEVzFQ6EiIiI6CHDQKwQ8h5izhATERER2QsDsUIIVv9SXGWCiIiIyH4YiBXCeoaYE8RERERE9sNArBBsmSAiIiKqHAzECmGVhxmIiYiIiOyIgVghZMuusYeYiIiIyG4YiBWCPcRERERElYOBWCHYMkFERERUORiIFYJPqiMiIiKqHAzECsGWCSIiIqLKwUCsELJl13hTHREREZHdMBArBHuIiYiIiCoHA7FCcNk1IiIiosrBQKwQ7CEmIiIiqhwMxArBVSaIiIiIKgcDsUKwh5iIiIiocjAQK4SsZYI9xERERER2w0CsELJl15iHiYiIiOyGgVghVFb/UiJbJoiIiIjshoFYIQQ+mIOIiIioUjAQKwRbJoiIiIgqBwOxQlivMsGWCSIiIiL7YSBWCEEQpFDMZdeIiIiI7IeBWEEsbRNmcxUPhIiIiOghwkCsIJYb6zhDTERERGQ/DMQKYll6jT3ERERERPbDQKwgUssE8zARERGR3TAQK4jUMsFETERERGQ3DMQKouIqE0RERER2x0CsIKpbiZg9xERERET2w0CsIFx2jYiIiMj+GIgVhA/mICIiIrI/BmIFsbRMMBATERER2Q8DsYJYWiaYh4mIiIjsh4FYQVRcdo2IiIjI7hiIFcTSQ8xVJoiIiIjsh4FYQdhDTERERGR/DMQKwkc3ExEREdmfQwfilStXon///mjWrBm6du2K+fPnQ6/X3/Wc/Px8TJ06FeHh4fjxxx9L7H/88ccRHh5e4k9UVFRlfRh2Y3l0s8hETERERGQ3mqoeQFnWrFmD6dOnY9q0aejZsycuXbqEadOmISsrC7Nnzy71nPPnz2PChAlScCzLyJEjMXLkSNk2jcZhSyHho5uJiIiI7M9hZ4gXL16M/v37Y/jw4QgODkZkZCTGjx+PlStXIjk5udRzPvvsM0RERODzzz+/67VdXV3h7+8v++Pj41MZH4ZdCSo+qY6IiIjI3hwyEF+9ehUJCQno1q2bbHtkZCTMZjN2795d6nmTJ0/GO++8o4jZ3vtxex1izhATERER2YtDBuK4uDgAQEhIiGx7YGAgtFqttP9OderUqfSxVSXVrX8tEQzFRERERPbikFOp2dnZAAB3d3fZdkEQ4ObmJu2/X6dPn8aoUaNw/vx5uLm5oX379hg/fjz8/Pxsuo5KJUhLodmbWq2S/Q3cniEGAJVagFrlkD/PVInS6kWlY61sw3rZhvUqP9bKNqyXbVgv2zhkIL7XTXH32n83Pj4+yMjIwIgRI1C3bl1cvHgRCxYswOHDh7Fq1So4OzuX+1o1arhVaCzl4enpIr2t093+5/LycoVWo67U11Yi63rR3bFWtmG9bMN6lR9rZRvWyzasV/k4ZCD29PQEAOTk5Mi2i6KIvLw8eHl53fe1f/vtN9n7jRs3hr+/P4YNG4aYmBj069ev3NdKT8+r1BliT08XZGcXwGQqvovObLp9N116eh50WgZii9LqRaVjrWzDetmG9So/1so2rJdtWK/bfHzc7nmMQwbiBg0aAADi4+PRunVraXtiYiIMBoO0317Cw8Ol69vCbBZhruQ1gU0mM4zG4k9k68lovcEka6GgYtb1ortjrWzDetmG9So/1so2rJdtWK/yccjGkuDgYDRs2BAxMTGy7TExMdBqtYiIiLiv68bGxmLatGklbso7efKk9LqOzLo9g0uvEREREdmHQwZiAJgwYQK2bt2KZcuWISkpCdu2bcPixYsxdOhQ+Pn54cSJE+jTpw8OHToknZOamorU1FSkp6cDAHJzc6VtJpMJQUFB2L9/PyZNmoT9+/cjMTERf/zxB2bMmIGGDRuiZ8+eVfXhlov1jDAfzkFERERkH5XaMpGRkQEPD4/7Whe4V69emD9/PqKjo7Fw4UL4+flh2LBhGD16NACgoKAAcXFxyM/Pl865c+Z4/vz5mD9/PgBg+/btCA4OxrfffotFixZh8uTJyM7ORq1atfDEE09g9OjR0Ol0FfhoK591uzKXXSMiIiKyjwoH4h07duDXX3/F4sWLpW179+7FO++8g+TkZLi6umLs2LEYMWKEzdeOiopCVFRUqfs6dOiA8+fPy7bd+X5pgoOD8eGHH9o8FkdgfQNfJbcuExEREVUbFQrEhw4dwptvvgmVSgWz2QyVSoWUlBSMGTMGBQUFaNy4MZKTk/Hhhx+iXr16iIyMtNOwqyd5DzETMREREZE9VKiH+JtvvoGLiwtWr14N1a2HRPz8888oKCjApEmTsGbNGmzZsgUBAQH44Ycf7DLg6owtE0RERET2V6FAbLmxzXoZtL/++gsuLi54+eWXAQDe3t7o1asXTp06VbGR0h0tEwzERERERPZQoUCclpaGkJAQ6f3MzEycPXsWbdq0kT3xzd/fv8KPW6Y7V5mowoEQERERPUQqFIh1Oh1yc3Ol9/fs2QNRFNG5c2fZcbm5uXB3d6/ISxHkD+YQmYiJiIiI7KJCgbhBgwb4888/YTQaYTab8c0330AQhBI3zx04cABBQUEVeSkC1yEmIiIiqgwVCsRRUVG4ePEievfujb59++LYsWPo0KED6tevDwDIz8/HvHnzcPz4cfTo0cMuA67OBC67RkRERGR3FVp2bciQIbh06RJWrVoFo9GIpk2bYt68edL+tLQ0LF++HI888giGDRtW4cFWd9YzxGyZICIiIrKPCgVilUqFWbNm4e2330Z+fj5q1Kgh2x8SEoLZs2cjKioKLi4uFRooyZddY8sEERERkX3Y5dHNzs7OslUlrD377LP2eAnCnS0TDMRERERE9lChHmIAOHPmDObOnSvbduHCBQwdOhStWrVCv379sHnz5oq+DOGOlgnmYSIiIiK7qFAgPn/+PIYMGYIffvgBZrMZQPFaxMOGDcPBgweh1Wpx5coVTJ48GUeOHLHLgKszFR/dTERERGR3FQrEy5Ytg9FoxJdffik9unnlypXIyMjAkCFDcODAAWzZsgWenp5YsWKFPcZbrQnsISYiIiKyuwoF4oMHD+KJJ56QPYhj+/bt0Gq1GDduHIDiG+t69+6No0ePVmykJH90M2eIiYiIiOyiQoH45s2baNCggfR+Xl4eTp48iZYtW8LT01PaXqtWLWRkZFTkpQjsISYiIiKqDBUKxGq1GkVFRdL7Bw4cgNFoRKdOnWTHFRYWws3NrSIvRQBUVv9abJkgIiIiso8KBeI6depg79690vs//fQTBEFAt27dZMedPHkSNWvWrMhLEQABXHaNiIiIyN4qFIh79+6NY8eO4fnnn8fw4cOxY8cOPPLII3j00UcBACaTCd999x327t2Lrl272mXA1Zm8h7gKB0JERET0EKnQgzlGjRqFw4cPY/fu3QCAwMBAfPTRR9L+K1eu4P3330dQUBCGDx9eoYGS/El1ImeIiYiIiOyiQoHYyckJS5cuxZUrV5CdnY3GjRtDp9NJ++vXr4/XX38dQ4YMgb+/f4UHW92p+KQ6IiIiIruzy6Ob69atW+p2QRAwceJEe7wEobieFmyZICIiIrIPuwTi69evY9OmTTh79iwyMjIgCAJ8fX3RtGlT9OvXDz4+PvZ4mWpPvuwaZ4iJiIiI7KHCgXjFihVYsGABjEZjiZC2Zs0aLFiwALNmzUJUVFRFX6raU/FJdURERER2V6FAvGPHDnzwwQdwcXHBk08+iebNm6NGjRowm81IS0vDkSNHsHnzZkybNg2hoaFo3ry5vcZdLQnsISYiIiKyuwoF4m+//Raenp5YuXIlQkNDS+x/4YUXMGrUKLzwwgtYsmQJFi1aVJGXq/ZkLRPsISYiIiKyiwqtQ3z69GkMGDCg1DBs0bhxY0RFReHIkSMVeSkCWyaIiIiIKkOFAnFOTg4CAgLueVxISAgyMzMr8lIEtkwQERERVYYKBWJPT08kJibe87ikpCR4eHhU5KUId64yUYUDISIiInqIVCgQt2jRAuvXr8eFCxfKPObcuXNYt24dWrVqVZGXIgCCdcuEmYmYiIiIyB4qdFPdiBEj8Ndff+HZZ59Fv3790KpVK/j6+kIURdy8eVNaZcJkMmHUqFH2GnO1ZT1DzJYJIiIiIvuoUCBu3749Zs2ahTlz5mD16tVYs2aNbL8oinBxccGsWbPQpk2birwUQf7oZuZhIiIiIvuo8IM5nn32WXTv3h0bN27EqVOnkJaWBkEQ4Ofnh2bNmvFJdXYkmyFmywQRERGRXdjl0c1+fn54+eWXy9y/fft2rF69GosXL7bHy1VbApddIyIiIrK7Ct1UV15Xr17F9u3bH8RLPdTYQ0xERERkfw8kEJN9sIeYiIiIyP4YiBWEPcRERERE9sdArCDsISYiIiKyPwZiBbFumeAMMREREZF9MBArCB/dTERERGR/DMQKomLLBBEREZHd2bwOcadOnWx+kcLCQpvPoZIELrtGREREZHc2B+KMjIz7eiHrMEf3R7bsmrkKB0JERET0ELE5EPMBG1WHD+YgIiIisj+bA3Ht2rUrYxxUDoJVxzcDMREREZF98KY6BZGtMsGWCSIiIiK7YCBWELZMEBEREdkfA7GCWN+XKDIQExEREdkFA7GCyJ5Ux0BMREREZBcMxAoiW4eYPcREREREdsFArCB8Uh0RERGR/TEQK4jswRwMxERERER2wUCsIPJVJqpwIEREREQPEQZiBbFeZcLMRExERERkFwzECsJ1iImIiIjsj4FYQeQ9xFU4ECIiIqKHCAOxgshmiNkyQURERGQXDMQKInDZNSIiIiK7YyBWED6pjoiIiMj+GIgVxLplgnmYiIiIyD4YiBWEPcRERERE9sdArCDWPcR8Uh0RERGRfTh0IF65ciX69++PZs2aoWvXrpg/fz70ev1dz8nPz8fUqVMRHh6OH3/80W7XdQSyHmLOEBMRERHZhaaqB1CWNWvWYPr06Zg2bRp69uyJS5cuYdq0acjKysLs2bNLPef8+fOYMGECBOupVDtc11HIWiaqcBxEREREDxOHnSFevHgx+vfvj+HDhyM4OBiRkZEYP348Vq5cieTk5FLP+eyzzxAREYHPP//crtd1FLKWCc4QExEREdmFQwbiq1evIiEhAd26dZNtj4yMhNlsxu7du0s9b/LkyXjnnXeg0ZQ+8X2/13UUgiDAkom57BoRERGRfThky0RcXBwAICQkRLY9MDAQWq1W2n+nOnXqVMp1y6JSCbK+XntSq1Wyv61f02QWIYqARuOQP89UibLqRSWxVrZhvWzDepUfa2Ub1ss2rJdtHDIQZ2dnAwDc3d1l2wVBgJubm7S/qq9bo4bbXfuV7cHT00X2viUQq1Qq+Pi4VeprK9Gd9aKysVa2Yb1sw3qVH2tlG9bLNqxX+ThkIL5XyLzfEGrv66an51XqDLGnpwuyswtgMt2+hc4yRIPRhIyMvEp5bSUqq15UEmtlG9bLNqxX+bFWtmG9bMN63VaeCUSHDMSenp4AgJycHNl2URSRl5cHLy8vh7iu2SxW+vJnJpMZRqN1IC5OxCazKNtOxe6sF5WNtbIN62Ub1qv8WCvbsF62Yb3KxyEbSxo0aAAAiI+Pl21PTEyEwWCQ9jvKdR8ky9JrXIeYiIiIyD4cMhAHBwejYcOGiImJkW2PiYmBVqtFRESEQ133QbJ0aPBJdURERET24ZCBGAAmTJiArVu3YtmyZUhKSsK2bduwePFiDB06FH5+fjhx4gT69OmDQ4cOSeekpqYiNTUV6enpAIDc3Fxpm8lkKtd1HZ2lZ5nLrhERERHZh0P2EANAr169MH/+fERHR2PhwoXw8/PDsGHDMHr0aABAQUEB4uLikJ+fL51z5wzv/PnzMX/+fADA9u3bERwcfM/rOjpBapmo4oEQERERPSQcNhADQFRUFKKiokrd16FDB5w/f1627c737+e6js7SMsEZYiIiIiL7cNiWCSqdpWWCPcRERERE9sFArDDSKhPMw0RERER2wUCsMJYHc3DZNSIiIiL7YCBWGMsMMVsmiIiIiOyDgVhhbi+7VsUDISIiInpIMBArzO0eYiZiIiIiIntgIFYYSw+xyCliIiIiIrtgIFYYzhATERER2RcDscII0jrEVTwQIiIioocEA7HCSDPEbJkgIiIisgsGYoWxPLpZBJdeIyIiIrIHBmKFsbRMAOwjJiIiIrIHBmKFsbRMAOwjJiIiIrIHBmKFsZogZh8xERERkR0wECuMILBlgoiIiMieGIgVRmXdQ2yuwoEQERERPSQYiBVG1kMMzhATERERVRQDscKwh5iIiIjIvhiIFUbeQ1yFAyEiIiJ6SDAQK4y8h5iJmIiIiKiiGIgVxrplgk+qIyIiIqo4BmKFUfFJdURERER2xUCsMOwhJiIiIrIvBmKFYcsEERERkX0xECuM9TrEvKmOiIiIqOIYiBVGULFlgoiIiMieGIgVRtYywURMREREVGEMxAoja5lgDzERERFRhTEQK4x1ywTzMBEREVHFMRArDGeIiYiIiOyLgVhhrPIwV5kgIiIisgMGYoWxniHmBDERERFRxTEQKwwf3UxERERkXwzECsMHcxARERHZFwOxwsh6iDlDTERERFRhDMQKwx5iIiIiIvtiIFYY9hATERER2RcDscKouOwaERERkV0xECuMwAdzEBEREdkVA7HCqPjoZiIiIiK7YiBWGC67RkRERGRfDMQKo+Kya0RERER2xUCsMOwhJiIiIrIvBmKFYQ8xERERkX0xECsMl10jIiIisi8GYoUR+GAOIiIiIrtiIFYYPrqZiIiIyL4YiBVG4CoTRERERHbFQKwwshli9hATERERVRgDscKoZD3EVTgQIiIioocEA7HCCFxlgoiIiMiuGIgVRn5THQMxERERUUUxECuMSmDLBBEREZE9MRArjIrrEBMRERHZFQOxwrCHmIiIiMi+GIgVhj3ERERERPbFQKwwXHaNiIiIyL4YiBVGdlMdEzERERFRhTEQKwwf3UxERERkXwzECiPvIa7CgRARERE9JDRVPYC7WblyJZYvX474+Hj4+Phg4MCBGDduHHQ6XanH6/V6LFq0COvXr0daWhpCQ0MxatQoPPPMM9Ixjz/+OJKSkkqc26hRI2zYsKHSPhZ74bJrRERERPblsIF4zZo1mD59OqZNm4aePXvi0qVLmDZtGrKysjB79uxSz3nvvfcQExODOXPmIDw8HDExMXjnnXeg0+kwYMAA6biRI0di5MiRsnM1GocthQx7iImIiIjsy2FbJhYvXoz+/ftj+PDhCA4ORmRkJMaPH4+VK1ciOTm5xPGJiYlYvXo1Jk2ahB49eiA4OBgvv/wy+vbti8WLF8uOdXV1hb+/v+yPj4/Pg/rQKoQ9xERERET25ZCB+OrVq0hISEC3bt1k2yMjI2E2m7F79+4S5+zduxdms7nEOd26dcOVK1eQkJBQqWN+UKxbJpiHiYiIiCrOIQNxXFwcACAkJES2PTAwEFqtVtpv7fLly9DpdAgICJBtDw0NlfY/DGQtE0zERERERBXmkI2z2dnZAAB3d3fZdkEQ4ObmJu23lpOTAzc3txLbLdewPuf06dMYNWoUzp8/Dzc3N7Rv3x7jx4+Hn5+fTeNUqQTZjK09qdUq2d8WWq38fY3GIX+meeDKqheVxFrZhvWyDetVfqyVbVgv27BetnHIQCwIdw+Zpe0v7zk+Pj7IyMjAiBEjULduXVy8eBELFizA4cOHsWrVKjg7O5d7nDVquN3zdSvK09NF9n5WoUl6W6vVwMen5A8B1dmd9aKysVa2Yb1sw3qVH2tlG9bLNqxX+ThkIPb09ARQPOtrTRRF5OXlwcvLq9Rz8vLyIIqiLKRarmE557fffpOd17hxY/j7+2PYsGGIiYlBv379yj3O9PS8Sp0h9vR0QXZ2AUwms7Q9N6dAeruw0ICMjLxKeX2lKateVBJrZRvWyzasV/mxVrZhvWzDet1WnslDhwzEDRo0AADEx8ejdevW0vbExEQYDAZp/53n6PV6JCcnIygoSNp+5coV2TVLEx4eLl3fFmazWOlLn5lMZhiNtz+RrV/vzn3EmtiCtbIN62Ub1qv8WCvbsF62Yb3KxyEbS4KDg9GwYUPExMTItsfExECr1SIiIqLEOREREdBoNPjzzz9LnBMeHo6goCDExsZi2rRpJW7KO3nypPS6jo4P5iAiIiKyL4cMxAAwYcIEbN26FcuWLUNSUhK2bduGxYsXY+jQofDz88OJEyfQp08fHDp0CABQs2ZNvPjii1i0aBG2b9+OxMREfPXVV4iJicHEiRMBAEFBQdi/fz8mTZqE/fv3IzExEX/88QdmzJiBhg0bomfPnlX5IZeLIFtlogoHQkRERPSQcMiWCQDo1asX5s+fj+joaCxcuBB+fn4YNmwYRo8eDQAoKChAXFwc8vPzpXOmTp0Kd3d3zJw5E+np6ahXrx4WLlyI7t27AwBcXFzw7bffYtGiRZg8eTKys7NRq1YtPPHEExg9enSZj4R2JNYtyyJniImIiIgqTBCZqu5bamrOvQ+6TxqNCj4+bsjIyJP1/qRnF+Kfn+8BALQN98eYQc0qbQxKUla9qCTWyjasl21Yr/JjrWzDetmG9brN39/jnsc4bMsElU7eQ1yFAyEiIiJ6SDAQK4ysh5iJmIiIiKjCGIgVhj3ERERERPbFQKwwbJkgIiIisi8GYoVRCVyHmIiIiMieGIgVxioPs4eYiIiIyA4YiBXGeoaYPcREREREFcdArDDsISYiIiKyLwZihWEPMREREZF9MRArjHUPscgpYiIiIqIKYyBWGEEQYMnEzMNEREREFcdArECWPmK2TBARERFVHAOxAlke38yWCSIiIqKKYyBWIMtCE5whJiIiIqo4BmIFEm4lYuZhIiIioopjIFYgy9JrnCEmIiIiqjgGYgWSWibYQ0xERERUYQzECiRwhpiIiIjIbhiIFUjFHmIiIiIiu2EgViCuMkFERERkPwzECiQ9mIM9xEREREQVxkCsQJaHNzMPExEREVUcA7ECqW79q4lsmSAiIiKqMAZiBZLWIeYUMREREVGFMRArkNRDzDxMREREVGEMxArEdYiJiIiI7IeBWIEsy66xh5iIiIio4hiIFeh2D3EVD4SIiIjoIcBArECC9KQ6zhATERERVRQDsQJJT6rjXXVEREREFcZArECWlgkRgIl9E0REREQVwkCsQL5eztLb8Sm5VTgSIiIiIuVjIFagsBBv6e0LCZlVNg4iIiKihwEDsQKFWwXi8/GZVTYOIiIioocBA7EC1fJzg7uLFgBwMTGTD+ggIiIiqgAGYgVSCYLUNpFXaERSal7VDoiIiIhIwRiIFUreNpFRdQMhIiIiUjgGYoWyvrHuPG+sIyIiIrpvDMQKFVLTHS5OGgDFK03wqXVERERE94eBWKFUKgFhwV4AgJx8A5LT8qt4RERERETKxECsYGGh3tLbbJsgIiIiuj8MxAoWHuIjvc0b64iIiIjuDwOxgtUJdIeTVg2AfcRERERE94uBWMHUKhUa3uojzszV40ZmQRWPiIiIiEh5GIgVjo9xJiIiIqoYBmKFC7e6se7sVfYRExEREdmKgVjh6gZ6wklX3Ed84GwKLnC1CSIiIiKbMBArnFajwoDOdQEAoggs2XAGBUXGMo9PvJGL+T8dxaqdsbwJj4iIiAgMxA+FPu1D0ejWzXU3swrx0/aLpR5XUGTEot9O4MyVDGzYcxUxR5Ie5DCJiIiIHBID8UNApRLwSlQTqXXi7xPJOHohtcRx3/9xATezCqX3f465hPiUnAc2TiIiIiJHxED8kPD3dsGLPRpJ76/YfA4pGbcf57z/TAr2nLouO8doMuN/606jSG96YOMkIiIicjQMxA+RiOa10KqRHwAgJ9+Afy/Zj19iLiHhRi6+2XJeOm5E38YIDXAHACSn5ePH7Rfu+zUNRhN7kYmIiEjRNFU9ALIfQRAwrE9jJNw4hJtZhTCaRGw+EI/NB+KlYzo0CUBE81poFOKNmcsPoshgws7jySgoMsHFSQO1SoCbiwY924TA001X5msV6o34afsl7D6ZjEfq+GDs082gu/XUPHszmc3YffI6snKL0KNNMFydtZXyOqW5mVmAs1cz0Ca8Jlyd+d+FiIjoYcTv8A8ZTzcdZo5sj437rmLLgQQYTWZpn6+nE4b2DoMgCAis4YqXeoVh2cazAICD527IrnMyNh1vDWldasiNTcrCV+vPSE/GOxWXjmUbz+L1gY9CEAS7fjzX0/OxdMMZxF7LBgDsO5OCcYObI8DH1a6vU5oTsTcRvfY0CvUm/H0yGdNebA2V6v4+PrMo4pvN53AqLh0v9gxD6zB/O4+WiIiI7hdbJh5CLk4aPNOtAea+1gEdmwQAAJy0arw64FHZ7GqXZoF4rHmtUq9xNSUH3245L2uHMJnNWLsrDv/57kiJx0QfOHsD63dfsdvHYBZFbDuUgPeWHZDCMFDc4vH+14cq9SEkoijij0MJ+O/KEyi81V99KTEL2w8n3vc1N++Px87jyUjPLsL/1p3mzYxEREQORBDZAHrfUlMrL9RoNCr4+LghIyMPRqP53ifcRXp2IQRBgI+HU4l9oigiPbsIRQYTTGYRmblF+Gz1SegNxa85pHcYHm8djJSMfHy1/gwuW4XTBkGe6NQ0EN9vvQDLJ9Hop5qiXeOaJV5HbzAhPacIOo0KNTydSx2nwWjGhcRMnIxNw/FLN5GScTt01/RxgVolIDmt+EZBtUpAVOe6qO3nBk83HWp4OaNObW8U5hfBZCr7Uzo7X4/9p1NQZDChRUM/BPu7yWa1i/Qm/PLnJfx5tOSSdDqtCrNGdUBNb5cyr1+aS0lZ+OC7IzBb/Vfz83LGjOHt4O7y4No/LMr7uWUWzTCaTTCajTCJxX8brN4u/mOCUSx+23TrWINogkm27/bxsnOlt03S+xBF6NRaaNU66FRa6KS/LW/roFNrkZNrRty1PIQF1UDdAB9ob223Pkej0tjlNxb2/L9YHbBe5cda2cZR62U2ixAE2P03pBXlqPWqCv7+Hvc8hoG4ApQSiG21/0wK/rfuNIDi4NmnQyi2HUpEkaF4tlQlCBjYpS76d64DtUqFTfuu4te/YgEAOo0KbRvXhN5gQpHBjNwCA9KyC5Gdp5eu7+fljCZ1fdA41AcGoxnxKbmIv5GD+JRc6TWs9WgdjMGRDWAWRUSvPY2Tl9PKHLtWo4Knqw5e7jqEBnigQZAnGgZ7wWg0449DCdh7OgUGq3oG+bmh/SM1YTaLOHs1A5evZcNkvv1fIqpzHeQVGKWA3DjUG1NeaFXuL3z5hQa8u+wg0rKLl7tzcVKjoKj4Y2xW3xfjBjcDIJYMmmLJEGkw3wqaJYKkEUUGA4pMBqhVouxc62ML9Hpk5Rcgu6AIBpMR7m5qODsJMJlNMNy6jskqnJpFZX8BFSDcCsk6Wch2Uuugtdouheg7Qrj2Vgh30TrBz9sTRfkmqESN/JoqrcN9E6xMRQYTvt58Dlm5egzpHYZavm4ljnlYvgmbzCYYRRMMZoPVD4DFPxSW9rdsv2iEwXTrb7Ph1v/V4r8NVn+bRBNUakAtauCkdoKz2glOmuK/nTXO0jZnjXyfk9oZLhqnavf554ifW3+fuIYftl2EVq1Ck7o+eLRuDTxar0aZEz8PkiPWq6owEFeyhzUQA8DPMRex5UBCie01fVzw6oAmaBDkJW0TRRHLfj+L3Xcs61YRKkFAw2AvDOxSF03q1pC2m80ifvnzErYeLDk2+xAhuGVB41yEbq1qoUFtD+Tr9Vi7Oxb5+iJAMKNFoxoIDnCF3mhAblERDCYTIJggCmYAZphwO4QmpWUjv0gPqMzQagFXFzVyCgohwgyozBAEM1B9vp89lCwhWisFa6uwbT3DrdJZ7b9jFrzETPjt7VqVBirh7t1toiiiUG+Cs05dIiBl5BThp+0XcSkpC65OGri7aOHuokUtP1d0aBKI2n4lQ21ZVmw6i53HkwEAXu46/OuFViVCsS1fu9KzC6FWCfByd5I+DrNoloVG6W/RxlB659+isdRQejusyo8X4fjfGgUIcNY4FQdnjXNxeJaFaierUO1cYl9xuL59nOXzLD4lB5evZcPVWQMPVx08XLUI8HGFVlO1XZZV/X3RmtksYuVfsbKb1q3VCfBAhyYB6NAkoNTfzj4IjlSvqsZAXMke5kBsMpux4KdjOBefKW3r2iIIz/doCGddyXsxDUYzPv5FfryFt7sOfl4uqOHphKxcPWKvZcFYSluDr6czwkO90byBLx6tVwNud1lN4ur1HMSn5CA7X4/sPANyCvTILzIhLasA2Xl65OYbSv125qxTo2uLIPh7u+DA2RRcTMyS7fdqEAe97/lSznz4iGYBEFWAqIKzRgsBaohmAaJZgFrQwNVJBw9nJ7jodFALahgNQGGRCL1ehLuzM/w8XeHqpINGpYFGKA5thUUi1IIaPu4u0Ko0t/api/9WqZGTZ8LqHVeRfLOg+LXNKoiiADcnJ0wc3Ap+Xi74ZNURXE3JBFQmQGWCoDIBahNUKjPEW9ugMiHAV4fU7DxAZQRUZmg0ZtTyd4aXpxpG0QC9yQC92YBCQxHy9UUwwQgzlBF07qRVaaBVaaERimelLX+MBhXyC0Tk5ppQVCTAVeeERkE1ULemN3QaHVJuFmHvyVQUFgqAWV1cb7P61ttqiGY1Qvy80KVJbbR9xB8uzqoSodLy9om4G/h9X9ytH+RMgMoMFxcVerSpBRcXlXScGSaotEBeQSH0xuKgqTcZUGQs/jcp0BuQpy9CoUEPo2iEIIjQOQEQzDAqJIg+rHRqHWDSoLAQgEkD0aQBTGqIJg10Kh1a1AtE7RpesllsmNSITy5AeqYJaRlG3LhpAMwa9GwTil5tQ+77RuTSlPV90Wgq/g3gwbM30LZxTfTpEAqV1Q+G6dmF+HbLeWTl6fFs94Z4pI5PhcZRUGTEV+vP4Nilm9I2nVYltRpaEwCEh3rj8dbBaB3mb9d63Cm3wAAAcHMubher6hxhIYpilf8mg4G4kj3MgRgo7reNXnMKOfkGPN21PlrdY2UEk9mM62n5UKkEOGnV0GnVcNapoVHLZxWKDCZcSszCpaQsOGnVqBPgjpAAjwr1095Zr4IiI+KSsxGblIVLSdko1BvRJrwmHmteCy5OtwN9WlYhTl5Og06rQuNQH2y+9jt2X9t/3+MojWhWQafWwEmjlQJiYZGI7FwjIKog3gqGxQFRKH7faptoVgGiUMq228fp1Br4eroiNUMPg14scQ1BVCE0wBOPhvqhZcMACGotPvv1ODJyiu45fh8PJ+QXGkttZwnwcUEtXzfcyCxASnq+1G7i5qxBg9peaBTsBbVKhbTsQqRlFeJ8QobUMqJRq+Dv7Sz1hXu4alHL1w0XEjIBFK+Y0rVFLWw9mCD7RqNRCxjaOxyPtQhC7LUsfLnuNFIzbz+B0cfDCQM610Vtfzf8cTABhy+kwvJVzkmrQpfmNfFYy5pQa4GktCxcS8/G0dgUXEvPvh3AVWZAXfy2oDLBy0uDQkMR9CYDRMEEQW0V1m/9UanN0GjNMCk0dFdHwq3/S5Y/EFXQCBro1Bo4a3Vw1eng5uQEV11xm41G0ECj1sBkFJCYko/46/kwGnDr/6S65P9Ps+WHPhVgVsPDxQmezs5ITC2Q9gECoDZCUBkBtQlN6nugWSMv+PpoUGTSo8hUhBvZ2TiXcBPJWVlwdRHgX0MDZxdAby5CoakIRcYiFJiKoDfp7/UhPzCiWYBa1MHTxQVuOhdpJlotaqGGFmrooIEWWkGHAG8PeDi5lmgNscxqW1pDSvu+eCkxC19vOYek1DzptVs29MMrUU3g6qzBpcQsLF59UmrbEwAM6FIXA7vUk8Kp2Szi2s08GM1maFQqqNUCNGoVNOpbb6tUyC00IDYpC7FJWTgRmyY99VUlCHipVyM81iIIccnZOB2XjuOX0nC1lBunA2q4ok/7EHR8NBB5BQbczCpEenYhTGYRTlo1nHRqOGnV8HDVwtvdqdTf+FgzGM24fC0LJy+n4+TlNCTcyAVQHM59PZ3h6+WMdk1qoW2YL1ysJrLyCg04cCYFSTfzoFapoLn18Trp1HBx0sDFSQ1XJy3qBHrAq5TlV/MKDbielg8vNx18PJ2gVqlujceE5LR8JN3Mw7WbeUhKLf47NbMATer64NWBj8LTtezlXCsTA3Ele9gDsZLYq175hgIcvnEchcZCqFXq4m+At2Y3TUZg7d/xuJlZBGeNFj7uLvBxd4GzVguDXoDeIEJfJKJIDxQViSgsMsNkBnq3DcXAiHqy1xFFEX8dTUJyej6C/NwQ7O+O2n5uuJFRgOOXbuJ4bBquJGejhqczWjbyQ8tGfgit6Y6jF29iz6nrUmis6e2CXu1C0KVZIJx1GpjNIpLT8xGfkgONWgUfDyfU8HCCl7tO+qJlqVXitUx8t/U8dp+0X6tLedX0ccHoJ5vC39sFC34+hrjkbNl+FycNpr7YCqEBHkjPLsQvf17CgbM34OvpjDGDmqJeLU/p2IIiI37cdhG7TibbZWwtG/rh0Xo1sGn/VaRn3/sHhjsF+rqgbbgfggNdEFTTGVdSMrH5wGVcz8opDtp3BGlBZYKgNsPFBXByEqDSmKX9BYYiFBqLyjhPuV+6xVthUCWoYTLe/mHPSaOFChrkF5hv/wZB+qFOjZrebhBNKuQXmpGXb4ZoEuDu7AwfdxfUcHeBs0YHgxEwGgRkZhtw+Vqe1Q+bdwRV8VYYLQcXJ7XsN2PZeXrZvQb3q3GoN7q1CcFvMRelgGVR08cFnZsGIuFGLo6cTy3xI5aPhxO6t6oNHw8nmMwijCYzrt3MxfG4FKTl5kFQGwG1ESq1CSG1nBEc4IQTV24g31BYHMDVRkBlLP7hTm2EWmuCGUZpn6AxAQ7yg50AAVpBBxetM1y0xbPSxTPZAm6mF89IF89mayDe2uft6opmdQOw+1gqjAa1tB9mNQAB4SHeiGheC6fj0nEqLl2aXbWFi5MGY55qikfr1SixLzktD/vPpGD/mRTZDeK2cNKq4eWmg6uzBi5OGrg6aSAIxW1QadmFyMrVl+tfSKtRocMjAWjewBdHL6bi0PlU2b00ZVGrBLRs5IfurWrjkTo+uJycjb+OJOHAuRvS+WqVgBqeTlAJAm5kFuBuidLX0xn/90wzhAbcO5zam+ID8cqVK7F8+XLEx8fDx8cHAwcOxLhx46DTlf4Thl6vx6JFi7B+/XqkpaUhNDQUo0aNwjPPPFOh65aFgdhxPKh6iaKIIoOp1LYRezOLIgSUfudyWlYh8ouMqO3nZvOv4O6sVVxyNuJTcuDl5gQfDyd4u+uQmlmIs1fTi280TM6Gh4sWdWt5om6gBwJ8XHE1JQfn4zMRl1x8E6JaJSDQ1xW1fN1gNJpxKSmr1G8wGrWADk0C8GLPMGmmPr/QgI9+PCbNqOg0Kkx+viUaBXvLzs3O08PVWVPiNw4WCTdysXrnZdmvMYHimebHW9VGVr4eu08kQ1/G50ctX1e80LMRmtbzBVA82/HnkSRs2h+PrDw9PN108HEvrlGgryvqBnqgToAHtBoVftsRi72nU8pVf7VKuK8w5eKkxqP1fKFRCTCLIkSY4e2pQcMQd9Su6QJRZcSFpDT8dTweSWnZxX3rOjPaNvFFaKALDObbbSQGkx5FJj30ZgNyCguQnpuPnDwDjEahlFlONSAKEM1qeDg7oUfrUDhrdTAZBWw7eA3pmXppBhSiCl6uzvBwdYHZAJhNAiCq4aLVwkXnBDcnHbzcnNE4tAbCQ7zh4qTB3lPXsXzTOdma6RYhNd2RnJZf6j5beLhq8VjzIKRmFuB0XDryi4zSPncXLcJCvOHqrEHijVwk3cwrV1gAin/L8VjzWujeqjbcXLRQqQSohOIZR73RDMOt31ZdSsrCmSsZOJ+QAb3BjJo+Lniue0O0e6QmatRwx43UHGw7lID1u6/cNZhp1KoK1wIoDnJPPVYPB86kyJa1BIp/u/PGU03RpI4PDGYDCk1FKNAXYu2+izhw/prVbPbtMF3TV4tAPx3UWjPMggGZ+Xm4npkNIwy3grkJgspxvo9ZQnPxb9IAQLj1W7hbX0tF4da24n3iHe9DFODqpEWwvztcdMWz1ypBBQECVLK3i79WZeUakHgjD5k5+ntet+QYit+XHXvHPsv7fl4ucNFpkJNvRE6+AUbLp3kFrwsRcHHSIr/QVHJ/Gde1/LCp06qgVgnSbwZ1WhVe6d8EbUtZjaoyKToQr1mzBtOmTcO0adPQs2dPXLp0CdOmTUOvXr0we/bsUs95++23ERMTgzlz5iA8PBwxMTGYO3cuPvroIwwYMOC+r1sWBmLHwXqVnz1rVWQwISdPL/u1GVD8g0NKRgHirmVDEABfL2f4ejrD292p1ACfW2DAst/PIjk9H0N6h+HRuiVnXMor9loWft9zFbmFBnRtHoQOTQKkm4FyCwzYcSwJh8+nwlmnRpCfG4L83FDbzw0Nb7V33EmtFuDh6YrcnIK71iv2WhZ+2n4RsUnZpe6vE+iBwZENEB7ijetp+biakoOEG7m4mVWIm1kFSMsqRF6hscR5wf7ueLxNbXRsElCuH8REUcSZKxm4lJSFjk0CEFCjfA+xMZtFnLmajj2nruPohZslWmS0GhWmv9wWITXdpW25BQas+fsyzCIQFuyFsBBv1KzhavPn1+Vr2Vi86gQyc4t/re3n5Yx/PN4QrcP8kZ2nx9ZDCfjzSJK0LrhGLcDf2wUatQrX0/PLDLA+Hk7o0z4UXVsGwenWQ4ZMZjNik7KRnlOIOgEeCKzhKvuh02wWkZKRj4QbudKf5LQ82X0PWo0KbcL90bttiHRDYHkYTWZk5erh41H8/+DO/4tFBhOOXEjFrhPJsrXWvdx0eKJ9KCJbBSE+JRdbDsTj2MWbpc4OqlUCwkO90aKBHwr0Ruw6kSybfQ4L8carUU3g6+UMs1nEX8eSsPKvWBTqTQit6Y6xTzeDXxlLS8YcScQPf1yUlpD0dNXipd7haBvuX+IH94IiI3798xL+OnateINQ/FuQ2gE6eLirodKaIKiNyCnMR2xyOkTLrLXaZBW4b71/x2y2oC7ZvkWORwUVVIIAQVDBaDTDbC4OzaJRg641H8cLbbs9sLEoOhD37NkTLVq0wIIFC6RtP/74I2bNmoWYmBjUqiV/oERiYiJ69eqFmTNn4rnnnpO2T5w4EWfOnMGWLVvu67p3w0DsOFiv8mOtbGNLvURRRFpWIS4nZyMuORtx17KhUgno3joYbcL9ZTf6lKZIb0KhwXRr2UITNGoVAnxcHvgNKQajGZcSM6XexKw8PV7qFYYOtx70czf3+/mVmVuErQcS4OPphMiWQdBq5E/JLCgy4trNPHi561DDw1nW/3kzqwDX0vJhNJrh4qyBi664D7J4/XLHff7U3Wp1M7MAxy7dhKuzBm3Da5Z4amhKej7OXM0ARBFqdXEfqKuTFuGh3rL7JMyiiAvxmTh8PhWBvq7o3qp2iR9Ks/P1SLiRi7Bg73uuJHEqLg3rdl9BiL87BnWtf897P67dzMONzAIE+7nB18u51M/lxBu5+G1HLI7H3l5S09/bGV2a1kLjOj5Iyy5Eclo+UjLyodGo4emihoeHGp5uAmoFOEGtNaHQWIQiU3GLUW5RAQ5cSEJqdi5Cgpzh662R9hWaipBvKERWQR7MMEOtAlQqQETxCieiKMJs9TbvB6gcTkX+WNh3ygN7PcUG4qtXr6J379746KOPMHDgQGl7cnIyIiMjMWfOHAwePFh2zq+//orp06dj586dCAi4/UV7zZo1mDp1KrZt2waz2Wzzde+GgdhxsF7lx1rZhvWyDetVfqyV3MXETJy9koHGdXzQKNirRHiuinpZQrFlSUApOEMsbl0SRZhhvh2mbx1f/L7Vcbe2lThXNEOE2epaotV55juud2vbrfGI0tt3HHfrb0EAnJw1yCsogslktjq+9I/l9g8E1h+L+Y7jRWm81j9A3B6D/FzzHa+VV2iAvkhAj9o9ENW0/QP5NwTKF4grvxHyPsTFxQEAQkJCZNsDAwOh1Wql/dYuX74MnU4nC8MAEBoaKu23ZH9brktERESVr1Gwd4l7B6qaIAgQIBQvBgL1vU9wIPyByzYOGYizs4t78Nzd3WXbBUGAm5ubtN9aTk4O3NxKLjBvuUZ2drYUiG257t2oVEKlrSmovnXjkLqMG4hIjvUqP9bKNqyXbViv8mOtbMN62Yb1so1DBuJ79cuVtv9+zrmfY6zVqOFW6b19np6l39xApWO9yo+1sg3rZRvWq/xYK9uwXrZhvcrHIQOxp2fxGqM5OfIeXVEUkZeXBy8vr1LPycvLK/FEFMs1vLy8YDabbb7u3aSn51XqDLGnpwuyswtgssMyOw871qv8WCvbsF62Yb3Kj7WyDetlG9brNh+fez+i3iEDcYMGDQAA8fHxaN26tbQ9MTERBoNB2n/nOXq9HsnJyQgKCpK2X7lyRdpvCcS2XPduzGYRZjsszn43JpOZvT82YL3Kj7WyDetlG9ar/Fgr27BetmG9yschG0uCg4PRsGFDxMTEyLbHxMRAq9UiIiKixDkRERHQaDT4888/S5wTHh6OoKCg+7ouERERET3cHDIQA8CECROwdetWLFu2DElJSdi2bRsWL16MoUOHws/PDydOnECfPn1w6NAhAEDNmjXx4osvYtGiRdi+fTsSExPx1VdfISYmBhMnTiz3dYmIiIioenHIlgkA6NWrF+bPn4/o6GgsXLgQfn5+GDZsGEaPHg0AKCgoQFxcHPLz86Vzpk6dCnd3d8ycORPp6emoV68eFi5ciO7du5f7ukRERERUvTjkgzmUgg/mcBysV/mxVrZhvWzDepUfa2Ub1ss2rNdt5Xkwh8O2TBARERERPQgMxERERERUrTEQExEREVG1xkBMRERERNUaAzERERERVWsMxERERERUrTEQExEREVG1xkBMRERERNUaAzERERERVWt8Uh0RERERVWucISYiIiKiao2BmIiIiIiqNQZiIiIiIqrWGIiJiIiIqFpjICYiIiKiao2BmIiIiIiqNQZiIiIiIqrWGIiJiIiIqFpjICYiIiKiao2B2AGtXLkS/fv3R7NmzdC1a1fMnz8fer2+qoflEFatWoUnn3wSLVu2RGRkJN555x2kpaVJ+2NjY/Hqq6+idevWaNWqFV555RXExsZW4Ygdw8iRIxEeHo7ExERp29GjR/HSSy+hZcuWaNeuHSZMmICUlJQqHGXVS0xMxJgxY9CqVSu0b98eY8aMYc1KYTQasWTJEvTt2xctWrSQ/i/euHFDOqa612rFihVo2rQpJk6cWGJfeWqTkpKCiRMnon379mjZsiVefPFFHD169EEN/4G6W60OHz6MYcOGoV27dujYsSNGjRqFs2fPyo7Jzs7GjBkz0LlzZzRr1gxPP/00/vzzzwc1/AfubvWy9sUXXyA8PByrVq2Sba9On1u2YCB2MGvWrMH06dPx7LPPYtOmTZg1axZWrlyJ2bNnV/XQqtyKFSswffp0PPPMM1i3bh3ef/997NmzB2PHjoUoikhPT8fLL78MURTxww8/4Oeff4ZKpcKwYcOQmZlZ1cOvMitXrsSBAwdk2y5fvowRI0YgODgYq1atwrJly5CYmIhRo0bBYDBU0UirVk5ODl5++WV4eHhg5cqVWLJkCa5fv45XX30VZrOZNbOycOFCLFq0CK+//jo2bNiADz/8EMeOHcNrr70Go9FYrWuVmZmJN954A0uXLoWTk1OJ/eWpjV6vx4gRIxAfH48lS5ZgzZo1qFOnDkaOHIkrV6484I+o8tyrVsePH8fw4cNRu3Zt/PTTT1i6dCkMBgOGDx+O1NRU6bhx48Zhz549WLhwITZu3IiIiAi8+eabOHTo0IP8cCrdveplLTY2Fl9++WWJ7dXlc+u+iORQevToIU6aNEm27YcffhAbN24sXrt2rYpGVfXMZrPYpUsXcdq0abLtv/76qxgWFibGxcWJn376qdiiRQsxKytL2p+ZmSk2b95c/OKLLx70kB1CSkqK2LZtW3H27NliWFiYmJCQIIqiKE6bNk2MjIwUDQaDdOylS5fEsLAwcd26dVU13Cq1ePFiMSoqSjSZTNK2uLg4cdOmTWJBQQFrZqVz584l/i/+/vvvYlhYmHjmzJlqXatvv/1WHDp0qHjz5k2xe/fu4oQJE2T7y1Ob1atXi2FhYeLly5elYwwGg9i1a1dx+vTpD+YDeQDuVasJEyaIPXr0kP2fjI+PF8PCwsSff/5ZFEVRPHDggBgWFibu2rVLdu5zzz0njhgxovI/iAfoXvWyMJlM4j/+8Q/p6/5vv/0m7asun1v3gzPEDuTq1atISEhAt27dZNsjIyNhNpuxe/fuKhpZ1RMEARs2bMBbb70l2+7v7w8AuHHjBnbv3o1WrVrB09NT2u/l5YWWLVvi77//fqDjdRSzZs1CmzZt0KtXL9n23bt3o0uXLtBoNNK2Bg0aICQkpNrWasuWLejXrx9UqttfFuvWrYs+ffrA2dmZNbMiiiLUarVsm6Uulq9V1bVW3bp1w/Lly+Hr61vq/vLUZteuXahTpw7q1asnHaPRaNClS5eHqn73qtWcOXOk3/RZ+Pn5AYDUnrNr1y44OTmhQ4cOsnO7du2KAwcOPFTthveql8W3336L5ORkTJgwocS+6vK5dT8YiB1IXFwcACAkJES2PTAwEFqtVtpfXXl7e8vCLgBs374dTk5OeOSRRxAXF1eidgAQGhpaLWu3adMm7Nu3DzNnzpRtz8/PR0pKCmtlxWAwIDY2FoGBgZg9ezYiIyPRuXNnTJo0CSkpKazZHV588UVs2rQJBw8ehCiKSElJwYoVK9CiRQvUq1evWtcqJCSkxA8LFuX9PLrb17Lk5GQUFBTYd9BV5G61AgBXV9cS4S8mJgYA0LJlSwDFtapVq5bsBwyguFYGgwEJCQn2HXQVule9gOL7ID755BPMnDkT7u7uJfZXl8+t+8FA7ECys7MBoMQnsSAIcHNzk/ZTsZiYGPzyyy94/fXX4eHhgZycHLi5uZU4zt3dvdrVLjMzE++//z7+9a9/ISAgQLYvJycHQMnPM8u26lYrAMjKyoLRaMR///tf+Pj44PPPP8eMGTNw8OBBvP7666zZHcaOHYvnn38eQ4YMQdOmTdG1a1eYzWZER0ezVndR3trc7WsZgGpbw8TERMyaNQudOnVCREQEgOJalVVPoPj/dnUyY8YMPP7444iMjCx1Pz+3yqa59yH0oAiCUKH91cmmTZswZcoU9O/fH6NHjwbA+libO3cuGjRogOeee66qh6IIRqMRANCmTRuMHTsWANCkSRNotVqMGTMGe/furcrhOZxly5bhxx9/xHvvvYdWrVohOTkZCxcuxP/93//ho48+qurhKR6/F5R06dIljBw5EjVr1sTChQul7azVbStXrsTZs2fx+++/l3kM61U2BmIHYmkHsMwiWIiiiLy8PHh5eVXFsBzOt99+i7lz5+LFF1/EO++8I/WXeXp6Ijc3t8TxOTk58Pb2fsCjrDo7d+7Etm3bsHbt2lL3l/V5ZtlWnWplYZkdadasmWx7+/btAUBaeo01AzIyMrBw4UK8+eabeOGFFwAAjRs3RmBgIJ566ikcPnwYAGtVmvL+37vb1zJBEEq0jj3sDh06hDFjxqBBgwaIjo6WfS/09PREUlJSiXMsNa4u3zdTU1Px4YcfYubMmahRo0aZx/Fzq2xsmXAgDRo0AADEx8fLticmJsJgMEj7q7Mff/wRc+bMweTJk/Hvf/9bdrNFgwYNcPXq1RLnXLlyBfXr13+Qw6xSmzZtQn5+Pp544gk0adIETZo0wfDhwwEAvXv3xhtvvIHatWuzVlbc3d3h5+eHjIwM2Xaz2SztZ82KXb16FQaDAQ0bNpRtt9ykEx8fz1qVwcXFpVy1udvXstq1a8PZ2bnSx+ooTp48iVdffRUdO3bE119/XSLgNmjQANeuXSuxnN+VK1eg0+kQHBz8IIdbZXbt2oWsrCxMnjxZ+rrfpEkTAMA777wjvc3PrbIxEDuQ4OBgNGzYULppwCImJgZarVbqmaqu9u7di1mzZuGdd97BK6+8UmJ/t27dcOzYMVmoSUtLw7Fjx/D4448/yKFWqQkTJmDdunVYs2aN9Of9998HAHz55Zd4//330a1bN+zatUv2TeTcuXO4du1ataqVta5du2L79u0QRVHadvDgQQBAWFgYa3aL5SanOx94Y7khrHbt2qzVXZSnNt26dUNiYiIuXrwoHaPX67Fr1y507979gY+5qqSlpeH111/HY489hk8++QQ6na7EMd26dYNery+xCtOff/6JiIiIUs95GPXo0QPr16+Xfd1fs2YNgOJ1mi1v83PrLqp21Te609atW8Xw8HBx6dKlYmJiovjHH3+Ibdu2FT/44IOqHlqVMpvNYt++fcUhQ4aIN27cKPEnNzdXzMnJESMjI8WRI0eK586dE8+ePSsOGzZM7N69u5ibm1vVH0KV2rdvn2wd4sTERLFNmzbiP//5T/Hy5cvi8ePHxYEDB4qDBw+WrflZnVy+fFls2bKlOG3aNPHChQvizp07xW7duon/+Mc/RFFkzay99tprYtu2bcW1a9eK8fHx4sGDB8XBgweL3bt3F3Nycqp1rTIyMqSvS127dhVHjx4tvV9QUFCu2hiNRnHQoEHioEGDxOPHj4uxsbHipEmTxHbt2j1U69Hfq1b//ve/xXbt2olXr14t8TU/IyNDus7o0aPF7t27i/v27RPj4+PFuXPnik2bNhVPnTpVdR9cJbhXvUpz5zrE1eVz634Iomg1HUIOYcOGDYiOjsaVK1fg5+eHwYMHY/To0fdcbuVhlpSUdNeZpbFjx+L//u//EB8fj7lz52L//v0QBAGdOnXCW2+9VW1+bVaW/fv34+WXX8b27dulWpw6dQrz5s3DiRMn4OTkhMcffxz/+te/7tp/9rA7ceIEPvzwQ5w8eRI6nQ5du3bF22+/Lc2KsmbFCgoKsGTJEvz+++9ITk6Gj48P2rRpgwkTJkhLOlXXWg0dOrTEkyEt/vOf/+Dpp58uV23S0tLwn//8Bzt27IBer0fLli3x1ltvoXHjxg/qQ6l096rV4sWLS+0PBor7+7/99lsAQF5eHhYsWIBNmzYhNzcXjzzyCCZNmoSOHTtW2tirQnk+t+4UHh5eYl91+Ny6HwzERERERFStsYeYiIiIiKo1BmIiIiIiqtYYiImIiIioWmMgJiIiIqJqjYGYiIiIiKo1BmIiIiIiqtYYiImIiIioWmMgJiKiChs6dCjCw8Nx8uTJqh4KEZHNNFU9ACKi6iwxMRE9evQo9/GWpzISEZH9MBATETkAFxeXcgXdVq1aPYDREBFVLwzEREQOwMnJCaNGjarqYRARVUsMxERECjRx4kRs3LgRa9euxeXLl7F06VJcvnwZANC0aVOMGzcO7dq1K3Hetm3b8P333+PMmTPIy8uDt7c3WrZsiZEjR6J169Yljo+Pj8fnn3+O3bt3IzMzEwEBAejYsSNGjx6N2rVrlzq2ffv24dNPP8XZs2fh5OSERx55BJMnT8ajjz4qO+7o0aNYsmQJjh8/joyMDLi7uyMkJAQDBgzAkCFDoFar7VApIqJ7YyAmIlIgnU4HAPjll1+wfv169O/fHz179sT58+exefNmjBgxAitWrEDbtm2lcxYtWoTPPvsM3t7eiIqKgr+/P+Lj47FhwwbExMRg/vz56Nevn3T82bNn8fLLL6OwsBADBw5EcHAwLl26hN9++w3bt2/Hjz/+iLp168rGtXfvXixduhS9e/dGly5dcOzYMezYsQNHjx7Fpk2bEBgYCAA4dOgQhg0bBmdnZ/Tt2xe1a9dGXl4e/vzzT8ydOxfHjx/HwoULK7+QREQAIBIRUZVJSEgQw8LCxPbt29t03tSpU8WwsDCxRYsWYmxsrGzf//73PzEsLEx86aWXpG2nT58Ww8PDxQ4dOojXrl2THX/w4EExPDxcbNeunZifny9tHzRokBgWFib+/fffsuO/++47MSwsTHzjjTekbUOGDBHDwsLEjh07inFxcbLjX331VTEsLExcvny5tG3SpEliWFiY+Ndff8mO1ev14pAhQ8Q2bdqUGCcRUWXhDDERkQMQRRGJiYl3PUar1SIgIEC2LSoqCvXr15dte+mll7Bo0SIcOnQIOTk58PDwwJo1ayCKIl544QXUqlVLdnzbtm3RsWNH7N27Fzt27ECfPn1w/vx5nD59GmFhYYiIiJAd/8wzzyApKQk1a9YsMcbnnnuuxKxx586dsWPHDly9elXalp2dDQBwdXUt8TEuX74cGg2/PRHRg8OvOEREDiArK+uey681btwYa9eulW1r06ZNiePc3NwQGhqK2NhYxMXFoXnz5jh16hQAlNonDADNmzfH3r17cfbsWfTp0wcnTpwAADzyyCMljnV2dsa//vWvUq9zZ58wAHh4eAAACgsLpW2RkZHYuXMn3nzzTbz66qvo3r07GjZsCAAMw0T0wPGrDhGRA3Bzc8OHH35412Pc3d1LbPP19S31WC8vLwC3Z2LT0tIAAH5+fqUeX6NGDQBARkYGACA9PV12nfIq7XhBEAAUz4JbvPjii7h58yaWLVuG+fPnY/78+fD390eXLl3w1FNPoVOnTja9LhFRRTAQExE5AK1Wi549e9p8XlmzqZbwqVIVP5C0tFBqzWw2y4678zr2JggCxo8fjxEjRmDHjh3YvXs39u7dizVr1mDNmjV47rnnMHv27Ep5bSKiO/HRzURECmaZyb1TZmYmgNsztpaZ4dTU1FKPt8wgW2acLTPGlu2VxdPTEwMGDMAHH3yAHTt2YMWKFQgMDMQvv/yCffv2VeprExFZMBATESnY8ePHS2zLy8tDYmIiNBoN6tWrBwBo1qwZAODIkSOlXufo0aOy45o3by5d/85ZYrPZjAkTJmD8+PEwGo33Ne6srCwkJSWV2N6pUyeMGDECAKS+ZyKiysZATESkYKtXr0ZCQoJs2/fffw+DwYAuXbpIqzg8/fTTUKlU+Pnnn3H9+nXZ8Tt37sThw4cREBAgrSgRHh6ORx99FElJSVi1apXs+I0bN2LTpk3Izc29rxvgcnJy0K1bN4wYMQJZWVkl9p87dw4ApDWLiYgqG3uIiYgcQFFREZYuXXrP45ycnDBkyBDp/e7du+OZZ57BgAEDULNmTZw9exabN2+Gs7MzJk6cKB3XqFEjjBs3Dp988gmeeuopDBgwAH5+frh48SI2bdoEZ2dnzJs3D1qtVjpnzpw5GDp0KKZPn449e/agYcOGiI2NxaZNm+Dt7Y0ZM2bc18fq4eGB119/HZ988on0QJHAwEAUFBTgyJEjOHDgAB599FH07t37vq5PRGQrBmIiIgdQUFBwz1UmgOIwaR2IBw8ejMjISCxbtgyxsbFQqVTo1KkTJk6cWGLJtNGjR6NRo0b47rvvsGbNGhQUFKBGjRro168fXn/9dWnZM4tHHnkEv/32m/To5i1btiAwMBBPPvkkxo4di6CgoPv+eEePHo169eph5cqViImJQXp6OjQaDerUqYNx48Zh+PDh0tP4iIgqmyBW1i3ERERUaaZNm4bVq1fjm2++QYcOHap6OEREisYeYiIiIiKq1hiIiYiIiKhaYyAmIiIiomqNgZiIiIiIqjXeVEdERERE1RpniImIiIioWmMgJiIiIqJqjYGYiIiIiKo1BmIiIiIiqtYYiImIiIioWmMgJiIiIqJqjYGYiIiIiKo1BmIiIiIiqtYYiImIiIioWvt/1dDSwHN6EZUAAAAASUVORK5CYII=\n", "text/plain": ["<Figure size 800x550 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.style.use(\"seaborn-v0_8\")\n", "plt.title(\"Learning Curves\", fontsize=20)\n", "plt.plot(np.linspace(1, n_epochs, n_epochs), epoch_loss_list, color=\"C0\", linewidth=2.0, label=\"Train\")\n", "plt.plot(\n", "    np.linspace(val_interval, n_epochs, int(n_epochs / val_interval)),\n", "    val_epoch_loss_list,\n", "    color=\"C1\",\n", "    linewidth=2.0,\n", "    label=\"Validation\",\n", ")\n", "plt.yticks(fontsize=12)\n", "plt.xticks(fontsize=12)\n", "plt.xlabel(\"Epochs\", fontsize=16)\n", "plt.ylabel(\"Loss\", fontsize=16)\n", "plt.legend(prop={\"size\": 14})\n", "plt.show()"]}, {"cell_type": "markdown", "id": "38724c9b", "metadata": {}, "source": ["## Sampling Brain Image\n", "\n", "In order to sample the brain images, we need to pass the model an image containing just noise and use it to remove the noise of the image iteratively. For that, we will use the `.sample()` function of the `inferer`."]}, {"cell_type": "code", "execution_count": 11, "id": "092eb6a0", "metadata": {"lines_to_next_cell": 2}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 1000/1000 [03:45<00:00,  4.44it/s]\n"]}], "source": ["model.eval()\n", "noise = torch.randn((1, 1, 32, 40, 32))\n", "noise = noise.to(device)\n", "scheduler.set_timesteps(num_inference_steps=1000)\n", "image = inferer.sample(input_noise=noise, diffusion_model=model, scheduler=scheduler)"]}, {"cell_type": "code", "execution_count": 12, "id": "5dc3e69d", "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.style.use(\"default\")\n", "plotting_image_0 = np.concatenate([image[0, 0, :, :, 15].cpu(), np.flipud(image[0, 0, :, 20, :].cpu().T)], axis=1)\n", "plotting_image_1 = np.concatenate([np.flipud(image[0, 0, 15, :, :].cpu().T), np.zeros((32, 32))], axis=1)\n", "plt.imshow(np.concatenate([plotting_image_0, plotting_image_1], axis=0), vmin=0, vmax=1, cmap=\"gray\")\n", "plt.tight_layout()\n", "plt.axis(\"off\")\n", "plt.show()"]}, {"cell_type": "markdown", "id": "f0acc27a", "metadata": {}, "source": ["### Sampling with Denoising Diffusion Implicit Model Scheduler\n", "\n", "Recent papers have proposed different ways to improve the sampling speed by using fewer steps in the denoising process. In this example, we are using a `DDIMScheduler` (from [<PERSON> et al. \"Denoising Diffusion Implicit Models\"](https://arxiv.org/abs/2010.02502)) to reduce the original number of steps from 1000 to 250."]}, {"cell_type": "code", "execution_count": 46, "id": "e3e43b95", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 250/250 [00:55<00:00,  4.52it/s]\n"]}], "source": ["scheduler_ddim = DDIMScheduler(\n", "    num_train_timesteps=1000, schedule=\"scaled_linear_beta\", beta_start=0.0005, beta_end=0.0195, clip_sample=False\n", ")\n", "\n", "scheduler_ddim.set_timesteps(num_inference_steps=250)\n", "\n", "model.eval()\n", "noise = torch.randn((1, 1, 32, 40, 32))\n", "noise = noise.to(device)\n", "\n", "image = inferer.sample(input_noise=noise, diffusion_model=model, scheduler=scheduler_ddim)"]}, {"cell_type": "code", "execution_count": 47, "id": "89f93ab8", "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.style.use(\"default\")\n", "plotting_image_0 = np.concatenate([image[0, 0, :, :, 15].cpu(), np.flipud(image[0, 0, :, 20, :].cpu().T)], axis=1)\n", "plotting_image_1 = np.concatenate([np.flipud(image[0, 0, 15, :, :].cpu().T), np.zeros((32, 32))], axis=1)\n", "plt.imshow(np.concatenate([plotting_image_0, plotting_image_1], axis=0), vmin=0, vmax=1, cmap=\"gray\")\n", "plt.tight_layout()\n", "plt.axis(\"off\")\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "a39c881c", "metadata": {}, "outputs": [], "source": []}], "metadata": {"jupytext": {"formats": "py:percent,ipynb"}, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.13"}}, "nbformat": 4, "nbformat_minor": 5}