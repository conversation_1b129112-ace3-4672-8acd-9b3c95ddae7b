{"cells": [{"cell_type": "code", "execution_count": null, "id": "99d4a6b2", "metadata": {}, "outputs": [], "source": ["# Copyright (c) MONAI Consortium\n", "# Licensed under the Apache License, Version 2.0 (the \"License\");\n", "# you may not use this file except in compliance with the License.\n", "# You may obtain a copy of the License at\n", "#     http://www.apache.org/licenses/LICENSE-2.0\n", "# Unless required by applicable law or agreed to in writing, software\n", "# distributed under the License is distributed on an \"AS IS\" BASIS,\n", "# WITHOUT WAR<PERSON><PERSON>IES OR CONDITIONS OF ANY KIND, either express or implied.\n", "# See the License for the specific language governing permissions and\n", "# limitations under the License."]}, {"cell_type": "markdown", "id": "d6ae75bf", "metadata": {}, "source": ["# AutoencoderKL\n", "\n", "This demo is a toy example of how to use MONAI's `AutoencoderKL` class. In particular, it uses\n", "the Autoencoder with a Kullback-Leibler regularisation as implemented by <PERSON><PERSON><PERSON> et. al [1].\n", "\n", "[1] <PERSON><PERSON><PERSON> et. al \"High-Resolution Image Synthesis with Latent Diffusion Models\" https://arxiv.org/pdf/2112.10752.pdf\n", "\n", "\n", "\n", "This tutorial was based on:\n", "\n", "[Registration Mednist](https://github.com/Project-MONAI/tutorials/blob/main/2d_registration/registration_mednist.ipynb)\n", "\n", "[Mednist Tutorial](https://github.com/Project-MONAI/tutorials/blob/main/2d_classification/mednist_tutorial.ipynb)\n", "\n", "\n"]}, {"cell_type": "markdown", "id": "2caa73e1", "metadata": {}, "source": ["## Set up environment using Colab"]}, {"cell_type": "code", "execution_count": 2, "id": "c942c848", "metadata": {}, "outputs": [], "source": ["!python -c \"import monai\" || pip install -q \"monai-weekly[tqdm]\"\n", "!python -c \"import matplotlib\" || pip install -q matplotlib\n", "%matplotlib inline"]}, {"cell_type": "markdown", "id": "76b639ff", "metadata": {}, "source": ["## Setup imports"]}, {"cell_type": "code", "execution_count": 1, "id": "350736c2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["MONAI version: 1.2.dev2304\n", "Numpy version: 1.23.5\n", "Pytorch version: 1.13.1+cu117\n", "MONAI flags: HAS_EXT = False, USE_COMPILED = False, USE_META_DICT = False\n", "MONAI rev id: 9a57be5aab9f2c2a134768c0c146399150e247a0\n", "MONAI __file__: /home/<USER>/PycharmProjects/GenerativeModels/venv/lib/python3.9/site-packages/monai/__init__.py\n", "\n", "Optional dependencies:\n", "Pytorch Ignite version: 0.4.10\n", "ITK version: 5.3.0\n", "Nibabel version: 5.0.0\n", "scikit-image version: 0.19.3\n", "Pillow version: 9.4.0\n", "Tensorboard version: 2.12.0\n", "gdown version: 4.6.3\n", "TorchVision version: 0.14.1+cu117\n", "tqdm version: 4.64.1\n", "lmdb version: 1.4.0\n", "psutil version: 5.9.4\n", "pandas version: 1.5.3\n", "einops version: 0.6.0\n", "transformers version: 4.21.3\n", "mlflow version: 2.1.1\n", "pynrrd version: 1.0.0\n", "\n", "For details about installing the optional dependencies, please visit:\n", "    https://docs.monai.io/en/latest/installation.html#installing-the-recommended-dependencies\n", "\n"]}], "source": ["import os\n", "import shutil\n", "import tempfile\n", "import time\n", "from pathlib import Path\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "import torch\n", "from monai import transforms\n", "from monai.apps import MedNISTDataset\n", "from monai.config import print_config\n", "from monai.data import DataLoader, Dataset\n", "from monai.networks.layers import Act\n", "from monai.utils import first, set_determinism\n", "from torch.nn import L1Loss\n", "from tqdm import tqdm\n", "\n", "from generative.losses import PatchAdversarialLoss, PerceptualLoss\n", "from generative.networks.nets import AutoencoderK<PERSON>, PatchDiscriminator\n", "\n", "print_config()"]}, {"cell_type": "code", "execution_count": 4, "id": "c9552991", "metadata": {}, "outputs": [], "source": ["# for reproducibility purposes set a seed\n", "set_determinism(42)"]}, {"cell_type": "markdown", "id": "6e3aacc9", "metadata": {}, "source": ["## Setup a data directory and download dataset"]}, {"cell_type": "markdown", "id": "4c821bb6", "metadata": {}, "source": ["Specify a `MONAI_DATA_DIRECTORY` variable, where the data will be downloaded. If not\n", "specified a temporary directory will be used."]}, {"cell_type": "code", "execution_count": 5, "id": "dbad31d5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/tmp/tmpveta1y62\n"]}], "source": ["directory = os.environ.get(\"MONAI_DATA_DIRECTORY\")\n", "root_dir = tempfile.mkdtemp() if directory is None else directory\n", "print(root_dir)"]}, {"cell_type": "markdown", "id": "6cb6282d", "metadata": {}, "source": ["### Download the training set"]}, {"cell_type": "code", "execution_count": 6, "id": "83d59e68", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["MedNIST.tar.gz: 59.0MB [00:03, 16.5MB/s]                                                                                                                                                                                        "]}, {"name": "stdout", "output_type": "stream", "text": ["2022-12-04 21:11:27,009 - INFO - Downloaded: /tmp/tmpveta1y62/MedNIST.tar.gz\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["2022-12-04 21:11:27,079 - INFO - Verified 'MedNIST.tar.gz', md5: 0bc7306e7427e00ad1c5526a6677552d.\n", "2022-12-04 21:11:27,080 - INFO - Writing into directory: /tmp/tmpveta1y62.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Loading dataset: 100%|██████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 47164/47164 [00:14<00:00, 3298.72it/s]\n"]}], "source": ["train_data = MedNISTDataset(root_dir=root_dir, section=\"training\", download=True, seed=0)\n", "train_datalist = [{\"image\": item[\"image\"]} for item in train_data.data if item[\"class_name\"] == \"Hand\"]\n", "image_size = 64\n", "train_transforms = transforms.Compose(\n", "    [\n", "        transforms.LoadImaged(keys=[\"image\"]),\n", "        transforms.EnsureChannelFirstd(keys=[\"image\"]),\n", "        transforms.ScaleIntensityRanged(keys=[\"image\"], a_min=0.0, a_max=255.0, b_min=0.0, b_max=1.0, clip=True),\n", "        transforms.RandA<PERSON>ed(\n", "            keys=[\"image\"],\n", "            rotate_range=[(-np.pi / 36, np.pi / 36), (-np.pi / 36, np.pi / 36)],\n", "            translate_range=[(-1, 1), (-1, 1)],\n", "            scale_range=[(-0.05, 0.05), (-0.05, 0.05)],\n", "            spatial_size=[image_size, image_size],\n", "            padding_mode=\"zeros\",\n", "            prob=0.5,\n", "        ),\n", "    ]\n", ")\n", "train_ds = Dataset(data=train_datalist, transform=train_transforms)\n", "train_loader = DataLoader(train_ds, batch_size=64, shuffle=True, num_workers=4, persistent_workers=True)"]}, {"cell_type": "markdown", "id": "8e3d936d", "metadata": {}, "source": ["### Visualise examples from the training set"]}, {"cell_type": "code", "execution_count": 7, "id": "ebeb6144", "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 640x480 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Plot 3 examples from the training set\n", "check_data = first(train_loader)\n", "fig, ax = plt.subplots(nrows=1, ncols=3)\n", "for image_n in range(3):\n", "    ax[image_n].imshow(check_data[\"image\"][image_n, 0, :, :], cmap=\"gray\")\n", "    ax[image_n].axis(\"off\")"]}, {"cell_type": "markdown", "id": "f00f0f19", "metadata": {}, "source": ["### Download the validation set"]}, {"cell_type": "code", "execution_count": 8, "id": "53c9eb04", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2022-12-04 21:11:46,339 - INFO - Verified 'MedNIST.tar.gz', md5: 0bc7306e7427e00ad1c5526a6677552d.\n", "2022-12-04 21:11:46,340 - INFO - File exists: /tmp/tmpveta1y62/MedNIST.tar.gz, skipped downloading.\n", "2022-12-04 21:11:46,340 - INFO - Non-empty folder exists in /tmp/tmpveta1y62/MedNIST, skipped extracting.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Loading dataset: 100%|████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 5895/5895 [00:01<00:00, 3265.53it/s]\n"]}], "source": ["val_data = MedNISTDataset(root_dir=root_dir, section=\"validation\", download=True, seed=0)\n", "val_datalist = [{\"image\": item[\"image\"]} for item in val_data.data if item[\"class_name\"] == \"Hand\"]\n", "val_transforms = transforms.Compose(\n", "    [\n", "        transforms.LoadImaged(keys=[\"image\"]),\n", "        transforms.EnsureChannelFirstd(keys=[\"image\"]),\n", "        transforms.ScaleIntensityRanged(keys=[\"image\"], a_min=0.0, a_max=255.0, b_min=0.0, b_max=1.0, clip=True),\n", "    ]\n", ")\n", "val_ds = Dataset(data=val_datalist, transform=val_transforms)\n", "val_loader = DataLoader(val_ds, batch_size=64, shuffle=True, num_workers=4, persistent_workers=True)"]}, {"cell_type": "markdown", "id": "083aea1b", "metadata": {}, "source": ["## Define the network"]}, {"cell_type": "code", "execution_count": 9, "id": "86b34ad5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Using cuda\n"]}, {"data": {"text/plain": ["AutoencoderKL(\n", "  (encoder): Encoder(\n", "    (blocks): ModuleList(\n", "      (0): Convolution(\n", "        (conv): Conv2d(1, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "      )\n", "      (1): <PERSON><PERSON><PERSON><PERSON>(\n", "        (norm1): GroupNorm(32, 128, eps=1e-06, affine=True)\n", "        (conv1): Convolution(\n", "          (conv): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "        )\n", "        (norm2): GroupNorm(32, 128, eps=1e-06, affine=True)\n", "        (conv2): Convolution(\n", "          (conv): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "        )\n", "        (nin_shortcut): Identity()\n", "      )\n", "      (2): <PERSON><PERSON>(\n", "        (conv): Convolution(\n", "          (conv): Conv2d(128, 128, kernel_size=(3, 3), stride=(2, 2))\n", "        )\n", "      )\n", "      (3): <PERSON><PERSON><PERSON><PERSON>(\n", "        (norm1): GroupNorm(32, 128, eps=1e-06, affine=True)\n", "        (conv1): Convolution(\n", "          (conv): Conv2d(128, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "        )\n", "        (norm2): GroupNorm(32, 256, eps=1e-06, affine=True)\n", "        (conv2): Convolution(\n", "          (conv): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "        )\n", "        (nin_shortcut): Convolution(\n", "          (conv): Conv2d(128, 256, kernel_size=(1, 1), stride=(1, 1))\n", "        )\n", "      )\n", "      (4): <PERSON><PERSON>(\n", "        (conv): Convolution(\n", "          (conv): Conv2d(256, 256, kernel_size=(3, 3), stride=(2, 2))\n", "        )\n", "      )\n", "      (5): <PERSON><PERSON><PERSON><PERSON>(\n", "        (norm1): GroupNorm(32, 256, eps=1e-06, affine=True)\n", "        (conv1): Convolution(\n", "          (conv): Conv2d(256, 384, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "        )\n", "        (norm2): GroupNorm(32, 384, eps=1e-06, affine=True)\n", "        (conv2): Convolution(\n", "          (conv): Conv2d(384, 384, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "        )\n", "        (nin_shortcut): Convolution(\n", "          (conv): Conv2d(256, 384, kernel_size=(1, 1), stride=(1, 1))\n", "        )\n", "      )\n", "      (6): AttnBlock(\n", "        (norm): GroupNorm(32, 384, eps=1e-06, affine=True)\n", "        (q): Convolution(\n", "          (conv): Conv2d(384, 384, kernel_size=(1, 1), stride=(1, 1))\n", "        )\n", "        (k): Convolution(\n", "          (conv): Conv2d(384, 384, kernel_size=(1, 1), stride=(1, 1))\n", "        )\n", "        (v): Convolution(\n", "          (conv): Conv2d(384, 384, kernel_size=(1, 1), stride=(1, 1))\n", "        )\n", "        (proj_out): Convolution(\n", "          (conv): Conv2d(384, 384, kernel_size=(1, 1), stride=(1, 1))\n", "        )\n", "      )\n", "      (7): <PERSON><PERSON><PERSON><PERSON>(\n", "        (norm1): GroupNorm(32, 384, eps=1e-06, affine=True)\n", "        (conv1): Convolution(\n", "          (conv): Conv2d(384, 384, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "        )\n", "        (norm2): GroupNorm(32, 384, eps=1e-06, affine=True)\n", "        (conv2): Convolution(\n", "          (conv): Conv2d(384, 384, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "        )\n", "        (nin_shortcut): Identity()\n", "      )\n", "      (8): AttnBlock(\n", "        (norm): GroupNorm(32, 384, eps=1e-06, affine=True)\n", "        (q): Convolution(\n", "          (conv): Conv2d(384, 384, kernel_size=(1, 1), stride=(1, 1))\n", "        )\n", "        (k): Convolution(\n", "          (conv): Conv2d(384, 384, kernel_size=(1, 1), stride=(1, 1))\n", "        )\n", "        (v): Convolution(\n", "          (conv): Conv2d(384, 384, kernel_size=(1, 1), stride=(1, 1))\n", "        )\n", "        (proj_out): Convolution(\n", "          (conv): Conv2d(384, 384, kernel_size=(1, 1), stride=(1, 1))\n", "        )\n", "      )\n", "      (9): <PERSON><PERSON><PERSON><PERSON>(\n", "        (norm1): GroupNorm(32, 384, eps=1e-06, affine=True)\n", "        (conv1): Convolution(\n", "          (conv): Conv2d(384, 384, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "        )\n", "        (norm2): GroupNorm(32, 384, eps=1e-06, affine=True)\n", "        (conv2): Convolution(\n", "          (conv): Conv2d(384, 384, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "        )\n", "        (nin_shortcut): Identity()\n", "      )\n", "      (10): GroupNorm(32, 384, eps=1e-06, affine=True)\n", "      (11): Convolution(\n", "        (conv): Conv2d(384, 8, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "      )\n", "    )\n", "  )\n", "  (decoder): Decoder(\n", "    (blocks): ModuleList(\n", "      (0): Convolution(\n", "        (conv): Conv2d(8, 384, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "      )\n", "      (1): <PERSON><PERSON><PERSON><PERSON>(\n", "        (norm1): GroupNorm(32, 384, eps=1e-06, affine=True)\n", "        (conv1): Convolution(\n", "          (conv): Conv2d(384, 384, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "        )\n", "        (norm2): GroupNorm(32, 384, eps=1e-06, affine=True)\n", "        (conv2): Convolution(\n", "          (conv): Conv2d(384, 384, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "        )\n", "        (nin_shortcut): Identity()\n", "      )\n", "      (2): AttnBlock(\n", "        (norm): GroupNorm(32, 384, eps=1e-06, affine=True)\n", "        (q): Convolution(\n", "          (conv): Conv2d(384, 384, kernel_size=(1, 1), stride=(1, 1))\n", "        )\n", "        (k): Convolution(\n", "          (conv): Conv2d(384, 384, kernel_size=(1, 1), stride=(1, 1))\n", "        )\n", "        (v): Convolution(\n", "          (conv): Conv2d(384, 384, kernel_size=(1, 1), stride=(1, 1))\n", "        )\n", "        (proj_out): Convolution(\n", "          (conv): Conv2d(384, 384, kernel_size=(1, 1), stride=(1, 1))\n", "        )\n", "      )\n", "      (3): <PERSON><PERSON><PERSON><PERSON>(\n", "        (norm1): GroupNorm(32, 384, eps=1e-06, affine=True)\n", "        (conv1): Convolution(\n", "          (conv): Conv2d(384, 384, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "        )\n", "        (norm2): GroupNorm(32, 384, eps=1e-06, affine=True)\n", "        (conv2): Convolution(\n", "          (conv): Conv2d(384, 384, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "        )\n", "        (nin_shortcut): Identity()\n", "      )\n", "      (4): <PERSON><PERSON><PERSON><PERSON>(\n", "        (norm1): GroupNorm(32, 384, eps=1e-06, affine=True)\n", "        (conv1): Convolution(\n", "          (conv): Conv2d(384, 384, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "        )\n", "        (norm2): GroupNorm(32, 384, eps=1e-06, affine=True)\n", "        (conv2): Convolution(\n", "          (conv): Conv2d(384, 384, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "        )\n", "        (nin_shortcut): Identity()\n", "      )\n", "      (5): AttnBlock(\n", "        (norm): GroupNorm(32, 384, eps=1e-06, affine=True)\n", "        (q): Convolution(\n", "          (conv): Conv2d(384, 384, kernel_size=(1, 1), stride=(1, 1))\n", "        )\n", "        (k): Convolution(\n", "          (conv): Conv2d(384, 384, kernel_size=(1, 1), stride=(1, 1))\n", "        )\n", "        (v): Convolution(\n", "          (conv): Conv2d(384, 384, kernel_size=(1, 1), stride=(1, 1))\n", "        )\n", "        (proj_out): Convolution(\n", "          (conv): Conv2d(384, 384, kernel_size=(1, 1), stride=(1, 1))\n", "        )\n", "      )\n", "      (6): <PERSON><PERSON><PERSON>(\n", "        (conv): Convolution(\n", "          (conv): Conv2d(384, 384, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "        )\n", "      )\n", "      (7): <PERSON><PERSON><PERSON><PERSON>(\n", "        (norm1): GroupNorm(32, 384, eps=1e-06, affine=True)\n", "        (conv1): Convolution(\n", "          (conv): Conv2d(384, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "        )\n", "        (norm2): GroupNorm(32, 256, eps=1e-06, affine=True)\n", "        (conv2): Convolution(\n", "          (conv): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "        )\n", "        (nin_shortcut): Convolution(\n", "          (conv): Conv2d(384, 256, kernel_size=(1, 1), stride=(1, 1))\n", "        )\n", "      )\n", "      (8): <PERSON><PERSON><PERSON>(\n", "        (conv): Convolution(\n", "          (conv): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "        )\n", "      )\n", "      (9): <PERSON><PERSON><PERSON><PERSON>(\n", "        (norm1): GroupNorm(32, 256, eps=1e-06, affine=True)\n", "        (conv1): Convolution(\n", "          (conv): Conv2d(256, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "        )\n", "        (norm2): GroupNorm(32, 128, eps=1e-06, affine=True)\n", "        (conv2): Convolution(\n", "          (conv): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "        )\n", "        (nin_shortcut): Convolution(\n", "          (conv): Conv2d(256, 128, kernel_size=(1, 1), stride=(1, 1))\n", "        )\n", "      )\n", "      (10): GroupNorm(32, 128, eps=1e-06, affine=True)\n", "      (11): Convolution(\n", "        (conv): Conv2d(128, 1, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "      )\n", "    )\n", "  )\n", "  (quant_conv_mu): Convolution(\n", "    (conv): Conv2d(8, 8, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "  )\n", "  (quant_conv_log_sigma): Convolution(\n", "    (conv): Conv2d(8, 8, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "  )\n", "  (post_quant_conv): Convolution(\n", "    (conv): Conv2d(8, 8, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "  )\n", ")"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "print(f\"Using {device}\")\n", "model = AutoencoderKL(\n", "    spatial_dims=2,\n", "    in_channels=1,\n", "    out_channels=1,\n", "    num_channels=(128, 256, 384),\n", "    latent_channels=8,\n", "    num_res_blocks=1,\n", "    norm_num_groups=32,\n", "    attention_levels=(False, False, True),\n", ")\n", "model.to(device)"]}, {"cell_type": "code", "execution_count": 10, "id": "671ad579", "metadata": {}, "outputs": [{"data": {"text/plain": ["PatchDiscriminator(\n", "  (initial_conv): Convolution(\n", "    (conv): Conv2d(1, 64, kernel_size=(4, 4), stride=(2, 2), padding=(1, 1))\n", "    (adn): ADN(\n", "      (D): Dropout(p=0.0, inplace=False)\n", "      (A): LeakyReLU(negative_slope=0.2)\n", "    )\n", "  )\n", "  (0): Convolution(\n", "    (conv): Conv2d(64, 128, kernel_size=(4, 4), stride=(2, 2), padding=(1, 1), bias=False)\n", "    (adn): ADN(\n", "      (N): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (D): Dropout(p=0.0, inplace=False)\n", "      (A): LeakyReLU(negative_slope=0.2)\n", "    )\n", "  )\n", "  (1): Convolution(\n", "    (conv): Conv2d(128, 256, kernel_size=(4, 4), stride=(2, 2), padding=(1, 1), bias=False)\n", "    (adn): ADN(\n", "      (N): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (D): Dropout(p=0.0, inplace=False)\n", "      (A): LeakyReLU(negative_slope=0.2)\n", "    )\n", "  )\n", "  (2): Convolution(\n", "    (conv): Conv2d(256, 512, kernel_size=(4, 4), stride=(1, 1), padding=(1, 1), bias=False)\n", "    (adn): ADN(\n", "      (N): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (D): Dropout(p=0.0, inplace=False)\n", "      (A): LeakyReLU(negative_slope=0.2)\n", "    )\n", "  )\n", "  (final_conv): Convolution(\n", "    (conv): Conv2d(512, 1, kernel_size=(4, 4), stride=(1, 1), padding=(1, 1))\n", "  )\n", ")"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["discriminator = PatchDiscriminator(\n", "    spatial_dims=2,\n", "    num_layers_d=3,\n", "    num_channels=64,\n", "    in_channels=1,\n", "    out_channels=1,\n", "    kernel_size=4,\n", "    activation=(Act.LEAKYRELU, {\"negative_slope\": 0.2}),\n", "    norm=\"BATCH\",\n", "    bias=False,\n", "    padding=1,\n", ")\n", "discriminator.to(device)"]}, {"cell_type": "code", "execution_count": 11, "id": "7f259580", "metadata": {}, "outputs": [{"data": {"text/plain": ["PerceptualLoss(\n", "  (perceptual_function): LPIPS(\n", "    (scaling_layer): ScalingLayer()\n", "    (net): alexnet(\n", "      (slice1): Sequential(\n", "        (0): Conv2d(3, 64, kernel_size=(11, 11), stride=(4, 4), padding=(2, 2))\n", "        (1): ReLU(inplace=True)\n", "      )\n", "      (slice2): Sequential(\n", "        (2): MaxPool2d(kernel_size=3, stride=2, padding=0, dilation=1, ceil_mode=False)\n", "        (3): Conv2d(64, 192, kernel_size=(5, 5), stride=(1, 1), padding=(2, 2))\n", "        (4): ReLU(inplace=True)\n", "      )\n", "      (slice3): Sequential(\n", "        (5): MaxPool2d(kernel_size=3, stride=2, padding=0, dilation=1, ceil_mode=False)\n", "        (6): Conv2d(192, 384, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "        (7): ReLU(inplace=True)\n", "      )\n", "      (slice4): Sequential(\n", "        (8): Conv2d(384, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "        (9): ReLU(inplace=True)\n", "      )\n", "      (slice5): Sequential(\n", "        (10): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "        (11): ReLU(inplace=True)\n", "      )\n", "    )\n", "    (lin0): NetLinLayer(\n", "      (model): Sequential(\n", "        (0): Dropout(p=0.5, inplace=False)\n", "        (1): Conv2d(64, 1, kernel_size=(1, 1), stride=(1, 1), bias=False)\n", "      )\n", "    )\n", "    (lin1): NetLin<PERSON>ayer(\n", "      (model): Sequential(\n", "        (0): Dropout(p=0.5, inplace=False)\n", "        (1): Conv2d(192, 1, kernel_size=(1, 1), stride=(1, 1), bias=False)\n", "      )\n", "    )\n", "    (lin2): NetLin<PERSON>ayer(\n", "      (model): Sequential(\n", "        (0): Dropout(p=0.5, inplace=False)\n", "        (1): Conv2d(384, 1, kernel_size=(1, 1), stride=(1, 1), bias=False)\n", "      )\n", "    )\n", "    (lin3): NetLin<PERSON>ayer(\n", "      (model): Sequential(\n", "        (0): Dropout(p=0.5, inplace=False)\n", "        (1): Conv2d(256, 1, kernel_size=(1, 1), stride=(1, 1), bias=False)\n", "      )\n", "    )\n", "    (lin4): NetLin<PERSON>ayer(\n", "      (model): Sequential(\n", "        (0): Dropout(p=0.5, inplace=False)\n", "        (1): Conv2d(256, 1, kernel_size=(1, 1), stride=(1, 1), bias=False)\n", "      )\n", "    )\n", "    (lins): ModuleList(\n", "      (0): NetLinLayer(\n", "        (model): Sequential(\n", "          (0): Dropout(p=0.5, inplace=False)\n", "          (1): Conv2d(64, 1, kernel_size=(1, 1), stride=(1, 1), bias=False)\n", "        )\n", "      )\n", "      (1): NetLinLayer(\n", "        (model): Sequential(\n", "          (0): Dropout(p=0.5, inplace=False)\n", "          (1): Conv2d(192, 1, kernel_size=(1, 1), stride=(1, 1), bias=False)\n", "        )\n", "      )\n", "      (2): NetLinLayer(\n", "        (model): Sequential(\n", "          (0): Dropout(p=0.5, inplace=False)\n", "          (1): Conv2d(384, 1, kernel_size=(1, 1), stride=(1, 1), bias=False)\n", "        )\n", "      )\n", "      (3): NetLinLayer(\n", "        (model): Sequential(\n", "          (0): Dropout(p=0.5, inplace=False)\n", "          (1): Conv2d(256, 1, kernel_size=(1, 1), stride=(1, 1), bias=False)\n", "        )\n", "      )\n", "      (4): NetLin<PERSON>ayer(\n", "        (model): Sequential(\n", "          (0): Dropout(p=0.5, inplace=False)\n", "          (1): Conv2d(256, 1, kernel_size=(1, 1), stride=(1, 1), bias=False)\n", "        )\n", "      )\n", "    )\n", "  )\n", ")"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["perceptual_loss = PerceptualLoss(spatial_dims=2, network_type=\"alex\")\n", "perceptual_loss.to(device)"]}, {"cell_type": "code", "execution_count": 12, "id": "f39cfd6e", "metadata": {}, "outputs": [], "source": ["optimizer_g = torch.optim.Adam(params=model.parameters(), lr=1e-4)\n", "optimizer_d = torch.optim.Adam(params=discriminator.parameters(), lr=5e-4)"]}, {"cell_type": "code", "execution_count": 13, "id": "b0656065", "metadata": {}, "outputs": [], "source": ["l1_loss = L1Loss()\n", "adv_loss = PatchAdversarialLoss(criterion=\"least_squares\")\n", "adv_weight = 0.01\n", "perceptual_weight = 0.001"]}, {"cell_type": "markdown", "id": "66041923", "metadata": {}, "source": ["## Model Training"]}, {"cell_type": "code", "execution_count": 14, "id": "ea9e0a54", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Epoch 0: 100%|███████████| 125/125 [01:21<00:00,  1.53it/s, recons_loss=0.137, gen_loss=0.93, disc_loss=0.437]\n", "Epoch 1: 100%|██████████| 125/125 [01:23<00:00,  1.50it/s, recons_loss=0.0754, gen_loss=0.68, disc_loss=0.195]\n", "Epoch 2: 100%|█████████| 125/125 [01:24<00:00,  1.48it/s, recons_loss=0.0511, gen_loss=0.571, disc_loss=0.297]\n", "Epoch 3: 100%|█████████| 125/125 [01:25<00:00,  1.47it/s, recons_loss=0.0425, gen_loss=0.447, disc_loss=0.268]\n", "Epoch 4: 100%|█████████| 125/125 [01:25<00:00,  1.46it/s, recons_loss=0.0388, gen_loss=0.347, disc_loss=0.228]\n", "Epoch 5: 100%|█████████| 125/125 [01:25<00:00,  1.46it/s, recons_loss=0.0358, gen_loss=0.345, disc_loss=0.233]\n", "Epoch 6: 100%|█████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.0333, gen_loss=0.332, disc_loss=0.243]\n", "Epoch 7: 100%|█████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.0324, gen_loss=0.334, disc_loss=0.244]\n", "Epoch 8: 100%|█████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.0298, gen_loss=0.328, disc_loss=0.243]\n", "Epoch 9: 100%|█████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.0288, gen_loss=0.307, disc_loss=0.243]\n", "Epoch 10: 100%|████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.0272, gen_loss=0.302, disc_loss=0.245]\n", "Epoch 11: 100%|█████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.026, gen_loss=0.303, disc_loss=0.245]\n", "Epoch 12: 100%|████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.0251, gen_loss=0.302, disc_loss=0.246]\n", "Epoch 13: 100%|████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.0244, gen_loss=0.297, disc_loss=0.247]\n", "Epoch 14: 100%|████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.0238, gen_loss=0.288, disc_loss=0.249]\n", "Epoch 15: 100%|████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.0226, gen_loss=0.292, disc_loss=0.246]\n", "Epoch 16: 100%|█████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.0223, gen_loss=0.29, disc_loss=0.242]\n", "Epoch 17: 100%|████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.0218, gen_loss=0.294, disc_loss=0.244]\n", "Epoch 18: 100%|████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.0212, gen_loss=0.292, disc_loss=0.246]\n", "Epoch 19: 100%|████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.0207, gen_loss=0.281, disc_loss=0.247]\n", "Epoch 20: 100%|██████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.02, gen_loss=0.283, disc_loss=0.246]\n", "Epoch 21: 100%|██████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.02, gen_loss=0.287, disc_loss=0.245]\n", "Epoch 22: 100%|████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.0197, gen_loss=0.284, disc_loss=0.245]\n", "Epoch 23: 100%|████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.0194, gen_loss=0.285, disc_loss=0.246]\n", "Epoch 24: 100%|█████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.019, gen_loss=0.283, disc_loss=0.246]\n", "Epoch 25: 100%|█████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.019, gen_loss=0.283, disc_loss=0.247]\n", "Epoch 26: 100%|████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.0187, gen_loss=0.281, disc_loss=0.247]\n", "Epoch 27: 100%|████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.0183, gen_loss=0.285, disc_loss=0.247]\n", "Epoch 28: 100%|████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.0179, gen_loss=0.283, disc_loss=0.245]\n", "Epoch 29: 100%|████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.0179, gen_loss=0.286, disc_loss=0.247]\n", "Epoch 30: 100%|████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.0175, gen_loss=0.283, disc_loss=0.245]\n", "Epoch 31: 100%|████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.0175, gen_loss=0.282, disc_loss=0.245]\n", "Epoch 32: 100%|████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.0173, gen_loss=0.287, disc_loss=0.245]\n", "Epoch 33: 100%|████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.0171, gen_loss=0.288, disc_loss=0.247]\n", "Epoch 34: 100%|████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.0174, gen_loss=0.289, disc_loss=0.248]\n", "Epoch 35: 100%|████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.0168, gen_loss=0.282, disc_loss=0.247]\n", "Epoch 36: 100%|████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.0166, gen_loss=0.282, disc_loss=0.246]\n", "Epoch 37: 100%|████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.0165, gen_loss=0.279, disc_loss=0.245]\n", "Epoch 38: 100%|████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.0166, gen_loss=0.287, disc_loss=0.246]\n", "Epoch 39: 100%|████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.0163, gen_loss=0.286, disc_loss=0.244]\n", "Epoch 40: 100%|████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.0164, gen_loss=0.292, disc_loss=0.245]\n", "Epoch 41: 100%|████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.0162, gen_loss=0.292, disc_loss=0.243]\n", "Epoch 42: 100%|████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.0163, gen_loss=0.295, disc_loss=0.244]\n", "Epoch 43: 100%|████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.0163, gen_loss=0.289, disc_loss=0.245]\n", "Epoch 44: 100%|█████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.016, gen_loss=0.293, disc_loss=0.242]\n", "Epoch 45: 100%|████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.0162, gen_loss=0.292, disc_loss=0.244]\n", "Epoch 46: 100%|██████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.016, gen_loss=0.298, disc_loss=0.24]\n", "Epoch 47: 100%|████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.0157, gen_loss=0.302, disc_loss=0.243]\n", "Epoch 48: 100%|█████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.016, gen_loss=0.306, disc_loss=0.241]\n", "Epoch 49: 100%|████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.0159, gen_loss=0.303, disc_loss=0.238]\n", "Epoch 50: 100%|████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.0162, gen_loss=0.314, disc_loss=0.234]\n", "Epoch 51: 100%|█████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.016, gen_loss=0.311, disc_loss=0.242]\n", "Epoch 52: 100%|████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.0161, gen_loss=0.314, disc_loss=0.234]\n", "Epoch 53: 100%|████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.0165, gen_loss=0.326, disc_loss=0.229]\n", "Epoch 54: 100%|█████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.0166, gen_loss=0.335, disc_loss=0.23]\n", "Epoch 55: 100%|████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.0163, gen_loss=0.343, disc_loss=0.226]\n", "Epoch 56: 100%|████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.0169, gen_loss=0.349, disc_loss=0.224]\n", "Epoch 57: 100%|█████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.017, gen_loss=0.336, disc_loss=0.235]\n", "Epoch 58: 100%|████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.0161, gen_loss=0.332, disc_loss=0.232]\n", "Epoch 59: 100%|████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.0159, gen_loss=0.329, disc_loss=0.232]\n", "Epoch 60: 100%|████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.0166, gen_loss=0.332, disc_loss=0.239]\n", "Epoch 61: 100%|████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.0163, gen_loss=0.334, disc_loss=0.233]\n", "Epoch 62: 100%|████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.0158, gen_loss=0.323, disc_loss=0.232]\n", "Epoch 63: 100%|█████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.0162, gen_loss=0.34, disc_loss=0.229]\n", "Epoch 64: 100%|████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.0163, gen_loss=0.354, disc_loss=0.225]\n", "Epoch 65: 100%|█████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.016, gen_loss=0.348, disc_loss=0.228]\n", "Epoch 66: 100%|████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.0162, gen_loss=0.354, disc_loss=0.223]\n", "Epoch 67: 100%|████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.0162, gen_loss=0.356, disc_loss=0.233]\n", "Epoch 68: 100%|████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.0159, gen_loss=0.362, disc_loss=0.216]\n", "Epoch 69: 100%|█████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.0169, gen_loss=0.386, disc_loss=0.21]\n", "Epoch 70: 100%|████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.0171, gen_loss=0.373, disc_loss=0.218]\n", "Epoch 71: 100%|████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.0158, gen_loss=0.377, disc_loss=0.225]\n", "Epoch 72: 100%|█████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.0161, gen_loss=0.35, disc_loss=0.225]\n", "Epoch 73: 100%|████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.0159, gen_loss=0.367, disc_loss=0.219]\n", "Epoch 74: 100%|████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.0156, gen_loss=0.373, disc_loss=0.214]\n", "Epoch 75: 100%|████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.0164, gen_loss=0.387, disc_loss=0.218]\n", "Epoch 76: 100%|████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.0163, gen_loss=0.404, disc_loss=0.196]\n", "Epoch 77: 100%|█████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.0162, gen_loss=0.38, disc_loss=0.221]\n", "Epoch 78: 100%|████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.0169, gen_loss=0.399, disc_loss=0.214]\n", "Epoch 79: 100%|████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.0161, gen_loss=0.391, disc_loss=0.207]\n", "Epoch 80: 100%|████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.0162, gen_loss=0.405, disc_loss=0.205]\n", "Epoch 81: 100%|████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.0163, gen_loss=0.389, disc_loss=0.214]\n", "Epoch 82: 100%|████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.0162, gen_loss=0.382, disc_loss=0.218]\n", "Epoch 83: 100%|████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.0164, gen_loss=0.399, disc_loss=0.215]\n", "Epoch 84: 100%|████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.0168, gen_loss=0.422, disc_loss=0.194]\n", "Epoch 85: 100%|████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.0162, gen_loss=0.393, disc_loss=0.217]\n", "Epoch 86: 100%|████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.0159, gen_loss=0.392, disc_loss=0.217]\n", "Epoch 87: 100%|█████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.016, gen_loss=0.392, disc_loss=0.213]\n", "Epoch 88: 100%|████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.0155, gen_loss=0.374, disc_loss=0.221]\n", "Epoch 89: 100%|██████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.016, gen_loss=0.38, disc_loss=0.219]\n", "Epoch 90: 100%|████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.0165, gen_loss=0.428, disc_loss=0.192]\n", "Epoch 91: 100%|████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.0155, gen_loss=0.394, disc_loss=0.214]\n", "Epoch 92: 100%|██████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.0162, gen_loss=0.429, disc_loss=0.2]\n", "Epoch 93: 100%|████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.0157, gen_loss=0.404, disc_loss=0.214]\n", "Epoch 94: 100%|█████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.0154, gen_loss=0.382, disc_loss=0.22]\n", "Epoch 95: 100%|████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.0159, gen_loss=0.402, disc_loss=0.213]\n", "Epoch 96: 100%|█████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.0151, gen_loss=0.38, disc_loss=0.223]\n", "Epoch 97: 100%|████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.0157, gen_loss=0.395, disc_loss=0.209]\n", "Epoch 98: 100%|████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.0155, gen_loss=0.381, disc_loss=0.218]\n", "Epoch 99: 100%|████████| 125/125 [01:26<00:00,  1.45it/s, recons_loss=0.0153, gen_loss=0.381, disc_loss=0.224]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["train completed, total time: 8708.349147081375.\n"]}], "source": ["kl_weight = 1e-6\n", "n_epochs = 100\n", "val_interval = 25\n", "epoch_recon_loss_list = []\n", "epoch_gen_loss_list = []\n", "epoch_disc_loss_list = []\n", "val_recon_epoch_loss_list = []\n", "intermediary_images = []\n", "n_example_images = 4\n", "\n", "total_start = time.time()\n", "for epoch in range(n_epochs):\n", "    model.train()\n", "    discriminator.train()\n", "    epoch_loss = 0\n", "    gen_epoch_loss = 0\n", "    disc_epoch_loss = 0\n", "    progress_bar = tqdm(enumerate(train_loader), total=len(train_loader), ncols=110)\n", "    progress_bar.set_description(f\"Epoch {epoch}\")\n", "    for step, batch in progress_bar:\n", "        images = batch[\"image\"].to(device)\n", "        optimizer_g.zero_grad(set_to_none=True)\n", "\n", "        reconstruction, z_mu, z_sigma = model(images)\n", "\n", "        recons_loss = l1_loss(reconstruction.float(), images.float())\n", "\n", "        kl_loss = 0.5 * torch.sum(z_mu.pow(2) + z_sigma.pow(2) - torch.log(z_sigma.pow(2)) - 1, dim=[1, 2, 3])\n", "        kl_loss = torch.sum(kl_loss) / kl_loss.shape[0]\n", "\n", "        logits_fake = discriminator(reconstruction.contiguous().float())[-1]\n", "        p_loss = perceptual_loss(reconstruction.float(), images.float())\n", "        generator_loss = adv_loss(logits_fake, target_is_real=True, for_discriminator=False)\n", "        loss_g = recons_loss + kl_weight * kl_loss + perceptual_weight * p_loss + adv_weight * generator_loss\n", "\n", "        loss_g.backward()\n", "        optimizer_g.step()\n", "\n", "        # Discriminator part\n", "        optimizer_d.zero_grad(set_to_none=True)\n", "\n", "        logits_fake = discriminator(reconstruction.contiguous().detach())[-1]\n", "        loss_d_fake = adv_loss(logits_fake, target_is_real=False, for_discriminator=True)\n", "        logits_real = discriminator(images.contiguous().detach())[-1]\n", "        loss_d_real = adv_loss(logits_real, target_is_real=True, for_discriminator=True)\n", "        discriminator_loss = (loss_d_fake + loss_d_real) * 0.5\n", "\n", "        loss_d = adv_weight * discriminator_loss\n", "\n", "        loss_d.backward()\n", "        optimizer_d.step()\n", "\n", "        epoch_loss += recons_loss.item()\n", "        gen_epoch_loss += generator_loss.item()\n", "        disc_epoch_loss += discriminator_loss.item()\n", "\n", "        progress_bar.set_postfix(\n", "            {\n", "                \"recons_loss\": epoch_loss / (step + 1),\n", "                \"gen_loss\": gen_epoch_loss / (step + 1),\n", "                \"disc_loss\": disc_epoch_loss / (step + 1),\n", "            }\n", "        )\n", "    epoch_recon_loss_list.append(epoch_loss / (step + 1))\n", "    epoch_gen_loss_list.append(gen_epoch_loss / (step + 1))\n", "    epoch_disc_loss_list.append(disc_epoch_loss / (step + 1))\n", "\n", "    if (epoch + 1) % val_interval == 0:\n", "        model.eval()\n", "        val_loss = 0\n", "        with torch.no_grad():\n", "            for val_step, batch in enumerate(val_loader, start=1):\n", "                images = batch[\"image\"].to(device)\n", "                reconstruction, _, _ = model(images)\n", "\n", "                # get the first sammple from the first validation batch for visualisation\n", "                # purposes\n", "                if val_step == 1:\n", "                    intermediary_images.append(reconstruction[:n_example_images, 0])\n", "\n", "                recons_loss = l1_loss(reconstruction.float(), images.float())\n", "\n", "                val_loss += recons_loss.item()\n", "\n", "        val_loss /= val_step\n", "        val_recon_epoch_loss_list.append(val_loss)\n", "\n", "total_time = time.time() - total_start\n", "print(f\"train completed, total time: {total_time}.\")"]}, {"cell_type": "markdown", "id": "f17a5406", "metadata": {}, "source": ["## Evaluate the training\n", "### Visualise the loss"]}, {"cell_type": "code", "execution_count": 15, "id": "2a7e9061", "metadata": {"lines_to_next_cell": 2}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 800x550 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.style.use(\"seaborn-v0_8\")\n", "plt.title(\"Learning Curves\", fontsize=20)\n", "plt.plot(np.linspace(1, n_epochs, n_epochs), epoch_recon_loss_list, color=\"C0\", linewidth=2.0, label=\"Train\")\n", "plt.plot(\n", "    np.linspace(val_interval, n_epochs, int(n_epochs / val_interval)),\n", "    val_recon_epoch_loss_list,\n", "    color=\"C1\",\n", "    linewidth=2.0,\n", "    label=\"Validation\",\n", ")\n", "plt.yticks(fontsize=12)\n", "plt.xticks(fontsize=12)\n", "plt.xlabel(\"Epochs\", fontsize=16)\n", "plt.ylabel(\"Loss\", fontsize=16)\n", "plt.legend(prop={\"size\": 14})\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 16, "id": "e2cc5b87", "metadata": {"lines_to_next_cell": 2}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAArsAAAILCAYAAADoqVT3AAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjYuMCwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy89olMNAAAACXBIWXMAAA9hAAAPYQGoP6dpAAC8NElEQVR4nOzdd3yT1f4H8E9mR9p0b1qgFMooe+8poBTEhZPlFkXU68+Nile9iuJiKIjidVwRUFCRoUwBWWWVWUZ36d5N0mb+/kjzkNBBR0qfls/79ULbZ+UkT8enJ99zjsRisVhARERERNQKSZu7AURERERETYVhl4iIiIhaLYZdIiIiImq1GHaJiIiIqNVi2CUiIiKiVothl4iIiIhaLYZdIiIiImq1GHaJiIiIqNVi2CUiIiKiVothl6gF+uWXXxAdHY3o6Gikp6c3d3NatPT0dOG1/OWXX5x23cWLFwvXbU2a6mtv+vTpiI6OxvTp0512TSIiAJA3dwOIWrNff/0VL7zwAgDA09MTe/bsgZubWzO3iuwpFAp07twZAODl5dVs7Th48CBmzJjR4POfeuopzJ0714ktqp6Xl5fweikUCqddNyIiAiUlJYiIiHDaNa+39PR0bNy4Efv27UNqaioKCwthsVjg7e2NyMhIDBo0CLfddhuCg4Obu6lENxSGXaIm9OOPPwIAAgMDkZOTgz/++AN33nlnM7eK7AUFBeHXX39t7mbA3d1dCJFXS09PR1lZGRQKBTp06FDtMf7+/k3ZPMHYsWMxduxYp1/3nXfecfo1r5eKigq89957WLt2LQwGAwDAw8MDoaGhsFgsyMzMxIEDB3DgwAEsWbIEDz74IObNmwe5nL+Cia4HfqcRNZFz587h2LFj8PX1xdNPP43XXnsNq1evZtilanXv3r3G0D1nzhxs374dgYGBogjmdEVpaSlmz56NkydPQiKRIDY2FjNnzkT37t0hkUgAAAaDAdu2bcMXX3yBc+fOYcWKFUhMTMTixYshlbKakKip8buMqInYenVvvvlmjB8/HkqlEidPnsSZM2eauWVE5Cwvv/wyTp48Cblcjg8//BCLFi1Cjx49hKALWMs9br75Zvz888+YPHkyAGDbtm3473//21zNJrqhMOwSNYGysjL89ttvAIDJkyfDy8tLeOt39erV1zzfbDbjp59+wrRp09CnTx/07t0bsbGxWLx4MXQ6XbXnzJ49G9HR0Rg3blyt1y4vL0efPn0QHR2N119/3WGf0WjEL7/8ggcffBCDBw9GTEwMBg0ahHvvvRfffPMN9Hp9tdccM2YMoqOjsWLFCmRlZeGpp57CwIEDMWzYMIfjsrKy8P777+PWW29Fnz59EBMTg+HDh2PatGn48ssvUVBQUGO7t23bhqeeegqjRo1C9+7d0b17d4wePRrPPfccjhw5Uu05tsFU3bt3BwCsXbsWEyZMQPfu3fG///0PwLUHqJnNZmzYsAGPPPIIhg0bhpiYGPTo0QM33XQTXnnlFZw/f77mF7sZ2J7LH3/8gQsXLmD27Nno168f7r777irHNuY1vXqAmv3rePz4ceh0OnzxxReYPHky+vTpg549eyI2NhZLliyp9uuopgFqtoF+EydOBABcuHABL7zwAsaMGYOYmBj0798fs2fPxp49e2p8TdLS0vDqq68Kz3PEiBH417/+hQsXLgAApk2bhujoaLz00kvXfoHt/P333/jrr78AWHvfY2Njaz1eLpfj3//+N0JCQhAYGIjS0lKH/bbXb/HixTVeo66v0xdffCG8Rrt378aECRMQHR19zbrwzMxMdO7cGdHR0Vi+fLnDPp1Oh2+//RYPPPAABg4ciJiYGAwdOhSzZ8/GunXrYDaba7zu3r17MW/ePIwZMwY9evRAr169cNNNN2HOnDn466+/YLFYam0XUWOwjIGoCfz222/QarVo3749evfuDQC48847sXnzZvz+++944YUX4OHhUe25FosF//d//4eNGzcCAFxdXREWFobi4mIsWbIE27Ztw7Rp06qcN3nyZPzzzz9IS0vD6dOn0a1bt2qvv3v3bmg0GgDArbfeKmwvKirCnDlzhJDj7u6OsLAwZGVl4ejRozh69ChWr16N//73vwgKCqr22mazGU888QTOnz+P8PBwKJVKYd+JEyfw4IMPoqysDIC1jjk4OBgFBQU4ceIETpw4gVWrVuG7775zqEu1vR6///47AMDFxQWhoaGoqKhAVlYW/vjjD2zatAmvvPJKrb/I//rrL7z22mvw9fVF27ZtIZPJajzWpqKiAo8++igOHDggvCZt2rRBWVkZUlNTkZqait9++w0fffQRxo8ff83rXU9lZWV4+OGHUVhYiDZt2sDT01PY56zXtCY6nQ4zZ87EiRMnEBISguDgYKSnp+PChQu4cOECTp06hS+++KLe1z106BAee+wxGAwGhIeHIzAwEBkZGfjnn3+wf/9+fPLJJ0LYs4mPj8fMmTOh1WoBAAEBAXB3d8fmzZuxY8cOLFmyBBUVFfVuCwB88803AABfX1888sgjdTrHzc0N69atg5+fn0PvrzN98803+PjjjxEUFCQM+LP9oREXF4eCggL4+vpWe+7mzZthsVggkUgwZcoUYXt6ejoeffRRXLp0CYC1JjksLEx4/f/55x+sW7cOK1eurPKz7YMPPsDKlSsBWL/WAgICIJfLkZmZidTUVGzfvh3jxo3DZ599VqfvS6L6Ys8uUROw9d7a1+cOGTIEYWFh0Gq1QpCtzm+//Sbsv+uuu7B//35s2rQJe/bswfr161FRUVFtUBg/fjxcXFwAAFu3bq3x+ps2bQIAhIeHo2/fvsL2559/HkeOHIGXlxc+/fRTHDlyBFu3bsXx48exdOlSBAQEICkpCXPmzKmxB2fbtm3QarXYsmULtmzZIvRuA8Drr7+OsrIydOvWDX/99Rf27NmDTZs24cCBA1i7di3at2+P/Px8vPHGGw7XXL9+vRDKHnzwQRw4cABbt27Frl27sH37dgwYMAAWiwXvv/8+UlJSqm2XxWLB4sWL8dxzz2Hfvn3YuHFjtT2dV1uxYoUQdF9++WUcPHgQW7Zswd69e7Fx40Z06tQJBoMBr776apVeuua2du1ahISEYNeuXdi0aZMQNgDnvKa1+eCDD6DRaLB+/Xrh8ffv34+bbroJALBz504cO3asXtcsKyvDc889hylTpmD//v1CWP3111/h4+MDi8WCjz/+2OEcg8GA5557DlqtFp6envj666+xd+9ebNmyBTt37kSfPn3w4osvNujelZeX4/DhwwCspUr2f9hdi7+/f5MFXb1ejy+//BLvv/8+/v77b2zatAkjR44UyidMJpPQG10d28+HAQMGICQkRLjmnDlzcOnSJYSGhuKbb74Rfj6cOHECb7/9NlQqFY4dO1ald/zMmTPC197jjz+OAwcOYPv27di6dSsOHTqE119/HQqFAtu2bcOGDRua4BUhYtglcrqjR48iISEBcrkcU6dOFbZLpVLcfvvtAICffvqpxvO//fZbAEC7du2wYMECuLu7C/u6du2KpUuXVvt2v4eHB0aPHg2g5rCr1Wqxe/duAHDotdm/f7/wNvBHH32EiRMnCgNnJBIJxo0bh08//RQAcOrUKWzbtq3a6588eRILFixAeHi4w/aioiKcO3cOAPDYY49VmV6qR48e+M9//oP+/fujTZs2Dm9z//nnnwCsPcEvvPCCw+sRGhqKd999F4C1BMN27NUMBgPUajUee+yxeg0Isl2vd+/emDVrlkOg6dixI1599VUAQElJSa1vozeH06dPY+HChdX24DnjNa3NpUuXsGLFCnTt2lXYplKpHILQwYMH63XN3NxcdO3aFQsWLHDope7cuTPuv/9+AEBycjKys7OFfbt27UJaWhoA4MUXX8TQoUOFfUFBQfj888/h6emJjIyM+j1BWAeg2r5Oe/XqVe/zm0pGRgaGDRvm8LMHsP48sZXzbNmypdpz09LScPLkSQCO7/r8+uuvSEhIgEKhwPLlyzF48GBhn0wmw1133YUFCxYAsL6Dcvr0aWG/7T57eHjgmWeecfhac3V1xf3334+HHnoII0eOhNFobMQzJ6oZwy6Rk9kGpo0aNarKdFC33347pFIpzpw5g/j4+CrnFhYW4tSpUwCsZQnVvaXXoUMHDBgwoNrHtvXeJCcnC+HS3s6dO4WaX/tfZrYe2E6dOlWps7Xp27evEF5qCkD+/v4YNGhQle329Xg5OTnVntu7d298//33eO+99xxC5RdffIFjx45h/fr11faGhYeHw9vbGwCEYFOdW265pcZ9Nfn9998RFxeHZcuWVbvfFh4AiG5xj5iYmBrnrHXWa1qTcePGISwsrMp2+3KKrKysel+3ppKKLl26CB/bh929e/cCsA4Qq66eVqlU4tFHH613OwAgPz9f+NjWAyoWNX2t234+HDp0CIWFhVX2b968GYA1hE6YMEHYbnsXYNiwYejUqVO11540aRJ8fHwAOP58sH3v6/X6ah8TAJ599lmsWLGiTu+2EDUEwy6RExUUFAi9JtVNMRYaGoohQ4YAqH6g2sWLF4WPa5pzFbD2hFZnxIgRwsII1fXe2N6i7N27N9q2bStst72lXNMvsqsft6YZJSIjI6vd7uPjIwSS9957D4sWLUJiYmKtj2XP3d291nlkVSoVANQ4gA5AjfPTXounp2eN9Y22xwXQ4LrPpnKt5+uM17Qm9n8EXM1Wz9mQ16um69rfh/LycuHjpKQkANavy5oWcxk+fHi92wFAqHsHrOFQTGq695MmTYJMJoPRaKz23Rnbz4dx48YJ98liseDEiRMAav/5IJVKERMTA8Dx58PgwYMhkUig1+sxbdo0bNiwQajbJ7peOECNyIl++eUX6PV6BAYGYsSIEdUec8cdd2Dv3r3YtGkTXn75ZYe3ZO17i/z8/Gp8nMDAwGq3K5VKTJw4ET/99BO2bt2KZ555RthXVlaGv//+G4BjCQMA5OXlAQA2btxYaz2xTU29craenep88MEHePDBB5GTk4MVK1ZgxYoVCA0NFWZtGD16tENosZednY0ff/wRBw4cQHZ2NvLy8uodwmoKrNeSnJyM1atXIy4uDjk5OSgoKBAWDhCz2u4F4JzXtCb2X9NXs5WRNGT0vVqtrvWaV1/XVu4TEBBQ4zX9/f3h6elZ77pd++cotvBW09e6v78/Bg8eLNQt33XXXcK+pKQknD17FoDjz4fS0lLhD4jly5dXmaGhOvY/H7p06YIXX3wRCxcuRFpaGl588UXIZDJ069YNgwYNwpgxY9CrV68mq2EmAhh2iZzGYrEItbg5OTkO9YrV0el0+PXXX/HAAw8I2+x7pWob8FLbvsmTJ+Onn35CYmIizp8/L/TGbNu2DXq9HgqFosrbnLbSBh8fnxpnWrBXU92rfT3e1Tp27IgtW7Zg9erVWLduHRITE3H58mWsX78e69evh0qlwoMPPog5c+Y4XH/fvn14+umnhUChVCoREBAADw8P4RfkpUuXrhlAG7JM84YNG/Daa68J13Zzc0NQUBBUKpXw2NWVi4hBbffCWa9pTZoquNT3urbvp2sta+zu7l7vsGsfoJOTkx3qWJtbbfd+8uTJ2Lt3Lw4ePIji4mLhnSBbr25AQIBDKZP9VIeBgYF1+qPx6uWQZ8+ejeHDh+Orr77Ctm3bUFJSgvj4eMTHx2PFihXo1KkTXn311WpLoIicgWGXyEn27t2L1NTUep3z008/OYRd22wKAGoNGvah+Gr9+vVDaGgoLl++jC1btghh11aPN2rUKKEe08bd3R0lJSUYMWIEFi5cWK/nUB8qlQoPPfQQHnroIaSlpWHfvn34559/sGfPHmg0GixevBiXL18WBkiVlZXh2WefRVlZGby9vTF//nyMGTOmyi/zMWPGNGiQUW3S0tKEoNumTRvMnz8fQ4YMqfKHRnR0tFMft6k152t6vdlC7rUGPtX2/VST6OhouLm5QafT4dChQ7j33nsb1Mb6aux8tOPGjYOrqyvKy8uxbds23HHHHQCu/HywlTrY2H9d3HnnnZg3b16DHjcqKgr/+c9/8O9//xvHjx8XBsWeOHEC58+fx0MPPYSVK1eK6o8Gaj1Ys0vkJLYa3L59+wq9FjX9sx17/vx5h8n77UNobQssXL58ucZ9EokEkyZNAnBlVobi4mLs27cPgOPANBtbT0xDBg01VHh4OO655x589tln+Pvvv4UBMT///LNQz7tr1y4UFxcDAN544w3ExsZWCWUWiwUlJSVOb9+mTZuEPzg++ugjjBo1qkrQtbWtJWnO1/R6s/Va1va9VFBQ0KD7qFAohGC2bds2hxKkazl37hw++OCDGttVW6Bt7H3x8PDAmDFjAFz5+WCb/xio+vPBw8NDKC9yxs8HuVyOfv36Ye7cuVizZg3WrVuHgIAAGI1GYcYXImdj2CVygqysLOzcuRMAcNttt8HFxaXWf7179xYGkdhPQ2Y/sKS2lbmuNUepbdT1xYsXkZaWhu3bt8NgMMDb2xsjR46scrxt4M+ZM2dqrdtsbE1nTT1snp6eePPNN4XPbc/dPtT369ev2nOPHj3aJHPc2no1lUolevbsWe0xtmncWpLmfE2vN9tsFElJSTW+U2KrY2+ImTNnArB+X9jejbgWo9GIV199FStXrsSdd97p8D1he2enphpgjUZTr4GdNbH9fDhw4AB0Op0wmLVTp05Vyq8kEonw88E2UK0mNf18MJvNNQb47t27Y/bs2QCAhISEuj8Jonpg2CVygjVr1sBkMsHFxaXKCk41sfWgbNmyRehZCgwMFAKvbSWjq509exbHjx+v9drR0dFC+cLu3buFkdc1TX5v++VXWlpa43LGer0eU6dOxW233VbvkPfNN99gxIgRtU7zZP9L39aTZF/WUV0vmE6nw7vvvivUcjpzRgTbCHu9Xl9t8CsoKMAnn3wifC622Rhq0pyv6fXWv39/AI7zS9vT6/UOi23U16BBg4TvnY0bNzp8PVSnoqICTz31lDC94Lx58yCXX6kmtL3DYhsodrUff/zRKYMjhw8fDm9vb1RUVODAgQPCz4erB67a2KZtu3TpErZv317tMcXFxRg1ahTuueceYa5es9mMmTNnom/fvrUOfLV979c0QJWosRh2iRrJaDRi7dq1AKz1cLWNRLc3ZcoUSKVSVFRUYP369cL2++67D4D1rc533nnHIWzEx8dj7ty5CA0Nveb1bb+Et2/fjv379wOovoQBsE4PZJuCaeHChVi9erXDL9VLly7h8ccfx6VLl5CYmIiOHTvW6TnadOrUCdnZ2di3bx8WLFhQZa7djIwMYcEBX19fIaTY9zx+/PHHDj1eR48exf333w93d3dhsYCTJ086LZzZP/b7778v9FqZzWb8/fffuOeeezBgwABhurWjR4/WuLKcmDTna3q9TZgwQfh+fOutt4SQCVhno5gzZw4MBkOtszVcy5tvvim8pp9//jmmT5+O/fv3O/zxptfr8fvvv+O2224T3gF68sknq3w/2ubPPnz4MP744w9hu8lkwpo1a/DZZ5/VuAx4fSgUCuGP8jVr1uDcuXOQSqU1ht1bb71V+OP5xRdfxJYtWxy+1uPj4zFr1izk5+cjLS1N+J6QSqUICAiAVqvFggULsGnTJofeX7PZjN27d+Orr74CAIe5fYmciQPUiBpp+/btQnirKUxWJyQkBAMGDMCBAwfw008/YdasWQCAe+65Bzt37sTevXvx3Xff4eeff0ZISAhKSkqQm5uLDh064NFHH8WLL75Y6/VjY2Px0Ucf4Z9//gEAtG3bFr17967x+A8++ABPPPEEjh07hjfeeAMLFy5EcHAwiouLhanJ3Nzc8OGHH9YpbNsbMmQIHn/8cXzxxRf43//+h9WrVyMgIABqtRolJSXCQgDu7u5YtGiR0KvarVs33HLLLdi0aRN27dqFoUOHIjQ0FIWFhSgsLERUVBQ+//xz/Prrr9i7dy/S0tIwatQo9O/fH5999lm92ni1sWPHonfv3jh27BjWrl2LTZs2ISgoCHl5eSgpKUH//v3x2muvYeHChUhMTMSRI0cwatQojB8/Hq+99lqjHrspNedrer2p1Wq8+eabeP7555GdnY077rgDISEhcHV1RWpqKtRqNb766ivMnTu3wY/h4eGBVatWYdGiRfjhhx9w6NAhHDp0CG5ubggMDIREIkFmZqbwB4Ofnx9eeeWVahe5eOSRR7B582ZhaeT//Oc/8Pb2RlZWFkpLS/Gvf/0LWVlZDiuUNdTkyZOxevVq7NixA4C1l7qmmViUSiWWLVuGRx55BElJSZg3bx68vLzg7++PgoICYbEIb29vLFmyxKGH9pVXXsGFCxdw7tw5PPvss3B1dUVgYCBkMhlyc3OFP7Z69OjhMFUikTMx7BI1km3FtKun7KmLqVOn4sCBA0hMTMTBgwcxcOBAyOVyfPHFF/jhhx/w66+/Ijk5GZcvX0ZISAhuu+02PPbYY9csYwCsC1j069cPhw8fBlDzW5Q2Pj4+wmP+8ccfOHv2LFJSUqBUKoWV1aZPn17voGvz7LPPYtSoUdiwYQP279+P7Oxs5Obmwt3dHd26dcOQIUPwwAMPVJm26IMPPkDnzp3x22+/ITU1Fenp6YiIiMCsWbPwwAMPwMPDAw888ADOnz+P3bt3o7y8vMb5WOtDJpPhq6++wrJly/Dnn38iMzMTly9fRocOHXD77bdj2rRpUCqVmDdvHrKysnD48GHodLoqM12IUXO9ps0hNjYWwcHB+PLLL3H8+HEUFBQgJCQE99xzDx555BGEhIQI5UINnTJNqVTi5ZdfxvTp07Fx40bs27cPqampwoAuPz8/dOnSBaNHj0ZsbGyN0+C1bdsWa9aswZIlS3D48GEUFRXBYrGgR48emDFjBkaNGoW33nqrYS/EVfr27YuwsDChNv1af6iHh4fj119/xU8//YQ///wTFy5cQEpKClxdXRETE4PRo0fj/vvvrzK/s6+vL9auXYs//vgDmzZtwpkzZ3D58mVIJBJ4e3ujV69emDhxIm677TaHkg4iZ5JYGjuPCRERUQs2cOBAFBUVYfr06aLulSeihmHNLhERtWoVFRU11h3n5eWhqKgIANCmTZvr2Coiul4YdomIqFXKzs7G2LFj0atXrxpnXbAfHMoVvIhaJ4ZdIiJqlYKCghAUFASz2YwvvvgCGzduFGYRMJvN+O2337B48WIAwNChQ9G5c+fmbC4RNRHW7BIRUauVlpaGmTNnCgOxPD09ERgYiLy8PGF+66ioKHz99dc1zkZARC0bwy4REbVqZWVl+O6777B9+3YkJSVBp9PBw8MDUVFRuOmmm3D33XdXWTKZiFoPhl0iIiIiarVYs0tERERErRZncK5Gbm5pk1xXKpXA11eFggINzGZ2qLdUvI+tA+9jy8d72DrwPrYOzXEfAwI863Qce3avI6lUAolEAqm0Yav0kDjwPrYOvI8tH+9h68D72DqI+T4y7BIRERFRq8WwS0REREStFsMuEREREbVaDLtERERE1Gox7BIRERFRq8WwS0REREStFsMuEREREbVaDLtERERE1Gox7BIRERFRq8WwS0REREStFsMuEREREbVaDLtERERE1Gox7BIRERFRq8WwS0REREStFsMuEREREbVaDLtERERE1GrJm7sBN7oTF/Pwy9+JGNUrFKP7tGnu5hAREbUIer0emzdvxO7dO3DhwnmUlpZALpcjODgEvXr1wR133I327SObu5kkAgy7zWzLwVSk5ZRh3e5LDLtERER1kJ6ehpde+hcyMtIwcWIsbr31Dvj6+qK4uBinT5/E779vwKZNG/HKK69j3LgJzd3cJrF48cdISDiLJUtWNHdTRI9ht5mZLBYAgK7CBKPJDLmMlSVEREQ10el0eP75eSgoyMeyZSvRpUs3h/3Dho3A1Kl34KmnHsW77y5Ahw4dW2UP79Gjh6FSeTR3M1oEht1m5qKQCR/rDSaGXSIiolr8/vt6pKen4umn/1Ul6NoEBQXjzTffRX5+HkJDQx32aTRlWLVqJfbs2YXs7Cy4urqha9cYzJgxG7169RGO++qr5Vi16kusXPkdDh78B5s2/Y7c3Fz4+/tj4sRJmDnzIchkV36H5+Xl4euvl+PAgX9QUJAPDw8P9OzZG7NnP4qoqI7Cce+88yY2b96IVat+wKJF7+P8+XP48stv0aFDFEwmE37+eQ02bfodqakpkMlkCAkJwcSJsZg27V7I5XJkZl7GXXdNEa43bFg/3HxzLF599U0AwKVLF7Fq1Zc4fvwoSktLoFZ7VbbjEXToECWc99RTj+LixfP49NMv8O67C5CWloLff/+zVQZoht1mZh92y/UmuLsqmrE1RERE4rZr1w5IpVLccsvkWo/r1i2myraKigrMnfsYUlNTMH36bPTs2RuFhQX48cfvMW/eE/jPfxZhyJBhDud8/vln8PRU45lnnodEIsV3363C11+vQEBAICZPngoAKCwsxGOPzUJFRTlmzHgI0dGdkZWViW+//RqPPz4bS5euRHR0Z4frfvzxBxgz5iY89dSzCAkJAQAsXfoJ1qz5EVOn3oGnn34OZrMZmzf/jmXLPkVhYQGefHIe/P0DsHLlt3j44Rno1KkzXnjhFXh5eQMALlw4jyeeeBD+/gF44om5CAtrg8zMy/j66y/x+OMP4ssv/4t27doLbbBYLPjww3dxzz33Izw8Ai4urvW9HS0Cw24zsw+7FQZTM7aEiIhI/JKSEhEcHAIPj/r3QP7223qcP5+AV155wyEsDxgwCPfddweWLPm4Stg1m814++33hc8DA4MwY8bd2LVrhxB2v/9+FbKzs/DZZ1+gT59+AICePXujZ88+uO++O7B8+VJ89NFih+t27NgJ06bd67BNq9Vi/Pib8fzzLwvbevXqg7i4Q9i8eSOefHIeFAoFOnfuCgBwd3cXPgaAFSuWQq/X44MPPkV4eIRwfvv2HfDww9OxatWXWLDgXeF4jUaDMWPG4+abY+v9WrYkDLvNzEVpX8ZgbsaWEBFRa3D4XA427ElEuV6cHSiuShluGx6Jfp0DG3S+VqtBUFBwg8795589kMlkuOmmiQ7bVSoPDBgwGJs3b0RhYSF8fHyEfWPH3uRwrC1ElpQUCdv27duLwMAgIejaBAcHo2vXboiPP1alLYMGDamy7aWX5lfZJpfLERYWjvj44ygvL4era/W9r0ajEUeOxCEqqqPQRpvOnbsgKCgYR4/G1akdrQ3DbjNzUVyp0WXPLhERNdaWgynIzNc2dzNqtflgaoPDrqenGqWlJdXumzhxFMrKyhy29erVR5ixIDs7CyaTCaNHD67x+jk52Q5h19/fsZ0KhbXc0Gy22J2TBb1ej2HDHMOuvZKSYqjVXsLnfn7+VY7JyEjHTz/9gIMH9yMvLxcVFRUO+83mmjvFiouLoNdXIDAwqNr9AQGBOHUqHkajEXL5lfhXXTtaG4bdZsYyBiIicqabB7bFepH37N48MOLaB9agU6doHDp0AHl5ufD3D3DYt3TpSpjNV57388/Pc9gvkUigVCqxfPmqGq8fFhZe5ZxrkyAkJAzvvruwxiPc3NwdPrcPnIB1gNujj86ETleO6dNnoWfP3vDw8IBEIsF7772NhISztbegsp0Wi6VOx9XUjtao9T9DkXMIuyL9wURERC1Hv86BDe41bQluumkiDh06gLVrV+OJJ+Y67LOfbQC40gtrExwcipSUZPj7Bzr03jZWSEgI8vJyERkZ5TBDQ33s3r0dxcXFmDv3Wdx99/0O+0pKqu/Jtufl5Q03NzdkZ2dXuz8nJxv+/gENbl9Lxnmumpl9zS57domIiGp3000T0bVrDFav/h67d++o8bhLly5WKXewDT77/ff1VY5fvnwpfv55TYPaNGTIcGg0Gmzf/pfDdrPZjEWL3se2bVuveQ2TyZoBfHx8HbZv3/4XMjMzHI65+hwAkMlkGDBgEC5duoDk5CSH486cOYWcnGwMHFhz+UZrJuqe3bVr12LVqlVITU2Fj48PYmNj8dxzz1X5S82mtLQUH374IbZt24aSkhJ07NgR//rXvzB06NDr3PK6YxkDERFR3cnlcrz33iK8/PLzePXVFzBy5GiMGjUWwcEhqKioQEZGOg4c2Id9+/YgKCgEDz30mHDu5MlTsWnT71i58gvodDoMGjQUWq0GW7ZsxPbtf2HOnHm1PHLNHnhgJnbu3Ib33vs38vJy0b17DxQVFeKXX9biyJHDDvP31qRPn/6QyWT473+/gkrlAXd3d/zzz14cOXIIEybcgq1bN+H33zdg5MjRCAtrg4CAQFy8eB6bN2+Ej48vBg0agkcffRKHDx/CSy89h9mzH0FwcAhSU1OwatWX8Pb2xuzZjzTo+bV0og27GzZswPz58/HSSy9h7NixSEhIwPz586HVarFgwYIqx1ssFjz66KNIS0vDW2+9hY4dO+Lrr7/GY489hp9++gndulU/8XRzY9glIiKqH19fP3z++VfYtm0rtm37E0uWfILi4iK4uLjAx8cP3bp1wxtvvI2RI8c41KQqlUosXvwF/vvfr7Fjx1/48cfvoFAoEBXVEa+//jbGj59Yy6PWzMvLGytWfIOvv/4S69atxvLlS+Dm5obo6K5YuPCTOs14YGvDN998ifnzX4KnpyeGDBmOTz5ZhtzcXJw6FY+VKz+HXl+BWbMexpw5T2Px4o/x/vtvY/DgoRg0aAjatm2H5ctX4auvvsDixR+htLQU3t4+GDBgkBB+b0QSy7UqmZvJuHHj0LNnTyxatEjYtnr1aixYsAC7du1CUJDjaMP9+/dj1qxZWLRoEWJjr8wXd9tttyE8PByfffZZnR87N7e08U+gGnK5FD4+KhQWamA0WkdUxl/KxydrTwAApgxth6nDW9+Shq1NdfeRWh7ex5aP97B14H1sHZrjPgYEeNbpOFHW7CYnJyMtLQ0jR4502D5ixAiYzWbs2bOnyjmnT58GAAwYMMBh+5gxY7Bv376ma2wjceoxIiIioqYjyrCblGQtrI6IcJyaJCQkBAqFAomJiVXOsb1NcfUUGr6+vigrK0N+fn4TtbZxXJVX2lvBRSWIiIiInEqUNbu2CaFVKpXDdolEApVKVWXCaABo39661nN8fDxGjRolbE9ISABgXRLPz8+vTo8vlUogldZlXr36kcmkDv8HAHe3K7fAYDRBLhfl3x9kp7r7SC0P72PLx3vYOvA+tg5ivo+iDLsNMWzYMERGRuL9999HaGgo2rdvj82bN2Pbtm0A6jdpsq+vqo6TSDeMWu0mfGySXPmiMEMCHx9VdaeQCNnfR2q5eB9bPt7D1oH3sXUQ430UZdhVq9UAUKUH12KxQKPRCPvtyWQyLF++HM8++ywmT55cOd/cAMydOxcLFiyAt7d3nR+/oEDTZD27arUbSkp0MJmsJQvlOoOwv0yjR2GhxumPS85V3X2klof3seXjPWwdeB9bh+a4j3XtIBRl2I2MtM5IkJKSgt69ewvb09PTYTAYEBUVVe15ERER+Pnnn5GbmwulUgkvLy+sWLECbdu2hbu7e7XnVMdstjisee1sJpNZGKkoswvVOr2RI1FbEPv7SC0X72PLx3vYOvA+tg5ivI/iK6wAEB4ejsjISOzcudNh+/bt2yGXyzF8+PAq55SVleHXX39FWloaAgIC4OXlBbPZjD/++APjx4+/Xk2vN7lMKgRePZcLJiIiInIqUYZdAJg3bx62bt2KVatWISMjA9u2bcPSpUsxY8YM+Pn5IT4+HhMnTkRcXBwA60TRH330EZ5//nmcPn0aiYmJePXVV1FYWIjZs2c387OpnW1hCU49RkRERORcog27EydOxMKFC7Fu3TpMmDABb7/9NmbOnIn/+7//AwDodDokJSVBq9UCsIbdr776CiqVCtOnT8edd96J4uJi/PDDD/D19a3toZqdi5Jhl4iIiKgpiLJm12bKlCmYMmVKtfsGDhwoTCtmExUVha+//vp6NM2plELPrrhqXIiIiIhaOtH27N5IbKuoVehNEOnqzUREREQtEsOuCLhW9uyaLRYYTQy7RERERM7CsCsCysqaXYB1u0RERETOxLArArbZGABAz7BLRERE5DQMuyJgH3bZs0tERFS7TZt+x7Bh/Rz+jRkzBLfddgv+7//m4ddff0F5ebnDOe+88yaGDeuHzMzL1729w4b1w1NPPeq069me/6ZNvzvtmq2ZqGdjuFHYh91yLixBRERUJ/fdNx1jxtwEAKio0CM7OwsHD/6DTz75AD/88F+8/fb76NSpMwDgwQcfxR13TIO/f8B1b+fKld/WayXXaxk6dDhWrvwWISGhTrtmTdatW43Vq3/AunUtN1gz7IqAi5JlDERERPUVFBSMzp27OmwbP34i7rnnfjz33Fw8++yT+O9/V8PfPwAhIaHXJRxW5+o2NpaXlze8vLydes2aHDkSd10epymxjEEEWMZARETkPB07RuPll19HcXExvv56BYDqyxhOnDiG//u/ebj11okYPXowbr11Il577UVcunTR4XoVFRX46qvluO++OzBmzFDccUcs3n13AbKzs4RjvvpqOYYN64ejR+Pw3HNzMXbsUOzbtwdA1TIG27Fnz57GsmWfYurUmzFmzFA8/PAMnDx5AlqtFh9++B9MmTIB48ePxLx5c5CWliqcX10Zw513TsasWfchKysTr732AmJjx2H8+JF47LHZOHq0amDdvHkjHntsNm66aTjGjh2K++67AytWLINWqxGOGTasH/bs2YWsrMwqzyErKxPvvrsAt912C0aNGoTx40fjkUcewfHjxxwex/a6X7iQgMcffxBjxgyp8vo2NYZdEXAMu1xYgoiIqLGGDBmGwMAg7N69A2Zz1d+tiYmX8OyzT8FoNOKFF17F4sXLMWfO00hOTsQTTzyEvLxcAIDZbMbLL/8Lq1d/j6lT78Snny7Dww8/jsOHD+Kppx5DcXGRw3VXrFiGzp274NNPv0DXrjG1tnHp0k+h1Woxf/5beOaZ55GUdAnz57+EBQtehUrlgTfffAezZz+Co0cP4/33377mc9ZqNXj++Xno3Lkr/v3v9/H0088iJSUJr7zyvEM716z5Ee+88yYCAgLxzjsf4MMPP8PgwcPw7bdf49133xKOW7nyW/j5+cPPzx8rV36LF154BQCQm5uDRx6ZiQMH/sH06bPxySfL8H//9xLy8vLw5JOPVRuuP/74A4wZcxM++2w5QkJCrvlcnIllDCJgW1QCsC4sQURE1FBHc+KxMfFPVJgqmrsp1XKRuSA2cjz6BPZo8sfq1Ckae/f+jcLCgir74uIOQq+vwJw5T6Njx2gAQExMD8TE9MCOHX9Bp9MBAA4e/AeHDh3A3LnPYtq0ewEA3bv3hIeHB/7zn39jz55diI2dKlxXrfbCo4/OqVP7PD3VeP75lwEAffv2x7FjR/DXX1swYMAgPPHEXABAnz798PffO3D8+FFotRq4u6tqvN7lyxl49dU3cfPNsQCA3r37IiUlBT/++B3i4g5h7NjxAIDCwgIMHjwUb775DuRyuXDsiRPHsHv3Dmi1Wri7u6Nz565QKBQAHEsxvv12FQoLC/DJJ8vQr98AAIBcLsWoUcMwZswYLF++FMuXr3JoW8eOnYTX73pj2BUBJcsYiIjISbal7Ea2Nqe5m1Grbam7r0vYdXOzDgrTaDRV9gUGBgGwlhQ88sgcdOgQBQAIC2uD6dNnC8fZShEGDx7qcP7w4aMwfPioKtcdNGhInds3dOgwh8+Dg609ngMHDr5qeyhOnoxHSUlprWFXIpFgzJhxDtvatAkHABQXFwvbHnvsyWrPb9u2Lc6dO4Ps7Cy0bx9Z4+McOrQfarWXEHRtAgMDERPTHSdOHBcCs019XhdnY9gVAVcuKkFERE4yru1I0ffsjosYeV0eyxbwvLy8quwbNWosHnroMXz//TfYu/dv+Pn5oWfPPhg6dDjGjh0v9Hjm5Fj/cPDx8avTY/r5+de5fd7evg6f2x7Tx6f67RZL7aWOnp5quLi4Omyz9czan1tQkI/Vq3/AP//sQXZ2NnQ6rcM513qc3NwchIe3rXZfQEAgzGYzCgryHcJufV4XZ2PYFQGHml2WMRARUSP0CexxXXpNxc5oNOLs2dMICQmtceaC2bMfwR13TMOhQwdw5MhhHDp0ADt2/IXvv/8GixevgLe3N6RSSeX1DHV6XFswrQuJRFLnY+t2vWsfU1FRjieeeAiZmZdx1133YtCgIVCrvSCVSrBy5RdCT/Y1HgmA5RptcWxMfV4XZ2PYFQGWMRARETnX1q2bUFpagqlT76j1OLXaC+PGTcC4cRNgsVjwyy9r8fHHC/Hzzz/hoYceE0oLsrOzHHpcLRYLiouLoVQqai0tEJu4uMPIyEjHnXfeg7lzn3XYZ6tTvpbg4GBkZ2fDYrFUCbXZ2VmQyWTw92++ntyrcTYGEeBywURERM5z5swpLF78EYKCgnHvvdOrPWbdutX48svPHbZJJBJhEFdJibUEonfvvgCAv/7a4nDskSOHERs7DmvW/Ojs5jcpk8maM3x8fBy2nzp1EidOHHM4BrC+JvafA8DgwcNQVlaKgwf3O2zPysrC6dOn0KtXnyrlFM2JPbsiYL+oRDnDLhERUZ1kZ2fh3LkzAACTyYy8vFzs378XW7b8gaCgYLz33kdQq9XVnmsymfDf/36F3NwcjB49Fp6eXigqKsT69esgk8mE0Dt8+Cj06dMPa9b8CDc3dwwYMAiXL2dgxYplCAoKxq231t5zLDYxMd3h5uaOX35Zi/DwtvDz88fx40ewefNG3HHHNKxZ8yO2bNkEpdIFbdu2g79/AE6discvv6yFn58/Ro4cjQcemIkdO/7CO++8iYcffhzt2rVHbm42/ve/byGTyYWZJMSCYVcEWLNLRERUf//733f43/++A2DtgfTw8ET79pGYM+dpTJ58G9zc3Go89+6774da7YXffluPt956HTqdFr6+foiM7IBPP/0cPXr0AgBIpVIsXPgJvvlmJf78czO+//4buLurMHjwUDz22JNVekjFztfXD++9twiff74Y7777Jlxd3dC3bz988skyyOVyHD16BL/8sgZarRYvvvgqHn74cfznP2/h008/RGRkB4wcORo+Pr5YvnwVVq78Al9/vQJFRYXw9PTEwIEDMX/+W2jfPqq5n6YDicViqb3C+AaUm1vaJNeVy6Xw8VGhsFADo/HKSEdtuRFPffI3AKBbOx/8657eTfL45Bw13UdqWXgfWz7ew9aB97F1aI77GBDgWafjWLMrAi5Ku0UluIIaERERkdMw7IqATCqFXGYdzcjZGIiIiIich2FXJGx1u6zZJSIiInIehl2RsM3IwJ5dIiIiIudh2BUJoWeXYZeIiIjIaRh2RUJpF3Y5QQYRERGRczDsioStZ9diAYwmzshARERE5AwMuyLhar+KGgepERERETkFw65IKO1XUWPdLhEREZFTMOyKhIuCC0sQERERORvDrki42PXs6tmzS0REROQUDLsiYR92ubAEERERkXMw7IqEi/0ANfbsEhERETkFw65IsIyBiIiIyPkYdkWCZQxEREREzsewKxIunHqMiIiIyOkYdkWC8+wSEREROR/DrkjYr6DGsEtERETkHAy7IuFYs8tFJYiIiIicgWFXJJQOK6ixZ5eIiIjIGRh2RcJ+nl1OPUZERETkHKIOu2vXrsUtt9yCmJgYDB8+HO+//z4MBkONxxcWFuLNN9/E2LFjERMTgzFjxmDZsmXQ6/XXsdUNw9kYiIiIiJxP3twNqMmGDRswf/58vPTSSxg7diwSEhIwf/58aLVaLFiwoMrxFosFTzzxBAoKCvD222+jTZs2iI+Px2uvvYb8/HzMnz+/GZ5F3dmH3XLOs0tERETkFKLt2V2yZAkmTZqEWbNmITw8HOPGjcO8efOwZs0aZGdnVzk+MTERx44dw5w5czB48GCEh4dj0qRJmDJlCn799ddmeAb1wxXUiIiIiJxPlGE3OTkZaWlpGDlypMP2ESNGwGw2Y8+ePTWeK5U6PiWlUtkkbXQ2qVQChdzadpYxEBERETmHKMNuUlISACAiIsJhe0hICBQKBRITE6uc06FDBwwcOBArV65Eeno6AOD06dPYtGkT7rnnnqZvtBPYencZdomIiIicQ5Q1u2VlZQAAlUrlsF0ikUClUgn7r7Zs2TI8/fTTGDt2LJRKJfR6Pe677z7861//qtfjS6USSKWShjW+FjKZ1OH/V3NRyFCmM0BvMEMuF+XfIYRr30dqGXgfWz7ew9aB97F1EPN9FGXYbQiLxYL/+7//Q2pqKj777DNEREQgPj4eixYtglqtxrPPPlvna/n6qiCROD/s2qjVbtVud3dTIL+kHBUGE3x8VNUeQ+JR032kloX3seXjPWwdeB9bBzHeR1GGXbVaDQBVenAtFgs0Go2w396uXbuwY8cO/PDDD+jXrx8AoEuXLigvL8d7772H++67D0FBQXV6/IICTZP17KrVbigp0cFkqrpKmkJmfcwKvQkFBWVNGrip4a51H6ll4H1s+XgPWwfex9ahOe5jXTsGRRl2IyMjAQApKSno3bu3sD09PR0GgwFRUVFVzrl06RIAoFOnTg7b27dvD7PZjLS0tDqHXbPZArPZ0tDmX5PJZIbRWPULQVlZumABoC03OszQQOJT032kloX3seXjPWwdeB9bBzHeR/EVVgAIDw9HZGQkdu7c6bB9+/btkMvlGD58eJVzQkNDAQAXL1502G4bzBYWFtZErXUeJReWICIiInIqUYZdAJg3bx62bt2KVatWISMjA9u2bcPSpUsxY8YM+Pn5IT4+HhMnTkRcXBwAYPTo0QgPD8frr7+O/fv3Iy0tDVu3bsXy5csxbNgwhISENPMzujaHuXa5sAQRERFRo4myjAEAJk6ciIULF2L58uVYtGgR/P39MXPmTMyZMwcAoNPpkJSUBK1WCwBwc3PDqlWr8OGHH+KZZ55BWVkZ/Pz8MGnSJDzzzDPN+EzqzkVpt4oae3aJiIiIGk20YRcApkyZgilTplS7b+DAgUhISHDYFh4ejk8//fR6NK1JuLCMgYiIiMipRFvGcCNiGQMRERGRczHsioiL4srtqDCIayQjERERUUvEsCsi9j275QZjM7aEiIiIqHVg2BUR+wFqevbsEhERETUaw66IOAxQY80uERERUaMx7IoIZ2MgIiIici6GXRFRKhl2iYiIiJyJYVdEWMZARERE5FwMuyLiyjIGIiIiIqdi2BURljEQERERORfDrog4rKDGqceIiIiIGo1hV0QcV1Bjzy4RERFRYzHsiojSfgU1DlAjIiIiajSGXRGRSiRQVvbu6tmzS0RERNRoDLsiY6vbZRkDERERUeMx7IoMwy4RERGR8zDsigzDLhEREZHzMOyKjG2Qmt5ghtliaebWEBEREbVsDLsi46q0n2uXvbtEREREjcGwKzIuDksGc2EJIiIiosZg2BUZJReWICIiInIahl2RcVgymAtLEBERETUKw67IuNjV7JazZ5eIiIioURh2RcaxZpdhl4iIiKgxGHZFhmUMRERERM7DsCsy7NklIiIich6GXZGxr9ll2CUiIiJqHIZdkXHo2WUZAxEREVGjMOyKDMsYiIiIiJyHYVdkXBwWleAKakRERESNwbArMkrW7BIRERE5DcOuyLCMgYiIiMh5GHZFxpUD1IiIiIichmFXZFjGQEREROQ8DLsi47CCGsMuERERUaMw7IqMUi6FpPJj9uwSERERNQ7DrshIJBIoK3t3OfUYERERUeMw7IqQbcngCr2xmVtCRERE1LIx7IqQbWEJ9uwSERERNY68uRtQm7Vr12LVqlVITU2Fj48PYmNj8dxzz0GhUFQ59pdffsHLL79c47W2b9+ONm3aNGVzncZFKGNgzS4RERFRY4g27G7YsAHz58/HSy+9hLFjxyIhIQHz58+HVqvFggULqhx/yy23YPjw4VW2L1u2DAcOHEBwcPD1aLZT2MKuwWiG2WyBVCq5xhlEREREVB3Rht0lS5Zg0qRJmDVrFgAgPDwceXl5WLBgAebMmYOgoCCH411dXeHq6uqwLSUlBevWrcPSpUshl4v2qVahvGoVNTeXltN2IiIiIjERZc1ucnIy0tLSMHLkSIftI0aMgNlsxp49e+p0nXfeeQeDBw/GiBEjmqKZTcaVC0sQEREROYUouwyTkpIAABEREQ7bQ0JCoFAokJiYeM1rnDhxArt378a6deuapI1NyUXBsEtERETkDKIMu2VlZQAAlUrlsF0ikUClUgn7a7N8+XIMGTIE3bt3r/fjS6WSJqmTlcmkDv+viatd2YLJbIFcLsoO+BtWXe8jiRvvY8vHe9g68D62DmK+j6IMu42VlpaGHTt24PPPP2/Q+b6+KkgkTTcoTK12q32/p4vwsdJFCR8fVS1HU3O51n2kloH3seXjPWwdeB9bBzHeR1GGXbVaDQBVenAtFgs0Go2wvyZ//vknXF1dMWTIkAY9fkGBpsl6dtVqN5SU6GAy1TKHrt2+vIIyFHq71HwsXXd1vo8karyPLR/vYevA+9g6NMd9rGtnoCjDbmRkJADrbAq9e/cWtqenp8NgMCAqKqrW8//66y8MGjQILi4NC4lmswVms6VB59aFyWSG0VjzF4LCrmxBozPWeiw1n2vdR2oZeB9bPt7D1oH3sXUQ430UX2EFrNOMRUZGYufOnQ7bt2/fDrlcXu18ujbl5eU4ceIE+vTp09TNbDL2A9T0HKBGRERE1GCiDLsAMG/ePGzduhWrVq1CRkYGtm3bhqVLl2LGjBnw8/NDfHw8Jk6ciLi4OIfzkpOTYTabq8zk0JJwNgYiIiIi5xBlGQMATJw4EQsXLsTy5cuxaNEi+Pv7Y+bMmZgzZw4AQKfTISkpCVqt1uG8oqIiAICnp+f1brLTuHCeXSIiIiKnEG3YBYApU6ZgypQp1e4bOHAgEhISqmwfNGhQtdtbkqtXUCMiIiKihhFtGcONzNU+7OoZdomIiIgaimFXhFjGQEREROQcDLsixDIGIiIiIudg2BUh+zKG8gqGXSIiIqKGYtgVIbVKIXxcrNU3Y0uIiIiIWjaGXRFSyGVQuVonyigqrWjm1hARERG1XAy7IuXtYV3quFijh8XSdEsXExEREbVmDLsi5eWhBAAYjGZoK4zN3BoiIiKilolhV6RsPbsAUFTGul0iIiKihmDYFSlbzy4AFJWxbpeIiIioIRh2Rcq+Z7eYYZeIiIioQRh2RYplDERERESNx7ArUt4sYyAiIiJqNIZdkfJizy4RERFRozHsipS36krPLmt2iYiIiBqGYVeklAoZ3F0qV1Fj2CUiIiJqEIZdEfP2rFxFrYyrqBERERE1BMOuiHlVljLojWbouIoaERERUb0x7IoYpx8jIiIiahyGXRHj9GNEREREjcOwK2KOq6ixZ5eIiIiovhh2RcyLPbtEREREjcKwK2Ks2SUiIiJqHIZdEWPNLhEREVHjMOyKmJdDzS7DLhEREVF9MeyKmItCBjdhFTWWMRARERHVF8OuyNlKGYo0FVxFjYiIiKieGHZFzjZITW8wQ1dhaubWEBEREbUsDLsiZz9IrVjDul0iIiKi+mDYFTn7QWpFpQy7RERERPXBsCtyDnPtajhIjYiIiKg+GHZFjnPtEhERETUcw67IeTvMtcueXSIiIqL6YNgVOfbsEhERETUcw67IOQxQY88uERERUb0w7Iqc4ypq7NklIiIiqg+G3RbAVspQXKbnKmpERERE9cCw2wLYBqlVGEwo13MVNSIiIqK6YthtAbw4SI2IiIioQRh2WwBvDlIjIiIiahBRh921a9filltuQUxMDIYPH473338fBoOh1nMOHDiAu+++Gz169MCwYcPw9ttvQ69v2QHRW3WlZ7eYPbtEREREdSbasLthwwbMnz8f06ZNw+bNm/HGG29gw4YNePvtt2s858SJE3j44YcxZMgQ/PHHH/j3v/+N33//Hf/+97+vY8udz9uTPbtEREREDSFv7gbUZMmSJZg0aRJmzZoFAAgPD0deXh4WLFiAOXPmICgoqMo5H330EUaMGIF58+YJ5yxZsgRGo/F6Nt3pvFSs2SUiIiJqCFH27CYnJyMtLQ0jR4502D5ixAiYzWbs2bOnyjlFRUU4dOgQYmNjHbb3798fgwcPbtL2NjXHnl2GXSIiIqK6EmXYTUpKAgBEREQ4bA8JCYFCoUBiYmKVcxISEmA2m+Hp6YnnnnsOQ4cOxejRo/HJJ59cs85X7LxVV8JuMcsYiIiIiOpMlGUMZWVlAACVSuWwXSKRQKVSCfvt5efnAwDefvttzJ49G4888ggOHTqEDz74ACUlJXj99dfr/PhSqQRSqaQRz6B6MpnU4f91JZdL4eYig67ChCKNHnK5KP9GuWE09D6SuPA+tny8h60D72PrIOb72KRht7CwEJ6enpDLmz5T23pvb7nlFtxzzz0AgC5duiAzMxPfffcdnnrqKfj6+tbpWr6+Kkgkzg+7Nmq1W73P8VW7ISO3DMVlFfDxUV37BGpyDbmPJD68jy0f72HrwPvYOojxPjY6he7evRtr167FkiVLhG3//PMPXn31VWRlZUGlUuHJJ5/E7Nmz63xNtVoNAFV6cC0WCzQajbDfnqenJwAgJibGYXu/fv2watUqXLhwAQMHDqzT4xcUaJqsZ1etdkNJiQ4mk7le56rdFcgAUK434XJWMdxcRNkpf0NozH0k8eB9bPl4D1sH3sfWoTnuY107/xqVmOLi4vDkk09CIpHAbDZDKpUiJycHTz75JHQ6Hbp27Yr09HQsXLgQ7dq1w+jRo+t03cjISABASkoKevfuLWxPT0+HwWBAVFRUlXPatWsHACguLnbYbrFYAAAeHh51fl5mswVms6XOx9eXyWSG0Vi/LwT7GRnyinQI8WPvbnNryH0k8eF9bPl4D1sH3sfWQYz3sVGFFd9++y3c3Nzw888/Qyq1Xuqnn36CTqfD008/jV9++QV//vkngoKCsHr16jpfNzw8HJGRkdi5c6fD9u3bt0Mul2P48OFVzomMjER4eDj++usvh+1xcXFwcXERwnBLZb+KGgepEREREdVNo8JufHw8xo8fj06dOgnbdu7cCVdXV8yYMQMA4O3tjXHjxuHUqVP1uva8efOwdetWrFq1ChkZGdi2bRuWLl2KGTNmwM/PD/Hx8Zg4cSLi4uKEc5555hns2LEDn332GdLS0rB27Vr8+OOPmDlzZpXBbi2Nlwfn2iUiIiKqr0aVMeTn56Nt27bC58XFxTh79iyGDBniUDYQGBhYpbzgWiZOnIiFCxdi+fLlWLRoEfz9/TFz5kzMmTMHAKDT6ZCUlAStViucExsbC4vFguXLl2PFihXw8/PDU089hYcffrgxT1MU7Ht2uYoaERERUd00KuwqlUqHQWT79u2DxWLB0KFDHY4rKytrUM/qlClTMGXKlGr3DRw4EAkJCVW2T548GZMnT673Y4mdN3t2iYiIiOqtUWUMHTp0wM6dO2E0GmE2m/Htt99CIpFUGYh26NAhhIWFNaqhNzqHml0Ne3aJiIiI6qJRYTc2NhYXLlzA+PHjMW7cOJw4cQIjRoxA+/btAQBarRbvv/8+Tpw4gXHjxjmlwTcqh5rdUvbsEhEREdVFo8oYHnjgAVy8eBG//PILjEYjunfvjvfee0/Yn5+fj1WrVqFLly71mmeXqnJVyuGqlKFcb11FjYiIiIiurVFhVyqV4q233sIrr7wCjUYDPz8/h/3h4eF49dVXcfvtt8PNTXwrarQ03h4uyCrQsmaXiIiIqI6csoCxq6trlaBrM3369BY/7ZdY2AapVehN0FUYm7k1REREROLX6LB75swZvPvuuw7bzp07hwceeAC9e/fGpEmTsGXLlsY+DIGD1IiIiIjqq1FhNyEhAQ888AD+97//wWy2Lg1XUlKCBx98EHFxcVAqlUhMTMRzzz2HI0eOOKXBNzL7QWrFLGUgIiIiuqZGhd2vv/4aRqMRy5YtE5YLXrt2LQoKCnDffffh4MGD2Lp1K9RqNb799lunNPhGZt+zW8iwS0RERHRNjQq7hw8fxvjx4zFixAhh219//QW5XI6nnnoKABAREYHx48fj2LFjjWspQa260rNbojE0Y0uIiIiIWoZGhd28vDxERUUJn2s0Gpw6dQo9e/aEr6+vsD0sLAwFBQWNeSiCY9gt1bJml4iIiOhaGhV2ZTIZKiquvJ1+6NAhGI3GKssF63Q6Tj3mBGp3u5pdDlAjIiIiuqZGhd22bdti//79wuc//vgjJBIJRo0a5XDcyZMnERQU1JiHIlzVs8uwS0RERHRNjVpUYvz48fjss89wzz33QCqV4tixY+jVqxe6du0KADCZTPjxxx+xf/9+PPjgg05p8I3Mw00OCQALgBKWMRARERFdU6PC7kMPPYQjR45g3759AICQkBAsXLhQ2J+cnIy3334boaGhDLtOIJNK4eGuQKnWwAFqRERERHXQqLDr4uKCr776CsnJySgpKUHnzp2hVF55qz0yMhKzZs3C7NmzHQasUcOpVUpr2NXqYbFYIJFImrtJRERERKLVqLBr065du2q3SyQSvPTSS854CKqkdlciAxoYjGaU601wc3HKLSQiIiJqlZySlLKysrBlyxacOXMGhYWFkEgk8PPzQ0xMDG655Rb4+Pg442EIV821q9Uz7BIRERHVotFJ6ZtvvsGiRYtgNBphsVgc9m3YsAGLFi3CW2+9hdjY2MY+FAHwdFcIH5do9AjycW/G1hARERGJW6PC7u7du/Hee+/Bzc0Nt956K3r06AFfX1+YzWYUFBTgyJEj2Lp1K1566SVERESgR48ezmr3DcuLq6gRERER1Vmjwu53330HLy8vrFmzBm3btq2y/5577sEjjzyCe++9FytXrsRnn33WmIcjAJ7ujmUMRERERFSzRi0qcerUKUyYMKHaoGvTqVMnTJgwAUePHm3MQ1ElLixBREREVHeNCrtlZWUIDg6+5nFt2rRBUVFRYx6KKjksGcyeXSIiIqJaNSrsqtVqpKWlXfO4y5cvQ61WN+ahqJJadWWAGnt2iYiIiGrXqLDbs2dP/Pnnn0hISKjxmHPnzmHz5s3o1atXYx6KKtn37JYw7BIRERHVqlED1GbPno1du3bhrrvuwqRJk9C7d29hpbT8/HzExcVh69atMJlMeOihh5zS4BudUiGDq1KGcr0JxVrOxkBERERUm0aF3QEDBuCtt97CO++8g/Xr12PDhg0O+y0WC9zc3PD222+jb9++jXkosqNWKVGu17GMgYiIiOgaGr2oxF133YXRo0dj06ZNOHXqFPLz84UV1Lp3745JkyZxBTUnU7srkVOog7bCCIPRDIW8UdUoRERERK2WU9aa9ff3x4wZM2rcv337dqxfvx5LlixxxsPd8BymH9Pq4at2bcbWEBEREYnXdekSTElJwfbt26/HQ90Q1PZLBnP6MSIiIqIa8f3vFkjNJYOJiIiI6oRhtwVyDLvs2SUiIiKqCcNuC+Qw1y7LGIiIiIhqxLDbArFnl4iIiKhuGHZbIE8OUCMiIiKqE4bdFsjLfuox9uwSERER1aje8+wOHjy43g9SXl5e73OoZm4ucshlEhhNFhRzNgYiIiKiGtU77BYWFjbogSQSSYPOo6okEgk83ZUoLK1AKcsYiIiIiGpU77DLxSHEQS2EXQPMFguk/GOCiIiIqIp6h92wsLCmaAfVk21GBrPFgjKdwWE6MiIiIiKyqnfYvZ7Wrl2LVatWITU1FT4+PoiNjcVzzz0HhUJR5dj09HSMHTu22uvcf//9eP3115u6udeVWnXlNSjV6Bl2iYiIiKoh2rC7YcMGzJ8/Hy+99BLGjh2LhIQEzJ8/H1qtFgsWLKjxvMWLF6N3794O29zc3Jq6udedw8ISGj3CApqxMUREREQiJdqwu2TJEkyaNAmzZs0CAISHhyMvLw8LFizAnDlzEBQUVO15Xl5eCAho/cnPYWEJLWdkICIiIqqOKOfZTU5ORlpaGkaOHOmwfcSIETCbzdizZ08ztUw8ru7ZJSIiIqKqRBl2k5KSAAAREREO20NCQqBQKJCYmNgczRIVx55dhl0iIiKi6oiyjKGsrAwAoFKpHLZLJBKoVCphf3X++OMPLFq0CKmpqfD29sbtt9+OWbNmQams+wAuqVQCqdT5U3nJZFKH/zeGj9pF+LhMZ4BcLsq/W1olZ95Haj68jy0f72HrwPvYOoj5Pooy7DaETCaDv78/ysvL8cILL8Dd3R179+7FZ599huTkZLz77rt1vpavr6pJF8FQq50wYE4mEz7U6k3w8VHVcjA1BafcR2p2vI8tH+9h68D72DqI8T6KMuyq1WoAqNKDa7FYoNFohP32QkJCsG/fPodtXbt2hUajwRdffIGnnnoKoaGhdXr8ggJNk/XsqtVuKCnRwWQyN+paJrMZEgAWAPlFOhQWapzSRro2Z95Haj68jy0f72HrwPvYOjTHfaxrR58ow25kZCQAICUlxWEasfT0dBgMBkRFRdX5Wl26dAEAZGdn1znsms0WmM2WerS4fkwmM4zGxn8hqNwUKNMZUFymd8r1qH6cdR+pefE+tny8h60D72PrIMb7KL7CClinGYuMjMTOnTsdtm/fvh1yuRzDhw+vcs62bdvw0ksvwWg0Omw/efIkpFJplcFurYFX5SC1Uq0eFkvThXMiIiKilkqUYRcA5s2bh61bt2LVqlXIyMjAtm3bsHTpUsyYMQN+fn6Ij4/HxIkTERcXBwAICgrCxo0b8eyzz+LUqVNISUnB999/j2+//RZ33nkn/Pz8mvkZOZ+nu3UVNb3RjHK9qZlbQ0RERCQ+oixjAICJEydi4cKFWL58ORYtWgR/f3/MnDkTc+bMAQDodDokJSVBq9UCALp3745Vq1Zh2bJlePjhh1FWVoawsDA89dRTeOihh5rzqTQZ++nHSrV6uLmI9nYSERERNQtRp6MpU6ZgypQp1e4bOHAgEhISHLb1798fq1atuh5NEwXHhSUMCPRpxsYQERERiZBoyxjo2ux7dou5ihoRERFRFQy7LdjVZQxERERE5IhhtwVzLGNg2CUiIiK6GsNuC2bfs1vCnl0iIiKiKhh2WzB15dRjAHt2iYiIiKrDsNuCeTr07BqasSVERERE4sSw24K5KGRwUcoAsGeXiIiIqDoMuy2cV+UgNYZdIiIioqoYdls4T5W1bldbYYTRZG7m1hARERGJC8NuC8fpx4iIiIhqxrDbwnk5LCzBQWpERERE9hh2WzhPdy4ZTERERFQTht0WjksGExEREdWMYbeFc1hFjT27RERERA4Ydls4+1XUWMZARERE5Ihht4VjGQMRERFRzRh2WziWMRARERHVjGG3hXN3kUMmlQAASjj1GBEREZEDht0WTiKRCL277NklIiIicsSw2wrYVlEr1RpgtliauTVERERE4sGw2wrYenbNFgtXUSMiIiKyw7DbCvh4uggfF5VWNGNLiIiIiMSFYbcV8LULuwWl5c3YEiIiIiJxYdhtBdizS0RERFQ9ht1mllBwER8f/RwHM480+Bo+avueXYZdIiIiIht5czfgRrclZQcuFiXhclkWBob0bdA1fDxdhY8LGXaJiIiIBOzZbWZGsxEAoDXqYDKbGnQN+5pdhl0iIiKiKxh2m5mL7MpyvxWmhi0K4eYih6tSBoBlDERERET2GHabmWPYbXhQtQ1SKywth4ULSxAREYme2WKp8+/svfGZ+GjNcaRklTZxq1ofht1mprQLu/oG9uwCV8Ku3mCGtsLY6HYRERFR0yksrcCLn/+Dl1ccQHFZ7Z1dJVo9vtl8DqcSC/DFr6e4Wmo9Mew2MxfZlXrbhpYxAI7TjxWWsJSBiIhIzPbGX0Z+SQVyCnXYffxyrcfGX8wXAm52oQ6nEvOvRxNbDYbdZqaUKYSPGxd2r8zIwLpdIiIicbuYUSJ8fPR8bq3HHr+Y5/D5X3HpTdKm1opht5k59uw2PKQ6zsjAVdSIiIiqk5ZThldWHMDSX07CaDI3SxvMFgsuZRQLn6fmlCG3SFftsQajCaeSHHtyTycV4HKepknb2Jow7DYz+wFqerOhwdfx4fRjRERE1/T9nwnIKtDiyPlcxCXkNEsbMvO1VcbX1NS7eya5EHqDNZS7u1xZHmHbEfbu1hXDbjNzmI3B2PjZGACWMRAREVXnfFoRLqRf6VHdF5/ZLO2w79W1OVJD2LUvYbh3XEe4VE41+s+pTGjKG95JdiNh2G1mSqld2DU3vGbXV81V1IiIiGqz+UCKw+dnkguRX3z9S/8u2oVdhdwaxS6lF1eZlcFssQhhVyGXol90IIbFhACwzr7094naB7aRFcNuM3ORX+mR1RsbHnZVrnLhG6aIYZeIiMhBem4ZTlxyrH21wNpDer3ZenZlUglG9w4T2nLsguNAtOTMUhSXWbNB17Y+cFHKMLZfG2H/jiPpMJmbp+64JWHYbWYOZQyN6NmVSCRCKQPLGIiIiBxtPpAqfDy+fzgklR/vPZl5XeetLdMZkJmvBQC0DfbE4G7Bwr6rSxmOX7zyee9OAQCAYF93dI/0AwDkl1Tg2HnHgExVMew2M2etoAZcmZFBV2GEjgtLEBERAQDyinU4eCYbgPWd0NuGR6JLOx8AQG5ROS6kFV23ttjX60aFeSEiyAN+laWI51IKHepwj9v19Pbs4Cd8fJNd7+62uLSmbG6rIOqwu3btWtxyyy2IiYnB8OHD8f7778NgqFsxdlFREYYOHYoxY8Y0cSsbx37qscasoAY4DlIrusZqLERERDeKrYfShN7bcf3C4aKUYVj3EGH/3us4UO3iVWFXIpGgT2WvrclsQfxFa6lFbpEO6bnW6cUiQ9Xw8rjyO75be1+E+LkDAM6nF3MJ4WsQbdjdsGED5s+fj2nTpmHz5s144403sGHDBrz99tt1Ov/dd99FUVFR0zbSCRwGqDU67HJhCSIiInslWj32VA7kUiqkGNvX2ivap1MA3Cqn8jqckHPd3hG179ntEOYFAOgbHSBss5Uy2Pfq9oryd7iGRCLBuL7s3a0r0YbdJUuWYNKkSZg1axbCw8Mxbtw4zJs3D2vWrEF2dnat5/7999/YunUrpkyZcp1a23AucmeGXS4ZTERE18+ljGL8eTgNFQaTU69botXjo5+O470fjqKgpHGzJWyPS4feaB3ENaJnKDzcrCuXKhUyDOwaBMA6s0Hcuaafc9doMiMx07pymp/aVfi9HRXmBbW7tV2nEvNRYTA5TDnWu6N/lWsNiQkR5t09eDYbJZrGZYjWTJRhNzk5GWlpaRg5cqTD9hEjRsBsNmPPnj01nltWVoY33ngDc+fORWhoaFM3tdGc2bPLVdSIiOh6Scspw3s/HMXq7Rfw/Z8JTruuyWzGFxtO4VRSAc6nFWHlxjMNHkBWrjdix1Hr4gsyqQQT+kc47LcvZdhzsulLGdJzy4QFIqLaeAnbpVKJMABNbzTj0JlsJKQWAQACvF0R6q+qci0XpQwjelpzjtFkqdMiE2U6A44k5KJC79w/TsROlGE3KSkJABAR4fhFGRISAoVCgcTExBrPXbRoEXx8fDB79uwmbaOzKGUK4ePGDlDz5ipqRER0HZjNFnyz+SxMZmsIPXA6u049sGbztUPrmh2XcK4y6AHAudQibI9r2Gphfx+/DE25tTxhUNcg+Hm5OuxvH+IpBMmL6cXIKtA26HHq6mK6Y72uPVvdLgCs3XVJCPi9ogIgkUhQnbF920Amte7bfiQd2vKaSzGMJjPe/99RLF1/EkvXn4TlOs5A0dzk1z7k+isrKwMAqFSOf8lIJBKoVCph/9Xi4uKwdu1arFmzBjKZrMGPL5VKIJVW/4XVGDKZ1OH/lY8GpVQBvdkAg0kPubzhf38E+LgJHxeWNe5aVLPq7yO1NLyPLR/vYfPZcjAFSZlXBkWZzBbsOJaBe8Z2rPGcPScu45tN59ApwhuPTO4qLIZkfx/3xWfir8r6U5lUIoTpdbsvoWdH/2p7OGtiNJmx9fCVWtbYoe2q/b04slcoftx2AQCw/1QW7hoTVefHqK/EyyXCx9ER3g7t6d7BD24uMugqTCjTXRmM369zQI2/z4P83DG0ewj+PnEZugojdh3PwJRh7as9dtfxDGRUDng7lVSACxnF6NrO1xlPC4C4vx9FGXYboqKiAq+++ipmzZqFrl27Nupavr6qGv+Kcga12s3hczeFK/QVBhgsBvj41P0b+WpeXu7CD4cSrb5R16Jru/o+UsvE+9jy8R5eX1n5Gvy82/oOq0RiDaVGkwW7j2VgZmw3uLsqqpxTXFaBH/46D4PJjNNJBZi/8hCeu68P+nUJEo7JKa7Aqk1nhc8fu70H0rJL8fueRBiMZny96SwWPjW8zmHqp20JwrucA7sFI6ZTULXH3TwsEj/tuAiz2YJ9p7Lw0G09hN5SZ7tUWa/ropShR3RQlecysFsIdh290ovt4abAgB5hkNfynO+/uQv2xl+G2WKddeLu8Z3h6uIY77TlBvy6N8lh28b9KRjaO7zW9hqMZkgl9QuwYvx+FGXYVavVAFClB9disUCj0Qj77S1evBhyuRxz585t9OMXFGiarGdXrXZDSYkOJtOVFU8UUusPBp2hAoWFmkY9ho+nC/KKy5FbqGv0tah6Nd1Hall4H1s+3sPrz2Kx4NPVx4Saz7F928BgNGN3ZbnAb7svYsKAiCrnfbvlnMNb7KVaPRasPIBbBrfF3WM7QiqX4+2vDwoDyUb1DsOgzgHo3cEXh09nIatAi/OpRfh+05kaey7tZeZr8NOf5wEAUokEkwZH1Po7sWeUH46dz0NBSTn2HElFz6iqA8Iaq6DE+rsZACJD1Cgp0VU5pnukr0PY7dHBD6XVHGfPTS7BwK7B2H86C6VaPX7ZcR43D2rrcMzPuy4JK7HZnLqUj/3H09G5rU+1142/lIfP1sUjwMsNLz3Qx2Hqs+o0x/djXTv1RBl2IyMjAQApKSno3bu3sD09PR0GgwFRUVXfYti0aRMyMzMdjjebzbBYLOjatSvmzJmDp556qk6PbzZb6lRX1FAmkxlG45UvBNsgtQpThcP2hvCuDLtlOgO0OgOUioaXc1Dtrr6P1DLxPrZ8vIfXz76TmTiVWADA2rly2/BIFJSUY/dx69ReWw+mYnSvMIcOo8x8DXYcyQAAuChkiI7wRnzlsr2b9qcgIbUILkoZ8itrfjuEqXHv2I4wGs2QSSR4aFIXvPv9EVgswPq/ExHT3hcRQZ41ttFsseDrjWdhqAxc4/uHo42/R61fI8NiQoSVyJb8fBJ+Xq7wUinhpVJCrVLCw00BmUwCmVQKuUwCmdT6cbtgT7QJ9KjTa5dgV4fcIUxdbXu6RvhAIZfCULmvRwe/On1t3zK4LfafzgJgfU1H9QqFQm79/V9UVoHNB1MAWHvhY4e0E3p5f9l9CS/c16fK9YrLKvDFhtPQG8zIyNNg2fpT+NfdverUESjG70dRht3w8HBERkZi586dmDp1qrB9+/btkMvlGD58eJVzvvrqqyoLTvzvf//D9u3b8dVXX8HPz6/KOWJhW0XNYDbCbDFDKml4vYvvVQtLBPq4N7p9REREJRo9Vm+/IHw+fUI03FzkCAvwQEykL04lFiCvuBxHz+eiX+dA4bi1O68Mtrp5YAQmD22Hv+LSsXbnRZjMFod5Z708lJgztTsUdjWqHcK8cMugtvhjfwpMZgu+3HgGr8/s73CMvb3xmUioXBHN38sVtw6/dk9w9w5+UKuUKNHoUWEw4XKeBpfzrv3uqEwqwUsP9EGHUK9rHlvb4DQbF6UMvTv649DZHLi5yIRlga8lzF+FvtEBOJKQi2KNHnviMzGmj3Ue3l/3JgkzQIzqHYbYIdZgnFOow7nUIpxPK0KncG/hWhaLBas2n3OoGz6bUohf9ybhthGRdWqP2IivirjSvHnzsHXrVqxatQoZGRnYtm0bli5dihkzZsDPzw/x8fGYOHEi4uLiAADt27dHp06dHP75+flBoVAIH4uV/SpqTp1rlzMyEBGRk/y4/YIws8GALoEOCx3YT+m19XCq8PHZlEJhvlhvDyUmDIiARCLB+P7heGV6X/jbzY4gk0rw5G3dHX6P2dw6rD3CK3tQM3I1+Hn3pWpnEyguq8CaHReFz2dMjIZLHd7hlMukeCS2K6LDvRHg7QplHQd4m8wWfLc1oU7vBtuvnBZZSzi+76ZOuG1EJJ6d1ktY9KIuYge3Ez7efCAFRpMZmfka7DlhnVLNVSnD5KHtIJNKMXnIlWOvruXdfeKy0POucpVDWjmGaeM/yTiVmF/n9oiJKHt2AWDixIlYuHAhli9fjkWLFsHf3x8zZ87EnDlzAAA6nQ5JSUnQapt2mpDrQSm7Mteu3qSHm9y1lqNrx1XUiIhaD6PJjIKScgR4uzXpwOlrOXExDwfPWBd0UrnKcd+4Tg77u7bzQZsAFdJzNbiUUYKLGcWIDFU7BM/bRkTCRXkleLYPUePN2f3x046LuHi5BHeMjKyxx1Muk+Lh2K5465vDMJkt+PNwGrIKtJgxIVqY1QEA/rftArSVK6EN7haEmPZ17+jq1t4X3dpbZyewWCwo15tQotGjWKOHptwAk8kCk9kCk9kMo8mCvw6nISNPg9TsMuw8liGszFYdvcGE1Gzr7BUhfu7CwhbVUbsrHcJoXbUN9kSPDn6Iv5SP/JIK7D+VheMX8670qg9qC7W7NW8M6haE3/clI6dIh7MphbiQXoSObbyRXah16L1/OLYrMvI0WLfrEiwAVvx+Bm/O7u/wmrcEog27ADBlypQaV0EbOHAgEhJqn8R67ty5Thmw1tRcZPYLSzQuoPqyZ5eIqFWwWCxY/PNJnEzMx6CuQXh4clehl+16Kix1nCXhnrEdoVYpHY6x9tZG4OvK47YeSkWvKH+kVAa88EAPDI0JwdXcXRV4ZEo3+PioUFioqbXWMzzQA3eN6oDVlQE6/lI+Xlt5ENNGR2FEr1DEX8rH4cpV0DzcFLi7lmnQrkUikcDNRQ43FzmCfKsvBwzxc8d/vj8KAPjl70voFx1Q4yCu5KxSYRq1mgK9M8QOaSf0yq7ddUkoRfD2UGJ8/yszL8ikUkwa0harNp0DAPy2NwnPTOuJL38/c6XkoVcoekb5o3sHP1xIK8KJS/ko0xnw+a+n8OJ9fWqdIUJsWk5LWzHHsGuo5chr45LBREStQ0JqEU5Wvm184Ew21u28dN3bYDSZ8fmGUyjRWn83xUT6YkhMcLXHDuwaBK/KEHz0fC7W7rrS3rvHRDlllqPxAyIw947u8PKwPk653oRvtybgwx+POazids/YKKEXs6l0bOMtrMCmqzBhzc6LNR5rX8LQlGE3KswLXSpnV7CvuZ06PLJKOcfgbsFCGcnp5EJ88etpYR7gQB83TKucb1gqkeCh2K7wq+zNvZRRgrXN8LXYGKLu2b1RKJ3Ys2sfdgu4ZDARUYu19VCqw+dbDqUiwMcNo3uHXbc2rNl5UQhqfmoXPBLbtcZyCoVcijF922D934mwWKwD2gDrjALOXLygd8cARId7Y/WOi9gbb61HtV9xrVs7HwzuVn0gd7Y7R3fAsQu50JQbsf90Nkb0DEV0RNWpvBwGp7VpurALAJOHtMPZlELh8xA/dwztXvX1kMukiB3SDt9stvbuHknIBWANt4/EdoWr8kpE9HBTYM5tMfjP90esJRxxacgr1kEqlcBstpZ3mC0WhAZ4InZQRK1lGs2BPbsiYD9ATd/IAWpeHkrYfg6xjIGIqGXKzNfgROXb0fY9ct//mSC8Td3UDp7JxrbKZXrlMgnm3NYdntfoLR3dO8xhcJdEAtw12vkrkrm7KvDgLV3wr7t7CT2OAKCUSzF9YufrVt+sdlfijpEdhM+/+/M8jFfNMZuWU4YL6UUArPXONZVFOEt0hDc62gXqu0ZFQSatPu4NiQl2GCQIALFD2qJDNb3P7UPUDivkHbuQhyMJuTh2IQ/xl/JxKrEAfx5Mwc5jGU56Js7DsCsCjmUMjQu7MqkU3pU1Qwy7REQt0192y9zeOqw9Jg60znZgsQCf/3pKGOxkT1tuwKnEfOQU1b4IQV1k5GmEHj8AuHdcJ7QPqbqg09U83BQY2v1Kbe7InqEIq8cSv/XVrb0v/v3wAEwYEI62QZ54dEo3BHpf3xW8RvQMFV6by3kaYbnjy3kafL7hFN74+pAwi0VUmFeT111LJBI8OKkLukf6Yerw9ugZVfMgPblMikmDryxA0S7YE7G1DI4b3TsMw3pUrb228fZwQUyk83rxnYVlDCLgctVsDI3l4+mCwtIKlGj0MJrMLaqInIjoRlei1WPfKesCAa5KGUb0DIWriwx5RTrEJeSiQm/Cp+vi8er0vjBbLDh+IQ/HL+YhIbUIJrMF7i5yvPXQgAaPmNdVGLH0l5OoMFhXSRsSE4xRvULrfP6UYe2RkVsGuVyK2+16PZuKq1KOu8c0fDBaY0mlEkyf0An//iYOFlin8krNLsOhs9mwnx3N20OJ2KHtrkubgnzc8ey0nnU6dliPEKTmlCG3SIfp46NrzQwSiQSzb+6MqcPaw2y2QCaTQiq1LrKhVMgQHOiJkhIdF5WgqpxZswtcqdu1wLqwhL+X+NapJiKi6u06miGsoDWiZyjcXa2/qh+O7YrC0mO4dLkEhaUVeG3lQZRXLttrT1thxK7jl3F7AxYAsFgsWLXpLLIKrNN6tgnwwPQJ0fUqC/BSKfHSA33r/dgtWbtgNUb3CcOOoxnQG8zCNG0AoHZX4JbB7TCqV6goVzWVSaWYPj66zsdLJJJq/5CSy6WQibRzTZytusE4c1EJgAtLEBG1VAajCTuOWutkpRIJxvW7MnerUiHD3Dt6CDWWVwddfy9X4S3yv09crlI7ei0JqYV497sjiKscqOTmIseTt8fUaVEGAm4fEQm1+5WBWSpXOe4c1QHvPz4E4/uHizLo3ijYsysCSifW7AIMu0RELdX+09nCNF/9OgdUeWdOrVLi2Wk98cGPx1BUpkf7EE/06hiA3lH+CAtQ4fMNpxCXkIsSjR5Hz+diQJegaz5mek4Z1u2+VGXg28OxXRDEJefrzN1VgXl39cTGf5LRLtgT4/qF12sFNGo6vAsi0BQ1uzYFnGuXiKhFsFgsDtONjbdbgtdeiJ8K7z02GEaTGe6ujlM8je7TRuiZ3XE0o9awm19cjvV7ErH/VBbsF7sN9VfhnjFRiIms++pjZNU+RI25d/Ro7mbQVRh2RcCZK6gBgK/dksFFZQy7REQtwcnEAmTmW2tlO7bxQmRozbMfKBWyat8W7xzhjRA/d2Tma3E+rQjpuWVoE+BR5bj84nIs+Oaww8IDPp4umDq8PYbGhDhlAQgisWDNrgg4cwU14OqFJRh2iYhaAvte3QkDqu/VvRaJROKw6ER1c55aLBZ8s+WcEHRVrnJMGx2F/zw6CMN7hDLoUqvDsCsCSocyhsaHU28P+5pdrqJGRCR2yVklwqpXgd5u6BXl3+BrDYkJgVJh/fX+z6ks6CqMDvv3xmfidFIBAGvnyLuPDsLEgREcQEWtFsOuCDh7NgaFXCqMCOUANSIi8dKUG7Bu1yW89/1RYdtN/cMb1bvq7ioXlsut0Jtw4HSWsK+wtAKrd1wUPp8xIfqaq6IRtXQMuyLgzBXUbHwq63aLSvUwmy3XOJqIiK6nCoMJf+xPxouf78emAynQV86rG+jjhmHda16hqq7sSxl2HMuAxWKBxWLBt1vOCT29g7sFo2cjepCJWgoOUBMBqUQKhVQOg9nolAFqgPWtqZTsUpgtFhRr9A51vEREdH2ZzRbkFeuQma9FWk4Zth9JR7HmSueGXCbBqF5hiB3aDi7KxpcTRAR5IirMCxczipGRq8GF9GLkF5fjROX0Yl4qJe4d13yrjhFdTwy7IqGUKWEwG50y9RgA+Kgd59pl2CUiahyLxYKj53MhkUjQK8q/1lIDi8WCw+dycPhcDrLytcgu1MJoqvoum0RiXY731mHtnb7a5eg+YbiYUQwA+H1fEpKzSoV90ydEw8NNUdOpRK0Kw65IuMhcoDFonVbG4Ot59SC1mqewISKia1u/Jwkb/0kGAEQEeuC+mzqhU7h3leMycsvw/Z/nkZBWVOv1+nYKwNQRkQjzVzm/sQD6RQfix20XUKYz4HRyobB9QJdA9OkU0CSPSSRGDLsiYavbrTA7q2aX048RkbjpDaYWMwPAkYQcIegCQGpOGd774SgGdAnEtNFR8FW7olxvxG97k/FXXBpMdmMl5DIJAn3cEeLrjmA/dwT7uqN9iBqhTRRybRRyKUb0DMWmAynCNk93Be6/qVOTPi6R2DDsioRt+jG9SQ+zxQyppHFjB33sFpbgjAxEJCbn04rw4/YLSM0qxaQh7XD7iMjmblKtMvI0WPnHWeFzH08X4efqobM5OH4hD8N7huLo+VyHn7eB3m64d1xHxET6QiZtnvHgo3qFYvOBFGGFtPtv6sTZF+iGw7ArEvbTjxnMRocZGhrCvozhSEIOXBUydGjjhcgQNdfqJqJmkVesw9qdl3D4XI6wbeM/yQj2dcOQmMbPQNAUtOUGLPk5HhV6EwBgUNcgPBzbFX/HX8YvuxNRpjNAbzRj+5F04Ry5TIpJg9vilkERUMibt+fa39sNY/u2wbYj6RjVKxT9Owc2a3uImgNTj0i4yK4MFKgwVTQ67Pp4ukAmlcBktiC3qBwb9iYBACQAwgJU6NXRH7GD27WYtxCJqOWq0Juw6UAKthxKhaFyii17/92SgDB/D7QN9myG1tXMbLFgxe9nkF2oAwCEB3pg5s2dIZVaZ07o3zkQv+5Nwo4jGTBbrH2nPTr44b5xHRHo496cTXdw77iOmDo8Eu6u/JVPNyZ+5YuEfc+uM2ZkUCpkuHtMFDb+k4wS7ZUliC0A0nM1SM/VIKtAhydu7QaJhEtDElHTOJWYj1Wbzzm8ve/prsBtIyKRnFmCv09kwmA0Y+n6k3h9Vn9RzRCwYU8S4iun6lK5yvHU7d3hYtdBoHJV4L5xnTCyVxgOnM5CVJgXenTwE93PVIlEwqBLNzR+9YuEUur8hSXG9QvH2L5tkFtcjkvpxbh4uRiX0ouRllsGiwWIO5eD3/xVuHVYe6c8HhGRjdlswW/7kvD7vmShXlQmlWBcvzaYPKQ93F3lMMSEIC1Hg6TMEuQVl2P5b6fx7F09G7V6mLPYD0iTSIDHp8YgwLv6qcHC/FW4Y2SH69g6IqoPhl2RcJE7P+wC1r/oA73dEOjthsEx1uUjj1/Mw+J18bAA+HVvEkL9VazjIiKnKdXqseL3MzidVCBsi2nvi/tu6oRg3ytv7yvkUjx5WwwWfHMYpVoDTicVYP2exGYPjgUl5fjKbkDaXaOi0K2dbzO2iIgag8sFi4SLQ89u086e0CvKH3eOvvLL5KuNZ5CcVdKkj0lEN4ZLGcV4c9VhIehKJMCdozrgmWk9HYKuja/aFU/cGgNp5Vv/f+xPwZGEnCrHXS8WiwXfbU1AeeWAtAFdAjFhQHiztYeIGo9hVyScXbN7LRMHRGBIZU+v3mjG4p9PoqisasguKqtAanYpjKaqg0qIiGwsFgu2xaXhvR+OCvW5apUS/3dPb9wyqK0QZqvTua0Pptn9Ab7yj7NY/3ciMvM1Td7uqx0+l3NlSV0PJWZMiBZdDS4R1Q/LGERCKWuaMoaaSCQSzJzYGTmFOlzMKEZhaQUW/xyPp+/ogcTLJTiTUoizKYW4nGf9ZRPo7YZ7xnVEryj/Jm8bEbUsBqMZ3249h30ns4Rtndp44fGpMfD2qNtS5Tf1D0dSVikOnslGhd6E3/9Jxu//JKNtsCcGdw3CgK5Bdb6WTZnOgMt5GuFfVoEWwX7uuGtUh2qnBCvTGfC/v84Lnz9wUye4u4pnwBwRNQzDrki4XOewC1TWy93eHW//9zDySyqQlFmKZ5fsq/bYnCIdPlsXjx4d/HDv2I4IqubtSCK68RRr9Fj6y0lczCgWtk0cGIE7RkbWayEFiUSCWRM7w2y2IO5cjjCoLSWrFClZpfhp50V0bOONbu19EdPeF22DHKcpM1ssyMjV4ExyAc6mFCI5s8RhJhqbU0kFSM8pw9w7elSZc3zNjovCOX06BaBvNMcyELUGDLsi4dize/1WPPNSKTH3jh74z/dHUWEwOeyTSID2IWpIJMClDGtNb/ylfJxOKsD4AeGYPKQdXJX8EiK6UaVml2Lxz/HIL7H+zFLIpXhoUhcM6BLUoOu5KGV4YmoMCksrcOhsNg6czkZKdikAwGKxrrx2Pq0I6/9OhMpVjm6RfugW6Yezifk4nVyA0mrCbXXOpRbhw9XH8Oy0XsJUZ2eSC7D3ZCYAwM1FxiV1iVoRJhWRsO/ZvR41u/YigjzxxNQYfLf1HFxd5OjS1gdd2/qiU7g33F3lsFgsOHwuBz/tuIjC0gqYzBZsPpCKA6ez8X/39q520AkRtW5HEnLx5cbT0Bus9fw+ni6Ye0d3tAtWN/raPp4umDAgAhMGROByngYHzmTj8NlsYXEHANCUG3HoTDYOncmu9hoebgq0CVAh1L/yn58KJosFX2w4BU25EUmZpXjvh6P419294O4qx7dbEoRz7xoVBR/P+pVMEJF4SSwWi+Xah91YcnNLm+S6crkUPj4qFBZqYLxqFaELhZfwybHlAIBxESNxW9SkJmlDY1ToTfjjQDK2HEyF0WT9somJ9MVz03o1b8Ous9ruI7UcvI8Nt+lACtbtuiR83j5Ejbl3dK93TW195RTpcCapAKeTC3AmuRC6CqOwz1UpQ+cIH+sf6+18EOqvqnZgWXpuGRb9dBzFZdZOBX8vV0SHe2PfKWu9cac2Xnjh/j61Dqgj5+L3YuvQHPcxIKBuqy6yZ1ckrvdsDA3hopTh9hEdMKx7CD748RjySypwKrEAF9OLEdXGq7mbR0TXwba4NIegO6hbEGbf3LnaAV/OFujthsDeYRjVOwwmsxlpORoUaAzwUckREehRpxrhNgEeePmBvvjwx2PIKy6v/GcNunKZxLocMIMuUavCqcdEojkGqDVUoI87Jg+9sura+j2JzdgaIrpeDp3Nxo/bLgif3zYiEo/Edr0uQfdqMqkUUW28MGFQW3Rs412vwXCB3m54+YG+CAtQOWyfPKQdQvxUNZxFRC0Vw65INNcAtYYaEhOMwMqlM8+mFCIhtbCZW0RETel0cgG+/P2MMEtC7JB2mDykXYudg9bH0wUv3tcHkaHWGuO2wZ64eVDbZm4VETUFhl2RsC9jEHvPLgDIZVJMHtpO+Hz9niSw/JuodUrJKsWSX07CZLZ+j4/oGYLbhre/xlni5+GmwEv398FL9/fBC/f2hlzGX4lErRG/s0WiJZUx2AzuFizMxHA+rQhnUti7S9TaZBdq8fGa46ioXD63d0d/TG9Fq4rJZVJ0CveuMucuEbUeDLsiIZPKIJdY697EOkDtalKpBLcOu9K7s2FPInt3iUQuIbUQf+xPRvylfGjKa56XVltuxOnkAnz003FhoYWObbzw2JRu9aqPJSJqbvxTVkSUMiWMRl2LqNm16d8lEBv/SUZGngaXMkpwMrEAPTr4NXeziKgaB05nOdTdAkCInzs6hHqhQ5gaJrMFSZdLkJhZgqx8rcNxYQEqPH1nDygV138wGhFRYzDsioiLzAVao67F9OwCgFRi7d1dtuEUAGvvbvdI31bzFidRa3H4XA6+3OgYdAEgM1+LzHytsHpYdfy9XPHctF5QuSqatpFERE1A1GF37dq1WLVqFVJTU+Hj44PY2Fg899xzUCiq/4FbWFiIpUuXYseOHcjJyYG/vz9uvvlmzJs3D66urte59fVnm5GhpdTs2vSJDkB4oAfScsqQnFWK4xfz0LtjQHM3i4gqHUnIxfJfT8NWZTSoaxA83BW4lFGC1OxSYeCZjUwqQXigB9qHqhEZokafTgGsaSWiFku0P702bNiA+fPn46WXXsLYsWORkJCA+fPnQ6vVYsGCBVWON5vNePjhh6HVavHOO++gTZs2iIuLw+uvv47c3Fx8+OGHzfAs6sfFLuxaLJYW0zsqlUgwdXh7LP75JADg592JCPB2Q5sAj2ZuGVHrZ7ZYkJ5TBoVcimBf9yo/N45fzMMXv56CuTLpDu8R4rBwgt5gQkp2KZIul0AqlaB9iBoRQR7NMncuEVFTEG3YXbJkCSZNmoRZs2YBAMLDw5GXl4cFCxZgzpw5CAoKcjj+7NmzSElJwbJlyzBgwADhnLi4OGzevLlFhEdb2LXAAoPZCKWs5bxl2CvKH+2CPZGcVYrLeRq8/tUhdGvng5v6RyAm0pcrEhE5kcViQWp2GQ6eycbBs9koLLXW+fuqXRDT3hcx7f3QpZ0PEi+XYNn6K1OGDYkJrrJCmFIhQ8c23ujYxrs5ngoRUZMTZdhNTk5GWloann76aYftI0aMgNlsxp49e3DnnXc67OvWrRvi4uKqXEsqlUImk4k+6AKO04/pTfoWFXYlEglmTuyMRT8dR5nOOnL7dHIhTicXIsTPHWP6tIFSLkVBaQXyS8pRWFKOgtIKGIxm+Hu5IsjXHYE+bgjysf4/2Nedc14SXSW7QIsDZ7Jx4Ew2sgu0VfYXlFTg7xOZ+PtEJiQS67sutqA7qGsQHrylC//wJKIbjijDblJSEgAgIiLCYXtISAgUCgUSE6+9PK3RaMSOHTuwceNGzJ07t0na6WxXr6LmgZa1bGXbYE+8//hg7DuZib/i0pBbVA7AOgDmh7/O13heXnE5zqUWOWzzcFNgTJ8wjOnbBmp3ZfUnEt0ASrV6HDqbg/2ns5B4uaTKfplUgm7tfWEyW5CQWgSjyQwAsFgAU2XpQr/oADwU2wVSKYMuEd14RBl2y8rKAAAqlWPYk0gkUKlUwv6a3HPPPThx4gRUKhVeeeUV3HXXXfV6fKlU0iS/FGSVPZWyGnos3RRXBtGZJEbI5S2vZ9NTrsTEQW0xfkAEjl3IxZaDqUi4KsjaKBVSKGRSaMqNVfaV6Qz4bV8ythxMxYheoZg4MAKBPu5N3Pq6udZ9pJZBzPfRaDLj6Plc7DuZifiL+VUGkEkAREd4Y3BMMPp1DoRn5R+EeoMJ51KLcDIxH6cu5SMjT4PB3YLxyJSurfKdEjHfQ6o73sfWQcz3UZRht7E+/vhjFBcXY+/evXjrrbeQk5ODJ598ss7n+/qqmrTsQa12q3a7p/uVMKd0l8LHxzHsaw06fHHoeyhlCjza/37RlzmM8/PAuEHtcTGtCMfO58DdRY4AH3f4e7shwMcNHm4KSCQSlGn1uJynweU8DTLzNEi6XIxDp7NgMlugN5qxLS4dO46kY2jPMNw7PhrhQZ7N/dQA1HwfqWUR0300mS3YfTQNP2w5h5xCXZX97UPVGNUnHCN6h8Hfu/p2BwWqMbKf9V0xg9EMRQv8o7m+xHQPqeF4H1sHMd5HUYZdtVoNAFV6cC0WCzQajbC/JiEhIQgJCUHnzp0hkUiwaNEi3HXXXQgMDKzT4xcUaJqsZ1etdkNJiQ6myrcaHRiv/FLKKyyGv1TjsHtP+gEcSD8KAAhyDcK4tiOc3sam4OehwLg+YQ7bjBUGFFVcWb0pwFOJAE8lerb3AdAGd43qgK2HUrHraAYqDCaYLcCe4xk4cCoT0ydEY2Sv0Garw77mfaQWoTH30WKxIC2nDClZpWgT6IH2IbX/TKrL9Y6ez8W6XZeQkev4fe/j6YLBMcEY2j0E4YGVM5xYzCgs1FRzpRsLvxdbB97H1qE57uPVnYI1EWXYjYyMBACkpKSgd+/ewvb09HQYDAZERUVVOScxMRGnTp3ClClTHLZ37NgRJpMJSUlJdQ67ZrMFZnPTLXtrMplhNFb9QlBIrvTUavXlVY7JLssTPt6fEYdRYcOarI3NzVulxN2jozBpUFvsPJaBbXFpKNUaYDCa8fUfZ3EmqQDTJ0TXOPenwWiGxWKpdbWncr0RcedysfdkJlKyS6F2V8Dfyw1+alf4e7nCz8sVHcK8EOxbfflETfeRWpar76PeYIJMJqmyJG6pVo/TyQU4nViAU0kFKNZcmQ+7b6cA3DmqA4Kq+VqxWCw4k1KIXccyUFBSDm8PF/h4XvmnkMvw56FUXLqqHjemvS8mDIxAlwgf4Y9vfr1Vj9+LrQPvY+sgxvsoyrAbHh6OyMhI7Ny5E1OnThW2b9++HXK5HMOHD69yTnx8PF588UW0a9cOPXr0ELafO3cOAKpMVSZGLjIX4ePqVlErqrjyyzC97DIyyjIR5hFyXdrWXDzcFJg8pB3G9w/Hmp0XsfNoBgDgwJlsJGWVYs7UGKG3S1dhxPGLeTh8NgenkgpgNlsQHuiBqDAvdGijRlSYF/zUrriYUYw98Zk4fC4HFXqT8Fi5epMwqM5GAuCecR1xU7/w6/acqXkkpBZi7a5LwiAwqUQChVwKhVwKuUyC4jJ9ldXHbI6cz8Xxi3kY1SsMk4e1g9pdCYPRhP2ns/FXXNpVvbWltbajQ6gad4zsgM5tfZzzxIiIbnCiDLsAMG/ePDzzzDNYtWoVxo8fj7Nnz2Lp0qWYMWMG/Pz8EB8fjxdeeAFvv/02+vXrh5tvvhnLly/HCy+8gFdffRXt2rXDsWPH8OWXX2LYsGFo165dcz+la3JxmI2hatgtrih2+PxAZhzu6Di5ydslBi4KGaaPj0Z0uDe+2XwO5XoTsgu0+Pd/4zBxYDjSczQ4lVQgjES3SckuRUp2KbZbqz/gqpSh3C7g2vh4uqBcb4SuwnGfBcCP2y5AKpFgbN82TfX0bhhGkxn5JeUoKKlAYant/9Z/bi5yTBwYceWt+usku0CL1dsu4Mj5XIftZosFFQYTKgxVv16UCim6RPggPMgDf5/IRIlGD5PZgu1H0/HP6Uz0jQ7EiYt5KNUaqpxbkzB/FW4fGYleUf4tYqpEIqKWQrRhd+LEiVi4cCGWL1+ORYsWwd/fHzNnzsScOXMAADqdDklJSdBqrXNNuri44JtvvsGiRYvwwgsvoKysDKGhobj33nvx2GOPNedTqTOHqcfM1fXsOobdw1nHMLXDLZBJb5yVjgZ0CULbYE98vuEUUrPLYDSZsfGflCrHeXko4eGmwOVcjUNvnH3QdVXKMLBrEIb1CEFkiBoSiQTacgPyisuRX1yOU8kFQk+ybeq0+gTecr0RpxILoCk3oEOoF0IDVDfMHKcWiwXJWaVIzylDVoEWmflaZBVokVukqzKzgL1DZ7Nx86AITB7SzikreJXpDNgWl4aCkgoE+VrncbbN6VxhMOGXPafw+55EhzYF+bhBrVLCYDTDYDJb/280w9NNga7tfdG9vS+i2ngLA79uGdQWWw6mYsuhVOgNZugqTNgbn+nQjqgwL9zUPxy9ovxRpjOgoLQcRaUVKCitQIlGj/BAD/SLDuTUYERETUBisViarji1hcrNrf1txoaSy60zLBQWaqqtZzmZdwZfxH8DAIhtPwE3tx8r7LNYLHhu92vQmx17ih7vMQvd/bs2SXvFzGA04acdF7GjMowC1oDbr1Mg+ncJRFQbL0glEmjLjUjMLMbF9GJcyihGep4GIb7uGNYjBH2jA+FSS02vxWLB+j1J2PhPsrDtgfGdMH5ARI33UVdhxIlLeYg7l4uTifkw2O33cFOgc4Q3Orf1QecIH6hVSpRXGKGtMEJXYe1V1htNULsr4evlCl9PF6dNF1Wi1SOvqBwB3q7CNFVXq9CbkJBWhDPJBUjKLIG3hws6t/VBl7Y+CPJxq1NvY5nOgH0nM7H7+GVkVbPoQV0F+bpj1sRoREc0/K381OxSLPnlJPKKy6vdr5BJYbB7J0CtUuL2EZEY1j2kQaGzqKwCG/YkYU/8ZVgs1jKIfp0DML5/BCJDGzeAjap3rZ+p1DLwPrYOzXEfAwLqNjuTaHt2b0QuVy0qYU9nLBeCrlKmFGp6D2YeuSHDrkIuwwPjo9Ezyh8X04vRrb2vEHDtubvKEdPeDzHt/er9GBKJBLcNbw+LxYI/9lt7j7//8zzkMinuGBcNACjR6JGWU4a0nDKcTyuqtpTCpkxnQFxCLuIScqvdX+XxYQ3wfmpX+Hi6wMNdCZWrHB5uCni4KaByVcDH0wVBvm5wVVb9Vi4srcCRhBwcScjF+fQi2P6sVauUCPNXIcxfhdAAFcq0BpxJLsDFjGIYTY5/+x4+lwMA8PZQoktbH3QK94aPpwtUlW3wcFPAzUWOSxnF2HXsMg6fy6nx+SvkUgT5uCPY1w3+Xm7CAC1ftSu8VErsib+MP/anwGS2ILtAi/f/dwyjeochdnBbuChlkEullQPHJNcM3gdOZ+Gbzeegr+UHri3oKuRSTBgQjpsHtq1xwGNdeHu4YNbNnTFhQDjOpxWhe6QffNWu1z6RiIiaFMOuiDgMULuqjMG+hKGnfzecK7iAUkMZTuadgcaghUohjgUXrrfukX7oHln/IFtXEokEt4+wzg5iC7zfbD6HuPO5SMsuRXFZ1XITG7VKib6dAhDg7YbzaUVISCuCrqLqAho1sQAoKtOjqJbHsPH2UCLY1x3Bvu7wdFfiTHJBldH9NiUaPUo0epxNKaxzW4rK9Nh/Ohv7T2dX2SepbOvVOkd4o3fHAIT4W9vlq3attYxj6vBI9OsciG82nxMGie06loFdxzKqHKuUS9EhzAt9OgWgT6cA+Hhav3dMZjPW7ryEPw+nCce2C/bE3WOiUKzRI7tAi+xCHbILtCjW6NGjYwBiB7eFt8p5q/SF+KkQ4teyVj8kImrNGHZFxKFm11hz2PV19UH/4N7YkbYHRosJR7KPY0SbIdetnTcaW+A1WyzYfCAVAHDqUn61x9pKKfp1DkDHNt7C2+ETB0bAbLYgJbsU51ILcSGtGEazGW5KOdxc5HB3kcPNRQaFXIZiTQXyi8uRX1KBgpJyhymuamILxVcvu2wT5OuOjm28kFekw+U8DUqqGTgV4O2Kbu180bWdLzpFeCO/uBxnUwpxLqUQ59OLoDdU30tqH3RVrnIM7R6Ckb1CGxT42gR44JUH+mLH0XT8vDux2sFhAKA3mnE2pRBnUwrxw1/nERmqRp9OATidVOAQ4od1D8H0CZ2qrf/lW6dERDcGhl0RcallgJr9tGPeLmp08G6PHWl7AAAHso4w7DYxiUSCO0d2gAQSbDpg7eH1cFMgPNADbQI8EB5Y+S/Io8beS6lUgvYharQPUePmgXV/bIPRhOIyPTTlRpTpDCjTGaApN6BMax1Ml1WoRXaBtsrI/zYBKvSNDkTf6ACE+TuuClii0SOjctU6hVyKzm19EHjVilxqdyXah6hxy6C2MJrMSLxcgtTsUuvj64zWNlS2R+Uqty5dGx1Y69zGdSGVSjCuXzh6dwzAlkOpKCgph9FkgdFkts7faLaguEyP/JIrtbiJl0uE3mAAkEkluG9cR4zqHcaZDYiIbnAMuyKirKVm137aMW8XL4R5hKCNRyjSyy4jpSQNWZpsBKvEP5dwSyaRSHDnqA4Y0zcM/n4ekJhMMJmafnynQi6Dv7cb/K9xnKbcgOwCHQpLyxEW4FHjYhiAtcRCrbLW4daFXCZFp3BvdAr3rnvDG8nPyxX339Sp2n0WiwXpuRocPZ+LIwm5SM+9stqil0qJObfFoGMb7+vUUiIiEjOGXRGpbVGJwqvCLgAMDOmL9AuXAQAHMo9gatQt16GVFOjjDh8vt8rlWsUzmYnKVYHIUAWA1j/yXyKRCL3ptw5rj5xCLY6ez0OpTo9xfcOFGl4iIiLnzGtETiGXyCCVWG/J1YtK2PfselWG3f5BvYXjD2UdhdnCukO6MQX6uGPiwAjcNSqKQZeIiBww7IqIRCIR6nav7tm11exKJVJ4Kq0DfzyVHujm1xkAUKwvwbmCC9extURERETix7ArMkqpNexe3bNrm43BS6kWenMBYFBIP+Hjg1lHrkMLiYiIiFoOhl2RcZFXDbsGsxFlBg2AK/W6NjF+nYU5dk/knqoSkomIiIhuZAy7IuMi9OxemY2h+Kppx+zJpXL0CogBYA3FF4uSrkMriYiIiFoGhl2RUVbOyGC2mGE0W1fbsl9QwtvVq8o5nX2vTM90ruB8E7eQiIiIqOVg2BUZWxkDcKWUoaiaacfsRftEQQLrxPkcpEZERER0BcOuyNjKGIArMzI4hF1l1TlUVQp3RKjbAAAua7Icyh6IiIiIbmQMuyJjv7CErW7XPrx6VdOzCwBdfDoKH7N3l4iIiMiKYVdkHJcMrtqz61NNzS4AdPa1C7uFDLtEREREAMOu6LhcI+x6VVPGAADtvdoKQflcwQVYLOJZxpaIiIiouTDsiox92L1Ss2stY1Ap3KGQKao9Ty6Vo6N3JACgRF+Ky5qsJm4pERERkfjJm7sB5MixZ7cCZotZqNmtbiYGe118O+F0/jkA1t7dMI+QpmtoMzJbzCgsL0KuLh+5unxUmCoQ4dkG7dQRUNbwxwCJl8VigcaohcFkgIdCVeMfdERERA3BsCsyjjW7BpQZNDBZTACuHXYd6nYLLmBsxIimaWQTMFvM0Bp00Bg00Bi1KNNrUGao/Ff5cam+DHnl+cjXFQqviT2ZRIYIzzB08G6PKO/26ODVDu6Vq8s1tE2F5cXILy9Avq4AeZX/L6wogtpNBW+FN3xdfBHg5ocANz/4uvlCIW3Yt5TFYoHRbESFWQ8JJJBKJJBAav2/RAqj2YjiihLrP32J8LG11MUCi+2/ldUrUokUMqkMMokUMokMMqkMSqkCfm629vpDpXCHRCIRnmtBeRGyNNnI1uYiT1cALxdPRHi2QYRnG3goVQ1+HQHAZDYhR5eHy2VZyNHmoqC8CIUVRdb/lxdCbzYIx7rKXOCh9ICnQuXwfw+Fyvqvcpu7wh3ucje4yl0cltCu7rW1PU8iIrrxMOyKzNWzMTjOsVt9va5NsHsgvJRqFOtLcKEoEQazscHhy8ZisQjXqWtgsFgsKCgvQqYmC5fLsnBZk4VcXT5MFhMsFktlNLNd2wCNQQudsVzY3lAmiwlJJalIKknFttTdkEtk6BXYHcPDBqODV7ta22+2mJGlyUFaaQZSS9ORWpqB9NIMhxDmoKj6zR4KFTyVHvBUekKt9ICn0gNKqRLlpgqUG8tRYapAubECOlM5KowVKDdVoMKkF3rxryc3uSsC3PxgspiRo82FoXIRk+r4ufogwrMNQj2CIZfIYYal8l6aHe+p3Tlmixl5unxkarKRo82r9g+U6pSbKlCuq0CeLr9Ox0sggZvcFe5yNyhlShjMBhjMRhhMBujNBhjNRrjL3RDiEYRQVQhCPYIR5hGMcHUI5BUWZGlyUVJeBo1BizKDFiazEcGqIIR5hMBN7lrtY9qeW7Y2F6V6DbRGLXQGHbRG6z+T2YRI73bo7tcVAe5+dXoeRETUNBh2Rebq2RjqMu2YjUQiQWffjjiYdQQGswGJRcmI9o2q0+OaLWacLbiAy2WZ/9/efYdXVaWLH/+emt47SUiDJEAIBAi9ShFEQMSKFBX74AyD1zvovdz54eM44vOT39VBR2dEHXF0FBRshCpKCx1Cb2mk94Tk5KSe/fsjZMshCSRIkpPM+3mePCFrr7OzThbJec/a734XxVUlFFWVqJ9r6muw19nhbu+Op507HvZueNh5YKc3UllrprKuElNt44eJ/MpCqq7Z7vh2MmoNeF9dSfV28MLH0QudRkdqWTqXylLJryxU+9Yp9RzOO87hvOP4O/kxpsdwhvoPQq/Vk2PKJbM8m8yKxo8cNUf612hcjc4x5f3qc7U3c10Vl8uzWtW36Or/hWMFJ2/rGAxaA5727njYuWOnt7u6il9BeU0FlXXmVp1DQVGDzJaY6iq5VJra5u20vR28CHLuQZBzD3QaLdmmPHJNueRW5t/wzQHAsYKTfHXxO/wcfYnxjqa/Vx98HL3Jrywg15RPbmU+eaYCCsxFeDt4Eu8/iDifGOybCbAVRSHHlMeJwtOUVZfj6+hNgJMfAU7+uBqdZeVaCCFuQIJdG3P9DWptWdkF1GAXGkqQtSbYraqrYs3pf3Km6HzLfeqryTXlkfsrgjitRosWDWg0aK7u+abT6nEyOP7yoXfC2eCIk8EJZ6OTeunaxejU0GZwavaFfWSPoQCU11SQXJbGhZJkDucdw1RbCUCuKY91F79hQ/IPWBRLq1ZRvew9CXQOwNvBEy8HT7ztGz77OnlidNZyKSeD3PICCq/mDhdVlVBeU86VmgpqW1oRvoa9zg47nRE7vR12OjvsdXZqvqqiNKycWhQLFixoNTrcjK6427ni1vhhdMVeb6funqfRNP4LLIpCnVJHvcVCvVKPRanHXFel5jkXVhZRYC6kuKoUjUaDj4MXfo6++Dv54u/oi7eDF8VVJVwuzyT9SiYZFVm3/GZAp9Hh5+hzNTjzw9/JD28HTzzs3K1SKa5XZ6mzSmOpqKmgvPaXf1fWma++2Wr4MNeaqbHUYtQaMOgM6LV6jNqGz43pH21VaC6i0FzE8V8R5OdV5pN3OZ8dl3e12KeoqpjzJZf44vwGBvj0Y5j/YKI8epFenklSwSmSCk5R0MJKt6PegQAnP0Jcg+nnFU0v9zD0bbyic6HkEusufItRZ+SByFmEuAa36fFCCGHLJNi1MdffoFZadW2w637Tx1vn7V5gVsS0G/YvrS7j3aQPyarIaXJMr9Xjae+Oi8GZ8poKSqpLb7qapUGDu50bgc7+9HAOIODq5WBfR59fnVLRGi5GZwb6xDDQJ4bZEXdxrOAke7L2k1yWBjQEUM3xsvcg2CWIEJcggl0DCXYJxNnQfJ6qXq/Fw8GJ3h4GwlxCmxxXFIXq+mquXBP42uvscdDbYa+3x15nh1FnvGGeaUeps9ShQYNOq2tyLIJQ4v3jgIaV/7zKAvIrC9HQGFhrfvmMhsZI+5eQu+ENmo+Dd7Pnvxm9Vo+7ndtNc9Vbq6LWRE5FLllX02sKzIU42tlhp7HHUdf4hssJUMgy5ZJVnk1WRU6TVBatRouPg5cavLvZueGod8DR4NDwWe9IjaWG00XnOFV4lpSy9Bum6Bi0BvXNUa2lVr0aodPoWpX6UVlnJrksjeSyNH7M2I29zo5oz0hivPvQzysKV6NLi4+tt9SzKXUbW9J3qmP8v0feYWrIHUwNnXhL82YLGvL/KyWFRAgBSLBrc67N2a2pr6Gq7pd0gNas7LoaXQh0DiCrIoeM8mwqakwt3lyUWZ7NX098pK4eO+gduLfXdAKc/PC098TF6GQVkCmKQkWtiZLqUkqqSqmpr8XR4IiToeEF3sngiIPe3iaCOACDzsBQ/0EM9R9EdkUue7IPcKrwDA56h4ZL0y49CHIOINC5B44Gh9v2fTUaTUNQq7fH19Hntp23PbR2BVCr0arBXVflbHCit0cEvT0igKtvWjycKCkxUVfX/Eq/RbFQUFlIZkU2iqIQ4Ozf6jdugc4BTAmZQEWNiTPF5zlddI7KWjN+jj74Ofni7+iDv5MfzgYn0q5kcDD3CEfykjDVNVyNuDbQ1Wq09HILY4BPDIHOAeSbG1Ihckx55JjyrK4AVdVXc7zgJMcLTqJBQ2/3cEb0iGegT3+raiVF5hI+PvMZKWXpTZ7zprTtnC46z8K+D+Ln5Nv6H3Inq62vZUfGbrak/0hNfQ3RHr25O/xOwtx6dvbQhBCdSKPI7gNNFBSUt8t5W/PiWmgu5o+JrwMw2HcAptpKdUe0N8b8H5xaUV3g60vfq5dMH+83l8F+A5v0OV10njWn1qobV3jZe/LcgMfx70IvbJ2lNfMobJ8tzmOdpY7TRec4kHuUjPIsAp0DGOATQ3/vPi1eaYCGVetzxRc5VXiWM0Xn1YD5Wg56B+L9BjKiRzxF5hL+eW495qt5zlqNlhnhd1JrqWNz2g41zcegNXBPr7sYFziyQ/KCFUVhT/YBiqtK6OPZmwi3sBuuLjfOYXFxBUdyT/L1xe8pqipu0i/GK5rp4VPo6RLUnsMXt8gWfxdF23XGPPr4tHzl6lqysmtjrt9BrXHFxqA14Khv3epjH49INdg9V3zRKthVFIXdWftZd/Eb9QUt1LUnz8Q+iovR+TY9CyHErdBr9QzwiWGAT0ybHudscGKI30CG+A3EolhILbvMqaKzHM8/Sb654aZNc52ZXVmJ7MpKtHqsl70Hj/V7RF39jPGK5uMzn5NfWUitpZZ1F77hcO4xJoWMJ9a7b7tduVEUhXUXv+XnzL0AbE3fiYPegX5eUcR696OvVyQOzfwNvFyaxd+P/IvzxZfUNg0aXI0uap72qaJznCo6xwDvfowJHIGXgycedm6dUtPZolg4lHsMi2JheMAQublQiA4gwa6NaZKzq24o4drqP4oRV29QqbPUcfaarYNPF53j+9StZFxzB/5An/4s7PuQbMYgRDeh1WiJcA8lwj2UmeFTSS5LY1/2QY7ln2iSfzzYdwAPR99rFUSGuAbzUvwSNiZv4ufMfQCkXrnM309+go+DF3cEj2V4wGCryjG/lqIobEj+QQ10G5nrzFY5zC5GZxTFguVquTuLYmlStjDSoxf39Z6Bv6Mv+3MOk5C2g5LqUgCSCk+TVHha7etscMLjapWZENdgIj0i6OkS1G65yhbFwj/PrWd/zmGgYUV+csj4dvleQohfSLBrYwxaAxo0KCiU11RQVV8F3HxDiWsZdQZ6uYVxruQiJdWl7Ms+SGLOIVKvXLbqN7HnWO6JuMtmcmyFELeXRqOh19VNVu6PnMWRvOPszzlMSXUZ08MmMyIgvtk30Q1VGe6hv1dfvrr0nVpKr8BcxBcXNvBD6lZGBMTjZHCkXrFgUeqxKBbqFQtuRlcG+sa06W/W96lb1atRGjRMDhlPkbmY00Xn1b+B9Uq9VW7y9bztPbm3993EevdTn9OowGEMDRjMvuyDbEnbQVmNdYpaY6nAjPIsNQi20xmJcA8j0j2CKM9eBDsH3pbV1zpLHR+f/tyqfN8PqduI843F28HzV59fCNEyydltRmfm7AK88PNyquqrre7GHuI3kMf6zW3199qW/hMbkzc1eyzYuQd3h99JjHeftj0BAUh+WXch89g6iqJwpvgCOy7/zPmSSzd/AA0Ba5RHL4b6D2KATwz2ersW+yak7uD71C3q1w9H3cvowOFAQ4B4qTSVk4VnOFt8gaq66oYShlergGi1WhyN9sT5xDKux6gbpiXU1NdyJD+JHFMupVVl6g5+ZdVXblgto4eTP6MDhzPUP65JGkW9pZ5zJZc4mHuE1LLLBDj5cWfoHYS7hVz3vWv4+8m1nCluWt6xj2ckvxmw6N86nUF+F7sHydkVbWLUNey4de3d2B6tKDt2rWjP3pBs3Rbg5MfdYVMY4BPzb/2HVQjRehqNhn5eUfTziiKjPIsdl3dxJD/phrWqFRTOlVzkXMlFjBc2MMA7hkiPCLzsPdR8WZ1Wx/bLP1sFuvf3nqUGutCQwxzt2duqpOK12vLiatQZGBEwpEl7vaWewqpiLpWkcKE0mQslyVy5ZgU425TLlxc2svHSDwzxi2NM4HA0Gg0Hc49yKO8Y5TUVat+iqmJOFZ0l2qM308Im0cs9DHNdFX9N+ojksoYNTQxaAwv6PshXF7+jtLqMs8UXOJx3XC3zJ4S4/STYtUF2zeTCubWi7Ni1Ap0DCHLuQWZFNr4O3kwPm8wgvwGSsiCEuGXBLoE82u9hZkVMI/1KBmg06DTaq6utWjRouFSawsHcY2pVhJr6Gg7lHeVQ3lH1PI31uBtzaQFm95rO+OBRHf2U0GkbNj3xc/RhVOAwFEUhrzKf81c3pmkszVZjqWVfzkH25Rxs9jyN6WeAGuj3dg+nqr5avU/CXmfHswMeb9j4Q6Pj/ZP/AGD9xW/p6xXVqmo7LVEUhYO5R0lI2/5LusY1F271Wj39vfsyJWQ8/l24hKCwTZW1lWxL+Yny+nJmhk7F1XB76qPfLhLs2qDmbvxoa2F9rUbLC4OfI8eU17DVaRctDi+EsD0e9u542Ls3eyzaszfTw6aQXJbGwdyjHM0/oZY4a6SgWAW6M8LvZFLPce044tbTaDT4X93pb1zQSLIqctiTtZ+DuUebbIOu1+iI8e7DUP9BRHtGciQviS1pOyi8GuhfLE1R+zoZHFk84Al6ujaUP4v16cdAnxiOF5yiotbExks/8Eif+29pzEXmEj4//xVniy+02KfGUsuB3CMczD3KAJ8YpobeQbBL4C19v9aorK1Eo9E0W0FD2JbKWjPfJG+itPoK90fOxNuh9ZuxKIrCobxjfHXxOypqTQB4Gjy5K3Ryew33lkiwa4Ou3ViiUWs2lLieUWeUbT+FEB3u+hvjLpWkkG8upKiqmCJzCcVXP5vrq5geNpmpoRM7e8gtCnQO4MGo2cyKuIsjecc5nJ+EFg0DffszyDfWajV2ZI94hvkP4nDecbak/0heZQEAbkYXno97qsmmLPdHzuJc8SWq6qvYl3OIof6D1E1PWsOiWNidtZ9vkjepNdMB/Bx90GmsFzhKqssw15lRUNRNR/p6RTE1ZCIR7qG38JNpWUZ5Fm8eeQeAeyKmMy6oY+o0dzaLYmFXViIHcg6j0WjxsHPDw84dd/uGnSD9HX0JdA7okJ9FobmIvdkH6eHkz+AbXNUtNBfxbtJH5FXmN3x9opgXBy++YZ59o1xTPl+c38CF0l9yJu31dsT59b89T+I2khvUmtHZN6itPv5Bk3for458ucWVFNGx5GaK7kHmsfMpivKrXvhteQ4tioXjBafIqshhdI9hLf793pW5jy8ubAQagtSX4pe0qv5vnimfT8+tJ+XqVujQcAXwoajZ9Pfu26R/VV01e7L3s+PyLqucZGi4SW5mxNQbbrpRVn2Fy+WZhLmF3HCDE4C/n/yE4wWn1K/7ekYxr8/9Labj/dp5tCgW0q5cJrsilwAnf0Jcg1q9O+S1FEUh31xInaUOnUaLRqNV03Qc9A446O1bfGxBZRGfnvuSS6WpN/wes3tNb9erGJW1Zjan7+DnjL3UXb3vp5d7GA9HzWmyaVRKWRrvn/iHuiLbaLDvAB7rN7fF382a+lq2pP/ItvSfrO4tGuTbn6eGzUVTbbC5G9Qk2G1GZwe71/+h0KDhrfGvSSqCjbDlF1jRejKPXV93mEOLYmHVkXfV0pB3htzBjPA7Www0LIqFnzL28E3KZuosdWr76B7DuKfXXTdNG6itryUx5xDbLv9McVWJ1bFBvrHcHX4nfle3Oa+31HOq6CyJOYc4XXQei2IhwMmPl+KXtPh6VGQu5o+JK5tUuHAyOPJI9H3NbphyK/NYZ6njQkkySQWnOFF4xiqAN2oNhLuFEukR0abayWvPfqnWQL5eY4WReP84BvjEqIGvRbGwKzORb5I3WdWxvjaH+1ouRmf+NPK/bvvreb2lnj3ZB/ghdSum2qY7KOo1Ou4MvYPJIRMwaPUczj3G2nPr1P9Dfo4+lFWXq6X+5vSewR3BY5qcp6SqlPdOfExmRbba5mXvwQOR9zDQv5/NVmOQYLcZnR3s/uPMvziY+8vNHG5GF14bvbxdxiTarju8wAqZx+6gu8xhVkUOrx96S61wEe3Rm9m9phPk0sOqX5G5mLVnv7TKBfZ28OKR6DlEevRq0/est9RzMPcom9K2WwW9Wo2W4f5DcNDbcyD3SJNVP4BHou9jZI+hzZ732u3qB/r0J6UszSoQHRkwlDm9Z1hdJm/LPBZXlfBdyhZOFp7BXFfVqufqbHDiNwMX3XDlOrsilz8dXNWq8xmu3uw3wCeGPVn7rebDy96DeX3uJ9wtlLLqckqrG8rc7crcR/LVVfgnY+Yz0LflS/2KolBgLsLD3h1DK1aozxZdYP3Fb8m9mooADTckjuoxlNOF59QccgA/R1+iPXtbbeAS5dGLJ2Lmc7E0hb9dvWlSq9Hyu7in6eUepvZLKUvnbyf/oVYg0Wq0TOo5jmmhEzHqjFJ6TLTN9Tm77m0sOyaEEKLrCHQOYGroRDalbgMaqjm8fugthgUMZkb4nbgZXdmXc5CvLn5nlZs7IWg0MyOm3tJudjqtjhE94hniH8ferAMkpG2notaERbE0W3HCzeiiVnnYnLaDYf6Dm6xOVtfXsC/7ENCwkvhQ1Gw0aPjs/FckXb1auS/nIOdLLvJQ1L309Ypq05gzyrN4N+nDJmkYBq2ePp5RRLiHklmew8XSZKsNSCpqTay/8C2/H/RsiyvmW9N3qv/u4xmJm50riqJQr9RTr1jIKM+i0FwEQK2ljqP5Jziaf8LqHGMDRzAr4i41kPdy8MDLwQMAJ70jq5M+AGBv9sEbBrtb0nfyXcpmApz8WDLomRumjRzNP8GaU59atQ3xG8jM8Gl4OXhQE1FDQtoOtl/+GYtiIa8yX83PBRjVYygPRs5Gp9UxwKcfU0ImsDV9JxbFwppTn7Is/ne42blyIOcIn51br6ZGeNt78lTsQgKdA1ocmy2RYNcGXb91763cnCaEEKLruCt0Ev6OPnyTnEBRVQkKCvtzDnMkL4kg5x6kXklX+3raezC/zwNEtuFmtpYYtHrGB49ieMAQdmbsYfvln9VL2XqNjliffowIiCfaszfvJn3I2eILFFWVsD/3MKN6DLM614GcI2rljSF+cbgYnYGGlczEnMOsu/gNNfU1FFWV8E7SGuL94pjTewYe+pu/xp0pOs8Hp9aqwb6D3p4Yr74M9OlHH68oq5KdjSujF0uS2ZK+k6KqYpLL0jhTfIF+zQTYheZijuQnAQ1B6ZP9FzQpAaooCmlXLnMw9xhH8o9bpQp42nswL/p+ojxbXl2P8uyFl70HRVUlDT9DczFezeycV1Zdzua0HQDkmPJYc+qfLB6wqNm0h6yKHNae+UL9Osw1hDm97ybsmk1NjDojsyKmMdh3AJ+d/6qhZCANaRb39LqLicFjrd4A3B02hfQrGZwvucSVmnI+OPUpEW6hbLv8k9qnt3s4T/Sff9PcbVti08HuunXr+Oijj7h8+TIeHh7cfffdLF26FIOh+eT9yspK/vrXv7J161Zyc3MJCAjgnnvuYdGiRS0+xhZdv7Lr1sayY0IIIboWjUbDYL+BxPrE8HPmXjan/Yi5zkytpdYq0B0REM+c3jNueLPUrbDX2zEtbCJjgoaTmH0IO52RQX4DrAKau8ImqzdPb0n7kWH+g9UbwRRF4adrLo2PDx5t9dxG9oinl3sYn51br172P5R3jDNF57k/agbT3Fu+aWtf9iE+P/+VmuYR7hbC07GPthhsaTQafB298XX0xsHgoK58fpecQB/P3k0qE+y4uuoJMC54VLO17jUaDWFuIYS5hXBf7xmcLb7A8YJTuBiduTNkAvY3mQ+tRsvIHkP5LmULCgr7cg4xI/zOJv22Xd5J7TW5vxdKLrH+4rc8GDXbql9FrYn3T/xDzRMe6j+IBX0ebHHlOsilB/8x+DfszT7AmaILjOoxtNldVHVaHY/1m8vrh96itLqMlLI0q5sgRwcO54Hes7rcPUQ2G+xu3LiR5cuXs2zZMiZOnMj58+dZvnw5lZWVrFixotnHLF26lKSkJFasWEF0dDSJiYm88sormM1mfv/733fwM7h11/+iycquEEL8ezBo9UzqOY7hAUPYnLaDXZmJ1Cv1uBideST6vmYrLdxOzgYnJoeMb/ZYuFsIfTwj1dXdA7lH1NXdc8UX1cvjvdzDCL4u3xjA19Gb38U9TWLOYTZc+p7KOjOmuko+Pv0FhwuOE+vVDz8HP/ydfHE2OKEoCptSt7Epbbt6joE+/VnY96EmV0BbMtAnhmDnHmRUZJNRkc3xglMM8o1Vj1+pKScxpyH1wqgzMj7o5hub6LQN9ZWbCxZvZHjAEH5I3YZFsZCYfYi7QidZBY1l1VfYk7UfaPh/oCgKdUo9u7IS6eHsz5jAEUBDvvWHp/6pbtzS0yWIh6Pm3LSyiVajZUzgCPU8LXExOvNk//n8vyN/VdMWtBot9/eeydigkW16zrbCZoPd1atXM336dB599FEAgoODKSwsZMWKFTz33HP4+VnXK0xOTmbnzp28/vrrTJkyBYCePXty8OBBPvvssy4e7MrKrhBC/DtxNjhxX++ZjA8aTWpZ+q/eYe12mX7N6u7ma1Z3d2buUfuMDxrd0sPVVd4Y72jWX/hWTR84lX+eU/nn1X4uBmdc7VzIqshR2yYEjebe3ne3aSdQrUbLjIipvJv0IQDfp2xloE+Meo6dGXuovVqRYHSPYe36M3a3c6O/Vx+SCk9TVnOF00XniPXppx7fmr5THcvYwJEEOPvz6dkvAfjywjf4OfoQ6dGLjcmbOF9yCWj4OT3Vf0Grg//WCnXtyUNR9/L5+a+x19vxeL9HWty2uyuwyb1j09LSyMjIYNw468saY8eOxWKxsHv37iaPCQsLY8+ePUyfPt2q3c/PD7PZjMXSde7UNWol2BVCCAHeDp7E+8fZRKALEOYWQl/PhrzX4qoSDuQcIa+ygNNF5wDwsHMnthWrz65GFx6PeYRnYx/Dy96jyfHy2gqrQHdOr7u5L3LmLW1539czigi3UADyKvPVakfmOjO7MhMB0Gl0TOw5ts3nbqtRgb/kOe/NPqD+u7S6jD1XvzZoDUwOGc+IgCFMDG4Yk0Wx8MHJT9mc9iM/ZjTEQFqNlif6z2+3GvwjesTz6qiXeW3Uf3fpQBdsdGU3NbWhKHPPnj2t2gMCAjAYDKSkpDR5jFarxcfHx6qtrq6OXbt2ERsbi1Zrk3F9s+z011djkDQGIYQQtuGusMmcKW5Yhd2c/iN9yzPVY+OCRrYpnzPGuw/9fKMorM/jQm46mVdyyTXlkVOZR3lNBUatgfl9H7RKPWgrjUbDzIhp/L+jfwXgh9RtDPYbyO7M/erNeMP8B3fIwlIfz0g87NwpqS7ldNF5SqpK8bB3Z2v6TrXm7digEerNfff0uoucyjzOFJ3HVFfJdymb1XM9EDnLqjRYe3A1tq60l62zyWC3oqKhhpuTk3XyuUajwcnJST1+M2+++SYpKSl88sknbfr+Wq0Grfb2b+en02mtPrfE0Wid6O7l5IFe33WC9e6utfMobJvMY9cnc9g5enuFEuMdzanCcxRXlagrkkatgbE9h7f59Uqn09PXK5Ig+2Dq63+5CmuqrUSn0d705q/WiPaOoK9XFGeKzlNcVcLu7H38mNmwQqpBw9TwCR30OqtldNBQvkve2lBxI+8wowOHsjfrl5/h1LBrx6Llqdh5vH7wL+SafikZNjpwGON72tY2zLb8+2iTwe6vpSgKK1eu5OOPP2bFihUMGTKkTY/39HRq1/9Arq433uHGu/6XlVxHgwMBPk3Lk4jOd7N5FF2DzGPXJ3PY8eYOnMXL289ZtY0LG06Qr08Lj7i56+fRg9tb2mrBoNks2/Y6AF9f+EHd4WxYcBzRQaG39XvdyF19x/N9yjYURSEx+xDVmNUbwaZGTiDE39+qvwdOvDTuN7y8fSWmmkqivMJ5dsQjrdpWujPY4u+jTQa7rq4Nwd71K7iKomAymdTjzamtrWXZsmVs2bKFN954g5kzZ7b5+xcXm9ptZdfV1YErV8xW716vV1P5yzE3oyslJU13sBGdp7XzKGybzGPXJ3PYeby0PurqbqORfsNu6fWqo+bRQ+PNIN/+HM0/abWV78TAcR36OqvFSH/vPpwoOEORuYRtyQ0rzHY6I+P8RzU7FnuceHno77hYksIgv1gqrtQANU36dabO+H308GjdGyKbDHbDw8MBSE9PJy4uTm3PzMyktraWXr2aL9ysKAp/+MMf+Omnn/j73//OiBE3Lq/REotFwWJpv12U6+stN9xKT6f88m7NzejapbfB7M5uNo+ia5B57PpkDjvHtNBJarAb7dEbX3vfXzUPHTGPd4VN4Vj+KTXY7eMZSQ/HgA7//zMyYCgnCs5YtY0LGoW91qHFsbgbPYj3Gwxg0//fbfH30fYSK2goMxYeHs7OnTut2nfs2IFer2fMmDHNPu6dd95hx44dvyrQtQWe9u54XN0iuKvfASmEEKJ7CnXtybzo+xnmP5j5fR/o7OG0SoCTH0P9B6lf3xkyoVPG0dczyuqGODudUa28IG4/mwx2AX73u9+xZcsWPvroI7Kysti+fTvvvPMOCxYswMvLixMnTjB16lQOHz4MQE5ODu+99x7z5s2jZ8+eFBQUWH3U1NjWcv+N6LQ6lsX/jiVxT3dIKRQhhBDiVozoEc+Cvg92qRKZD0bN5s6QO1jY9yF634Ytl2+FTqtjREC8+vW4oFE4G7vO9rtdjU2mMQBMnTqVN954g/fff58333wTb29vFi5cyHPPPQeA2WwmNTWVysqG/an3799PbW0tH3zwAR988EGT833yyScMGzasSbutcjY60dvYOb+EQgghRHdlpzMyM2JqZw+DST3HkVeZj06j486QOzp7ON2aRlGU9ktO7aIKCsrb5bx6vRYPDydKSkw2l88iWk/msXuQeez6ZA67B5nH7qEz5tHHp3V1gG02jUEIIYQQQohfS4JdIYQQQgjRbUmwK4QQQgghui0JdoUQQgghRLclwa4QQgghhOi2JNgVQgghhBDdlgS7QgghhBCi25JgVwghhBBCdFsS7AohhBBCiG5Lgl0hhBBCCNFtSbArhBBCCCG6LQl2hRBCCCFEtyXBrhBCCCGE6LYk2BVCCCGEEN2WBLtCCCGEEKLbkmBXCCGEEEJ0WxLsCiGEEEKIbkujKIrS2YMQQgghhBCiPcjKrhBCCCGE6LYk2BVCCCGEEN2WBLtCCCGEEKLbkmBXCCGEEEJ0WxLsCiGEEEKIbkuCXSGEEEII0W1JsCuEEEIIIbotCXaFEEIIIUS3JcGuEEIIIYTotiTY7SDr1q3jrrvuIiYmhjFjxrBy5Upqa2s7e1jiBtavX8+sWbOIi4tjwoQJ/Pd//zdFRUXq8YsXL/LEE08QFxdHXFwcTz75JMnJyZ04YnEzjz/+OFFRUWRmZqpthw8f5pFHHmHAgAEMGTKEJUuWkJeX14mjFM3JzMxk8eLFDBo0iPj4eJ577jmys7PV4zKPtq+uro41a9Zw9913Exsby/Dhw3n55ZcpKChQ+8g82qaPP/6YmJgYfv/73zc51po5y83NZcmSJcTHxxMbG8vcuXM5duxYRw1fgt2OsHHjRpYvX84DDzxAQkICf/zjH9m4cSOvvvpqZw9NtOCjjz5i+fLlzJo1i40bN7JixQp2797N888/j6IolJSUsGDBAgD+9a9/sXbtWnQ6HQsXLuTKlSudPHrRnPXr13PgwAGrtpSUFBYtWkRwcDAbNmzg/fffJzs7myeeeELejNqQK1eusGDBAurr6/niiy9Ys2YNubm5LFq0CIvFIvPYRbz11lu89dZbPPnkk/zwww+89dZbHD9+nCeffJK6ujqZRxtUWlrKM888w5o1a7Czs2tyvDVzVlNTw2OPPUZGRgZr1qxh3bp1hIWF8fjjj5ORkdExT0QR7W7ixInK0qVLrdo+//xzJTo6WsnNze2kUYmWWCwWZdSoUcqyZcus2r/44gslMjJSOXv2rPKXv/xFGTBggFJaWqoeLy0tVWJjY5X33nuvo4csbiIvL08ZMmSIsmLFCiUyMlLJyMhQFEVRli1bpowbN06pra1V+yYnJyuRkZHKd99911nDFddZvXq1MmrUKMVsNqttqampSkJCglJVVSXz2EWMHDmyyd/VH374QYmMjFROnz4t82iD1q5dq8yfP18pLCxUJkyYoCxZssTqeGvmbMOGDUpkZKSSnJys9qmtrVXGjh2rLF++vEOeh6zstrO0tDQyMjIYN26cVfvYsWOxWCzs3r27k0YmWqLRaPj+++95+eWXrdr9/PwAMJlM7Nmzh7i4ONzc3NTjbm5uDBgwgF27dnXoeMXNvfLKK8TFxXHnnXdate/Zs4fRo0ej1+vVtvDwcIKCgmQebcjWrVuZNGkS9vb2altoaChTp07Fzs5O5rEL0el0Vl8bjUb13zKPtmfcuHF89NFHeHl5NXu8NXO2e/duQkJCCA8PV/vo9XpGjhzZYfMqwW47S01NBaBnz55W7QEBARgMBlJSUjpjWOIm3N3dcXFxsWrbsWMHjo6OREZGkpqaSnBwcJPHhYSEyJzamISEBPbu3cuKFSus2k0mE/n5+U1+N0Hm0ZbU1tZy6dIlgoODWbVqFXfccQcjRozghRdeoLi4WOaxC3n44YdJSEjg4MGDABQWFvLhhx8ycOBAevbsKfNog4KDg5u8QWnU2t+9G71e5uTkYDabb++gmyHBbjurqKgAwMnJyapdo9Hg5OSkHhe27ccff+TLL7/k6aefxsXFBZPJ1GROAZydnSkvL++EEYrmlJaW8uqrr/LCCy8QEBBgdayl302QebQlZWVl1NXV8Y9//IPq6mpWr17NihUrOHToEI8++qjMYxeyePFiHnroIebPn09MTAyjRo1CURTee+89TCYTIPPYlbT2d+9Gr5dAh8yt/uZdhPj3lpCQwIsvvsiMGTN4+umnO3s4og1ee+01goODmTt3bmcPRdyiuro6oGGF6aWXXgKgb9++6PV6nn322SY3HQrb9eGHH/LZZ5+xfPlyBg8eTFZWFv/7v//L888/zxtvvNHZwxPdmAS77czV1RWgyQquoiiYTCb1uLBNa9eu5bXXXmPu3Ln813/9FxqNBkBd3b1eeXm5VR6v6Dy7du1i69atfPXVV2i1TS9iNaapNHd1RebRdjSu/sTExFi1x8fHA3D27FlA5tHWlZaWsmrVKp577jnmzZsHQJ8+fQgKCmLWrFkcPnwYkHnsSlr7N/RGr5cajaZD4iAJdttZY0J2eno6cXFxantmZia1tbX06tWrs4YmbuLzzz/nT3/6Ey+88AJPPvmk1bHw8HDS09ObPCYtLY2IiIiOGqK4gYSEBKqqqpgxY4bapigKAFOmTCE+Pp6AgIAW53H48OEdNlbRMmdnZ3x8fCgrK7Nqt1gsAPj6+so8dgGXL1+mtraWyMhIq/awsDAAMjIyZB67GEdHx1bNWXh4OEePHm22T2BgoNWNp+1FcnbbWXBwMOHh4ezcudOqfceOHej1esaMGdNJIxM3kpiYyCuvvMKyZcuaBLrQcIfqsWPHKCkpUdsKCws5fvw4d9xxR0cOVbRgyZIlfPvtt2zcuFH9aKxt/be//Y1XX32VcePGsXv3bqsanmfOnCE7O1vm0YaMHTuWXbt2UV1drbY1rgRGRUXJPHYBgYGBAFy6dMmqvXEjnsDAQJnHLqg1czZ+/HgyMjKs5r6mpobdu3czYcKEjhlohxQ4+zeXkJCgREVFKR9++KGSmZmpbNu2TRkyZIjy+uuvd/bQRDMsFosybdo05eGHH1by8/ObfFRUVChXrlxRxowZozz++OPKuXPnlHPnzikLFy5UJkyYoJhMps5+CqIF+/fvt6qze/nyZSUuLk558cUXlZSUFCUpKUmZOXOmcv/99yv19fWdPFrRKDU1VYmLi1OeeeYZJTk5WdmzZ48yYcIE5cEHH1QUReaxq1iyZIkyePBgZePGjcrly5eVQ4cOKbNnz1ZGjRqllJaWyjzaoJKSEvW1b+zYscqzzz6rfm02m1s1Z7W1tcqsWbOU2bNnK0lJSUpycrKydOlSZciQIUpOTk6HPA+Noly9rifa1bfffsv7779Peno63t7e3HfffTz33HPN5hKKzpWVlXXDVYTFixfz/PPPk56ezmuvvcbBgwfRaDSMGDGCl156iaCgoA4crWiLAwcOsGDBAnbs2KHO08mTJ1m5ciUnTpzA3t6eCRMmsGzZMjw8PDp5tOJap06dUufJaDQyefJkXn75ZTWnV+bR9lVWVvL222+zefNmCgoKcHZ2Jj4+nv/4j/8gNDQUkHm0NfPnz1dLxV3vz3/+M/fee2+r5qywsJA///nP7Nq1i5qaGuLi4li2bBnR0dEd8jwk2BVCCCGEEN2WLCsKIYQQQohuS4JdIYQQQgjRbUmwK4QQQgghui0JdoUQQgghRLclwa4QQgghhOi2JNgVQgghhBDdlgS7QgghhBCi25JgVwghxA3Nnz+fqKgoTp482dlDEUKINtN39gCEEKK7yszMZOLEia3u37g7nxBCiNtHgl0hhGhnDg4OrQpi4+LiOmA0Qgjx70WCXSGEaGd2dnYsWrSos4chhBD/liTYFUIIG7Ns2TI2bNjAypUr8fHxYfXq1Zw/fx5FUYiKiuKZZ55h/PjxTR63fft2/vnPf3LmzBlMJhNubm7ExcWxaNGiZleNc3Nzeffdd9m1axeFhYW4ubkxYcIEFi9ejL+/f7NjS0xM5O233+bcuXMA9OvXj6VLlzJo0CCrfseOHeODDz4gKSmJkpISnJ2dCQ4OZsaMGcybNw+dTvfrf1BCCNEKEuwKIYSNOnDgAAkJCUyePJnRo0eTmZnJt99+yzPPPMO7777LHXfcofZ9++23eeedd/Dw8GDKlCn4+flx+fJltmzZwo8//sibb77JtGnT1P4pKSk89NBDmM1mZs6cSVBQEJcuXeKrr75i27ZtrFu3jp49e1qNZ9++fXz44YfMnDmTcePGkZiYyP79+1m0aBGbNm0iICAAgMOHD7Nw4ULs7e2ZNm0agYGBlJeX8/PPP/Paa6+RlJTEqlWrOuaHKIQQihBCiHaRkZGhREZGKkOHDm3T4/7whz8okZGRSlRUlLJ7926rY+vXr1ciIyOVqVOnqm2nT59WoqKilKFDhyo5OTlW/Q8dOqRER0cr8fHxSmVlpdp+7733KpGRkU3O/+mnnyqRkZHK008/rbbNmzdPiYyMVIYPH66kpqaq7RaLRXn00UeVyMhI5aOPPlLbly5dqkRGRio//fST1blramqUhx9+WBk8eLCSnZ3dpp+JEELcKlnZFUKIdqYoCpmZmTfsYzAY8PPzs2qLi4tj9OjRVm333HMPK1euJCUlhYyMDIKDg9m4cSOKojB37twm6QdDhgxh2LBhJCYmsnv3bqZMmcLZs2c5deoU0dHRTc4/Z84csrKy8PX1bTLGBx54gNDQUPVrjUbDmDFj2LdvH1lZWWp7WVkZQJNUBYPBwCeffIJeLy89QoiOI39xhBCinZWVld20BFl0dDTffPONVdv1ebDQEECGhYVx/PhxUlJSCA4O5tSpUy32B4iNjSUxMZHTp08zZcoUtV5unz59mvS1t7fnP//zP5s9T0xMTJM2V1dXACoqKtS2CRMmsHv3bpYuXcqiRYuYNGkSERERABLoCiE6nPzVEUKIdubk5MQbb7xxwz7Ozs5N2ry8vJrt6+7uDsCVK1cAKCoqumF/T09PAEpKSqz6NwaqrdVcf622YW8iRVHUtkceeQSTycR7773HqlWrWLVqFT4+PowePZrZs2czbNiwNn1fIYT4NSTYFUKIdmYwGJg0aVKbH9cYSF7PYrEADSXNoCGdAKwDzub6N/ZrPG9NTU2bx9RaTz31FA8//DA//fQTe/bsYe/evWzYsIENGzZw//338+qrr7bb9xZCiGvJdsFCCGGjGldir1daWgr8spLb+LlxxfZ6xcXFzfZvbG8vLi4uzJgxg5UrV7J7927WrFmDn58f69atIzExsV2/txBCNJJgVwghbFRSUlKTtrq6OlJTUwEICgoCoH///gAcOXKk2fMcPXrUql/j58OHD1NfX2/V12KxsGTJEn77299SV1d3S+MuKyuzumENGlaVR48ezRNPPAHA6dOnb+ncQgjRVhLsCiGEjTpw4ACHDh2yavv6668pLy+nb9++avWGOXPmoNVq+de//kVOTo5V/71793LkyBH8/PzUygtRUVH069ePoqIivv76a6v+mzZtIiEhAZPJdEs3k5WUlDBy5Egee+wxtSrDtRqD3MaavEII0d4kZ1cIIdpZdXU1a9asuWk/Ozs75s2bp349a9YsnnrqKSZOnEhYWJi6qYROp+PFF19U+/Xu3ZslS5awatUq7r33XqZOnYqXlxcpKSls27YNe3t7Vq5cicFgUB/zpz/9ifnz5/M///M/HDhwgIiICJKTk0lISMDZ2bnFigw34+HhwbPPPstf/vIXpk+fzqRJk/D398dsNnP06FEOHjxIv379mDx58i2dXwgh2kqCXSGEaGdms/mm1RigIcf12mA3JiaGOXPmsHr1anbu3InFYiE2Npbnn3+ekSNHWj326aefplevXqxdu5bvv/8es9mMp6cnU6dOVY9dq0+fPmzYsIHVq1ezb98+Nm/ejJubG9OnT2fx4sVNdk9ri8WLFxMVFcWXX37J9u3bKS0txWAwEBoaym9/+1sWLlyI0Wi85fMLIURbaJSWbt8VQgjRKZYtW8aGDRtYvny5VfArhBCi7SRnVwghhBBCdFsS7AohhBBCiG5Lgl0hhBBCCNFtSbArhBBCCCG6LblBTQghhBBCdFuysiuEEEIIIbotCXaFEEIIIUS3JcGuEEIIIYTotiTYFUIIIYQQ3ZYEu0IIIYQQotuSYFcIIYQQQnRbEuwKIYQQQohuS4JdIYQQQgjRbUmwK4QQQgghuq3/D/aM/lEGprAnAAAAAElFTkSuQmCC\n", "text/plain": ["<Figure size 800x550 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# %%\n", "plt.title(\"Adversarial Training Curves\", fontsize=20)\n", "plt.plot(np.linspace(1, n_epochs, n_epochs), epoch_gen_loss_list, color=\"C0\", linewidth=2.0, label=\"Generator\")\n", "plt.plot(np.linspace(1, n_epochs, n_epochs), epoch_disc_loss_list, color=\"C1\", linewidth=2.0, label=\"Discriminator\")\n", "plt.yticks(fontsize=12)\n", "plt.xticks(fontsize=12)\n", "plt.xlabel(\"Epochs\", fontsize=16)\n", "plt.ylabel(\"Loss\", fontsize=16)\n", "plt.legend(prop={\"size\": 14})\n", "plt.show()"]}, {"cell_type": "markdown", "id": "8c4701ac", "metadata": {}, "source": ["### Visualise some reconstruction images"]}, {"cell_type": "code", "execution_count": 17, "id": "8adf85ac", "metadata": {"lines_to_next_cell": 2}, "outputs": [{"data": {"image/png": "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******************************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\n", "text/plain": ["<Figure size 800x550 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Plot every evaluation as a new line and example as columns\n", "val_samples = np.linspace(val_interval, n_epochs, int(n_epochs / val_interval))\n", "fig, ax = plt.subplots(nrows=len(val_samples), ncols=1, sharey=True)\n", "for image_n in range(len(val_samples)):\n", "    reconstructions = torch.reshape(intermediary_images[image_n], (64 * n_example_images, 64)).T\n", "    ax[image_n].imshow(reconstructions.cpu(), cmap=\"gray\")\n", "    ax[image_n].set_xticks([])\n", "    ax[image_n].set_yticks([])\n", "    ax[image_n].set_ylabel(f\"Epoch {val_samples[image_n]:.0f}\")"]}, {"cell_type": "code", "execution_count": 18, "id": "fd170679", "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 800x550 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# %%\n", "fig, ax = plt.subplots(nrows=1, ncols=2)\n", "ax[0].imshow(images[0, 0].detach().cpu(), vmin=0, vmax=1, cmap=\"gray\")\n", "ax[0].axis(\"off\")\n", "ax[0].title.set_text(\"Inputted Image\")\n", "ax[1].imshow(reconstruction[0, 0].detach().cpu(), vmin=0, vmax=1, cmap=\"gray\")\n", "ax[1].axis(\"off\")\n", "ax[1].title.set_text(\"Reconstruction\")\n", "plt.show()"]}, {"cell_type": "markdown", "id": "8ffdc149", "metadata": {}, "source": ["### Cleanup data directory\n", "\n", "Remove directory if a temporary was used."]}, {"cell_type": "code", "execution_count": null, "id": "fd57125a", "metadata": {}, "outputs": [], "source": ["if directory is None:\n", "    shutil.rmtree(root_dir)"]}], "metadata": {"jupytext": {"cell_metadata_filter": "-all", "formats": "auto:light,ipynb", "main_language": "python", "notebook_metadata_filter": "-all"}, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}}, "nbformat": 4, "nbformat_minor": 5}