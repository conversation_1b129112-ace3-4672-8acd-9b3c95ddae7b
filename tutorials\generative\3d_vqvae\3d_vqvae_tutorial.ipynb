{"cells": [{"cell_type": "code", "execution_count": null, "id": "8bd4c6b4", "metadata": {}, "outputs": [], "source": ["# Copyright (c) MONAI Consortium\n", "# Licensed under the Apache License, Version 2.0 (the \"License\");\n", "# you may not use this file except in compliance with the License.\n", "# You may obtain a copy of the License at\n", "#     http://www.apache.org/licenses/LICENSE-2.0\n", "# Unless required by applicable law or agreed to in writing, software\n", "# distributed under the License is distributed on an \"AS IS\" BASIS,\n", "# WITHOUT WAR<PERSON><PERSON>IES OR CONDITIONS OF ANY KIND, either express or implied.\n", "# See the License for the specific language governing permissions and\n", "# limitations under the License."]}, {"cell_type": "markdown", "id": "0b4285e3", "metadata": {}, "source": ["# Vector Quantized Variational Autoencoders for 3D reconstruction of images\n", "\n", "This tutorial illustrates how to use MONAI for training a Vector Quantized Variational Autoencoder (VQVAE)[1] on 3D images.\n", "\n", "Here, we will train our VQVAE model to be able to reconstruct the input images.  We will work with the Decathlon Dataset available on [MONAI](https://docs.monai.io/en/stable/apps.html#monai.apps.DecathlonDataset). In order to train faster, we will select just one of the available tasks (\"Task01_BrainTumour\").\n", "\n", "The VQVAE can also be used as a generative model if an autoregressor model (e.g., PixelCNN, Decoder Transformer) is trained on the discrete latent representations of the VQVAE bottleneck. This falls outside of the scope of this tutorial.\n", "\n", "[1] - <PERSON><PERSON> et al. \"Neural Discrete Representation Learning\" https://arxiv.org/abs/1711.00937\n", "\n", "\n", "### Set up environment"]}, {"cell_type": "code", "execution_count": null, "id": "2859b87c", "metadata": {}, "outputs": [], "source": ["!python -c \"import monai\" || pip install -q \"monai-weekly[tqdm, nibabel]\"\n", "!python -c \"import matplotlib\" || pip install -q matplotlib\n", "%matplotlib inline"]}, {"cell_type": "markdown", "id": "e6a4ca0e", "metadata": {}, "source": ["### Setup imports"]}, {"cell_type": "code", "execution_count": 1, "id": "bb14df03", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["MONAI version: 1.1.dev2248\n", "Numpy version: 1.23.3\n", "Pytorch version: 1.8.0+cu111\n", "MONAI flags: HAS_EXT = False, USE_COMPILED = False, USE_META_DICT = False\n", "MONAI rev id: 3400bd91422ccba9ccc3aa2ffe7fecd4eb5596bf\n", "MONAI __file__: /media/walter/Storage/Projects/GenerativeModels/venv/lib/python3.8/site-packages/monai/__init__.py\n", "\n", "Optional dependencies:\n", "Pytorch Ignite version: 0.4.10\n", "Nibabel version: 4.0.2\n", "scikit-image version: NOT INSTALLED or UNKNOWN VERSION.\n", "Pillow version: 9.2.0\n", "Tensorboard version: 2.11.0\n", "gdown version: NOT INSTALLED or UNKNOWN VERSION.\n", "TorchVision version: 0.9.0+cu111\n", "tqdm version: 4.64.1\n", "lmdb version: NOT INSTALLED or UNKNOWN VERSION.\n", "psutil version: 5.9.3\n", "pandas version: NOT INSTALLED or UNKNOWN VERSION.\n", "einops version: 0.6.0\n", "transformers version: NOT INSTALLED or UNKNOWN VERSION.\n", "mlflow version: NOT INSTALLED or UNKNOWN VERSION.\n", "pynrrd version: NOT INSTALLED or UNKNOWN VERSION.\n", "\n", "For details about installing the optional dependencies, please visit:\n", "    https://docs.monai.io/en/latest/installation.html#installing-the-recommended-dependencies\n", "\n"]}], "source": ["import os\n", "import shutil\n", "import tempfile\n", "import time\n", "\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "import torch\n", "from monai import transforms\n", "from monai.apps import DecathlonDataset\n", "from monai.config import print_config\n", "from monai.data import DataLoader\n", "from monai.utils import set_determinism\n", "from torch.nn import L1Loss\n", "from tqdm import tqdm\n", "\n", "from generative.networks.nets import VQVAE\n", "\n", "print_config()"]}, {"cell_type": "code", "execution_count": 2, "id": "352bd8ea", "metadata": {}, "outputs": [], "source": ["# for reproducibility purposes set a seed\n", "set_determinism(42)"]}, {"cell_type": "markdown", "id": "d0618c17", "metadata": {}, "source": ["### Setup a data directory\n", "\n", "Specify a `MONAI_DATA_DIRECTORY` variable, where the data will be downloaded. If not\n", "specified a temporary directory will be used."]}, {"cell_type": "code", "execution_count": 3, "id": "4fc6c2f9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/tmp/tmpc398pj0s\n"]}], "source": ["directory = os.environ.get(\"MONAI_DATA_DIRECTORY\")\n", "root_dir = tempfile.mkdtemp() if directory is None else directory\n", "print(root_dir)"]}, {"cell_type": "markdown", "id": "a342ff79", "metadata": {}, "source": ["### Setup used transforms and download dataset"]}, {"cell_type": "code", "execution_count": 4, "id": "1e1b3bd0", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/media/walter/Storage/Projects/GenerativeModels/venv/lib/python3.8/site-packages/monai/utils/deprecate_utils.py:107: FutureWarning: <class 'monai.transforms.utility.array.AddChannel'>: Class `AddChannel` has been deprecated since version 0.8. please use MetaTensor data type and monai.transforms.EnsureChannelFirst instead.\n", "  warn_deprecated(obj, msg, warning_category)\n"]}], "source": ["train_transform = transforms.Compose(\n", "    [\n", "        transforms.LoadImaged(keys=[\"image\"]),\n", "        transforms.Lambdad(keys=\"image\", func=lambda x: x[:, :, :, 1]),\n", "        transforms.EnsureChannelFirstd(keys=[\"image\"], channel_dim=\"no_channel\"),\n", "        transforms.ScaleIntensityd(keys=[\"image\"]),\n", "        transforms.CenterSpatialCropd(keys=[\"image\"], roi_size=[176, 224, 155]),\n", "        transforms.Resized(keys=[\"image\"], spatial_size=(32, 48, 32)),\n", "    ]\n", ")\n", "\n", "val_transform = transforms.Compose(\n", "    [\n", "        transforms.LoadImaged(keys=[\"image\"]),\n", "        transforms.Lambdad(keys=\"image\", func=lambda x: x[:, :, :, 1]),\n", "        transforms.EnsureChannelFirstd(keys=[\"image\"], channel_dim=\"no_channel\"),\n", "        transforms.ScaleIntensityd(keys=[\"image\"]),\n", "        transforms.CenterSpatialCropd(keys=[\"image\"], roi_size=[176, 224, 155]),\n", "        transforms.Resized(keys=[\"image\"], spatial_size=(32, 48, 32)),\n", "    ]\n", ")"]}, {"cell_type": "code", "execution_count": 5, "id": "399ab576", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Task01_BrainTumour.tar: 7.09GB [07:59, 15.9MB/s]                                                                                                                                                                                "]}, {"name": "stdout", "output_type": "stream", "text": ["2022-11-30 00:27:21,262 - INFO - Downloaded: /tmp/tmpc398pj0s/Task01_BrainTumour.tar\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["2022-11-30 00:27:29,463 - INFO - Verified 'Task01_BrainTumour.tar', md5: 240a19d752f0d9e9101544901065d872.\n", "2022-11-30 00:27:29,464 - INFO - Writing into directory: /tmp/tmpc398pj0s.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Loading dataset: 100%|████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 388/388 [03:31<00:00,  1.84it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["2022-11-30 00:31:12,636 - INFO - Verified 'Task01_BrainTumour.tar', md5: 240a19d752f0d9e9101544901065d872.\n", "2022-11-30 00:31:12,636 - INFO - File exists: /tmp/tmpc398pj0s/Task01_BrainTumour.tar, skipped downloading.\n", "2022-11-30 00:31:12,637 - INFO - Non-empty folder exists in /tmp/tmpc398pj0s/Task01_BrainTumour, skipped extracting.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Loading dataset: 100%|██████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 96/96 [00:52<00:00,  1.82it/s]\n"]}], "source": ["train_ds = DecathlonDataset(\n", "    root_dir=root_dir, task=\"Task01_BrainTumour\", transform=train_transform, section=\"training\", download=True\n", ")\n", "\n", "train_loader = DataLoader(train_ds, batch_size=16, shuffle=True, num_workers=8)\n", "\n", "val_ds = DecathlonDataset(\n", "    root_dir=root_dir, task=\"Task01_BrainTumour\", transform=val_transform, section=\"validation\", download=True\n", ")\n", "\n", "val_loader = DataLoader(val_ds, batch_size=16, shuffle=False, num_workers=8, persistent_workers=True)"]}, {"cell_type": "markdown", "id": "7c896e0a", "metadata": {}, "source": ["### Visualize the training images"]}, {"cell_type": "code", "execution_count": 6, "id": "5a32be9f", "metadata": {}, "outputs": [{"data": {"image/png": "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****************************************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\n", "text/plain": ["<Figure size 1000x600 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.subplots(1, 4, figsize=(10, 6))\n", "for i in range(4):\n", "    plt.subplot(1, 4, i + 1)\n", "    plt.imshow(train_ds[i * 20][\"image\"][0, :, :, 15].detach().cpu(), vmin=0, vmax=1, cmap=\"gray\")\n", "    plt.axis(\"off\")\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "id": "5ae4f2c7", "metadata": {}, "source": ["### Define network, optimizer and losses"]}, {"cell_type": "code", "execution_count": 8, "id": "b28d46a4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Using cuda\n"]}, {"data": {"text/plain": ["VQVAE(\n", "  (encoder): Sequential(\n", "    (0): Convolution(\n", "      (conv): Conv3d(1, 256, kernel_size=(4, 4, 4), stride=(2, 2, 2), padding=(1, 1, 1))\n", "      (adn): ADN(\n", "        (A): ReLU()\n", "      )\n", "    )\n", "    (1): VQVAEResidualUnit(\n", "      (conv1): Convolution(\n", "        (conv): Conv3d(256, 256, kernel_size=(3, 3, 3), stride=(1, 1, 1), padding=(1, 1, 1))\n", "        (adn): ADN(\n", "          (D): Dropout(p=0.1, inplace=False)\n", "          (A): ReLU()\n", "        )\n", "      )\n", "      (conv2): Convolution(\n", "        (conv): Conv3d(256, 256, kernel_size=(3, 3, 3), stride=(1, 1, 1), padding=(1, 1, 1))\n", "      )\n", "    )\n", "    (2): VQVAEResidualUnit(\n", "      (conv1): Convolution(\n", "        (conv): Conv3d(256, 256, kernel_size=(3, 3, 3), stride=(1, 1, 1), padding=(1, 1, 1))\n", "        (adn): ADN(\n", "          (D): Dropout(p=0.1, inplace=False)\n", "          (A): ReLU()\n", "        )\n", "      )\n", "      (conv2): Convolution(\n", "        (conv): Conv3d(256, 256, kernel_size=(3, 3, 3), stride=(1, 1, 1), padding=(1, 1, 1))\n", "      )\n", "    )\n", "    (3): Convolution(\n", "      (conv): Conv3d(256, 256, kernel_size=(4, 4, 4), stride=(2, 2, 2), padding=(1, 1, 1))\n", "      (adn): ADN(\n", "        (D): Dropout(p=0.1, inplace=False)\n", "        (A): ReLU()\n", "      )\n", "    )\n", "    (4): VQVAEResidualUnit(\n", "      (conv1): Convolution(\n", "        (conv): Conv3d(256, 256, kernel_size=(3, 3, 3), stride=(1, 1, 1), padding=(1, 1, 1))\n", "        (adn): ADN(\n", "          (D): Dropout(p=0.1, inplace=False)\n", "          (A): ReLU()\n", "        )\n", "      )\n", "      (conv2): Convolution(\n", "        (conv): Conv3d(256, 256, kernel_size=(3, 3, 3), stride=(1, 1, 1), padding=(1, 1, 1))\n", "      )\n", "    )\n", "    (5): VQVAEResidualUnit(\n", "      (conv1): Convolution(\n", "        (conv): Conv3d(256, 256, kernel_size=(3, 3, 3), stride=(1, 1, 1), padding=(1, 1, 1))\n", "        (adn): ADN(\n", "          (D): Dropout(p=0.1, inplace=False)\n", "          (A): ReLU()\n", "        )\n", "      )\n", "      (conv2): Convolution(\n", "        (conv): Conv3d(256, 256, kernel_size=(3, 3, 3), stride=(1, 1, 1), padding=(1, 1, 1))\n", "      )\n", "    )\n", "    (6): Convolution(\n", "      (conv): Conv3d(256, 32, kernel_size=(3, 3, 3), stride=(1, 1, 1), padding=(1, 1, 1))\n", "    )\n", "  )\n", "  (quantizer): VectorQuantizer(\n", "    (quantizer): EMAQuantizer(\n", "      (embedding): Embedding(256, 32)\n", "    )\n", "  )\n", "  (decoder): Sequential(\n", "    (0): Convolution(\n", "      (conv): Conv3d(32, 256, kernel_size=(3, 3, 3), stride=(1, 1, 1), padding=(1, 1, 1))\n", "    )\n", "    (1): VQVAEResidualUnit(\n", "      (conv1): Convolution(\n", "        (conv): Conv3d(256, 256, kernel_size=(3, 3, 3), stride=(1, 1, 1), padding=(1, 1, 1))\n", "        (adn): ADN(\n", "          (D): Dropout(p=0.1, inplace=False)\n", "          (A): ReLU()\n", "        )\n", "      )\n", "      (conv2): Convolution(\n", "        (conv): Conv3d(256, 256, kernel_size=(3, 3, 3), stride=(1, 1, 1), padding=(1, 1, 1))\n", "      )\n", "    )\n", "    (2): VQVAEResidualUnit(\n", "      (conv1): Convolution(\n", "        (conv): Conv3d(256, 256, kernel_size=(3, 3, 3), stride=(1, 1, 1), padding=(1, 1, 1))\n", "        (adn): ADN(\n", "          (D): Dropout(p=0.1, inplace=False)\n", "          (A): ReLU()\n", "        )\n", "      )\n", "      (conv2): Convolution(\n", "        (conv): Conv3d(256, 256, kernel_size=(3, 3, 3), stride=(1, 1, 1), padding=(1, 1, 1))\n", "      )\n", "    )\n", "    (3): Convolution(\n", "      (conv): ConvTranspose3d(256, 256, kernel_size=(4, 4, 4), stride=(2, 2, 2), padding=(1, 1, 1))\n", "      (adn): ADN(\n", "        (D): Dropout(p=0.1, inplace=False)\n", "        (A): ReLU()\n", "      )\n", "    )\n", "    (4): VQVAEResidualUnit(\n", "      (conv1): Convolution(\n", "        (conv): Conv3d(256, 256, kernel_size=(3, 3, 3), stride=(1, 1, 1), padding=(1, 1, 1))\n", "        (adn): ADN(\n", "          (D): Dropout(p=0.1, inplace=False)\n", "          (A): ReLU()\n", "        )\n", "      )\n", "      (conv2): Convolution(\n", "        (conv): Conv3d(256, 256, kernel_size=(3, 3, 3), stride=(1, 1, 1), padding=(1, 1, 1))\n", "      )\n", "    )\n", "    (5): VQVAEResidualUnit(\n", "      (conv1): Convolution(\n", "        (conv): Conv3d(256, 256, kernel_size=(3, 3, 3), stride=(1, 1, 1), padding=(1, 1, 1))\n", "        (adn): ADN(\n", "          (D): Dropout(p=0.1, inplace=False)\n", "          (A): ReLU()\n", "        )\n", "      )\n", "      (conv2): Convolution(\n", "        (conv): Conv3d(256, 256, kernel_size=(3, 3, 3), stride=(1, 1, 1), padding=(1, 1, 1))\n", "      )\n", "    )\n", "    (6): Convolution(\n", "      (conv): ConvTranspose3d(256, 1, kernel_size=(4, 4, 4), stride=(2, 2, 2), padding=(1, 1, 1))\n", "    )\n", "  )\n", ")"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "print(f\"Using {device}\")\n", "model = VQVAE(\n", "    spatial_dims=3,\n", "    in_channels=1,\n", "    out_channels=1,\n", "    num_channels=(256, 256),\n", "    num_res_channels=256,\n", "    num_res_layers=2,\n", "    downsample_parameters=((2, 4, 1, 1), (2, 4, 1, 1)),\n", "    upsample_parameters=((2, 4, 1, 1, 0), (2, 4, 1, 1, 0)),\n", "    num_embeddings=256,\n", "    embedding_dim=32,\n", ")\n", "model.to(device)"]}, {"cell_type": "code", "execution_count": 9, "id": "dbefd7a9", "metadata": {}, "outputs": [], "source": ["optimizer = torch.optim.Adam(params=model.parameters(), lr=1e-4)\n", "l1_loss = L1Loss()"]}, {"cell_type": "markdown", "id": "8fe3cb3c", "metadata": {}, "source": ["### Model training\n", "Here, we are training our model for 100 epochs (training time: ~60 minutes)."]}, {"cell_type": "code", "execution_count": 10, "id": "7ba11fab", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Epoch 0: 100%|█████████████████| 25/25 [00:35<00:00,  1.44s/it, recons_loss=0.0964, quantization_loss=1.45e-5]\n", "Epoch 1: 100%|█████████████████| 25/25 [00:35<00:00,  1.43s/it, recons_loss=0.0776, quantization_loss=1.04e-5]\n", "Epoch 2: 100%|█████████████████| 25/25 [00:36<00:00,  1.45s/it, recons_loss=0.0441, quantization_loss=8.18e-6]\n", "Epoch 3: 100%|█████████████████| 25/25 [00:36<00:00,  1.46s/it, recons_loss=0.0312, quantization_loss=3.03e-5]\n", "Epoch 4: 100%|█████████████████| 25/25 [00:36<00:00,  1.47s/it, recons_loss=0.0239, quantization_loss=1.14e-5]\n", "Epoch 5: 100%|█████████████████| 25/25 [00:37<00:00,  1.48s/it, recons_loss=0.0213, quantization_loss=1.51e-5]\n", "Epoch 6: 100%|█████████████████| 25/25 [00:37<00:00,  1.48s/it, recons_loss=0.0194, quantization_loss=9.73e-6]\n", "Epoch 7: 100%|██████████████████| 25/25 [00:37<00:00,  1.49s/it, recons_loss=0.018, quantization_loss=1.46e-5]\n", "Epoch 8: 100%|█████████████████| 25/25 [00:37<00:00,  1.49s/it, recons_loss=0.0167, quantization_loss=9.45e-6]\n", "Epoch 9: 100%|██████████████████| 25/25 [00:37<00:00,  1.49s/it, recons_loss=0.0156, quantization_loss=1.3e-5]\n", "Epoch 10: 100%|████████████████| 25/25 [00:37<00:00,  1.50s/it, recons_loss=0.0156, quantization_loss=7.13e-6]\n", "Epoch 11: 100%|████████████████| 25/25 [00:37<00:00,  1.50s/it, recons_loss=0.0146, quantization_loss=7.13e-6]\n", "Epoch 12: 100%|████████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.0139, quantization_loss=1.28e-5]\n", "Epoch 13: 100%|████████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.0142, quantization_loss=8.03e-6]\n", "Epoch 14: 100%|█████████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.0135, quantization_loss=8.1e-6]\n", "Epoch 15: 100%|████████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.0135, quantization_loss=7.39e-6]\n", "Epoch 16: 100%|████████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.0143, quantization_loss=1.17e-5]\n", "Epoch 17: 100%|████████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.0128, quantization_loss=6.57e-6]\n", "Epoch 18: 100%|████████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.0125, quantization_loss=8.15e-6]\n", "Epoch 19: 100%|█████████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.0127, quantization_loss=8.6e-6]\n", "Epoch 20: 100%|████████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.0121, quantization_loss=7.32e-6]\n", "Epoch 21: 100%|████████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.0123, quantization_loss=5.92e-6]\n", "Epoch 22: 100%|█████████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.012, quantization_loss=4.29e-6]\n", "Epoch 23: 100%|████████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.0119, quantization_loss=3.72e-6]\n", "Epoch 24: 100%|████████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.0119, quantization_loss=9.14e-6]\n", "Epoch 25: 100%|████████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.0115, quantization_loss=3.31e-6]\n", "Epoch 26: 100%|████████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.0118, quantization_loss=5.89e-6]\n", "Epoch 27: 100%|████████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.0112, quantization_loss=9.95e-6]\n", "Epoch 28: 100%|████████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.0111, quantization_loss=6.78e-6]\n", "Epoch 29: 100%|████████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.0108, quantization_loss=3.85e-6]\n", "Epoch 30: 100%|█████████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.0121, quantization_loss=5.7e-6]\n", "Epoch 31: 100%|████████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.0112, quantization_loss=7.31e-6]\n", "Epoch 32: 100%|████████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.0108, quantization_loss=4.53e-6]\n", "Epoch 33: 100%|████████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.0107, quantization_loss=5.36e-6]\n", "Epoch 34: 100%|████████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.0106, quantization_loss=6.23e-6]\n", "Epoch 35: 100%|████████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.0107, quantization_loss=2.98e-6]\n", "Epoch 36: 100%|████████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.0103, quantization_loss=4.57e-6]\n", "Epoch 37: 100%|████████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.0102, quantization_loss=3.09e-6]\n", "Epoch 38: 100%|████████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.0106, quantization_loss=3.28e-6]\n", "Epoch 39: 100%|████████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.0103, quantization_loss=2.81e-6]\n", "Epoch 40: 100%|███████████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.01, quantization_loss=6.5e-6]\n", "Epoch 41: 100%|████████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.0099, quantization_loss=2.46e-6]\n", "Epoch 42: 100%|████████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.0101, quantization_loss=3.62e-6]\n", "Epoch 43: 100%|████████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.0107, quantization_loss=6.03e-6]\n", "Epoch 44: 100%|████████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.0108, quantization_loss=2.58e-6]\n", "Epoch 45: 100%|████████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.0105, quantization_loss=4.09e-6]\n", "Epoch 46: 100%|███████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.00974, quantization_loss=4.14e-6]\n", "Epoch 47: 100%|███████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.00961, quantization_loss=3.92e-6]\n", "Epoch 48: 100%|███████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.00958, quantization_loss=6.57e-6]\n", "Epoch 49: 100%|███████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.00974, quantization_loss=5.11e-6]\n", "Epoch 50: 100%|████████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.0098, quantization_loss=2.66e-6]\n", "Epoch 51: 100%|███████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.00948, quantization_loss=4.26e-6]\n", "Epoch 52: 100%|███████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.00932, quantization_loss=2.78e-6]\n", "Epoch 53: 100%|███████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.00915, quantization_loss=3.76e-6]\n", "Epoch 54: 100%|███████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.00942, quantization_loss=2.41e-6]\n", "Epoch 55: 100%|███████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.00929, quantization_loss=2.18e-6]\n", "Epoch 56: 100%|███████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.00981, quantization_loss=3.42e-6]\n", "Epoch 57: 100%|████████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.0103, quantization_loss=2.66e-6]\n", "Epoch 58: 100%|███████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.00952, quantization_loss=2.19e-6]\n", "Epoch 59: 100%|███████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.00972, quantization_loss=5.11e-6]\n", "Epoch 60: 100%|███████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.00919, quantization_loss=3.34e-6]\n", "Epoch 61: 100%|████████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.00894, quantization_loss=4.7e-6]\n", "Epoch 62: 100%|███████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.00897, quantization_loss=2.94e-6]\n", "Epoch 63: 100%|███████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.00898, quantization_loss=2.08e-6]\n", "Epoch 64: 100%|███████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.00895, quantization_loss=6.23e-6]\n", "Epoch 65: 100%|███████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.00874, quantization_loss=3.02e-6]\n", "Epoch 66: 100%|█████████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.0086, quantization_loss=1.7e-6]\n", "Epoch 67: 100%|███████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.00869, quantization_loss=5.49e-6]\n", "Epoch 68: 100%|███████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.00863, quantization_loss=2.99e-6]\n", "Epoch 69: 100%|███████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.00888, quantization_loss=5.46e-6]\n", "Epoch 70: 100%|███████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.00874, quantization_loss=6.97e-6]\n", "Epoch 71: 100%|████████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.00852, quantization_loss=4.5e-6]\n", "Epoch 72: 100%|███████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.00837, quantization_loss=3.29e-6]\n", "Epoch 73: 100%|███████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.00836, quantization_loss=3.99e-6]\n", "Epoch 74: 100%|███████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.00851, quantization_loss=4.51e-6]\n", "Epoch 75: 100%|███████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.00852, quantization_loss=2.96e-6]\n", "Epoch 76: 100%|███████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.00838, quantization_loss=3.18e-6]\n", "Epoch 77: 100%|████████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.00892, quantization_loss=3.5e-6]\n", "Epoch 78: 100%|███████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.00853, quantization_loss=5.36e-6]\n", "Epoch 79: 100%|███████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.00818, quantization_loss=1.95e-6]\n", "Epoch 80: 100%|███████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.00829, quantization_loss=3.55e-6]\n", "Epoch 81: 100%|███████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.00874, quantization_loss=4.41e-6]\n", "Epoch 82: 100%|███████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.00853, quantization_loss=3.41e-6]\n", "Epoch 83: 100%|███████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.00816, quantization_loss=4.04e-6]\n", "Epoch 84: 100%|███████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.00837, quantization_loss=3.13e-6]\n", "Epoch 85: 100%|████████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.00913, quantization_loss=2.3e-6]\n", "Epoch 86: 100%|███████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.00876, quantization_loss=3.61e-6]\n", "Epoch 87: 100%|███████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.00825, quantization_loss=3.02e-6]\n", "Epoch 88: 100%|███████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.00823, quantization_loss=3.47e-6]\n", "Epoch 89: 100%|████████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.00834, quantization_loss=3.9e-6]\n", "Epoch 90: 100%|███████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.00843, quantization_loss=2.41e-6]\n", "Epoch 91: 100%|███████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.00912, quantization_loss=4.24e-6]\n", "Epoch 92: 100%|███████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.00994, quantization_loss=2.73e-6]\n", "Epoch 93: 100%|████████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.00875, quantization_loss=3.5e-6]\n", "Epoch 94: 100%|████████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.00875, quantization_loss=2.9e-6]\n", "Epoch 95: 100%|███████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.00835, quantization_loss=3.81e-6]\n", "Epoch 96: 100%|███████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.00813, quantization_loss=2.94e-6]\n", "Epoch 97: 100%|███████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.00793, quantization_loss=3.69e-6]\n", "Epoch 98: 100%|███████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.00791, quantization_loss=4.25e-6]\n", "Epoch 99: 100%|███████████████| 25/25 [00:37<00:00,  1.51s/it, recons_loss=0.00768, quantization_loss=1.91e-6]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["train completed, total time: 3827.494425058365.\n"]}], "source": ["n_epochs = 100\n", "val_interval = 10\n", "epoch_recon_loss_list = []\n", "epoch_quant_loss_list = []\n", "val_recon_epoch_loss_list = []\n", "intermediary_images = []\n", "n_example_images = 4\n", "\n", "total_start = time.time()\n", "for epoch in range(n_epochs):\n", "    model.train()\n", "    epoch_loss = 0\n", "    progress_bar = tqdm(enumerate(train_loader), total=len(train_loader), ncols=110)\n", "    progress_bar.set_description(f\"Epoch {epoch}\")\n", "    for step, batch in progress_bar:\n", "        images = batch[\"image\"].to(device)\n", "        optimizer.zero_grad(set_to_none=True)\n", "\n", "        # model outputs reconstruction and the quantization error\n", "        reconstruction, quantization_loss = model(images=images)\n", "\n", "        recons_loss = l1_loss(reconstruction.float(), images.float())\n", "\n", "        loss = recons_loss + quantization_loss\n", "\n", "        loss.backward()\n", "        optimizer.step()\n", "\n", "        epoch_loss += recons_loss.item()\n", "\n", "        progress_bar.set_postfix(\n", "            {\"recons_loss\": epoch_loss / (step + 1), \"quantization_loss\": quantization_loss.item() / (step + 1)}\n", "        )\n", "    epoch_recon_loss_list.append(epoch_loss / (step + 1))\n", "    epoch_quant_loss_list.append(quantization_loss.item() / (step + 1))\n", "\n", "    if (epoch + 1) % val_interval == 0:\n", "        model.eval()\n", "        val_loss = 0\n", "        with torch.no_grad():\n", "            for val_step, batch in enumerate(val_loader, start=1):\n", "                images = batch[\"image\"].to(device)\n", "\n", "                reconstruction, quantization_loss = model(images=images)\n", "\n", "                # get the first sample from the first validation batch for\n", "                # visualizing how the training evolves\n", "                if val_step == 1:\n", "                    intermediary_images.append(reconstruction[:n_example_images, 0])\n", "\n", "                recons_loss = l1_loss(reconstruction.float(), images.float())\n", "\n", "                val_loss += recons_loss.item()\n", "\n", "        val_loss /= val_step\n", "        val_recon_epoch_loss_list.append(val_loss)\n", "\n", "total_time = time.time() - total_start\n", "print(f\"train completed, total time: {total_time}.\")"]}, {"cell_type": "markdown", "id": "5fc01098", "metadata": {}, "source": ["### Learning curves"]}, {"cell_type": "code", "execution_count": 11, "id": "ac5f7c56", "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAkwAAAHZCAYAAAB0J2SuAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjYuMCwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy89olMNAAAACXBIWXMAAA9hAAAPYQGoP6dpAAB2BElEQVR4nO3dd3hUVeI+8PdOSya9FwJJIIQSqogUQVSIiyBBEdAoItZVV5Z1xf2h7CpYvjYsq4uoqIu6WIIgShFBkCogIk0IJQkJoSSQ3ieZydzfH5e5zDCTTJKZTAnv53nyMHPrmZMQXs459xxBFEURRERERNQkhbsLQEREROTpGJiIiIiI7GBgIiIiIrKDgYmIiIjIDgYmIiIiIjsYmIiIiIjsYGAiIiIisoOBiYiIiMgOBiYiIiIiOxiYiMij3XDDDRAEATfccIO7i0JEVzAGJiIX2bJlCwRBgCAImD9/vruLQx7i6NGjeP755zFq1Ch06dIFWq0WAQEBSEhIQFpaGt566y0UFha6u5hEVzyVuwtARHQlKi0txRNPPIEvvvgCRqPRan9NTQ3y8/OxZs0azJkzBw899BD+7//+D2FhYW4oLRExMBGRR9uyZYu7i+B0J0+exLhx43DixAkAQFRUFO6++26MGjUKsbGxEAQB586dw5YtW7BixQqcPXsWH3zwAcaOHYvbbrvNvYUnukIxMBERuVBtbS3S0tLksPTggw/i7bffRmBgoNWxkyZNwoIFC/DBBx/gn//8p6uLSkRmGJiIiFzomWeeQWZmJgDgoYcewkcffdTs8RqNBrNmzcKYMWOg0+lcUUQisoGDvom8zObNmzFjxgx069YNfn5+CAoKQr9+/fCPf/wD586da/bcw4cP46WXXsLYsWPRuXNn+Pj4ICAgAMnJyZgxYwZ2797d7Pnz58+XB64DQEVFBV588UVcddVVCAkJgSAI+PTTT20eq9PpsGDBAgwaNAiBgYEIDAzEkCFDsHDhQhgMhibv2dxTcnl5efI9TPf96aefkJaWhpiYGPj4+KBr16547LHHcObMmWY/GwCUlJTg//2//4eePXtCq9UiOjoaN910E1auXAkA+PTTT+X75eXl2b3e5YqKiuSAFBsbi3//+98tPrdPnz64+uqrLba19AnCy78Xl7v8YYSff/4ZU6dORZcuXaBWq5GYmIja2loEBgZCEARMmzbNbnl37dolX3fRokU2jyksLMQ///lPDB48GGFhYfDx8UGXLl1wxx13YOPGjc1ev7GxEZ9++inGjh2LmJgYaDQaBAcHIzk5GWPGjMHLL78sB1MipxCJyCU2b94sAhABiPPmzWv1+XV1dWJ6erp8DVtf/v7+4qpVq+zev7mvp59+uskyzJs3Tz7uxIkTYmJiotX5S5YssTq2sLBQHDhwYJP3TEtLExsbG23e8/rrrxcBiNdff73VvtzcXIv7Pv30003eIzIyUszMzGzysx06dEiMjo5u8vw///nP4pIlS+T3ubm5TV6rKe+++658/rPPPtvq8y/XXN2YM/9e2GL+czl37lyrz56QkCCKoijec8898s9ZdXV1s/d8/PHHRQCiSqUSi4qKrPYvXbpU9Pf3b/Zn8cEHHxT1er3VuVVVVeJ1111n92d58uTJzZaRqDXYJUfkBURRxJQpU7B27VoAQFpaGu644w5069YNCoUCe/bswZtvvon8/HxMmTIFv/zyCwYPHmxxDYPBAH9/f9xyyy0YPXo0evXqhaCgIFy4cAFHjhzBu+++i1OnTuHVV19Fjx49cP/99zdbpilTpuDs2bP461//iokTJyI0NBRZWVlISEiwOvb2229HZmYmZs2ahbS0NISFheH48eN48cUXcfToUaxevRofffQRHnnkkTbX0UcffYSdO3fi+uuvxyOPPIIePXqgvLwcn3/+OT7//HMUFRXhgQcewK5du6zOLS8vx80334zz588DAKZPn467774bkZGRyM7OxjvvvIPFixfj4MGDbS4fAGzdulV+fcsttzh0rfbw7bff4o8//kC/fv3w97//HX379kVdXR0OHDgAAJg2bRqWLl2KmpoafP/997j77rttXsdgMOCbb74BAIwdOxYREREW+5ctW4bp06dDFEV069YNM2fOREpKCiIjI5GXl4dPPvkEP/zwAz755BMEBQXhrbfesjh//vz52L59OwBgwoQJmDZtGuLj4+Hr64sLFy5g//79WLNmTZMtakRt4u7ERnSlcKSFafHixSIAUa1Wi+vWrbN5TGlpqdinTx8RgDhixAir/UVFRWJZWVmT96ivrxdvuukmuUXBYDBYHWPeUqFQKMT169c3eT3zY9Vqtbh582arY0pKSuRWnf79+9u8TktbmACIDz/8sGg0Gq2Oe+ihh+Rj9u3bZ7X/iSeekPf/+9//ttpvMBjEW2+91eJebWlh6t69u1x3Op2u1edfztktTADEMWPGNFk2vV4vRkVFiQDEW265pcn7rVu3Tr7el19+abGvqKhIDA4OFgGIDzzwgM0WJFEU5ZYuhUIhHjt2zGJfly5dRADilClTmvvYYklJSbP7iVqDY5iIPJwoinjttdcAALNmzcLNN99s87jQ0FAsWLAAAPDLL78gKyvLYn9ERARCQkKavI9Go5HPP3XqlNyq0JT77rsPf/rTn1r0Gf7617/aHGcTFhYmt2T98ccfqKioaNH1bImNjcV//vMfm60KTz31lPza1DJhUl9fL49/uuaaa/C3v/3N6nylUokPP/wQvr6+bS4fII2RAoDg4GD4+Pg4dK32oFAo8PHHHzdZNpVKhTvvvBMAsGHDBvnzXO6LL74AAAQEBODWW2+12Pf++++joqICcXFxWLRoEVQq2x0dzz//POLi4mA0GvH5559b7DNN5Hndddc1+3k4ZxU5EwMTkYfLzMxETk4OAKkbrDmjRo2SX9vqejJXX1+P/Px8ZGZm4vDhwzh8+DBEUZT32+t+asnA35YcaxrILIoicnNzW3zNy02ZMqXJf+h79uyJgIAAANIcSOb27t2L8vJyAMA999zT5PWjo6MxduzYNpcPAKqqqgAA/v7+Dl2nvYwYMQKJiYnNHmP6Xur1eixbtsxqf11dHb777jsAwG233QY/Pz+L/atWrQIgdaU1FxpVKhWGDx8OwPpnOTY2FgCQkZGB2traZstL5CwMTEQebu/evfLr4cOHy08e2foyhQIANpfTqKmpwSuvvIIBAwbA398fCQkJ6NOnD/r164d+/frhqquuko8tLi5utlz9+/dv8Wfo1atXk/vMWwFMgaItmrsHILXA2brH4cOH5deXP4V2ucvHhbWWaa6lmpoah67TXlryPR06dCiSkpIAXGpJMrdq1SpUV1cDsA7KjY2Ncsvlhx9+2OzPsiAIWL58OQDrn+UZM2YAAHbu3ImuXbti5syZWLlyJYqKilr3gYlagYGJyMNduHChTedd/j/vvLw89OvXD3PnzsWhQ4fQ2NjY7Pl1dXXN7jcFkJa4vJXBnEJx6deQvTK19R7m97n8HmVlZfLryMjIZq9hb7894eHhAKTpGOrr6x26Vnto6ffUFIR27txpNb2CKURFRUUhNTXVYl9paWmzU0g05fKf5WeffRYPPPAABEHAhQsX8N577+H2229HVFQU+vbti3nz5skD+ImchU/JEXk483/gV69ebbfLxCQqKsri/fTp05GbmwtBEHD//fcjPT0dvXv3RmRkJDQaDQRBgNFohFKpBACL7jlbTMdRyw0YMADZ2dkwGo04cOAAhg4d6u4iWWjp93TatGl44YUXIIoivvrqKzzzzDMApEC0fv16AMCdd95pNT7J/Gf5oYcesjlezBaNRmPxXq1W45NPPsHs2bPx1Vdf4eeff8bevXvR0NCAI0eO4MiRI3jrrbewdOlSqzFURG3FwETk4UytEgAQEhKCvn37tvoax44dw44dOwAAc+fOxUsvvWTzuNLS0rYV0ouZt6oUFRWhR48eTR7raJfP9ddfjxUrVgAA1q5d63BgMrWa2Vq815yzuwB79OiBwYMHY+/evfjyyy/lwLR8+XI0NDQAsD1uzbz7VRTFNv0sm0tJScGLL76IF198ETqdDjt27MCXX36Jzz//HNXV1bjrrruQk5Mjj3kicgS75Ig8nPm4ol9++aVN1zhy5Ij82vSUky3m46WuFH369JFf//77780e62j9pKenQ6vVAgA+/vhjh4OMaUyUebeiLaZ165zJFIgOHz6MQ4cOAbjUHZeUlGQzDGo0Grm+2/qz3BRfX1+kpqbiv//9r/y0Z11dHdasWePU+9CVi4GJyMMNGjQInTt3BgAsXry4TeuJmY8bae4f6Q8++KD1BfRygwcPRnBwMABg6dKlTR53/vx5ubuprSIjI/Hwww8DAAoKCvDEE0+0+NzMzEyrQNe1a1cAUiBqasB8cXExfvrpp7YVuBnp6elyF94XX3yBM2fOyFM2NPdU5MSJEwFIrZ6O1mdTxowZI7+29/ACUUsxMBF5OIVCgblz5wKQHom/9957mx0wXFlZiYULF1psS05Oll+b5hy63Pvvv4/vv//e8QJ7GV9fX9x7770AgN9++w3vvPOO1TFGoxGPPPKIUxa/ffnll9G7d28AUivTww8/LD9VZoter8fChQsxdOhQnD592mLf9ddfDwBoaGjAf/7zH5vnPvTQQ3YH8LdFTEwMRo8eDQD46quv8OWXX8rj3poLTH/729/kpznvv/9+i9ZPW9auXSu3YAFSt/Hq1aubHWO3YcMG+bUpVBI5imOYiNzgwIEDTQYXc6NHj0Z8fDweffRR/PTTT1i5ciW++eYb7Nu3D4888giGDBmC4OBgVFZW4tixY9iyZQtWrVoFX19fzJw5U77OVVddhb59++Lw4cP48MMPUVZWhunTpyM2NhZnzpzB0qVLsXz5cowYMcLpXSXeYP78+fjmm29QWFiIJ554Ar///jumTZtmsTTKzp07MWTIEOzZswcA2rzshr+/P9asWYNx48bhxIkT+Pjjj7Fq1SpMmzYN119/PWJjYyGKIgoKCrBt2zasWLEC+fn5Nq91yy23ICEhAadOncKzzz6L4uJi3H777fD19ZWXu9m/fz+GDRtmd2Hltpg2bRp++uknnD59Gq+88goAqcWuuXFg0dHR+OyzzzBlyhQUFBRg8ODBuO+++zBu3Dh07twZer0eZ86cwZ49e7B8+XKcPHkSq1evlqc8qKysxMSJE5GYmIjbb78dQ4cORUJCAlQqFQoKCrB69Wp8/PHHAIC4uDhMmDDB6Z+brlBum2Oc6ArT0sVvzb9Wrlwpn9/Q0CA+9thjoiAIds/r2rWr1f33798vhoaGNnlOv379xHPnzjW7fIu9JTbacqx5vdhaPqU1i+82JyEhQQQgzpgxw+b+AwcOiJGRkU3Wz3333Sd+8skn8vvCwsJm72dPSUmJOH36dFGhUNj9fqrVanHWrFlieXm51XW2b9/e5CK2SqVSfOedd1q1+G5rVFZWilqt1uKeb7/9dovOXbVqlRgWFmb3sysUCvHnn3+Wz7t8OZymvmJjY8W9e/e26vMQNYddckReQq1WY9GiRTh48CD++te/ol+/fggODoZSqURwcDAGDhyIBx98EMuXL8fRo0etzh84cCAOHDiARx99FAkJCVCr1QgLC8OQIUPwxhtvYM+ePVf000QDBgxAZmYmZs+ejeTkZPj4+CAiIgI33ngjvvzySyxZsgSVlZXy8aZxT20VFhaGzz//HIcPH8a8efMwcuRIxMXFwcfHB35+foiPj0daWhrefvttnDlzBu+8847Ne44cORK///47pk+fjk6dOkGtViM2NhaTJ0/Gtm3bMGvWLIfK2ZzAwECkpaXJ75VKJdLT01t0blpaGnJzc/HGG29g9OjRiI6OhlqthlarRdeuXTFhwgS89dZbyMvLw4033iifl5CQgD179mD+/Pn405/+hJ49eyIkJAQqlQoREREYNWoUFixYgGPHjtmdiJSoNQRRtDPZChERAZDmDvrkk0/QuXNnq/FERNSxsYWJiKgF6urq5EHxw4YNc3NpiMjVGJiIiADk5OQ0+eRVY2MjHnvsMfkRddNaZkR05WCXHBERgPvuuw979uxBeno6hg4diqioKNTV1eHQoUP46KOPsG/fPgBAamoqNmzY0Oan5IjIO3FaASKii44ePYp58+Y1uX/EiBH4+uuvGZaIrkAe28Kk1+uRkZGB7du3o7q6GgkJCUhPT5fn4mjKuXPnsGHDBmRnZyM3N1ee9O3yhUhN9u7di2+++QZnzpxBUFAQbrzxRkyePJkLixJdYY4fP44VK1Zg48aNyMvLQ1FREfR6PcLDwzF48GDceeedSE9Pl9dvI6Iri8cGpn//+9/49ddfMX78eMTGxmLLli3IycnBvHnz0KtXrybP27JlC95//3107twZSqUSeXl5TQam/fv349VXX0VKSgpGjBiB/Px8rF+/HqmpqfLyBUREREQe2SWXnZ2NnTt34p577pHXHRo1ahRmz56NpUuXNrnSOiDNMvvpp59Cq9Vi1apVyMvLa/LY//3vf4iPj8e//vUvuUXJz88PK1euxPjx4xEXF+fUz0VERETeySPblnfv3g2FQoHU1FR5m0ajwejRo3HixIlmF1MMCAiQVwNvzpkzZ3DmzBmkpqZadL/96U9/giiK7bKMABEREXknj2xhys3NRWxsLPz8/Cy2d+/eHQCQl5eHiIgIh+8BAElJSRbbw8LCEB4eLu9vjbKyMotV4dsiMjISRUVFDl2DWoZ17Tqsa9dhXbsW69t12qOuVSoVQkNDW3asU+/sJOXl5TY/gGlbWVmZw/cwXSMkJMRqX0hISLP30Ov10Ov18ntBEKDVamEwGBwKTKYnbxobG5tdiZscx7p2Hda167CuXYv17TqeUNceGZgaGhqgVquttpu2NTQ0OOUe5tc0p9FoUFtb2+S5K1euxPLly+X3Xbt2xWuvvYbIyEiHywUAMTExTrkO2ce6dh3Wteuwrl2L9e067qxrjwxMGo3GogXHxLRNo9E45R7m1zTX0NDQ7D0mTZqECRMmyO9NybeoqMjhFqaYmBgUFhbyfyvtjHXtOqxr12Fduxbr23Xaq65VKlWLGzs8MjCFhISgtLTUarupm6yl/Y3NMV2jvLzcajxUeXm51dgmc2q12mbLFACnfCNFUeRfPhdhXbsO69p1WNeuxfp2HXfWtUc+JZeYmIiCggKrbrGsrCx5vzPuAUjrR5krLS1FSUmJU+5BREREHYNHBqZhw4bBaDRi48aN8ja9Xo8tW7YgOTlZbhEqLi7G2bNn23SPLl26IC4uDhs3boTRaJS3m9aI4mrkREREZOKRXXLJyckYNmwYvvrqK1RWViImJgZbt25FUVERHn30Ufm4hQsXIjMzE8uWLZO31dbWYt26dQCkpQ4A4Mcff4S/vz/8/f1x8803y8fec889eP311/HSSy/h2muvxenTp/Hjjz9i9OjR6Ny5s4s+LREREXk6jwxMADBz5kxkZGRg27ZtqKmpQXx8PObMmYOUlJRmz6uurkZGRobFtjVr1gCQ5nAwD0xXX301Zs+ejeXLl2PJkiUICgrCpEmTMGXKFOd/ICIiIvJaHruWnDcyLdbZVoIgIDY2FgUFBRxA2M5Y167DunYdd9a10WiETqdzePJeb6PRaJwy1Q3Z15q61mq1TT6cZU6tVnv3U3JEROQ9jEYjqqqq4OvrC61WK0+1ciVQq9UO/UeZWq6ldd3Y2Ijq6moEBgZCoXDeUG2PHPRNRETeQ6fTwdfXFz4+PldUWCLPpFQq4efnh7q6Oqdel4GJiIgcYjAYnDKhMJGzqFQqNDY2OvWaDExEROQwtiyRJ2mPn0eOYfJg4oHdEM+fA2qqIdx2DwQn9sUSERFRyzEweTDj5nVA5n4AgHDz7YBfgJtLREREdGVik4UHE/z8L72prXFfQYiIiK5wDEyezCIwVbuvHERE5FHi4uI4ybKLsUvOk5l3wbGFiYjIo8TFxbXq+LaufUqegYHJk7FLjojIYz355JNQKBQWC7h//PHHqKysxJNPPtmu9966dSu0Wm273oMsMTB5MrMWJrG2Gnxol4jIc8yePdtq9ully5ahsrISs2fPbtd7d+/evV2vT9Y4hsmTsYWJiMjrnT59GnFxcXjiiSeQlZWFBx98EH369EFcXBxOnz4NAFi3bh3+8pe/YMSIEUhKSkKvXr0wadIkrF271uY1bY1heuKJJxAXF4f8/Hx88sknGDVqFLp27YohQ4bgrbfesmgJo9ZjC5MHE7T+kJfP5KBvIiKvlpeXh7S0NPTq1Qt33HEHysrK5AViX3nlFWg0GlxzzTWIjo5GSUkJNmzYgD//+c948cUX8cADD7T4Pi+++CJ2796N1NRU3HDDDfjxxx/x5ptvoqGhAU8//XR7fbwOj4HJk/lz0DcRUUfx22+/4e9//zueeuopq33/+9//kJCQYLGtpqYGt956KxYsWIC77rqrxWOWDh8+jI0bNyI6OhqA1PI0cuRILFmyBE8++SSXsWkjBiZPpjXrkqtjYCIi79P40pNARZm7i9G84FAo//VWu98mKioKs2bNsrnv8rAEAP7+/pg6dSpeeOEFHDhwAMOHD2/RfZ544gk5LAFAWFgY/vSnP+Gbb75BTk4Oevfu3bYPcIVjYPJkZi1MYg275IjIC1WUAeUl7i6FR0hJSWmydae4uBgLFy7E5s2bcebMGeh0Oov958+fb/F9+vXrZ7UtNjYWAFBZWdmKEpM5BiZPpvW79JotTETkjYJD3V0C+1xUxoiICJvby8rKMH78eJw9exbXXHMNrrvuOgQFBUGpVOLIkSNYv3496uvrW3yfwMBAq20qlfTPfWNjY9sKTwxMnkxQqQGND9BQD7CFiYi8kCu6uryFINieHObrr7/G2bNn8Y9//ANPPPGExb6FCxdi/fr1Ligd2cNpBTydaS4mtjAREXVIeXl5AICxY8da7fv1119dXBpqCgOTpzPNxcSn5IiIOqTOnTsDAPbs2WOxfeXKlfj555/dUSSygV1yns7UwtRQD9Ggl7rpiIiow5g8eTIWLVqEZ599Fjt37kTnzp2RmZmJHTt2YPz48fjhhx/cXUQCW5g8H2f7JiLq0Dp16oTly5dj5MiR2LFjB5YuXQq9Xo8vv/wSqamp7i4eXSSIoijaP4xaoqioyGJNodYSBAGxsbEoKCiA6dti/OQtiLu3AAAULy6CENPZGUW94tmqa2ofrGvXcVddV1ZWIigoyGX38ySXryVH7ae1dd2Sn0u1Wo3IyMgWXY8tTJ7Oj7N9ExERuRsDk6djlxwREZHbMTB5OrMWJpEL8BIREbkFA5OnYwsTERGR2zEweTiBC/ASERG5HQOTpzNbgJfLoxAREbkHA5OnYwsTERGR2zEweTq2MBEREbkdA5OnM2thEtnCRERE5BYMTJ7OVwsIgvSaT8kRERG5BQOThxMUikutTJyHiYiIyC0YmLyBaS4mtjARERG5BQOTNzDN9l1bzcVLiYiI3ICByRuYWpiMRqBe596yEBGRS2RkZCAuLg4ZGRkW24cOHYqhQ4c6fB1nevPNNxEXF4edO3e22z3cjYHJG3B5FCIij/P4448jKioK3333XbPHVVVVISkpCb1790ZdXZ1rCudkO3fuRFxcHN588013F8VtGJi8gGC2AC8HfhMReYb09HQAwNdff93scd999x10Oh1uvfVWaLVah++bkZHRrq1FbXH//fdj69atuOqqq9xdlHajcncBqAXYwkRE5HFGjhyJ+Ph4/PLLLzh79izi4uJsHmcKN3fddZdT7puYmOiU6zhTWFgYwsLC3F2MdsUWJm/AFiYiIo8jCALuuusuGI3GJlt8jh8/jv3796N3797o2rUr3nvvPUyePBmDBg1CYmIiBg0ahFmzZiEvL6/F921qDFNZWRnmzJmDAQMGICkpCePHj8e6deuavM7XX3+N+++/H0OHDkW3bt3Qp08f3H333fjll18sjnvzzTcxdepUAMBbb72FuLg4+ev06dPyMU2NYdqwYQOmTJmCXr16ISkpCampqfjwww9hMBgsjjt9+jTi4uLwxBNPIDc3Fw8++CBSUlLQvXt33HnnnTh8+HCL66g9MDB5A7MWJpEtTEREHiM9PR0KhQLLli2z+RSzeetSVlYW3njjDfj6+uLmm2/GQw89hP79++O7777DLbfcgjNnzrS5HHV1dZg6dSqWLl2KhIQEPPTQQ0hKSsJjjz2GtWvX2jznn//8J4qKinDdddfh4YcfxpgxY/D7778jPT0d69evl48bPny4HJiGDx+OJ598Uv4KCgpqtlwffvgh7r//fhw9ehS33XYbZsyYAZ1OhxdeeAGPPPKIzTo7c+YM0tLSUF5ejjvvvBOjRo3Cjh07cPvtt6OoqKjNdeQodsl5Ay7AS0TkkeLi4nD99ddj8+bN2LFjB6677jp5n8FgwLfffgsfHx/cfvvtUCqV2LdvH0JDQy2u8csvvyA9PR3vvPMOFixY0KZyLFq0CEePHsW0adPw+uuvy9snT56MadOm2Txn8+bNiI+Pt9h2/vx5jB8/Hi+++CLGjh0LALj22msBAN988w2GDx+O2bNnt6hMeXl5ePnllxEREYEffvhB7rKcM2cO0tPT8eOPP2LFihWYMmWKxXm7du3C3Llz8fjjj8vbXn/9dbzzzjvIyMjAzJkzW3R/Z2Ng8gKCfwDkDM4FeInIizy5Lg/ldQb7B7pRiFaFt8Yltvn89PR0bN68GRkZGRaBaePGjSgqKkJaWppVSDI3YsQI9OzZE9u3b29zGZYvXw6NRoOnnnrKYvsNN9yAkSNHYseOHVbnXB6WACA6Ohrjx4/Hf//7X5w5cwadO3duc5lWrlwJg8GARx55xGJ8l4+PD+bOnYvbbrsNy5YtswpM8fHxeOyxxyy23XXXXXjnnXdw8ODBNpfHUQxM3oAtTETkpcrrDCjx8MDkqLFjxyI8PBzr1q1DZWWl3E1lenrOfLD3zp078fHHH2P//v0oLS21GMej0WjadP+qqirk5+ejR48eiIqKsto/dOhQm4Hp1KlTWLhwIX755RcUFhaivr7eYn9hYaFDgenIkSMApG68yw0ePBi+vr7yMeb69OkDhcJyxFBsbCwAoKKios3lcRQDkzfwNx/0zcBERN4jROv5/8w4Wka1Wo3Jkydj8eLFWLlyJWbMmIELFy5g8+bNiIuLk1udVq9ejcceewz+/v64/vrr0aVLF2i1WgiCgGXLlrV5DFNVVRUAIDw83Ob+iIgIq225ubmYMGECqqqqcO211yI1NRWBgYFQKBTYtWsXdu3ahYaGhjaV5/JyRUZGWu0TBAEREREoLCy02hcQEGC1TaWSvkdGo9GhMjnC83+SyaKFSeRTckTkRRzp6vImd911FxYvXoyvv/4aM2bMwIoVK2AwGHDnnXfKrSVvvfUWfHx8sG7dOnTr1s3i/O+//77N9w4MDAQAlJSU2NxfXFxste2jjz5CeXk53n33XUyePNli35w5c7Br1642l+fychUVFVm1VImiiOLiYpvhyFPxKTlvwHmYiIg8Wo8ePTBo0CAcOnQImZmZyMjIgCAIuPPOO+VjTp06heTkZKuwdP78eeTn57f53oGBgYiPj0deXh4uXLhgtf/XX3+12nbq1CkAkAd2m4iiiL1791odr1QqAQCNjY0tLlefPn0AwGb42rdvH3Q6nXyMN2Bg8gKCxgdQqaU3bGEiIvJIprFKc+fORVZWFq677jqLlpW4uDjk5eVZPBqv0+nwzDPPQK/XO3TvyZMno6GhAW+88YbF9q1bt9ocv2QahL1nzx6L7QsXLsSxY8esjg8JCQEAnDt3rsVlmjRpElQqFRYvXmzR9dbQ0ICXX34ZAHDHHXe0+Hruxi45b+HnD1SWs4WJiMhDTZw4EfPmzcNvv/0G4NLSKSYPPPAA/vWvf2Hs2LG45ZZbYDAYsG3bNgBASkoKMjMz23zvv/zlL1i3bh2++OILHD9+HMOGDcO5c+ewevVqjBkzBps2bbI4/t5778WyZcvw8MMPy0/x7du3D4cPH7Z5fPfu3RETE4NVq1ZBo9EgNjYWgiDggQceaHIupsTERMydOxcvvPACUlNTkZaWBj8/P/z000/IycnB2LFjrboDPRlbmLyFabZvPiVHROSRAgICkJaWBkBqkbn55pst9t9333149dVXERISgi+//BI//vgjhg8fjlWrViE4ONihe/v5+WH58uWYNm0acnNz8fHHHyM7Oxvvv/8+brnlFqvj+/btiy+//BL9+vXDunXrkJGRgaCgIHz33XcYMGCA1fFKpRIfffQRBg0ahO+//x5vvPEGFixYYPeptUceeQRLlixBr1698O2332LJkiXQaDR47rnnsHjxYgiC4NDndiVBtDXNJrVJUVGRQ82qgiAgNjYWBQUFVrOfNr7yD+DkcQCA4sOVEBRKh8p6pWuursm5WNeu4666Nn+U/kqjVqsd7k6jlmltXbfk51KtVtt8is8WtjB5C/OB33W17isHERHRFYiByUsI5gvwcrZvIiIil2Jg8hZ+nO2biIjIXRiYvIUfZ/smIiJyFwYmb2ExeSW75IiIiFyJgclbWCyPwhYmIiIiV2Jg8hKCxQK8bGEiIiJyJQYmb6HlenJE5Lk4xxZ5kvb4eWRg8hb+HPRNRJ5JpVKhoaHB3cUgkhkMBnnBYGdhYPIWWg76JiLP5OvrC51Oh/r6erY0kds1NjaitrYWWq3Wqdf12MV39Xo9MjIysH37dlRXVyMhIQHp6eno37+/3XNLS0vx6aef4tChQxBFEX369MGMGTMQHR1tcVxtbS2+/fZb7NmzByUlJQgODka/fv0wdepUREREtNdHaxs/DvomIs+kUCgQGBgInU6HqqoqdxfHpTQaDVvXXKQ1de3n5weFwrltQh4bmN577z38+uuvGD9+PGJjY7Flyxa88sormDdvHnr16tXkeTqdDs8//zxqa2sxadIkKJVKrF27FvPnz8frr7+OwMBAAIDRaMSLL76IM2fOYOzYsYiNjUVhYSE2bNiAgwcP4u2333Z6OnUIW5iIyIMpFAr4+fm5uxguxXUSXccT6tojA1N2djZ27tyJe+65BxMnTgQAjBo1CrNnz8bSpUvx0ksvNXnu+vXrUVBQgJdffhndu3cHAFx11VWYPXs2Vq9ejbvvvhsAkJWVhZycHDzwwAMWK0p36tQJ77//Pv744w8MGTKkHT9l6whKJeCrBXR1HMNERETkYh45hmn37t1QKBRITU2Vt2k0GowePRonTpxAcXFxs+cmJSXJYQkA4uLi0K9fP+zatUveVldXBwAICQmxOD80NFS+n8cxdctxaRQiIiKX8sjAlJubi9jYWKvmXVMIysvLs3me0WhEfn4+kpKSrPYlJSXh/PnzclDq1q0bfHx8kJGRgcOHD6O0tBSZmZlYunQpkpKS0K9fP+d+KGcwLY/CxXeJiIhcyiO75MrLy+WWHnOmbWVlZTbPq66uhl6vt2o1uvxcrVaLoKAg/P3vf8eHH36IF154QT5uwIABmD17drOPI+r1euj1evm9IAjyeCdBEOx/wCaYzm3yGqYWJoMeMOghqD2wFcxL2K1rchrWteuwrl2L9e06nlDXHhmYGhoaoFarrbabtjU1St60vaXnBgUFITExEWPHjkWXLl2Ql5eHVatWYdGiRXjyySebLN/KlSuxfPly+X3Xrl3x2muvITIysgWfzr6YmBib24vDIlB38XV0gD+UYR72JJ8XaqquyflY167DunYt1rfruLOuPTIwaTQaixYcE9O2psYXmba35Nzz58/j+eefx+OPP45hw4YBAK655hpERkZi0aJF2L9/P6666iqb95k0aRImTJggvzcl3qKiIhgMhhZ9RlsEQUBMTAwKCwttPgXQqLjU6nU+9ySEeuvPSS1jr67JeVjXrsO6di3Wt+u0V12rVKoWN3Z4ZGAKCQlBaWmp1XZTV5yt7joACAgIgFqtRnl5ud1zt2zZgoaGBlx99dUWxw0ePBgAcPz48SYDk1qtttmKBThnOnZRFG1fx2IB3mqAf0Ed1mRdk9Oxrl2Hde1arG/XcWdde+Sg78TERBQUFKC2ttZie1ZWlrzfFoVCgfj4eOTk5Fjty87ORnR0tDzWqKKiAoA0UNxcY2OjxZ8exY8L8BIREbmDRwamYcOGwWg0YuPGjfI2vV6PLVu2IDk5WZ6Fu7i4GGfPnrU4d+jQocjJybEITefOncPhw4flrjcAiI2NhSiKFlMNAMAvv/wCQBqX5HE42zcREZFbeGSXXHJyMoYNG4avvvoKlZWViImJwdatW1FUVIRHH31UPm7hwoXIzMzEsmXL5G1jx47Fpk2b8OqrryItLQ1KpRJr1qxBcHAw0tLS5ONuuOEGrF69GosXL0Zubi66dOmCkydP4ueff0aXLl08atJKmR8X4CUiInIHjwxMADBz5kxkZGRg27ZtqKmpQXx8PObMmYOUlJRmz9NqtZg/fz4+++wzrFixAqIoIiUlBffddx+CgoLk4wIDA/Hqq68iIyMDv//+O3766ScEBgbixhtvxF133QWVyvOqRvDzh9xzyy45IiIilxFEjlRzmqKiIptP6LWUvbVyxBOHYVwwVzr2T5OgmHp/m+91pfOEdYmuFKxr12Fduxbr23Xaq67VanWLn5LzyDFM1AQO+iYiInILBiZv4qu99FpX1/RxRERE5FQMTN5E4yO/FPW2ZzsnIiIi52Ng8ibmM5w31LuvHERERFcYBiZvor7UwgS2MBEREbkMA5MXEZRKQHlxPbkmFiAmIiIi52Ng8jbqi91ybGEiIiJyGQYmb2Ma+M0xTERERC7DwORtTC1MDExEREQuw8DkbUwtTOySIyIichkGJm/DMUxEREQux8DkbUxzMTU2QjQY3FsWIiKiKwQDk7dRm01eyVYmIiIil2Bg8jYa88krOfCbiIjIFRiYvIxgHpg4eSUREZFLMDB5G3bJERERuRwDk7exWICXgYmIiMgVGJi8jUULE8cwERERuQIDk7exGMPEwEREROQKDEzeRs0uOSIiIldjYPI2Zi1MIgd9ExERuQQDk7dhCxMREZHLMTB5Gw0HfRMREbkaA5O3UXPQNxERkasxMHkZzvRNRETkegxM3kbDmb6JiIhcjYHJ23DQNxERkcsxMHkbDvomIiJyOQYmb8NB30RERC7HwORtuPguERGRyzEweRvO9E1ERORyDEzeRs2n5IiIiFyNgcnbWHTJcQwTERGRKzAweRlBoQSUKukNW5iIiIhcgoHJG5lamdjCRERE5BIMTN7INPCbT8kRERG5BAOTNzIN/GaXHBERkUswMHkjU2BiCxMREZFLMDB5I1OXnL4eoii6tyxERERXAAYmb2RqYTIagUaDe8tCRER0BWBg8kZcHoWIiMilGJi8kdnyKBz4TURE1P4YmLyQoOZs30RERK7EwOSNNFxPjoiIyJUYmLyRml1yRERErsTA5I3MW5jq2SVHRETU3hiYvBEHfRMREbkUA5M3Mh/0rWcLExERUXtjYPJGZl1yIudhIiIiancMTN6Ig76JiIhcioHJG3EeJiIiIpdiYPJCApdGISIicikGJm9k8ZQcW5iIiIjaGwOTN1KzhYmIiMiVGJi8EedhIiIicikGJm/EFiYiIiKXYmDyRho+JUdERORKDEzeyKxLTuSgbyIionbHwOSNzCeuZJccERFRu2Ng8kbmXXIc9E1ERNTuGJi8kZqBiYiIyJUYmLyQoFAAKpX0pp5jmIiIiNqbyt0FaIper0dGRga2b9+O6upqJCQkID09Hf3797d7bmlpKT799FMcOnQIoiiiT58+mDFjBqKjo62OLS8vx7Jly/D777+juroaISEh6Nu3Lx577LH2+FjOo/YBDAa2MBEREbmAx7Ywvffee1i7di1GjhyJ+++/HwqFAq+88gqOHTvW7Hk6nQ7PP/88jh49ikmTJmHq1KnIzc3F/PnzUVVVZXFscXExnnnmGezfvx833XQTHnzwQYwePRqVlZXt+dGcw/SkHJ+SIyIiance2cKUnZ2NnTt34p577sHEiRMBAKNGjcLs2bOxdOlSvPTSS02eu379ehQUFODll19G9+7dAQBXXXUVZs+ejdWrV+Puu++Wj/3oo4+gVCrxyiuvIDAwsH0/lLOZBn7zKTkiIqJ255EtTLt374ZCoUBqaqq8TaPRYPTo0Thx4gSKi4ubPTcpKUkOSwAQFxeHfv36YdeuXfK2s2fPYv/+/Zg4cSICAwPR0NAAg8HQPh+oPZgGfrNLjoiIqN15ZAtTbm4uYmNj4efnZ7HdFILy8vIQERFhdZ7RaER+fj5uvPFGq31JSUk4ePAg6urqoNVq8ccffwAAgoOD8cILL+Dw4cNQKBTo378/HnroIURFRbXDJ3Mi9aUWJlEUIQiCe8tDRETUgXlkYCovL0doaKjVdtO2srIym+dVV1dDr9cjJCSk2XO1Wi0KCgoAAIsXL0ZSUhKeeOIJFBcXY/ny5XjxxRfxxhtvwMfHx+o6gDQgXa/Xy+8FQYBWq5Vft5Xp3BZdwzSGSTRCaGyEoFa3+b5XolbVNTmEde06rGvXYn27jifUtUcGpoaGBqhtBADTtoYmxu2YtrfkXJ1OBwAICQnB008/DYVC6p0MDw/HO++8gx07dmDMmDE277Ny5UosX75cft+1a1e89tpriIyMbNHnsycmJsbuMUWBQdCZjg8LhSLAy8ZgeYiW1DU5B+vadVjXrsX6dh131rVHBiaNRmPRgmNi2qYxn+n6svPMj2vuXNOfw4cPl8OS6f3ChQtx/PjxJgPTpEmTMGHCBPm9KfEWFRU5NA5KEATExMSgsLAQoig2e2yj2f7C0/kQQsLafN8rUWvqmhzDunYd1rVrsb5dp73qWqVStbixwyMDU0hICEpLS622m7ribHXXAUBAQADUajXKy8vtnmv6Mzg42OI4hUKBgIAA1NTUNFk+tVptsxULgFO+kaIo2r+O2WzfYkM9wL+sbdKiuianYF27DuvatVjfruPOuvbIp+QSExNRUFCA2tpai+1ZWVnyflsUCgXi4+ORk5NjtS87OxvR0dHyWKNu3boBgFUwMxgMqKqqQlBQkKMfo32ZL4/CqQWIiIjalUcGpmHDhsFoNGLjxo3yNr1ejy1btiA5OVl+Qq64uBhnz561OHfo0KHIycmxCE3nzp3D4cOHMWzYMHlbnz59EBwcjB07dliMidqyZQuMRmOLZhR3K/NuyQZOXklERNSePLJLLjk5GcOGDcNXX32FyspKxMTEYOvWrSgqKsKjjz4qH7dw4UJkZmZi2bJl8raxY8di06ZNePXVV5GWlgalUok1a9YgODgYaWlp8nFqtRr33HMP3nvvPcybNw+jRo1CcXExfvjhB/Tu3RtDhw516WduNbXZE3yc7ZuIiKhdeWRgAoCZM2ciIyMD27ZtQ01NDeLj4zFnzhykpKQ0e55Wq8X8+fPx2WefYcWKFRBFESkpKbjvvvusutmuv/56qFQqfPfdd1i6dCn8/Pxw00034a677rIYCO6RNGaBiV1yRERE7cpjA5NGo8H06dMxffr0Jo+ZP3++ze3h4eF48sknW3SfESNGYMSIEW0ponuZd8lxtm8iIqJ25eHNKNSky5+SIyIionbDwOStOOibiIjIZRiYvJXFoG92yREREbUnh8YwGY1G6HQ6+Pj4QKlUytsbGhrw/fffIy8vD5GRkZg4cSLCwjgTtTMJGh/IU3cxMBEREbUrhwLT8uXLsWLFCsyfPx+9e/cGIM3COX/+fIt5kPbs2YPXX38dAQEBjpWWLtFw4koiIiJXcahL7o8//kBISIgclgDg999/R05ODmJjYzFjxgwMGDAAJSUl2LRpk8OFJTPmM31zHiYiIqJ25VBgunDhAuLi4iy2/fbbbwCAWbNmYfz48ZgzZw6CgoKwe/duR25Fl+PSKERERC7jUGCqrq5GSEiIxbbjx48jLCxMXqtNqVQiOTkZxcXFjtyKLmcxcSVbmIiIiNqTQ4FJoVBAp9PJ76urq1FQUICePXtaHKfVaq0W0iUHqTlxJRERkas4FJiio6ORlZUFo9EIANi3bx8AoFevXhbHVVZWWi1LQg4ya2ES2SVHRETUrhwKTIMHD0ZlZSVef/11/PDDD/jiiy+gUCgwePBg+RhRFJGbm4uoqCiHC0tmuDQKERGRyzg0rcDEiRPx22+/Yf/+/di/fz8A4NZbb0VERIR8zLFjx1BVVWXV6kQOUnMMExERkas4FJj8/PzwyiuvYPfu3SgvL0f37t2RkpJicUxVVRXGjRuHa6+91qGC0mXU6kuvGZiIiIjalUOBCQA0Gg1GjRrV5P4hQ4ZgyJAhjt6GLiMoFIBKDRj07JIjIiJqZ+26llxtbS1EUbR/ILWNaeA3B30TERG1K4damPLz83H48GEMHDgQnTp1krcfPnwY77//PoqLixEQEIDp06fjhhtucLSsdDmNBqgFW5iIiIjamUMtTOvWrcPnn38OjdkTW1VVVViwYIE8UWV1dTU++OAD5ObmOlZSsmaai4lLoxAREbUrhwLT8ePH0aVLF4un4rZt2wadTofU1FQsWbIEjz/+OERRxLp16xwuLF2GXXJEREQu4VBgqqioQHh4uMW2Q4cOQaFQID09HX5+fhg1ahQSExORlZXlUEHJBlMLU0M9x4oRERG1I4cCU21tLfz8/Cy2ZWdnIzExEYGBgfK22NhYlJaWOnIrssV88kqD3n3lICIi6uAcCkx+fn4oKyuT3585cwbV1dXo0aOHwwWjFrBYgJfdckRERO3FocCUmJiI48ePo7CwEADw888/A4DV5JUXLlxAaGioI7ciWywW4OXAbyIiovbi0LQCqampOHz4MObMmYPo6GicOnUKwcHBGDRokHxMXV0d8vLycPXVVztcWLIkqH0gj1xiCxMREVG7caiFafjw4Zg6dSqMRiNOnTqFyMhIPPnkk1CbLduxa9cuNDY2WrU6kROYj2Hi8ihERETtxuGlUaZMmYLbbrsNtbW1CAoKstrfv39/vPbaa4iJiXH0VnQ5iy45tjARERG1F4cDEwCoVCqbYQkAIiIiLOZpIifioG8iIiKXcEpgAgCDwYCTJ0/K0weEhYWhW7duUKmcdgu6nIaDvomIiFzB4TTT2NiIb775Bj/++CPq6uos9mm1WowbNw5TpkyBUql09FZ0OTVbmIiIiFzBocBkNBrx+uuv48CBAwAAf39/REVFAZCmEqipqcG3336LkydPYs6cOVAoHBpjTpcza2ES9Q0Q3FgUIiKijsyhwPTzzz/jwIEDiIyMxPTp0zF06FCL/Xv27MHnn3+OAwcO4Oeff0ZqaqpDhaXLqPmUHBERkSs41OSzdetWaDQaPPfcc1ZhCQCGDBmC5557Dmq1Glu3bnXkVmSLxbQC7JIjIiJqLw4FptOnTyMlJUXuhrMlKioKffv2xenTpx25FdkgmD8lx0HfRERE7cahwKTX660W37XF19cXej0Xh3U6DvomIiJyCYcCU0REBE6cOAGj0djkMUajEVlZWQgPD3fkVmSLhhNXEhERuYJDgWnAgAEoLi7GkiVLYDAYrPYbDAb897//RXFxMQYOHOjIrcgWDvomIiJyCYeekrvtttuwY8cObNiwAXv37sW1114rj2c6f/48du3ahdLSUgQEBOC2225zRnnJHFuYiIiIXMKhwBQWFoa5c+fi7bffRnFxMdasWWN1TEREBGbPno2wsDBHbkW2WCyNwhYmIiKi9uLwTN/du3fHO++8g127duHIkSMoKysDAISGhqJPnz4YPnw4zpw5g8zMTKSkpDhcYDJjNuhbZAsTERFRu3Ha4rvXXXcdrrvuOpv7P/roI+Tk5ODrr792xu3IhPMwERERuYTL1ioRRdFVt7pycNA3ERGRS3BxN2+m5qBvIiIiV2Bg8mKCIFwKTWxhIiIiajcMTN7O9KQcW5iIiIjaDQOTt5NbmBiYiIiI2gsDk7czPSnHFiYiIqJ206ppBbZu3dqmm1RWVrbpPGoBjmEiIiJqd60KTIsWLWqvclBbmY1hEkVRGghORERETtWqwBQREdFe5aC2Ml8eRd9g+Z6IiIicolWB6b333muvclBbXT4XEwMTERGR03HQt7fj8ihERETtjoHJywlcHoWIiKjdMTB5O4sxTAxMRERE7YGBydtp/S69rqlxXzmIiIg6MAYmbxcUKr8UK8vdVw4iIqIOjIHJ2wWHXHpdWea2YhAREXVkDExeTjBrYUIFAxMREVF7YGDydsFmgYktTERERO2CgcnbmY9hqih3XzmIiIg6MAYmb+cfACiV0mu2MBEREbULBiYvJygUQGCI9IYtTERERO2CgakjMI1jqiqHaGx0b1mIiIg6IAamjiAoRPrTaASqq9xaFCIioo5I5e4CNEWv1yMjIwPbt29HdXU1EhISkJ6ejv79+9s9t7S0FJ9++ikOHToEURTRp08fzJgxA9HR0U2ec+zYMTz33HMAgI8//hhBQUFO+yztTQgOhWh6U1l2KUARERGRU3hsC9N7772HtWvXYuTIkbj//vuhUCjwyiuv4NixY82ep9Pp8Pzzz+Po0aOYNGkSpk6ditzcXMyfPx9VVbZbX4xGI/773//Cx8fH5n6PZx6QOI6JiIjI6TwyMGVnZ2Pnzp246667MH36dKSmpuK5555DREQEli5d2uy569evR0FBAebMmYNbb70VEyZMwL/+9S+UlZVh9erVNs/ZuHEjSkpKMGbMmPb4OO3PYmoBPilHRETkbB4ZmHbv3g2FQoHU1FR5m0ajwejRo3HixAkUFxc3e25SUhK6d+8ub4uLi0O/fv2wa9cuq+Orq6uRkZGBO+64A35+flb7vYHA5VGIiIjalUeOYcrNzUVsbKxVgDGFoLy8PERERFidZzQakZ+fjxtvvNFqX1JSEg4ePIi6ujpotVp5+9dff42QkBDcdNNNWL58eYvKp9frodfr5feCIMjXFAShRdewxXRuq68RHHbpdWW5Q2W4UrS5rqnVWNeuw7p2Lda363hCXXtkYCovL0doaKjVdtO2sjLbrSjV1dXQ6/UICQlp9lxTuDl16hQ2btyIZ555BgpFyxvbVq5caRGuunbtitdeew2RkZEtvkZzYmJiWnW83qhH4cXX2gYdwmNjnVKOK0Fr65rajnXtOqxr12J9u44769ojA1NDQwPUarXVdtO2hoaGJs8zP87euUuWLMHAgQMxYMCAVpVv0qRJmDBhgvzelHiLiopgMBhadS1zgiAgJiYGhYWFEEXR/gkXiQ2XWrvqzhegoKCgzWW4UrS1rqn1WNeuw7p2Lda367RXXatUqhY3dnhkYNJoNBZdXiambRqNpsnzzI9r7tydO3fi+PHjePPNN1tdPrVabTOUAXDKN1IUxdYFJo0voPEBGuohVpTxL24rtLauqe1Y167DunYt1rfruLOuPTIwhYSEoLS01Gq7qSvOVncdAAQEBECtVqO8vNzuuf/73/8wfPhwqFQqXLhwAQBQW1sLACgpKYHBYEBYWJjVdTyRIAjSbN9FhUBlubuLQ0RE1OF4ZGBKTEzEkSNHUFtbazHwOysrS95vi0KhQHx8PHJycqz2ZWdnIzo6Wh6/VFJSgh07dmDHjh1Wx86ZMwcJCQlYsGCBEz6NiwSFSIGppgqiXg+hiRYwIiIiaj2PDEzDhg3D6tWrsXHjRkycOBGA1KW2ZcsWJCcny0/IFRcXo76+HnFxcfK5Q4cOxZdffomcnBwkJSUBAM6dO4fDhw8jLS1NPu6pp56yuu/OnTuxc+dOzJw502tal2TBZq1uVeVAmHMGoBMREZGHBqbk5GQMGzYMX331FSorKxETE4OtW7eiqKgIjz76qHzcwoULkZmZiWXLlsnbxo4di02bNuHVV19FWloalEol1qxZg+DgYIvANGTIEKv75uXlAQAGDhzoVUujAIAQZLY8SkU5AxMREZETeWRgAoCZM2ciIyMD27ZtQ01NDeLj4zFnzhykpKQ0e55Wq8X8+fPx2WefYcWKFRBFESkpKbjvvvu8LgS1CievJCIiajeCyKH9TlNUVGTzCb2WEgQBsbGxKCgoaPVTAMZt6yH+7z3pOtMfh2LU2DaX40rgSF1T67CuXYd17Vqsb9dpr7pWq9UtnlbAI5dGodYTzMcwsYWJiIjIqRiYOgqzBXhRUe62YhAREXVEDEwdhdkYJpEtTERERE7FwNRRBIZcel3BwERERORMDEwdhKBWA34B0hvO9k1ERORUDEwdiWngN9eTIyIicioGpo4kKET6s6EeqK9za1GIiIg6EgamDsRiagE+KUdEROQ0DEwdicXUAhz4TURE5CwMTB0Jl0chIiJqFwxMHYlZC5PILjkiIiKnYWDqQLg8ChERUftgYOpILLrkyt1VCiIiog6HgakjseiSYwsTERGRszAwdSQBgYDi4reULUxEREROw8DUgQgK5aU15djCRERE5DQMTB2NaRxTVTlEo9GtRSEiIuooGJg6GtM4psZGoKbavWUhIiLqIBiYOhiBk1cSERE5HQNTR2NagBfgOCYiIiInYWDqaMynFmALExERkVMwMHU05rN9c3kUIiIip2Bg6mCEIC6PQkRE5GwMTB2N+aBvjmEiIiJyCgamjsZ8DFN5qRsLQkRE1HEwMHU0Wj/AP1B6ff6ce8tCRETUQTAwdTCCIACxXaQ35SUQa2vcWyAiIqIOgIGpAxI6dbn0puC0+wpCRETUQTAwdUSxlwKTyMBERETkMAamDogtTERERM7FwNQRxcbLL8VzDExERESOYmDqiELCpKflALYwEREROQEDUwdk8aRcyQWIujr3FoiIiMjLMTB1UEJs50tvzp91X0GIiIg6AAamjorjmIiIiJyGgamDsnxSLt99BSEiIuoAGJg6KvO5mNjCRERE5BAGpo4qLBLQ+Eiv+aQcERGRQxiYOihBobjUylR0HmJDvXsLRERE5MUYmDowwRSYRCNw/px7C0NEROTFGJg6sk7m45g48JuIiKitGJg6MMFs4DcKz7ivIERERF6Ogakj45NyRERETsHA1JFFRAMqtfSaT8oRERG1GQNTByYolUBMnPTmwjmIBr17C0REROSlGJg6OHkcU2MjcKHAvYUhIiLyUgxMHZ3FEinsliMiImoLBqYOTuAivERERA5jYOro2MJERETkMAamji4yFlAqAQAiAxMREVGbMDB1cIJKBUR1kt4UnoXY2OjeAhEREXkhBqYrgelJOYMeKD7v3rIQERF5IQamK4DQyWzgd+YB9xWEiIjISzEwXQGEgUPl1+JP37FbjoiIqJUYmK4AQkIS0HuA9KaoEOK+ne4tEBERkZdhYLpCKMZNkV+L65ZDFEU3loaIiMi7MDBdKXr1BxK6S69P5wIcy0RERNRiDExXCEEQoLj5dvm98ccVbiwNERGRd2FgupIMGg5ExUqvjx2CmJvl3vIQERF5CQamK4igUEIYO0l+z1YmIiKilmFgusIIw0cDQSHSm/27IBaedWt5iIiIvIHK3QVoil6vR0ZGBrZv347q6mokJCQgPT0d/fv3t3tuaWkpPv30Uxw6dAiiKKJPnz6YMWMGoqOj5WOKi4uxefNm7Nu3D4WFhVAoFOjSpQtuv/32Ft3DWwlqDYTUWyF++xkgihB/XAHhvlnuLhYREZFH89gWpvfeew9r167FyJEjcf/990OhUOCVV17BsWPHmj1Pp9Ph+eefx9GjRzFp0iRMnToVubm5mD9/PqqqquTj9u7di++//x4xMTG48847MXnyZNTV1eGll17C5s2b2/vjuZVw/c2A1g8AIO7cBDGn+TolIiK60nlkYMrOzsbOnTtx1113Yfr06UhNTcVzzz2HiIgILF26tNlz169fj4KCAsyZMwe33norJkyYgH/9618oKyvD6tWr5eP69OmDRYsW4W9/+xtuvvlmjB8/Hi+99BLi4uKwbNmy9v6IbiX4+UOYkC69EUUYP/sPRL3evYUiIiLyYB4ZmHbv3g2FQoHU1FR5m0ajwejRo3HixAkUFxc3e25SUhK6d+8ub4uLi0O/fv2wa9cueVuXLl0QFBRkca5arcZVV12FkpIS1NXVOfETeR5hTNqleZkKTkNct9y9BSIiIvJgHhmYcnNzERsbCz8/P4vtphCUl5dn8zyj0Yj8/HwkJSVZ7UtKSsL58+ftBqHy8nL4+PjAx8enbYX3EoJSCcWMvwIK6UdA/OEbiOfy3VwqIiIiz+SRg77Ly8sRGhpqtd20rayszOZ51dXV0Ov1CAkJafZcrVZr8/zCwkL8+uuvGD58OBSKprOkXq+H3qwLSxAE+ZqCIDR5nj2mcx25RqvuF98N4s2TIf7wDdBogPHzhVDOeQ1CM5+9o3B1XV/JWNeuw7p2Lda363hCXXtkYGpoaIBarbbabtrW0NDQ5Hnmx7Xm3Pr6erz11lvQaDS4++67my3fypUrsXz5pS6srl274rXXXkNkZGSz57VUTEyMU67TEsaH/obzB36F4Vw+kHMMgft2IDDtTpfd391cWddXOta167CuXYv17TrurGuPDEwajcaiBcfEtE2j0TR5nvlxLT3XaDTi3//+N86cOYO5c+ciLCys2fJNmjQJEyZMkN+bEm9RUREMBkOz5zZHEATExMSgsLDQpYvjitMeBRbMBQCUL1mIqoQeECI79i8Ad9X1lYh17Tqsa9difbtOe9W1SqVqcWOHRwamkJAQlJaWWm03dcXZ6q4DgICAAKjVapSXl7fq3A8++AD79u3DX//6V/Tt29du+dRqtc1WLABO+UaKoujav3w9+kIYNRbitvVAfR0a330Biqdfh+Dn77oyuInL6/oKxrp2Hda1a7G+Xcedde2Rg1USExNRUFCA2tpai+1ZWVnyflsUCgXi4+ORk5NjtS87OxvR0dFW45f+97//YcuWLZgxYwZGjhzpnA/ghYTJ9wExcdKbgtMwfvg6xMZGt5aJiIjIU3hkYBo2bBiMRiM2btwob9Pr9diyZQuSk5MREREBQJqt++xZy6U9hg4dipycHIvQdO7cORw+fBjDhg2zOHbVqlVYvXo1Jk2ahPHjx7fjJ/J8gp8/FH99FvAPlDZk7oeY8ZF7C0VEROQhPLJLLjk5GcOGDcNXX32FyspKxMTEYOvWrSgqKsKjjz4qH7dw4UJkZmZaTDQ5duxYbNq0Ca+++irS0tKgVCqxZs0aBAcHIy0tTT5uz549WLp0KWJjYxEXF4dt27ZZlKF///42n7bryISoTlD85RkY33oOaDRA3PwDjNGdoRgzwf7JREREHZhHBiYAmDlzJjIyMrBt2zbU1NQgPj4ec+bMQUpKSrPnabVazJ8/H5999hlWrFgBURSRkpKC++67z2KiStNcTgUFBVi4cKHVdebNm3fFBSYAEHr0hXDv4xCXvAMAEDM+hhgVA6HfYDeXjIiIyH0EkSPVnKaoqMjmE3otJQgCYmNjUVBQ4PYBhMZvP780+7ePLxSzX4LQtYdby+RMnlTXHR3r2nVY167F+nad9qprtVrd4qfkPHIME7mfcNs9wNXXSm/qdTC++zzEgjPuLRQREZGbMDCRTYJCAcWDTwI9Lk6zUF0F47/nQSxteh0/IiKijoqBiZokqDVQPP5PoHNXaUNpEYzvzIdYU+XeghEREbkYAxM1S/Dzh+KJ+YBp5u9z+TD+50WI9Tq3louIiMiVGJjILiE4FIonngeCQqQNOcdgXDAXYoXtRZCJiIg6GgYmahEhKhaKv80HtBeXSzmVDeMr/4B4Nt+t5SIiInIFBiZqMSG+GxRzXgXCLj6CWXIBxtfmQDx60L0FIyIiamceO3EleSYhLgGKZxbA+J8XgfwclDQqYfzgbUQNuhrwDwB8tYCvFkJENNB3MAQVf8SIiMj78V8zajUhJAyKf7wM40dvYG11JL6LvwERujL0OXUSvStykFKRi7jaCxCSU6B49GkIprFPREREXoqBidpE8NVC8fhcZH71OwCg2DcUW2OuxtaYqwEAQQ3V6FWRh5QPv0SfcTchKaU7lArBnUUmIiJqMwYmajNBocQ1/btBc7YCJ8r0aDBe2lepCcCeyL7YAwAHG+F76Ch6RgcgJcoPKZFa9IzQwkfFIXREROQdGJjIIXf0i8Ad/SKgbxSRU6pD5oVaZBbVIvN8LWoMl9b70YkKHCysxcHCWgCASgEkhfkiJdIPKVFa9I70Q6CP0l0fg4iIqFkMTOQUaqWAXpFa9IrU4naEwyiKOFVcg8wfNyKzSIfMkESU+oTIxxuMwPFiHY4X67DyqLQtPliDPlF+UitUlBYRfmq79xV1dcDJ40BSLwg+vu306YiI6ErHwETtQiEI6BoZgMR7bsW4LetgXPY6LqgCkRncFUeDu+JoRA+c1YRanJNf0YD8igasyyoHAEQHqJESqZUDVFygBoJwaRyUeHAPjF98AJQVA1GxUDy9AEJgkCs/psuIooiq+kaU1hkQolUhxJd/dYmIXIm/daldCYIA4cbxEPoOQsyKzxD9+y+48fw+4ARQrvbHseCuyAzuisyQrsgL6ASjcGlc0/lqPc5X67E5txIAEOyrREqkFr0Dgd6/rUbi3vVQihcHTl0ogPGDV6H4+/MQVPZbpjyJKIqorG9ESa0BxbV6FNcaLnutR0mtAQ2Nl7o4owPU6BmuRY8IX/SM0KJrqC/USg6qd5Z6gxGnKxogQkTYxYDKhxaIrmyCKIqi/cOoJYqKiqDX69t8viAIiI2NRUFBATrqt0XMzoQx4xMgL8tyh6BAbVRnHG/wxdEQKURlBXWBXtF0+PE11KNXZR56V+YjpSwH3avy4TtyDIR7/mLREmWLq+paFEVUmIehmksByDwc6Y2OlUGtENAtzBc9LwaonhFaRPip7NaDK3j6z3W9wYjcsnrklOqQXarDyVId8ivqYf4tUQhAiK8KYVoVwv2kP8O0KoRZvFYjUKNwa517el13NKxv12mvular1YiMjGxZGRiYnIeBqWVEoxHYtxNizjEgIgZCQhLQpSsEH1+IuVkwfrcUyNwPvaBEdmBnOUAdC05ErUrb5HVVRgOSqs4gpVMQ+l7TH70itQjQ2B5I7oy6Nl5sGTKFoOKLIajE4k8DDA6GIT+1AhF+KoT7qRGqVaKwSo/sUp1Fi5MtoVqVRYDqHubrlicTPennuk5vRG6ZDjmll77OVDbAwW+RTK0QLgtRF0OW/FqNMK0KWnX7fB88qa6vBKxv12Fg6mAYmJxHPHFYCk5ZmfK2RgjI94/B0ZCuONptKDL94lBWb2zyGgKAhBAf9IkyjYPyQ5hW6oW2V9dGUUSlrhFFF4PP5V1kppYhR8OQv1qBCD81wv2klosIf7UcjiIubvNTW4c+g1HEqfJ6HC+uw/HiOpworsO5quZ/9hQC0DXUBz3CtXKIig1Ut3uLiLt+rmv1jcgtrUe2WTg6W9kAeyVQCECXYB8khflAo1SgpNaA0jrpq0JncEq48lMrbLZShZuFqlCtqtXdrPwd4lqsb9dhYOpgGJicT6yuBGqrAZ0O0NUB9XVSq1RsZ4iiiMJq/cWpDOpw5OR5FIjNPykXoxWQ4tuAlMYS9AzR4GxUN5RAc7GV6FIoKq3Tw9B0FmsRf40CEVo1IvxVFwORFIIiLv4Z1kQYaqtKnQEnSnRygDpRokOtvvkPEahRoEfEpQCVHO4L/yZa5drKFT/XNQ2Ncig6eTEknatqsHueUgDiQ3yQFOYrfyWG+DTZEtdoFFGuuxigag0oufhnaZ30uuziz05Vg4M/PBcF+yibaLFSy6+DfJTy+Cr+DnEt1rfrMDB1MAxM7iUajSj54C0cPVOGoxcHk58KiLUYSO4sARqFRQAK91Nd1jKkbrdul5YyiiLOVDbgxMVWqOPFOuSX1zfbwiIA6ByskQNUj3BfdAn2cWjAs7N/rqvqGy8GI53celRYbf/vnUohtTiah6OEEKkVydkaGo1ykCqtM1i0UpXW6uVt9Xa6VVtCIUjdr6YWqrjwYECvg59KAa3a7EulgJ/Zez+1Ar4qBRQeMM7NW/F3tuswMHUwDEzuJ+rqYPz0HWD/bsBoRI3SF8eD45EZ3A1HLw4kNyiafzg0QGFEhKhDmL4SEQ1ViAjyRXhsFCISuyAixB/hWveHIROxXgcUnAbiu0FQ2G8ZqtU3IvtiK5Q0D1YdKusbmz3HV6VAj3Dfiy1R0p+tmdbAkZ/rSp0BOWX1yCm5OCC7TIfzLQhHaoWAxFDLcBQf7ONRTxKKoog6g/FSmDK1WJm1WpXW6lGmMzjc2tkcX5UArVppHaiaDVxK6bXKcv+V9iQhf2e7DgNTB8PA5DnE+nrgVDbE3OMQTx4HTucCWn/oozsjOzwJmdpOqKyuQ2DWAYTXlSOivhzh9RUIq6+Ar7GJ76FCASR0hxAdBygVgFIFKJSAUgkICmm/6Ss0AsKgYRCCQm1eSjToAYWiRSGnyc9YVAjjgrnSPFTde0Px8FMQwlr2F1++xsVuTVM33vFiHXLLdLDX8BEToJYDVM8ILRJDmp7WoKU/1+U6A3JKdMgxDcou0aGo1mD3M2iUArqG+qBbqC+6h0vhqEuwD1Qd5B9v48U5uCxaqeRQdam1qkLXaHd8VnvzUQoWLVhSoFI22cJlHrj8LoYw0zHe0PLF39muw8DUwTAweQ9TXZ878DuM3y2F+Nv2driJAujdH8KQURAGDAHOn4N47BDEoweBnGOAKEIYdgOEm2+HENO5VZcWy0tgfP0ZoKjw0kb/QCju/5t0LwfUG4w4WarD8ZJLrVAldoKLWiEgyWxagx5m0xrY+rkurTNYdKnllOrs3gOQ/kHuGuqLpHBfdA/zRbdQH4e7DDsKg1FEha4RyoAQ5BdcQF1DI+oMRtTqjajTX/zTIL2u0zdKf5rtN7131hODjtKqFPDTKOB/sUXLXy2997sYrvwvBit/zaWgJYeui+ep26G71Rx/Z7uGURRRUKWH0j8YsSodA1NHwMDkPS6va/FUNsRtGwDRCMTEQYjuDMTEAT6+QHYmxOOHIR7/Q+r+cn5hgKuGQzF+CoSE7nYPF6sqYVzwTJNlEVJvhTD5XqdO4Flcq5dboE4U17VoWoMweVoDP/TqEoUDuYXIKa1Ddmk9yurshyNflQLdzLvVwn0RF6hhOGqGo79DRFFEQ6N4WchqvPTaLFhdHsJq9Ubo9EbU6i8FNXeHL5VCsApacrjSmIWui61gl46VjvPXND/Oi7+z20dpnQFZFx9cOVFSh+yLD7B0CvbFB2ldGZg6AgYm79HWuharK4HqKsDYCDQ2mv1plL5EI2AwQDxxGOKvW4GSC7YvFB4F1NYAdTWW24NDgcAQICgEQlAIEBYBIakX0L03BL8AiHW1ML75L+BUtnR8RDQUM5+FcdUXwL5dl64T2wXCiDEQBo+EEB7VqrppCYNRRF5Z/aWuvJI6FNiZ1qA5furLwlGYLzoFabyiW8aTeNLvEFP4utSqZStkWbeC1TY0oubi+5qL750xOL6tBEDuQpRbtzRS0ArQKNG7cwRCFQ1ICNFwyaI2MI2rzCrRIatECknNtTb/b0oygpy4UDsDk5swMHkPV9S1KIrAyeMQ92yDmHMMQmQM0HsAhN4DIETGQKyrhbh1HcSNq4CKMnsFBuISpUB29pS0LTgMijmvStcSRYib10L85r+A4bJfNt16Qrh6BIQuXYGIaGl8lcr5v9jNpzWQgpQOdTZGK/trFEgKlUJRtzCpay0mUN2icCTqaoHaWkDfADTUS19aPyCmMwSFZwzEd6eO+juk0Si1etXqGy+GKiNqTK8vvq81f69vRE2DFMxqzPa1d4tXsK8SCSE+SAzxufinL7oEa9wyYawnMs0fZ5r2JKukDmcq7M+NZpqEd1BiFK6NViKQgcn7MTB5D0+qa1HfAHHnzxB3/QyUFgOV5UCjnS6rgEAonnoFQly85bVO5cD4xftA7ommzxUUQEiY9KX1h+AfAGj9AV9foK4WYlUlUH3xy8cXwqDhEIZeb7elSmxsBI4fgnhkP+AfCGP/a3DWPwYnSuvRoPBFiLIBSaE+iA5o/WSZ4plcGFcuBQ79ZvuAwGAIvfpLgTRlYLu0qnkDT/q59jSiKKK+UURNQ+Ol1itTCGswC1pyALsshDVIr1vb2qUQgJgADRJDpRBlClTRAS37T4K3Mj1QcqK4DlklOpwokaYCsbcMlK9KgeRwXySH+6LHxfUyw/3UHPTd0TAweQ9PrmtRFKXJOivLgXP5ELMyIWYdkZ70E0VA6wfFky9CSExu+hrnz0Hcu0MazG5qkXJUcoo0SL1LN8BXC/hopT8vnIP461bpXpe3lEXGQBgwBBGjx6E0OALQ+LTqlmJRIcRVX0rdm635PvXsB8Vdf4YQl9Cq+3kyUVcn1W9UbJOB05N/rjsKg9E0xqsRFfVGVMIXB/MuIK9Mh1Pl9aiwM02Hia9KQJfgS61RpiAV5KXdeuU6A7KKpTFHpu61ajsTuCoFIDHUFz0uBqTkCG2T4xQZmDoYBibv4Y11LdbWAKdPAtGdIISEt/y8gtMQjx0Cii8AJRcglkh/oqqi+RN9tNLM6s4iKIC4eCnodU2GENNFGrMVHAL4aCEIAsSaKuDsKYhn84HcExD3bLNsbQsJl7oYNRopfKk1EIvPA8f/kGaCN6dUQhiTBiEtHYKvn/M+hwuJxkbg2CGpBXL/LqChAUgZCMXdj0KI7mR1vDf+XHszW/VdXmdAXnk9TpXXX/xTh9MVDXYfkjAJ1aou69bzQZdgTbs/8WePKIpyUNcZjMgpvTjmqFgaf3Shxv6/fbGBaiSHa+V53bqGtnziWAamDoaByXuwri/+Y6yrkwaf19ZIr7V+QEAQEBAEQa2GWHxeaj3avQUoPNP8BVUqoO9gCEOuAyrLIR74FThxWBoMb49GA2h8pW5AW/wDIYyfCuHG8RDUGuvP0tgI5GVBPHoQ4s5NltMthIRDuP1eCN16Sq99mm/lEo1GqSWn+DxQUyVNDlpfJy3PIxqlJxmTetksh7OIxechbvsR4q4tQHmJ9QEqNYRxkyGMm2JZjrpaRPlrcaER0rg3alct/T3SaBRRUNVgFqKkr5bMUA9I3XqdAq279aL8238tSEODHqfWrkXWkSxkxfZBdlRP5Ffp7Y4HC/ZRokeEL5LDtRe72LQOjT1iYOpgGJi8B+u6dURRBPJPQjz0mxRqdHXSAGxdnfSP94Ah0sBy/wDL82qqgSO/Q5ufg5rMg1L3YEsClImPL4SbboVw020Q/PxbVlZ9A8Qfv4X4wzeAwcbfRz9/qaVK6weo1FLQU6mlpx2Lz0tfts4zp9ZITy72HiA9xRjVCQgObfE/XqJeD6hUFseLogicOALjplXAgT3SAH+LcgdIwbK89NK2qFgI/YdALMgHzp2WJjEFpEHwqRMhDLvRbkCktnP090itvhGnK8yC1MVuvZauRahVKeQAlRDiIweqgDauBymKIi7U6KUxR8V1OFFQgZyyBjTYWR3BRymg+8VQ1OPin5H+KqeGOQamDoaByXuwrl3HvK6NOh1wOgdiXjZQWgRUlEOsLJPGa9XVSgGgUzzQKV4af9SlKwRfbZvuKxYVwvjVYuCPvc79QE3R+ACRMUBkLISoWOmzRMYAUbGAXg/x5DEg5xjE7KPSHFoqNRAaDoSGQwiNkLohz+RaXlOpBPpeDcXw0UD/awBjI8TVX0Pc+L0U8OwJCIQwahyE68e2ehZ4sq89fo+IoojSOoNVa9TpigYYWviYX7ifympsVFyQ9dJAlfWNyL74KH/WxcHZ9sZgKcRGxDeUIblHAnrEBDplvcmWYGDqYBiYvAfr2nXcWdeiKAJH9kM8egAoK4FYXiK10JSXSlMTXE6tkaZeiIyRwk5g8MUB7r7SmC59PXDiMMSjh6TA116CwyDcMA7CqD/ZXF5HPHsKxqXvA9mZlzb6+QNxCdCoVGg4esj6miHhQGIyhMTuEBKSpKV9DAagUQ9Rb5DmFBONgIhLA+yVSmkCVJVKOl7rJ83x1cLWPm8lNjYCNVVS13Qz01W4anoScefP0K/OwLnAGOQnX4P8yGScEvxxqrweF2rsTwILSAOsOwf5ICHUBxCBEyV1LeoSjK4rQXd9MZK7dkLyL9+i64UT0vJR8UlQPPkCBP9ARz9iizAwdTAMTN6Dde06nlrXorER0Buk7jeDXpqhMCC4RfM5iaIIFBVIg+nP5kMsKgQuFEjdefamhFAqgbgEqYWorFgaP2bStYc0UP3qa+3O1C4ajdL0Ebo6IC5empdLoZCW/Nm9HcafvpeeXGxJS1RrhUUCcQkQOidILWqhEUBYBBAaIQVMXZ3UdVtTJU30CkjbTV+iCORLLY1iXhZwKkcKbGGRQHgkhLAoICIKQmwXoHMiEB7V7mN1AOnBCnH7BoibVkvfm5Bw6XsxaLjUBXvZ2o/2frbF8+ekpZD8A6UpQKI6tWoONPFCAYxLFwFHD1rvDAqBMGAI6oamIj803qI16lR5PWr0rV+xOUDUI7nsJJIr85FceRrdq04jZNRoaQygxgfi2XwY3/znpQdGOidC6He15UWi4yAMHOr0IMXA1MEwMHkP1rXrXEl1LRobgZIioKhQDlHihQIAgNCthzTeKSHZYlyRqKuTBnYrlFJXngOslvwpK4G4fT3EE0ek2eEvf5KwPSgUrRun1hJaPymgxXaRWgAjoqW5tsKjpHFdAABBGuiuq7v0NGjxeWluM32DFNJMs/ErlVJ3aFik1FUZECRNw7F9Q9N1FBgM4aphEPpfA/TqD8HH1+bPtlhbDfG3HdK8ajnHLK+hVElLL3VOBPoNhtD3aqtxf4DUwiX+9B3EVV9ZtoQKgu3pNRKTpaA9eAQElRqiKKK49rJuvbJ6nKmslxfW1iiAboEKJGuNSK45i+7bVyC6/AzkWBoZA8W9M6X5zczLVnAaxjf+KXWjN0WplOZEGzwSwsBhNj9jazEwdTAMTN6Dde06rGvXaa6uRaNRWgA6LwsoyAcgSOOo1GrpH3KlUvoHWRCkfYDU6mO42ArXaJDGnJ3NA87lS2POnCU4TGp5Ki2y3VXqDvFJ0kMKtloMVWqgVz8oUq5CYEAAKs+cAirKIFaUAdlH7T80YKJUAsl9pBAmGqV5186dlgbwm0/pER4FxbTHgIQkiH/slZ5AzdwvTTNhLjgUQt+rpS5UhUKaykMUgepKiJXl0FdU4JxeBaOuDl1qz0N1+YMFAOCrhXDLHRDGTISgtt3KKRacgfHfz0lh1B6FQurG7ZwIdOkGoUui1GoYGNKqVkMGpg6Ggcl7sK5dh3XtOq6qa1EUpXBzLl9qySkrAUqLIZYVS+sj+gdCCAgC/AOBgItdM7o6+UtsNEDoFA8hsbvUOnJxXjFRFKXunpIiiEUFUnfn2Txp0tb2HDNmolJDuHa0tIB1bGeptejQbxB/3wUc2df6MBeXAGHIKKll61w+xHP5QOFZ+922JoICwpgJEG6dZvXwg1hfL7WKbVol1Y+jBAHCiFQIt90DIdh63NzlRIMeyM2SQrWJXg/x6AGIe3fYD1P+gVKQ6tQFiL640LlCIYVIhQKCf5D00EdI2MXiMTB1KAxM3oN17Tqsa9fpyHUt1lZL48RKLkAslrrbxLJiKXyIIuQFydTqi911kRDCo4HwSKn1SlBIrWcKBaDXA2VFEEuKpCBWViIN9L/uT9Ki17buX6+TJhE99BvEQ3ttz48FSF13Q0ZBuHa01KJyWSuKaNBLT0se2APx4K+Wc4aZREQD8d2guHkyhK49mq8XUQSyjsC4aQ2wf7f1dBTmND7SZLGmudb8A+RQKwwYKq036QTyOpp7f5EeuCg80/axdMGhQHwShPhuCBswGOWdEqVw5SQMTG7CwOQ9WNeuw7p2Hda1a4iiCJzJA/KyENopDuWNIsSgECAoFND6tXw+LlEEzp2GePyQ1BXWKV5qdWljIBCrKoCKUqlFy2i8GCRFKSAFhbR5ig5HiXo9UJAP8XQecCZXamk7d7rp0NkM5RufSl24TtKawOSdi9YQERG5iSAIUndRfDf4xcaioqCgdWsdml8nLt5qEe02lyswWJoKw8MIavXFVqIki+1ibQ1QcFp6QKLRILVCGY1SN19pEcT8k0D+SelpSwCKkDCnhqXWYmAiIiIilxP8/KVlhpJ6NXmMPF4u/yRCfDSoFAS3tZ4yMBEREZFHEgRBmocrIhr+sbGoLChwW1ncu/wxERERkRdgYCIiIiKyg4GJiIiIyA4GJiIiIiI7GJiIiIiI7GBgIiIiIrKDgYmIiIjIDgYmIiIiIjsYmIiIiIjsYGAiIiIisoOBiYiIiMgOBiYiIiIiOxiYiIiIiOxQubsAHYlK5ZzqdNZ1yD7Wteuwrl2Hde1arG/XcXZdt+Z6giiKolPvTkRERNTBsEvOg9TV1WHOnDmoq6tzd1E6PNa167CuXYd17Vqsb9fxhLpmYPIgoigiNzcXbPRrf6xr12Fduw7r2rVY367jCXXNwERERERkBwMTERERkR0MTB5ErVZjypQpUKvV7i5Kh8e6dh3Wteuwrl2L9e06nlDXfEqOiIiIyA62MBERERHZwcBEREREZAcDExEREZEdDExEREREdnABHA+g1+uRkZGB7du3o7q6GgkJCUhPT0f//v3dXTSvlZ2dja1bt+LIkSMoKipCQEAAkpOTkZ6ejk6dOlkce+bMGXz22Wc4duwYVCoVBg0ahBkzZiAoKMhNpfd+3377Lb7++mt06dIFb775psW+48ePY+nSpcjNzYVWq8Xw4cNx9913w9fX102l9T4nT57EN998g2PHjkGv1yM6OhpjxozB+PHj5WNYz44rKChARkYGjh07hurqakRERGDkyJFIS0uDj4+PfBzrunV0Oh1WrVqFrKwsZGdno6amBn/5y19www03WB3b0t/PRqMRq1evxoYNG1BeXo7Y2FjcdtttGDlypNPKzcDkAd577z38+uuvGD9+PGJjY7Flyxa88sormDdvHnr16uXu4nml77//HsePH8ewYcOQkJCA8vJy/Pjjj5gzZw7+7//+D/Hx8QCAkpISzJs3D35+frjrrrug0+mwevVq5Ofn45VXXuGimm1QUlKClStXWvyDYpKXl4cXXngBnTt3xr333ovS0lKsXr0ahYWFmDt3rhtK630OHjyI1157DV27dsXkyZPh6+uL8+fPo7S0VD6G9ey44uJizJ07F35+frj55psREBCAEydOYNmyZTh58iT+3//7fwBY121RWVmJ5cuXIyIiAomJiThy5IjN41rz+/nrr7/Gd999hzFjxiApKQl79+7Fu+++C0EQMGLECOcUXCS3ysrKEqdOnSp+//338rb6+npx5syZ4j//+U83lsy7HTt2TNTr9Rbbzp07J959993iO++8I2/76KOPxGnTpolFRUXytoMHD4pTp04Vf/rpJ5eVtyN5++23xeeff16cN2+e+OSTT1rse/nll8U///nPYk1Njbxt48aN4tSpU8UDBw64uqhep6amRnzooYfEBQsWiI2NjU0ex3p23IoVK8SpU6eK+fn5Ftv/85//iFOnThWrqqpEUWRdt0VDQ4NYVlYmiqIoZmdni1OnThU3b95sdVxLfz+XlJSI6enp4scffyxvMxqN4nPPPSc++uijzf5daQ2OYXKz3bt3Q6FQIDU1Vd6m0WgwevRonDhxAsXFxW4snffq2bOnVetQbGwsOnfujLNnz8rbfv31VwwaNAgRERHytv79+yM2Nha7du1yWXk7iszMTOzevRv33Xef1b7a2locOnQI1113Hfz8/OTt119/PXx9fVnfLbBjxw5UVFQgPT0dCoUCOp0ORqPR4hjWs3OYFnkNDg622B4aGgpBEKBSqVjXbaRWqxESEmL3uJb+fv7tt9/Q2NiIsWPHytsEQcBNN92EkpISnDhxwinlZmBys9zcXMTGxlr8ZQOA7t27A5Cae8k5RFFERUWF3PddWlqKiooKJCUlWR3bvXt35ObmurqIXs1oNGLJkiUYPXq03OVpLj8/H42NjejWrZvFdpVKhcTERNZ3C/zxxx/QarUoLS3F3/72N9x7772YMWMGPvroIzQ0NABgPTtLnz59AAAffPAB8vLyUFxcjJ07d2LDhg0YN24cfH19WdftqDW/n3Nzc+Hj44O4uDir40z7nYEDNNysvLwcoaGhVttN28rKylxdpA5r+/btKC0txR133AHgUt02Vf/V1dXQ6/Vc9qCFNmzYgKKiIjz77LM295eXlwOwXd8hISE4duxYexavQygsLITRaMSCBQtw44034u6778aRI0fw448/oqamBk888QTr2UkGDhyIO++8EytXrsTevXvl7bfffjvS09MB8Ge6PbXm93N5eTlCQkIgCILVcebXchQDk5s1NDTY/AfZtM30v0ZyzNmzZ/HJJ5+gR48e8pMYprq1NbDbvP4ZmOyrqqrCsmXLMHny5CafLjTVt6361Gg0/FlvAZ1Oh/r6etx000144IEHAABDhw6FwWDAxo0bceedd7KenSgyMhK9e/fG0KFDERgYiH379mHlypUICQnBzTffzLpuR635/dzQ0GD3OGdgYHIzjUYDvV5vtd20TaPRuLpIHU55eTleffVV+Pn54cknn4RCIfVEm+rWYDBYncP6b52vv/4aAQEBGDduXJPHmOrS1s97Q0MD67oFTHV0+VM/I0eOxMaNG3HixAn56UTWs2N++eUXLF68GO+88w7Cw8MBSOFUFEV88cUXGDFiBH+m21Frfj9rNBqX/B7nGCY3CwkJsdlc2FxzJLVcbW0tXn75ZdTU1OCf//wnwsLC5H3NNdeWlZUhICCArUstUFBQgI0bN2LcuHEoLS3FhQsXcOHCBej1ehgMBly4cAHV1dXyIE9b9d1U1zRZMtXR5QNmTQOTa2pqWM9OsmHDBnTt2lUOSyaDBw9GfX09cnNzWdftqDW/n0NCQlBeXg5RFK2OM7+WoxiY3CwxMREFBQWora212J6VlSXvp7ZpaGjAa6+9hoKCAjz99NPo3Lmzxf6wsDAEBQUhJyfH6tzs7GzWfQuVlpZCFEUsWbIEM2fOlL+ysrJQUFCAmTNnYvny5YiPj4dSqcTJkyctzjcYDMjLy2N9t4BpcLH5nEvApX8YgoKCWM9OUl5ebvUEInCpxcNoNLKu21Frfj8nJiaivr7e4glo03Gm/c7AwORmw4YNg9FoxMaNG+Vter0eW7ZsQXJyssXjlNRyRqMR//73v3HixAn8/e9/R48ePWweN3ToUOzbt89i+oY//vgDBQUFGDZsmKuK69W6dOmCp556yuqrS5cuiIiIwFNPPYXRo0fDz88P/fr1w/bt2+VHtgFg27Zt0Ol0GD58uBs/hXcw1dHPP/9ssX3Tpk1QKpVISUlhPTtJbGwscnNzce7cOYvtv/zyCwRBQHx8POu6nbX09/M111wDpVKJ9evXy9tEUcRPP/2EsLAw9OzZ0ynl4RgmN0tOTsawYcPw1VdfobKyEjExMdi6dSuKiorw6KOPurt4Xuvzzz/H3r17cfXVV6O6uhrbtm2z2D9q1CgAwKRJk7B79248//zzGD9+vDxlf3x8PG688UZ3FN3rBAUFYciQIVbbf/jhBwCw2Jeeno5nn30W8+fPx5gxY+RZkQcMGICBAwe6qsheq2vXrrjxxhuxefNmNDY2IiUlBUeOHMHu3btx2223yV3OrGfHTZw4EQcOHMC8efMwduxYedD3/v37MXr0aNa1g0xPdppaR/fu3YuSkhIAwLhx4+Dn59fi38/h4eG45ZZbsGrVKjQ2NiIpKQm//fYbjh49ilmzZsnjVh0liJd3+pHLNTQ0yGvJ1dTUID4+HnfeeSf/sjlg/vz5yMzMbHL/smXL5NenT5/G559/Lq9VdNVVV+Hee+9t0cRq1LT58+ejqqrKai25Y8eO4YsvvsDJkyct1t3SarVuKql3MRgMWLlyJbZs2YLS0lJERkZi7NixuOWWWyyOYz07Ljs7G9988w1yc3NRVVWFqKgoXH/99bj11luhVCrl41jXrff444+jqKjI5r6FCxciKioKQMt/PxuNRnz//ffYuHEjysrK5LXkrrvuOqeVmYGJiIiIyA6OYSIiIiKyg4GJiIiIyA4GJiIiIiI7GJiIiIiI7GBgIiIiIrKDgYmIiIjIDgYmIiIiIjsYmIiIiIjs4NIoROQxmpv919xf/vIX3HDDDe1fICe44447AFjOLk9E3oeBiYg8Ts+ePRETE9Pk/ub2ERG1BwYmIvI4Y8aM8ZoWJCK6MnAMExEREZEdbGEiIq9mPkZo48aN+Omnn3Du3DkolUr07NkTkydPRo8ePWyeW11djVWrVmHv3r24cOECFAoFYmNjce2112LcuHHQaDQ2zystLcXatWtx4MABFBUVQRRFhIWFoUePHkhNTUXPnj1tnrd7926sXbsW+fn5MBqNSExMxKRJkzBo0CCrY8vKyvDdd9/hwIEDKC4uhiAICAwMRGxsLAYOHIiJEye2scaIqC0YmIioQ/jss8/www8/oGfPnhg8eDDy8/Oxf/9+HDp0CH//+98xZMgQi+PPnz+PF154AUVFRQgKCsJVV12FxsZGHDlyBF988QV27tyJZ599FgEBARbn/fHHH3jrrbdQU1OD4OBg9O3bFyqVCkVFRdixYwcA2AxMy5Ytw4oVK9CjRw9cddVVOHv2LI4fP47XXnsNs2fPtihfeXk5nn76aZSVlSEiIgIDBgyARqNBWVkZ8vLycPLkSQYmIhdjYCKiDuGnn37Cs88+i759+8rbVq1ahaVLl2LRokXo2bMngoOD5X3vvvsuioqKMHjwYMyaNQu+vr4AgMrKSvzf//0fcnNz8d///hezZs2SzykuLsabb76J2tpa3HbbbbjjjjugUl36NVpRUYGCggKb5Vu3bh1eeuklJCcny9uWLVuG5cuX44svvrAITBs3bkRZWRlSU1Px8MMPQxAEeZ/BYMDRo0cdqCkiagsGJiLyOIsWLcKiRYua3L9kyRL4+/tbbEtNTbUISwAwceJE7Nq1Czk5Odi0aRNuv/12AMCxY8eQlZUFHx8f/PnPf5bDEgAEBQXhkUcewdNPP41ffvkF06ZNQ3h4OABgzZo1qK2txdVXX427777bqlzBwcEWoczcHXfcYRGWAGDSpEn44YcfUFBQgOLiYkRERACQWpgAYODAgRZhCQBUKhX69evXZN0QUftgYCIij2NvWgHzVh2Tpp6qGzVqFHJycpCZmSkHpiNHjgAABgwYgJCQEKtzunXrhoSEBJw6dQqZmZm47rrrAAAHDx4EIIWz1rr66quttqnVakRHRyM3NxelpaVyYOrevTs2bNiAL774AqIoYsCAARahjohcj4GJiDxOW6YViIqKanZ7SUmJvK20tLTZcwAgOjoap06dko8FIE+qGRcX16qyAZDD0OW0Wi0AQK/Xy9tGjRqFQ4cOYceOHXjzzTehUCjQuXNn9OrVC8OGDbNqSSOi9sfARETkAgpFy2dxUSgUmDVrFm6//Xbs27cPx44dw/Hjx7FhwwZs2LABV199Nf7xj3+06ppE5BgGJiLqEC5cuIDExESr7aZWobCwMHmb6fWFCxeavd7l50VERODcuXM4e/asS2Yb79y5Mzp37oyJEydCFEUcPnwY7777Ln7//Xds3boVN954Y7uXgYgk/O8JEXUI27Zta3Z7nz595G2m1wcOHJAHWJvLzc1FXl4eBEFA79695e0DBw4EAGzatMlJpW45QRDQr18/jBgxAgCQl5fn8jIQXckYmIioQ9iwYYM8mNtkzZo1yM7OhlarxejRo+XtvXr1QnJyMhoaGrB48WLU19fL+yorK7F48WIAwIgRIyzGHk2YMAFarRZ79+7F119/DYPBYHG/iooKHDt2zOHPsnXrVpw8edJqe11dHTIzMwEAkZGRDt+HiFqOXXJE5HE2bdpkFX7MDRgwACNHjrTYlpqaihdeeAG9evVCWFgYTp8+jfz8fCgUCjz22GNWT8PNmjULL7zwAvbu3YuZM2eid+/eMBgMOHLkCOrq6tC1a1c88MADFudERETgySefxFtvvYVvv/0WmzZtQo8ePaBUKlFcXIzc3FyMHDkSvXr1cujz//rrr3jvvfcQGhqKxMRE+Pv7o6amBsePH0dtbS26dOmCMWPGOHQPImodBiYi8jjHjx/H8ePHm9zv7+9vFZjuu+8+dOrUCRs3bsRvv/0GpVKJgQMHYvLkyTZn3o6OjsZrr72GVatW4bfffsPvv/8OhUKBTp06Yfjw4Rg/frzNpVEGDBiAN998E2vWrMGBAwdw4MABKJVKhIaGYtSoUU4JMmlpaYiKisKJEyeQm5uL6upqBAQEoHPnzhg5ciRuuOEGTjNA5GKCKIqiuwtBRNRW5mvJERG1F45hIiIiIrKDgYmIiIjIDgYmIiIiIjs4homIiIjIDrYwEREREdnBwERERERkBwMTERERkR0MTERERER2MDARERER2cHARERERGQHAxMRERGRHQxMRERERHYwMBERERHZ8f8Bwy7CpbHD//AAAAAASUVORK5CYII=\n", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.style.use(\"ggplot\")\n", "plt.title(\"Learning Curves\", fontsize=20)\n", "plt.plot(np.linspace(1, n_epochs, n_epochs), epoch_recon_loss_list, color=\"C0\", linewidth=2.0, label=\"Train\")\n", "plt.plot(\n", "    np.linspace(val_interval, n_epochs, int(n_epochs / val_interval)),\n", "    val_recon_epoch_loss_list,\n", "    color=\"C1\",\n", "    linewidth=2.0,\n", "    label=\"Validation\",\n", ")\n", "plt.yticks(fontsize=12)\n", "plt.xticks(fontsize=12)\n", "plt.xlabel(\"Epochs\", fontsize=16)\n", "plt.ylabel(\"Loss\", fontsize=16)\n", "plt.legend(prop={\"size\": 14})\n", "plt.show()"]}, {"cell_type": "markdown", "id": "6df78259", "metadata": {}, "source": ["###  Plotting  evolution of reconstructed images"]}, {"cell_type": "code", "execution_count": 19, "id": "040b52ba", "metadata": {"lines_to_next_cell": 2}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 1850x3050 with 10 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Plot every evaluation as a new line and example as columns\n", "val_samples = np.linspace(val_interval, n_epochs, int(n_epochs / val_interval))\n", "fig, ax = plt.subplots(nrows=len(val_samples), ncols=1, sharey=True)\n", "fig.set_size_inches(18.5, 30.5)\n", "for image_n in range(len(val_samples)):\n", "    reconstructions = intermediary_images[image_n]\n", "    reconstructions = np.concatenate(\n", "        [\n", "            reconstructions[0, :, :, 15],\n", "            np.flipud(reconstructions[0, :, 24, :].T),\n", "            np.flipud(reconstructions[0, 15, :, :].T),\n", "        ],\n", "        axis=1,\n", "    )\n", "\n", "    ax[image_n].imshow(reconstructions, cmap=\"gray\")\n", "    ax[image_n].set_xticks([])\n", "    ax[image_n].set_yticks([])\n", "    ax[image_n].set_ylabel(f\"Epoch {val_samples[image_n]:.0f}\")"]}, {"cell_type": "markdown", "id": "1c3b5cff", "metadata": {}, "source": ["### Plotting the reconstructions from final trained model"]}, {"cell_type": "code", "execution_count": 20, "id": "709f9c57", "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 640x480 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, ax = plt.subplots(nrows=1, ncols=2)\n", "plt.style.use(\"default\")\n", "plotting_image_0 = np.concatenate([images[0, 0, :, :, 15].cpu(), np.flipud(images[0, 0, :, 24, :].cpu().T)], axis=1)\n", "plotting_image_1 = np.concatenate([np.flipud(images[0, 0, 15, :, :].cpu().T), np.zeros((32, 32))], axis=1)\n", "image = np.concatenate([plotting_image_0, plotting_image_1], axis=0)\n", "\n", "ax[0].imshow(image, vmin=0, vmax=1, cmap=\"gray\")\n", "ax[0].axis(\"off\")\n", "ax[0].title.set_text(\"Inputted Image\")\n", "\n", "plotting_image_2 = np.concatenate(\n", "    [reconstruction[0, 0, :, :, 15].cpu(), np.flipud(reconstruction[0, 0, :, 24, :].cpu().T)], axis=1\n", ")\n", "plotting_image_3 = np.concatenate([np.flipud(reconstruction[0, 0, 15, :, :].cpu().T), np.zeros((32, 32))], axis=1)\n", "reconstruction_3d = np.concatenate([plotting_image_2, plotting_image_3], axis=0)\n", "ax[1].imshow(reconstruction_3d, vmin=0, vmax=1, cmap=\"gray\")\n", "ax[1].axis(\"off\")\n", "ax[1].title.set_text(\"Reconstruction\")\n", "plt.show()"]}, {"cell_type": "markdown", "id": "54ef9b14", "metadata": {}, "source": ["### Cleanup data directory\n", "\n", "Remove directory if a temporary was used."]}, {"cell_type": "code", "execution_count": null, "id": "d53b24f4", "metadata": {}, "outputs": [], "source": ["if directory is None:\n", "    shutil.rmtree(root_dir)"]}], "metadata": {"jupytext": {"cell_metadata_filter": "-all", "formats": "auto:percent,ipynb", "main_language": "python", "notebook_metadata_filter": "-all"}, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.13"}}, "nbformat": 4, "nbformat_minor": 5}