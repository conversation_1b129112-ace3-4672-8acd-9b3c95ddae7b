{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Copyright (c) MONAI Consortium\n", "# Licensed under the Apache License, Version 2.0 (the \"License\");\n", "# you may not use this file except in compliance with the License.\n", "# You may obtain a copy of the License at\n", "#     http://www.apache.org/licenses/LICENSE-2.0\n", "# Unless required by applicable law or agreed to in writing, software\n", "# distributed under the License is distributed on an \"AS IS\" BASIS,\n", "# WITHOUT WAR<PERSON><PERSON>IES OR CONDITIONS OF ANY KIND, either express or implied.\n", "# See the License for the specific language governing permissions and\n", "# limitations under the License."]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "# Diffusion Models for Implicit Image Segmentation Ensembles<br>\n", "<br>\n", "This tutorial illustrates how to use MONAI for 2D segmentation of images using DDPMs, as proposed in [1].<br>\n", "The same structure can also be used for conditional image generation, or image-to-image translation, as proposed in [2,3].\n", "<br>\n", "<br>\n", "[1] - <PERSON><PERSON><PERSON> et al. \"Diffusion Models for Implicit Image Segmentation Ensembles\", https://arxiv.org/abs/2112.03145<br>\n", "[2] - <PERSON><PERSON><PERSON> et al. \"A Diffusion Model Predicts 3D Shapes from 2D Microscopy Images\", https://arxiv.org/abs/2208.14125<br>\n", "[3] - <PERSON><PERSON> et al. \"Diffusion Models for Contrast Harmonization of Magnetic Resonance Images\", https://aps.arxiv.org/abs/2303.08189\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup environment"]}, {"cell_type": "code", "execution_count": 59, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["running install\n", "/home/<USER>/anaconda3/envs/experiment/lib/python3.10/site-packages/setuptools/command/install.py:34: SetuptoolsDeprecationWarning: setup.py install is deprecated. Use build and pip and other standards-based tools.\n", "  warnings.warn(\n", "/home/<USER>/anaconda3/envs/experiment/lib/python3.10/site-packages/setuptools/command/easy_install.py:144: EasyInstallDeprecationWarning: easy_install command is deprecated. Use build and pip and other standards-based tools.\n", "  warnings.warn(\n", "running bdist_egg\n", "running egg_info\n", "writing generative.egg-info/PKG-INFO\n", "writing dependency_links to generative.egg-info/dependency_links.txt\n", "writing requirements to generative.egg-info/requires.txt\n", "writing top-level names to generative.egg-info/top_level.txt\n", "reading manifest file 'generative.egg-info/SOURCES.txt'\n", "writing manifest file 'generative.egg-info/SOURCES.txt'\n", "installing library code to build/bdist.linux-x86_64/egg\n", "running install_lib\n", "warning: install_lib: 'build/lib' does not exist -- no Python modules to install\n", "\n", "creating build/bdist.linux-x86_64/egg\n", "creating build/bdist.linux-x86_64/egg/EGG-INFO\n", "copying generative.egg-info/PKG-INFO -> build/bdist.linux-x86_64/egg/EGG-INFO\n", "copying generative.egg-info/SOURCES.txt -> build/bdist.linux-x86_64/egg/EGG-INFO\n", "copying generative.egg-info/dependency_links.txt -> build/bdist.linux-x86_64/egg/EGG-INFO\n", "copying generative.egg-info/requires.txt -> build/bdist.linux-x86_64/egg/EGG-INFO\n", "copying generative.egg-info/top_level.txt -> build/bdist.linux-x86_64/egg/EGG-INFO\n", "zip_safe flag not set; analyzing archive contents...\n", "creating 'dist/generative-0.1.0-py3.10.egg' and adding 'build/bdist.linux-x86_64/egg' to it\n", "removing 'build/bdist.linux-x86_64/egg' (and everything under it)\n", "Processing generative-0.1.0-py3.10.egg\n", "Removing /home/<USER>/anaconda3/envs/experiment/lib/python3.10/site-packages/generative-0.1.0-py3.10.egg\n", "Copying generative-0.1.0-py3.10.egg to /home/<USER>/anaconda3/envs/experiment/lib/python3.10/site-packages\n", "generative 0.1.0 is already the active version in easy-install.pth\n", "\n", "Installed /home/<USER>/anaconda3/envs/experiment/lib/python3.10/site-packages/generative-0.1.0-py3.10.egg\n", "Processing dependencies for generative==0.1.0\n", "Searching for monai-weekly==1.2.dev2304\n", "Best match: monai-weekly 1.2.dev2304\n", "Adding monai-weekly 1.2.dev2304 to easy-install.pth file\n", "\n", "Using /home/<USER>/anaconda3/envs/experiment/lib/python3.10/site-packages\n", "Searching for numpy==1.23.2\n", "Best match: numpy 1.23.2\n", "Adding numpy 1.23.2 to easy-install.pth file\n", "Installing f2py script to /home/<USER>/anaconda3/envs/experiment/bin\n", "Installing f2py3 script to /home/<USER>/anaconda3/envs/experiment/bin\n", "Installing f2py3.10 script to /home/<USER>/anaconda3/envs/experiment/bin\n", "\n", "Using /home/<USER>/anaconda3/envs/experiment/lib/python3.10/site-packages\n", "Searching for torch==1.12.1\n", "Best match: torch 1.12.1\n", "Adding torch 1.12.1 to easy-install.pth file\n", "Installing convert-caffe2-to-onnx script to /home/<USER>/anaconda3/envs/experiment/bin\n", "Installing convert-onnx-to-caffe2 script to /home/<USER>/anaconda3/envs/experiment/bin\n", "Installing torchrun script to /home/<USER>/anaconda3/envs/experiment/bin\n", "\n", "Using /home/<USER>/anaconda3/envs/experiment/lib/python3.10/site-packages\n", "Searching for typing-extensions==4.3.0\n", "Best match: typing-extensions 4.3.0\n", "Adding typing-extensions 4.3.0 to easy-install.pth file\n", "\n", "Using /home/<USER>/anaconda3/envs/experiment/lib/python3.10/site-packages\n", "Finished processing dependencies for generative==0.1.0\n"]}], "source": ["!python -c \"import monai\" || pip install -q \"monai-weekly[pillow, tqdm]\"\n", "!python -c \"import matplotlib\" || pip install -q matplotlib\n", "!python -c \"import seaborn\" || pip install -q seaborn"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "## Setup imports"]}, {"cell_type": "code", "execution_count": 60, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["MONAI version: 1.2.dev2304\n", "Numpy version: 1.23.2\n", "Pytorch version: 1.12.1\n", "MONAI flags: HAS_EXT = False, USE_COMPILED = False, USE_META_DICT = False\n", "MONAI rev id: 9a57be5aab9f2c2a134768c0c146399150e247a0\n", "MONAI __file__: /home/<USER>/anaconda3/envs/experiment/lib/python3.10/site-packages/monai/__init__.py\n", "\n", "Optional dependencies:\n", "Pytorch Ignite version: 0.4.10\n", "ITK version: 5.3.0\n", "Nibabel version: 4.0.1\n", "scikit-image version: 0.19.3\n", "Pillow version: 9.2.0\n", "Tensorboard version: 2.12.0\n", "gdown version: 4.6.4\n", "TorchVision version: 0.13.1\n", "tqdm version: 4.64.1\n", "lmdb version: 1.4.0\n", "psutil version: 5.9.4\n", "pandas version: 1.5.3\n", "einops version: 0.6.0\n", "transformers version: 4.21.3\n", "mlflow version: 2.1.1\n", "pynrrd version: 1.0.0\n", "\n", "For details about installing the optional dependencies, please visit:\n", "    https://docs.monai.io/en/latest/installation.html#installing-the-recommended-dependencies\n", "\n"]}], "source": ["import os\n", "import tempfile\n", "import time\n", "\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "import torch\n", "import torch.nn.functional as F\n", "from monai import transforms\n", "from monai.apps import DecathlonDataset\n", "from monai.config import print_config\n", "from monai.data import DataLoader\n", "from monai.utils import set_determinism\n", "from torch.cuda.amp import GradScaler, autocast\n", "from tqdm import tqdm\n", "\n", "from generative.inferers import DiffusionInferer\n", "from generative.networks.nets.diffusion_model_unet import DiffusionModelUNet\n", "from generative.networks.schedulers.ddpm import DDPMScheduler\n", "\n", "torch.multiprocessing.set_sharing_strategy(\"file_system\")\n", "print_config()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup data directory"]}, {"cell_type": "code", "execution_count": 61, "metadata": {"lines_to_next_cell": 2}, "outputs": [], "source": ["directory = os.environ.get(\"MONAI_DATA_DIRECTORY\")\n", "root_dir = tempfile.mkdtemp() if directory is None else directory"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "## Set deterministic training for reproducibility"]}, {"cell_type": "code", "execution_count": 62, "metadata": {}, "outputs": [], "source": ["set_determinism(42)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "# Preprocessing of the BRATS Dataset in 2D slices for training\n", "We download the BRATS training dataset from the Decathlon dataset. \\\n", "We slice the volumes in axial 2D slices, and assign slice-wise ground truth segmentations of the tumor to all slices.\n", "Here we use transforms to augment the training dataset:\n", "\n", "1. `LoadImaged` loads the brain MR images from files.\n", "1. `EnsureChannelFirstd` ensures the original data to construct \"channel first\" shape.\n", "1. `ScaleIntensityRangePercentilesd` takes the lower and upper intensity percentiles and scales them to [0, 1].\n"]}, {"cell_type": "code", "execution_count": 63, "metadata": {"lines_to_next_cell": 2}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["<class 'monai.transforms.utility.array.AddChannel'>: Class `AddChannel` has been deprecated since version 0.8. please use MetaTensor data type and monai.transforms.EnsureChannelFirst instead.\n"]}], "source": ["channel = 0  # 0 = Flair\n", "assert channel in [0, 1, 2, 3], \"Choose a valid channel\"\n", "\n", "\n", "train_transforms = transforms.Compose(\n", "    [\n", "        transforms.LoadImaged(keys=[\"image\", \"label\"]),\n", "        transforms.EnsureChannelFirstd(keys=[\"image\", \"label\"]),\n", "        transforms.Lambdad(keys=[\"image\"], func=lambda x: x[channel, :, :, :]),\n", "        transforms.EnsureChannelFirstd(keys=[\"image\"], channel_dim=\"no_channel\"),\n", "        transforms.EnsureTyped(keys=[\"image\", \"label\"]),\n", "        transforms.Orientationd(keys=[\"image\", \"label\"], axcodes=\"RAS\"),\n", "        transforms.Spacingd(keys=[\"image\", \"label\"], pixdim=(3.0, 3.0, 2.0), mode=(\"bilinear\", \"nearest\")),\n", "        transforms.CenterSpatialCropd(keys=[\"image\", \"label\"], roi_size=(64, 64, 64)),\n", "        transforms.ScaleIntensityRangePercentilesd(keys=\"image\", lower=0, upper=99.5, b_min=0, b_max=1),\n", "        transforms.RandSpatialCropd(keys=[\"image\", \"label\"], roi_size=(64, 64, 1), random_size=False),\n", "        transforms.Lambdad(keys=[\"image\", \"label\"], func=lambda x: x.squeeze(-1)),\n", "    ]\n", ")"]}, {"cell_type": "code", "execution_count": 64, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Loading dataset: 100%|█████████████████████████████████████████████████████████████████████████████| 388/388 [02:59<00:00,  2.16it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Length of training data: 388\n", "Train image shape torch.Size([1, 64, 64])\n", "Train label shape torch.Size([1, 64, 64])\n"]}], "source": ["batch_size = 32\n", "\n", "train_ds = DecathlonDataset(\n", "    root_dir=root_dir,\n", "    task=\"Task01_BrainTumour\",\n", "    section=\"training\",  # validation\n", "    cache_rate=1.0,  # you may need a few Gb of RAM... Set to 0 otherwise\n", "    num_workers=4,\n", "    download=False,  # Set download to True if the dataset hasnt been downloaded yet\n", "    seed=0,\n", "    transform=train_transforms,\n", ")\n", "\n", "print(f\"Length of training data: {len(train_ds)}\")  # this gives the number of patients in the training set\n", "print(f'Train image shape {train_ds[0][\"image\"].shape}')\n", "print(f'Train label shape {train_ds[0][\"label\"].shape}')\n", "\n", "\n", "train_loader = DataLoader(\n", "    train_ds, batch_size=batch_size, shuffle=True, num_workers=4, drop_last=True, persistent_workers=True\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Preprocessing of the BRATS Dataset in 2D slices for validation\n", "We download the BRATS validation dataset from the Decathlon dataset. We define the dataloader to load 2D slices as well as the corresponding ground truth tumor segmentation for validation."]}, {"cell_type": "code", "execution_count": 65, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Loading dataset: 100%|███████████████████████████████████████████████████████████████████████████████| 96/96 [00:45<00:00,  2.12it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Length of training data: 96\n", "Validation Image shape torch.Size([1, 64, 64])\n", "Validation Label shape torch.Size([1, 64, 64])\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["val_ds = DecathlonDataset(\n", "    root_dir=root_dir,\n", "    task=\"Task01_BrainTumour\",\n", "    section=\"validation\",\n", "    cache_rate=1.0,  # you may need a few Gb of RAM... Set to 0 otherwise\n", "    num_workers=4,\n", "    download=False,  # Set download to True if the dataset hasnt been downloaded yet\n", "    seed=0,\n", "    transform=train_transforms,\n", ")\n", "print(f\"Length of training data: {len(val_ds)}\")\n", "print(f'Validation Image shape {val_ds[0][\"image\"].shape}')\n", "print(f'Validation Label shape {val_ds[0][\"label\"].shape}')\n", "\n", "val_loader = DataLoader(\n", "    val_ds, batch_size=batch_size, shuffle=False, num_workers=4, drop_last=True, persistent_workers=True\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "## Define network, scheduler, optimizer, and inferer\n", "\n", "At this step, we instantiate the MONAI components to create a DDPM, the UNET, the noise scheduler, and the inferer used for training and sampling. We are using the DDPM scheduler containing 1000 timesteps, and a 2D UNET with attention mechanisms in the 3rd level (`num_head_channels=64`).<br>\n"]}, {"cell_type": "code", "execution_count": 66, "metadata": {}, "outputs": [], "source": ["device = torch.device(\"cuda\")"]}, {"cell_type": "code", "execution_count": 67, "metadata": {}, "outputs": [{"data": {"text/plain": ["DiffusionModelUNet(\n", "  (conv_in): Convolution(\n", "    (conv): Conv2d(2, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "  )\n", "  (time_embed): Sequential(\n", "    (0): Linear(in_features=64, out_features=256, bias=True)\n", "    (1): SiLU()\n", "    (2): Linear(in_features=256, out_features=256, bias=True)\n", "  )\n", "  (down_blocks): ModuleList(\n", "    (0): DownBlock(\n", "      (resnets): ModuleList(\n", "        (0): ResnetBlock(\n", "          (norm1): GroupNorm(32, 64, eps=1e-06, affine=True)\n", "          (nonlinearity): SiLU()\n", "          (conv1): Convolution(\n", "            (conv): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          )\n", "          (time_emb_proj): Linear(in_features=256, out_features=64, bias=True)\n", "          (norm2): GroupNorm(32, 64, eps=1e-06, affine=True)\n", "          (conv2): Convolution(\n", "            (conv): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          )\n", "          (skip_connection): Identity()\n", "        )\n", "      )\n", "      (downsampler): Downsample(\n", "        (op): Convolution(\n", "          (conv): Conv2d(64, 64, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1))\n", "        )\n", "      )\n", "    )\n", "    (1): DownBlock(\n", "      (resnets): ModuleList(\n", "        (0): ResnetBlock(\n", "          (norm1): GroupNorm(32, 64, eps=1e-06, affine=True)\n", "          (nonlinearity): SiLU()\n", "          (conv1): Convolution(\n", "            (conv): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          )\n", "          (time_emb_proj): Linear(in_features=256, out_features=64, bias=True)\n", "          (norm2): GroupNorm(32, 64, eps=1e-06, affine=True)\n", "          (conv2): Convolution(\n", "            (conv): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          )\n", "          (skip_connection): Identity()\n", "        )\n", "      )\n", "      (downsampler): Downsample(\n", "        (op): Convolution(\n", "          (conv): Conv2d(64, 64, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1))\n", "        )\n", "      )\n", "    )\n", "    (2): AttnDownBlock(\n", "      (attentions): <PERSON><PERSON>le<PERSON><PERSON>(\n", "        (0): AttentionBlock(\n", "          (norm): GroupNorm(32, 64, eps=1e-06, affine=True)\n", "          (to_q): Linear(in_features=64, out_features=64, bias=True)\n", "          (to_k): Linear(in_features=64, out_features=64, bias=True)\n", "          (to_v): Linear(in_features=64, out_features=64, bias=True)\n", "          (proj_attn): Linear(in_features=64, out_features=64, bias=True)\n", "        )\n", "      )\n", "      (resnets): ModuleList(\n", "        (0): ResnetBlock(\n", "          (norm1): GroupNorm(32, 64, eps=1e-06, affine=True)\n", "          (nonlinearity): SiLU()\n", "          (conv1): Convolution(\n", "            (conv): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          )\n", "          (time_emb_proj): Linear(in_features=256, out_features=64, bias=True)\n", "          (norm2): GroupNorm(32, 64, eps=1e-06, affine=True)\n", "          (conv2): Convolution(\n", "            (conv): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          )\n", "          (skip_connection): Identity()\n", "        )\n", "      )\n", "    )\n", "  )\n", "  (middle_block): AttnMidBlock(\n", "    (resnet_1): ResnetBlock(\n", "      (norm1): GroupNorm(32, 64, eps=1e-06, affine=True)\n", "      (nonlinearity): SiLU()\n", "      (conv1): Convolution(\n", "        (conv): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "      )\n", "      (time_emb_proj): Linear(in_features=256, out_features=64, bias=True)\n", "      (norm2): GroupNorm(32, 64, eps=1e-06, affine=True)\n", "      (conv2): Convolution(\n", "        (conv): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "      )\n", "      (skip_connection): Identity()\n", "    )\n", "    (attention): AttentionBlock(\n", "      (norm): GroupNorm(32, 64, eps=1e-06, affine=True)\n", "      (to_q): Linear(in_features=64, out_features=64, bias=True)\n", "      (to_k): Linear(in_features=64, out_features=64, bias=True)\n", "      (to_v): Linear(in_features=64, out_features=64, bias=True)\n", "      (proj_attn): Linear(in_features=64, out_features=64, bias=True)\n", "    )\n", "    (resnet_2): ResnetBlock(\n", "      (norm1): GroupNorm(32, 64, eps=1e-06, affine=True)\n", "      (nonlinearity): SiLU()\n", "      (conv1): Convolution(\n", "        (conv): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "      )\n", "      (time_emb_proj): Linear(in_features=256, out_features=64, bias=True)\n", "      (norm2): GroupNorm(32, 64, eps=1e-06, affine=True)\n", "      (conv2): Convolution(\n", "        (conv): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "      )\n", "      (skip_connection): Identity()\n", "    )\n", "  )\n", "  (up_blocks): ModuleList(\n", "    (0): AttnUpBlock(\n", "      (resnets): ModuleList(\n", "        (0): ResnetBlock(\n", "          (norm1): GroupNorm(32, 128, eps=1e-06, affine=True)\n", "          (nonlinearity): SiLU()\n", "          (conv1): Convolution(\n", "            (conv): Conv2d(128, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          )\n", "          (time_emb_proj): Linear(in_features=256, out_features=64, bias=True)\n", "          (norm2): GroupNorm(32, 64, eps=1e-06, affine=True)\n", "          (conv2): Convolution(\n", "            (conv): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          )\n", "          (skip_connection): Convolution(\n", "            (conv): Conv2d(128, 64, kernel_size=(1, 1), stride=(1, 1))\n", "          )\n", "        )\n", "        (1): ResnetBlock(\n", "          (norm1): GroupNorm(32, 128, eps=1e-06, affine=True)\n", "          (nonlinearity): SiLU()\n", "          (conv1): Convolution(\n", "            (conv): Conv2d(128, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          )\n", "          (time_emb_proj): Linear(in_features=256, out_features=64, bias=True)\n", "          (norm2): GroupNorm(32, 64, eps=1e-06, affine=True)\n", "          (conv2): Convolution(\n", "            (conv): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          )\n", "          (skip_connection): Convolution(\n", "            (conv): Conv2d(128, 64, kernel_size=(1, 1), stride=(1, 1))\n", "          )\n", "        )\n", "      )\n", "      (attentions): <PERSON><PERSON>le<PERSON><PERSON>(\n", "        (0): AttentionBlock(\n", "          (norm): GroupNorm(32, 64, eps=1e-06, affine=True)\n", "          (to_q): Linear(in_features=64, out_features=64, bias=True)\n", "          (to_k): Linear(in_features=64, out_features=64, bias=True)\n", "          (to_v): Linear(in_features=64, out_features=64, bias=True)\n", "          (proj_attn): Linear(in_features=64, out_features=64, bias=True)\n", "        )\n", "        (1): AttentionBlock(\n", "          (norm): GroupNorm(32, 64, eps=1e-06, affine=True)\n", "          (to_q): Linear(in_features=64, out_features=64, bias=True)\n", "          (to_k): Linear(in_features=64, out_features=64, bias=True)\n", "          (to_v): Linear(in_features=64, out_features=64, bias=True)\n", "          (proj_attn): Linear(in_features=64, out_features=64, bias=True)\n", "        )\n", "      )\n", "      (upsampler): Up<PERSON><PERSON>(\n", "        (conv): Convolution(\n", "          (conv): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "        )\n", "      )\n", "    )\n", "    (1): UpBlock(\n", "      (resnets): ModuleList(\n", "        (0): ResnetBlock(\n", "          (norm1): GroupNorm(32, 128, eps=1e-06, affine=True)\n", "          (nonlinearity): SiLU()\n", "          (conv1): Convolution(\n", "            (conv): Conv2d(128, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          )\n", "          (time_emb_proj): Linear(in_features=256, out_features=64, bias=True)\n", "          (norm2): GroupNorm(32, 64, eps=1e-06, affine=True)\n", "          (conv2): Convolution(\n", "            (conv): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          )\n", "          (skip_connection): Convolution(\n", "            (conv): Conv2d(128, 64, kernel_size=(1, 1), stride=(1, 1))\n", "          )\n", "        )\n", "        (1): ResnetBlock(\n", "          (norm1): GroupNorm(32, 128, eps=1e-06, affine=True)\n", "          (nonlinearity): SiLU()\n", "          (conv1): Convolution(\n", "            (conv): Conv2d(128, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          )\n", "          (time_emb_proj): Linear(in_features=256, out_features=64, bias=True)\n", "          (norm2): GroupNorm(32, 64, eps=1e-06, affine=True)\n", "          (conv2): Convolution(\n", "            (conv): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          )\n", "          (skip_connection): Convolution(\n", "            (conv): Conv2d(128, 64, kernel_size=(1, 1), stride=(1, 1))\n", "          )\n", "        )\n", "      )\n", "      (upsampler): Up<PERSON><PERSON>(\n", "        (conv): Convolution(\n", "          (conv): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "        )\n", "      )\n", "    )\n", "    (2): UpBlock(\n", "      (resnets): ModuleList(\n", "        (0): ResnetBlock(\n", "          (norm1): GroupNorm(32, 128, eps=1e-06, affine=True)\n", "          (nonlinearity): SiLU()\n", "          (conv1): Convolution(\n", "            (conv): Conv2d(128, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          )\n", "          (time_emb_proj): Linear(in_features=256, out_features=64, bias=True)\n", "          (norm2): GroupNorm(32, 64, eps=1e-06, affine=True)\n", "          (conv2): Convolution(\n", "            (conv): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          )\n", "          (skip_connection): Convolution(\n", "            (conv): Conv2d(128, 64, kernel_size=(1, 1), stride=(1, 1))\n", "          )\n", "        )\n", "        (1): ResnetBlock(\n", "          (norm1): GroupNorm(32, 128, eps=1e-06, affine=True)\n", "          (nonlinearity): SiLU()\n", "          (conv1): Convolution(\n", "            (conv): Conv2d(128, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          )\n", "          (time_emb_proj): Linear(in_features=256, out_features=64, bias=True)\n", "          (norm2): GroupNorm(32, 64, eps=1e-06, affine=True)\n", "          (conv2): Convolution(\n", "            (conv): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          )\n", "          (skip_connection): Convolution(\n", "            (conv): Conv2d(128, 64, kernel_size=(1, 1), stride=(1, 1))\n", "          )\n", "        )\n", "      )\n", "    )\n", "  )\n", "  (out): Sequential(\n", "    (0): GroupNorm(32, 64, eps=1e-06, affine=True)\n", "    (1): SiLU()\n", "    (2): Convolution(\n", "      (conv): Conv2d(64, 1, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "    )\n", "  )\n", ")"]}, "execution_count": 67, "metadata": {}, "output_type": "execute_result"}], "source": ["model = DiffusionModelUNet(\n", "    spatial_dims=2,\n", "    in_channels=2,\n", "    out_channels=1,\n", "    num_channels=(64, 64, 64),\n", "    attention_levels=(False, False, True),\n", "    num_res_blocks=1,\n", "    num_head_channels=64,\n", "    with_conditioning=False,\n", ")\n", "model.to(device)"]}, {"cell_type": "code", "execution_count": 68, "metadata": {}, "outputs": [], "source": ["scheduler = DDPMScheduler(num_train_timesteps=1000)\n", "optimizer = torch.optim.Adam(params=model.parameters(), lr=2.5e-5)\n", "inferer = DiffusionInferer(scheduler)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "### Model training of the Diffusion Model<br>\n", "We train our diffusion model for 4000 epochs.\\\n", "In every step, we concatenate the original MR image to the noisy segmentation mask, to predict a slightly denoised segmentation mask.\\\n", "This is described in Equation 7 of the paper https://arxiv.org/pdf/2112.03145.pdf."]}, {"cell_type": "code", "execution_count": 71, "metadata": {}, "outputs": [], "source": ["n_epochs = 4000\n", "val_interval = 50\n", "epoch_loss_list = []\n", "val_epoch_loss_list = []"]}, {"cell_type": "code", "execution_count": 72, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Epoch 0 Validation loss 0.95528511206309\n", "Epoch 50 Validation loss 0.012534369714558125\n", "Epoch 100 Validation loss 0.006971345127870639\n", "Epoch 150 Validation loss 0.007212877739220858\n", "Epoch 200 Validation loss 0.0032773815716306367\n", "Epoch 250 Validation loss 0.0032613773364573717\n", "Epoch 300 Validation loss 0.0027105089587469897\n", "Epoch 350 Validation loss 0.00643360164637367\n", "Epoch 400 Validation loss 0.004260187192509572\n", "Epoch 450 Validation loss 0.003338431240990758\n", "Epoch 500 Validation loss 0.003913956073423226\n", "Epoch 550 Validation loss 0.002972103344897429\n", "Epoch 600 Validation loss 0.004632922820746899\n", "Epoch 650 Validation loss 0.0021505119123806558\n", "Epoch 700 Validation loss 0.0031663976066435375\n", "Epoch 750 Validation loss 0.0030567607997606197\n", "Epoch 800 Validation loss 0.002882685783940057\n", "Epoch 850 Validation loss 0.0033122211073835692\n", "Epoch 900 Validation loss 0.002124195219948888\n", "Epoch 950 Validation loss 0.0046148050266007585\n", "Epoch 1000 Validation loss 0.0033069681376218796\n", "Epoch 1050 Validation loss 0.002037846017628908\n", "Epoch 1100 Validation loss 0.00229898770339787\n", "Epoch 1150 Validation loss 0.002420713659375906\n", "Epoch 1200 Validation loss 0.0041328890559573965\n", "Epoch 1250 Validation loss 0.004329038473467032\n", "Epoch 1300 Validation loss 0.0023907446302473545\n", "Epoch 1350 Validation loss 0.0030839802930131555\n", "Epoch 1400 Validation loss 0.0031827394074449935\n", "Epoch 1450 Validation loss 0.0028609122770527997\n", "Epoch 1500 Validation loss 0.0028457901595781245\n", "Epoch 1550 Validation loss 0.004309413023293018\n", "Epoch 1600 Validation loss 0.0026823601219803095\n", "Epoch 1650 Validation loss 0.0026449985646953187\n", "Epoch 1700 Validation loss 0.0023076763997475305\n", "Epoch 1750 Validation loss 0.002638093564504137\n", "Epoch 1800 Validation loss 0.0023806413325170674\n", "Epoch 1850 Validation loss 0.0018986108091970284\n", "Epoch 1900 Validation loss 0.0031037907659386597\n", "Epoch 1950 Validation loss 0.003802627402668198\n", "Epoch 2000 Validation loss 0.002883425874946018\n", "Epoch 2050 Validation loss 0.0025882223077739277\n", "Epoch 2100 Validation loss 0.0024797461228445172\n", "Epoch 2150 Validation loss 0.002770921913906932\n", "Epoch 2200 Validation loss 0.0031128532718867064\n", "Epoch 2250 Validation loss 0.0026554526605953774\n", "Epoch 2300 Validation loss 0.0006344413559418172\n", "Epoch 2350 Validation loss 0.003407757030799985\n", "Epoch 2400 Validation loss 0.003249160130508244\n", "Epoch 2450 Validation loss 0.0028327014297246933\n", "Epoch 2500 Validation loss 0.001949470800658067\n", "Epoch 2550 Validation loss 0.0037267633403340974\n", "Epoch 2600 Validation loss 0.0025921284686774015\n", "Epoch 2650 Validation loss 0.0028454426986475787\n", "Epoch 2700 Validation loss 0.0028132296477754912\n", "Epoch 2750 Validation loss 0.002190210895302395\n", "Epoch 2800 Validation loss 0.00311990175396204\n", "Epoch 2850 Validation loss 0.0022285515442490578\n", "Epoch 2900 Validation loss 0.0025012800081943474\n", "Epoch 2950 Validation loss 0.0019996101036667824\n", "Epoch 3000 Validation loss 0.002304922246063749\n", "Epoch 3050 Validation loss 0.002658801774183909\n", "Epoch 3100 Validation loss 0.0020781653001904488\n", "Epoch 3150 Validation loss 0.002127699709186951\n", "Epoch 3200 Validation loss 0.0021539490359524884\n", "Epoch 3250 Validation loss 0.003169172133008639\n", "Epoch 3300 Validation loss 0.002952027212207516\n", "Epoch 3350 Validation loss 0.0019326623684416215\n", "Epoch 3400 Validation loss 0.0023521527958412967\n", "Epoch 3450 Validation loss 0.0018178095730642478\n", "Epoch 3500 Validation loss 0.0019376981460178893\n", "Epoch 3550 Validation loss 0.003424471477046609\n", "Epoch 3600 Validation loss 0.001281890688308825\n", "Epoch 3650 Validation loss 0.00280005915556103\n", "Epoch 3700 Validation loss 0.002113828396735092\n", "Epoch 3750 Validation loss 0.0026302541761348643\n", "Epoch 3800 Validation loss 0.003950760544588168\n", "Epoch 3850 Validation loss 0.0018702246791993578\n", "Epoch 3900 Validation loss 0.003523522444690267\n", "Epoch 3950 Validation loss 0.003113662280763189\n", "train diffusion completed, total time: 11462.321615695953.\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["\n", "\n", "scaler = GradScaler()\n", "total_start = time.time()\n", "\n", "for epoch in range(n_epochs):\n", "    model.train()\n", "    epoch_loss = 0\n", "\n", "    for step, data in enumerate(train_loader):\n", "        images = data[\"image\"].to(device)\n", "        seg = data[\"label\"].to(device)  # this is the ground truth segmentation\n", "        optimizer.zero_grad(set_to_none=True)\n", "        timesteps = torch.randint(0, 1000, (len(images),)).to(device)  # pick a random time step t\n", "\n", "        with autocast(enabled=True):\n", "            # Generate random noise\n", "            noise = torch.randn_like(seg).to(device)\n", "            noisy_seg = scheduler.add_noise(\n", "                original_samples=seg, noise=noise, timesteps=timesteps\n", "            )  # we only add noise to the segmentation mask\n", "            combined = torch.cat(\n", "                (images, noisy_seg), dim=1\n", "            )  # we concatenate the brain MR image with the noisy segmenatation mask, to condition the generation process\n", "            prediction = model(x=combined, timesteps=timesteps)\n", "            # Get model prediction\n", "            loss = F.mse_loss(prediction.float(), noise.float())\n", "        scaler.scale(loss).backward()\n", "        scaler.step(optimizer)\n", "        scaler.update()\n", "        epoch_loss += loss.item()\n", "\n", "    epoch_loss_list.append(epoch_loss / (step + 1))\n", "    if (epoch) % val_interval == 0:\n", "        model.eval()\n", "        val_epoch_loss = 0\n", "        for step, data_val in enumerate(val_loader):\n", "            images = data_val[\"image\"].to(device)\n", "            seg = data_val[\"label\"].to(device)  # this is the ground truth segmentation\n", "            timesteps = torch.randint(0, 1000, (len(images),)).to(device)\n", "            with torch.no_grad():\n", "                with autocast(enabled=True):\n", "                    noise = torch.randn_like(seg).to(device)\n", "                    noisy_seg = scheduler.add_noise(original_samples=seg, noise=noise, timesteps=timesteps)\n", "                    combined = torch.cat((images, noisy_seg), dim=1)\n", "                    prediction = model(x=combined, timesteps=timesteps)\n", "                    val_loss = F.mse_loss(prediction.float(), noise.float())\n", "            val_epoch_loss += val_loss.item()\n", "        print(\"Epoch\", epoch, \"Validation loss\", val_epoch_loss / (step + 1))\n", "        val_epoch_loss_list.append(val_epoch_loss / (step + 1))\n", "\n", "torch.save(model.state_dict(), \"./segmodel.pt\")\n", "total_time = time.time() - total_start\n", "print(f\"train diffusion completed, total time: {total_time}.\")\n", "plt.style.use(\"seaborn-bright\")\n", "plt.title(\"Learning Curves Diffusion Model\", fontsize=20)\n", "plt.plot(np.linspace(1, n_epochs, n_epochs), epoch_loss_list, color=\"C0\", linewidth=2.0, label=\"Train\")\n", "plt.plot(\n", "    np.linspace(val_interval, n_epochs, int(n_epochs / val_interval)),\n", "    val_epoch_loss_list,\n", "    color=\"C1\",\n", "    linewidth=2.0,\n", "    label=\"Validation\",\n", ")\n", "plt.yticks(fontsize=12)\n", "plt.xticks(fontsize=12)\n", "plt.xlabel(\"Epochs\", fontsize=16)\n", "plt.ylabel(\"Loss\", fontsize=16)\n", "plt.legend(prop={\"size\": 14})\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "# Sampling of a new segmentation mask for an input image of the validation set<br>\n", "\n", "Starting from random noise, we want to generate a segmentation mask for a brain MR image of our validation set.\\\n", "Due to the stochastic generation process, we can sample an ensemble of n different segmentation masks per MR image.\\\n", "First, we pick an image of our validation set, and check the ground truth segmentation mask."]}, {"cell_type": "code", "execution_count": 120, "metadata": {"lines_to_end_of_cell_marker": 2}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAdUAAAHWCAYAAAAhLRNZAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjUuMywgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy/NK7nSAAAACXBIWXMAAA9hAAAPYQGoP6dpAAAH0ElEQVR4nO3dQW7CQBAAwTji/1/efABZ4LTXC646g7QKh9YcJrONMcYPAPBvv1c/AAC+hagCQERUASAiqgAQEVUAiIgqAEREFQAiogoAEVEFgMjj1Q9u23bmOwBgaa/8A0KTKgBERBUAIqIKABFRBYCIqAJARFQBICKqABARVQCIiCoAREQVACKiCgARUQWAiKgCQERUASAiqgAQEVUAiIgqAEREFQAiogoAEVEFgIioAkBEVAEgIqoAEBFVAIiIKgBERBUAIqIKABFRBYCIqAJARFQBICKqABARVQCIiCoAREQVACKiCgARUQWAiKgCQERUASAiqgAQEVUAiIgqAEREFQAiogoAEVEFgIioAkBEVAEgIqoAEBFVAIiIKgBERBUAIqIKABFRBYCIqAJARFQBICKqABARVQCIiCoAREQVACKiCgARUQWAiKgCQERUASAiqgAQEVUAiIgqAEREFQAiogoAEVEFgIioAkBEVAEgIqoAEBFVAIiIKgBERBUAIqIKABFRBYCIqAJARFQBICKqABARVQCIiCoAREQVACKiCgARUQWAiKgCQERUASAiqgAQEVUAiIgqAEREFQAiogoAEVEFgIioAkBEVAEgIqoAEBFVAIiIKgBERBUAIqIKABFRBYCIqAJARFQBICKqABARVQCIiCoAREQVACKiCgARUQWAiKgCQERUASAiqgAQEVUAiIgqAEREFQAiogoAEVEFgIioAkBEVAEgIqoAEBFVAIiIKgBERBUAIqIKABFRBYCIqAJARFQBICKqABARVQCIiCoAREQVACKiCgARUQWAiKgCQERUASAiqgAQEVUAiIgqAEREFQAiogoAEVEFgIioAkBEVAEgIqoAEBFVAIiIKgBERBUAIqIKABFRBYCIqAJARFQBICKqABARVQCIiCoAREQVACKiCgARUQWAiKgCQERUASAiqgAQEVUAiIgqAEREFQAiogoAEVEFgIioAkBEVAEgIqoAEBFVAIiIKgBERBUAIqIKABFRBYCIqAJARFQBICKqABARVQCIiCoAREQVACKiCgARUQWAiKgCQERUASAiqgAQEVUAiIgqAEREFQAiogoAEVEFgIioAkBEVAEgIqoAEBFVAIiIKgBERBUAIqIKABFRBYCIqAJARFQBICKqABARVQCIiCoAREQVACKiCgARUQWAiKgCQERUASAiqgAQEVUAiIgqAEREFQAiogoAEVEFgIioAkBEVAEgIqoAEBFVAIiIKgBERBUAIqIKABFRBYCIqAJARFQBICKqABARVQCIiCoAREQVACKiCgARUQWAiKgCQERUASAiqgAQEVUAiIgqAEREFQAiogoAEVEFgIioAkBEVAEg8rj6AcC5xhhXP2FJ27Zd/QS+kEkVACKiCgARUQWAiKgCQERUASAiqgAQsVIDC7H+Ms/e39q6DUeZVAEgIqoAEBFVAIiIKgBERBUAIqIKABFRBYCIqAJARFQBICKqABARVQCIiCoAREQVACKu1MBksy/RzL64stKlHddmmM2kCgARUQWAiKgCQERUASAiqgAQEVUAiFipgS93dMXl6DrK3vdmvwVmM6kCQERUASAiqgAQEVUAiIgqAEREFQAiVmq4PWsez610bcZvxKcwqQJARFQBICKqABARVQCIiCoAREQVACJWariFM9ZDVlo5AdZgUgWAiKgCQERUASAiqgAQEVUAiIgqAESs1HALe9dKrMY858ILvM+kCgARUQWAiKgCQERUASAiqgAQEVUAiFip4fbuvG5jbQZaJlUAiIgqAEREFQAiogoAEVEFgIioAkDESg3suPO6DfA+kyoAREQVACKiCgARUQWAiKgCQERUASBipQYOmn3hxQoPrM+kCgARUQWAiKgCQERUASAiqgAQEVUAiFipgQ/hYg6sz6QKABFRBYCIqAJARFQBICKqABARVQCIWKmBLzD7Yg7wnEkVACKiCgARUQWAiKgCQERUASAiqgAQEVUAiIgqAEREFQAiogoAEVEFgIioAkBEVAEgIqoAEBFVAIiIKgBERBUAIqIKABFRBYCIqAJARFQBICKqABARVQCIiCoAREQVACKiCgARUQWAiKgCQERUASAiqgAQEVUAiIgqAEREFQAiogoAEVEFgIioAkBEVAEgIqoAEBFVAIiIKgBERBUAIqIKABFRBYCIqAJARFQBICKqABARVQCIiCoAREQVACKiCgARUQWAiKgCQERUASAiqgAQEVUAiIgqAEREFQAiogoAEVEFgIioAkBEVAEgIqoAEBFVAIiIKgBERBUAIqIKABFRBYCIqAJARFQBICKqABARVQCIiCoAREQVACKiCgARUQWAiKgCQERUASAiqgAQEVUAiIgqAEREFQAiogoAEVEFgIioAkBEVAEgIqoAEBFVAIiIKgBERBUAIqIKABFRBYCIqAJARFQBICKqABARVQCIiCoAREQVACKiCgARUQWAiKgCQERUASAiqgAQEVUAiIgqAEREFQAiogoAEVEFgIioAkBEVAEgIqoAEBFVAIiIKgBERBUAIqIKABFRBYCIqAJARFQBICKqABARVQCIiCoAREQVACKiCgARUQWAiKgCQERUASAiqgAQebz6wTHGme8AgI9nUgWAiKgCQERUASAiqgAQEVUAiIgqAEREFQAiogoAEVEFgMgfibtAwXYFF6YAAAAASUVORK5CYII=\n", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["DiffusionModelUNet(\n", "  (conv_in): Convolution(\n", "    (conv): Conv2d(2, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "  )\n", "  (time_embed): Sequential(\n", "    (0): Linear(in_features=64, out_features=256, bias=True)\n", "    (1): SiLU()\n", "    (2): Linear(in_features=256, out_features=256, bias=True)\n", "  )\n", "  (down_blocks): ModuleList(\n", "    (0): DownBlock(\n", "      (resnets): ModuleList(\n", "        (0): ResnetBlock(\n", "          (norm1): GroupNorm(32, 64, eps=1e-06, affine=True)\n", "          (nonlinearity): SiLU()\n", "          (conv1): Convolution(\n", "            (conv): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          )\n", "          (time_emb_proj): Linear(in_features=256, out_features=64, bias=True)\n", "          (norm2): GroupNorm(32, 64, eps=1e-06, affine=True)\n", "          (conv2): Convolution(\n", "            (conv): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          )\n", "          (skip_connection): Identity()\n", "        )\n", "      )\n", "      (downsampler): Downsample(\n", "        (op): Convolution(\n", "          (conv): Conv2d(64, 64, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1))\n", "        )\n", "      )\n", "    )\n", "    (1): DownBlock(\n", "      (resnets): ModuleList(\n", "        (0): ResnetBlock(\n", "          (norm1): GroupNorm(32, 64, eps=1e-06, affine=True)\n", "          (nonlinearity): SiLU()\n", "          (conv1): Convolution(\n", "            (conv): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          )\n", "          (time_emb_proj): Linear(in_features=256, out_features=64, bias=True)\n", "          (norm2): GroupNorm(32, 64, eps=1e-06, affine=True)\n", "          (conv2): Convolution(\n", "            (conv): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          )\n", "          (skip_connection): Identity()\n", "        )\n", "      )\n", "      (downsampler): Downsample(\n", "        (op): Convolution(\n", "          (conv): Conv2d(64, 64, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1))\n", "        )\n", "      )\n", "    )\n", "    (2): AttnDownBlock(\n", "      (attentions): <PERSON><PERSON>le<PERSON><PERSON>(\n", "        (0): AttentionBlock(\n", "          (norm): GroupNorm(32, 64, eps=1e-06, affine=True)\n", "          (to_q): Linear(in_features=64, out_features=64, bias=True)\n", "          (to_k): Linear(in_features=64, out_features=64, bias=True)\n", "          (to_v): Linear(in_features=64, out_features=64, bias=True)\n", "          (proj_attn): Linear(in_features=64, out_features=64, bias=True)\n", "        )\n", "      )\n", "      (resnets): ModuleList(\n", "        (0): ResnetBlock(\n", "          (norm1): GroupNorm(32, 64, eps=1e-06, affine=True)\n", "          (nonlinearity): SiLU()\n", "          (conv1): Convolution(\n", "            (conv): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          )\n", "          (time_emb_proj): Linear(in_features=256, out_features=64, bias=True)\n", "          (norm2): GroupNorm(32, 64, eps=1e-06, affine=True)\n", "          (conv2): Convolution(\n", "            (conv): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          )\n", "          (skip_connection): Identity()\n", "        )\n", "      )\n", "    )\n", "  )\n", "  (middle_block): AttnMidBlock(\n", "    (resnet_1): ResnetBlock(\n", "      (norm1): GroupNorm(32, 64, eps=1e-06, affine=True)\n", "      (nonlinearity): SiLU()\n", "      (conv1): Convolution(\n", "        (conv): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "      )\n", "      (time_emb_proj): Linear(in_features=256, out_features=64, bias=True)\n", "      (norm2): GroupNorm(32, 64, eps=1e-06, affine=True)\n", "      (conv2): Convolution(\n", "        (conv): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "      )\n", "      (skip_connection): Identity()\n", "    )\n", "    (attention): AttentionBlock(\n", "      (norm): GroupNorm(32, 64, eps=1e-06, affine=True)\n", "      (to_q): Linear(in_features=64, out_features=64, bias=True)\n", "      (to_k): Linear(in_features=64, out_features=64, bias=True)\n", "      (to_v): Linear(in_features=64, out_features=64, bias=True)\n", "      (proj_attn): Linear(in_features=64, out_features=64, bias=True)\n", "    )\n", "    (resnet_2): ResnetBlock(\n", "      (norm1): GroupNorm(32, 64, eps=1e-06, affine=True)\n", "      (nonlinearity): SiLU()\n", "      (conv1): Convolution(\n", "        (conv): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "      )\n", "      (time_emb_proj): Linear(in_features=256, out_features=64, bias=True)\n", "      (norm2): GroupNorm(32, 64, eps=1e-06, affine=True)\n", "      (conv2): Convolution(\n", "        (conv): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "      )\n", "      (skip_connection): Identity()\n", "    )\n", "  )\n", "  (up_blocks): ModuleList(\n", "    (0): AttnUpBlock(\n", "      (resnets): ModuleList(\n", "        (0): ResnetBlock(\n", "          (norm1): GroupNorm(32, 128, eps=1e-06, affine=True)\n", "          (nonlinearity): SiLU()\n", "          (conv1): Convolution(\n", "            (conv): Conv2d(128, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          )\n", "          (time_emb_proj): Linear(in_features=256, out_features=64, bias=True)\n", "          (norm2): GroupNorm(32, 64, eps=1e-06, affine=True)\n", "          (conv2): Convolution(\n", "            (conv): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          )\n", "          (skip_connection): Convolution(\n", "            (conv): Conv2d(128, 64, kernel_size=(1, 1), stride=(1, 1))\n", "          )\n", "        )\n", "        (1): ResnetBlock(\n", "          (norm1): GroupNorm(32, 128, eps=1e-06, affine=True)\n", "          (nonlinearity): SiLU()\n", "          (conv1): Convolution(\n", "            (conv): Conv2d(128, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          )\n", "          (time_emb_proj): Linear(in_features=256, out_features=64, bias=True)\n", "          (norm2): GroupNorm(32, 64, eps=1e-06, affine=True)\n", "          (conv2): Convolution(\n", "            (conv): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          )\n", "          (skip_connection): Convolution(\n", "            (conv): Conv2d(128, 64, kernel_size=(1, 1), stride=(1, 1))\n", "          )\n", "        )\n", "      )\n", "      (attentions): <PERSON><PERSON>le<PERSON><PERSON>(\n", "        (0): AttentionBlock(\n", "          (norm): GroupNorm(32, 64, eps=1e-06, affine=True)\n", "          (to_q): Linear(in_features=64, out_features=64, bias=True)\n", "          (to_k): Linear(in_features=64, out_features=64, bias=True)\n", "          (to_v): Linear(in_features=64, out_features=64, bias=True)\n", "          (proj_attn): Linear(in_features=64, out_features=64, bias=True)\n", "        )\n", "        (1): AttentionBlock(\n", "          (norm): GroupNorm(32, 64, eps=1e-06, affine=True)\n", "          (to_q): Linear(in_features=64, out_features=64, bias=True)\n", "          (to_k): Linear(in_features=64, out_features=64, bias=True)\n", "          (to_v): Linear(in_features=64, out_features=64, bias=True)\n", "          (proj_attn): Linear(in_features=64, out_features=64, bias=True)\n", "        )\n", "      )\n", "      (upsampler): Up<PERSON><PERSON>(\n", "        (conv): Convolution(\n", "          (conv): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "        )\n", "      )\n", "    )\n", "    (1): UpBlock(\n", "      (resnets): ModuleList(\n", "        (0): ResnetBlock(\n", "          (norm1): GroupNorm(32, 128, eps=1e-06, affine=True)\n", "          (nonlinearity): SiLU()\n", "          (conv1): Convolution(\n", "            (conv): Conv2d(128, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          )\n", "          (time_emb_proj): Linear(in_features=256, out_features=64, bias=True)\n", "          (norm2): GroupNorm(32, 64, eps=1e-06, affine=True)\n", "          (conv2): Convolution(\n", "            (conv): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          )\n", "          (skip_connection): Convolution(\n", "            (conv): Conv2d(128, 64, kernel_size=(1, 1), stride=(1, 1))\n", "          )\n", "        )\n", "        (1): ResnetBlock(\n", "          (norm1): GroupNorm(32, 128, eps=1e-06, affine=True)\n", "          (nonlinearity): SiLU()\n", "          (conv1): Convolution(\n", "            (conv): Conv2d(128, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          )\n", "          (time_emb_proj): Linear(in_features=256, out_features=64, bias=True)\n", "          (norm2): GroupNorm(32, 64, eps=1e-06, affine=True)\n", "          (conv2): Convolution(\n", "            (conv): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          )\n", "          (skip_connection): Convolution(\n", "            (conv): Conv2d(128, 64, kernel_size=(1, 1), stride=(1, 1))\n", "          )\n", "        )\n", "      )\n", "      (upsampler): Up<PERSON><PERSON>(\n", "        (conv): Convolution(\n", "          (conv): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "        )\n", "      )\n", "    )\n", "    (2): UpBlock(\n", "      (resnets): ModuleList(\n", "        (0): ResnetBlock(\n", "          (norm1): GroupNorm(32, 128, eps=1e-06, affine=True)\n", "          (nonlinearity): SiLU()\n", "          (conv1): Convolution(\n", "            (conv): Conv2d(128, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          )\n", "          (time_emb_proj): Linear(in_features=256, out_features=64, bias=True)\n", "          (norm2): GroupNorm(32, 64, eps=1e-06, affine=True)\n", "          (conv2): Convolution(\n", "            (conv): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          )\n", "          (skip_connection): Convolution(\n", "            (conv): Conv2d(128, 64, kernel_size=(1, 1), stride=(1, 1))\n", "          )\n", "        )\n", "        (1): ResnetBlock(\n", "          (norm1): GroupNorm(32, 128, eps=1e-06, affine=True)\n", "          (nonlinearity): SiLU()\n", "          (conv1): Convolution(\n", "            (conv): Conv2d(128, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          )\n", "          (time_emb_proj): Linear(in_features=256, out_features=64, bias=True)\n", "          (norm2): GroupNorm(32, 64, eps=1e-06, affine=True)\n", "          (conv2): Convolution(\n", "            (conv): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          )\n", "          (skip_connection): Convolution(\n", "            (conv): Conv2d(128, 64, kernel_size=(1, 1), stride=(1, 1))\n", "          )\n", "        )\n", "      )\n", "    )\n", "  )\n", "  (out): Sequential(\n", "    (0): GroupNorm(32, 64, eps=1e-06, affine=True)\n", "    (1): SiLU()\n", "    (2): Convolution(\n", "      (conv): Conv2d(64, 1, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "    )\n", "  )\n", ")"]}, "execution_count": 120, "metadata": {}, "output_type": "execute_result"}], "source": ["idx = 0\n", "data = val_ds[idx]\n", "inputimg = data[\"image\"][0, ...]  # Pick an input slice of the validation set to be segmented\n", "inputlabel = data[\"label\"][0, ...]  # Check out the ground truth label mask. If it is empty, pick another input slice.\n", "\n", "\n", "plt.figure(\"input\" + str(inputlabel))\n", "plt.imshow(inputimg, vmin=0, vmax=1, cmap=\"gray\")\n", "plt.axis(\"off\")\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "plt.figure(\"input\" + str(inputlabel))\n", "plt.imshow(inputlabel, vmin=0, vmax=1, cmap=\"gray\")\n", "plt.axis(\"off\")\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "\n", "model.eval()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Then we set the number of samples in the ensemble n. \\\n", "Starting from the input image (which ist the brain MR image), we follow Algorithm 1 of the paper \"Diffusion Models for Implicit Image Segmentation Ensembles\" (https://arxiv.org/pdf/2112.03145.pdf) n times.\\\n", "This gives us an ensemble of n different predicted segmentation masks."]}, {"cell_type": "code", "execution_count": 122, "metadata": {"lines_to_next_cell": 2}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|████████████████████████████████████████████████████████████████████████████████████████████| 1000/1000 [00:19<00:00, 52.45it/s]\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["100%|████████████████████████████████████████████████████████████████████████████████████████████| 1000/1000 [00:18<00:00, 52.95it/s]\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["100%|████████████████████████████████████████████████████████████████████████████████████████████| 1000/1000 [00:18<00:00, 53.05it/s]\n"]}, {"data": {"image/png": "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********************************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\n", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["100%|████████████████████████████████████████████████████████████████████████████████████████████| 1000/1000 [00:18<00:00, 52.93it/s]\n"]}, {"data": {"image/png": "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******************************************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\n", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["100%|████████████████████████████████████████████████████████████████████████████████████████████| 1000/1000 [00:18<00:00, 53.06it/s]\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["n = 5\n", "input_img = inputimg[None, None, ...].to(device)\n", "ensemble = []\n", "for k in range(5):\n", "    noise = torch.randn_like(input_img).to(device)\n", "    current_img = noise  # for the segmentation mask, we start from random noise.\n", "    combined = torch.cat(\n", "        (input_img, noise), dim=1\n", "    )  # We concatenate the input brain MR image to add anatomical information.\n", "\n", "    scheduler.set_timesteps(num_inference_steps=1000)\n", "    progress_bar = tqdm(scheduler.timesteps)\n", "    chain = torch.zeros(current_img.shape)\n", "    for t in progress_bar:  # go through the noising process\n", "        with autocast(enabled=False):\n", "            with torch.no_grad():\n", "                model_output = model(combined, timesteps=torch.Tensor((t,)).to(current_img.device))\n", "                current_img, _ = scheduler.step(\n", "                    model_output, t, current_img\n", "                )  # this is the prediction x_t at the time step t\n", "                if t % 100 == 0:\n", "                    chain = torch.cat((chain, current_img.cpu()), dim=-1)\n", "                combined = torch.cat(\n", "                    (input_img, current_img), dim=1\n", "                )  # in every step during the denoising process, the brain MR image is concatenated to add anatomical information\n", "\n", "    plt.style.use(\"default\")\n", "    plt.imshow(chain[0, 0, ..., 64:].cpu(), vmin=0, vmax=1, cmap=\"gray\")\n", "    plt.tight_layout()\n", "    plt.axis(\"off\")\n", "    plt.show()\n", "    ensemble.append(current_img)  # this is the output of the diffusion model after T=1000 denoising steps"]}, {"cell_type": "markdown", "metadata": {"lines_to_next_cell": 2}, "source": ["\n", "## Segmentation prediction\n", "The predicted segmentation mask is obtained from the output of the diffusion model by thresholding.\\\n", "We compute the Dice score for all predicted segmentations of the ensemble, as well as the pixel-wise mean and the variance map over the ensemble.\\\n", "As shown in the paper \"Diffusion Models for Implicit Image Segmentation Ensembles\" (https://arxiv.org/abs/2112.03145), we see that taking the mean over n=5 samples improves the segmentation performance.\\\n", "The variance maps highlights pixels where the model is unsure about it's own prediction.\n", "\n"]}, {"cell_type": "code", "execution_count": 123, "metadata": {}, "outputs": [], "source": ["def dice_coeff(im1, im2, empty_score=1.0):\n", "    im1 = np.asarray(im1).astype(bool)\n", "    im2 = np.asarray(im2).astype(bool)\n", "\n", "    im_sum = im1.sum() + im2.sum()\n", "    if im_sum == 0:\n", "        return empty_score\n", "\n", "    # Compute Dice coefficient\n", "    intersection = np.logical_and(im1, im2)\n", "\n", "    return 2.0 * intersection.sum() / im_sum"]}, {"cell_type": "code", "execution_count": 128, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Dice score of sample0 0.8455882352941176\n", "Dice score of sample1 0.860655737704918\n", "Dice score of sample2 0.8475836431226765\n", "Dice score of sample3 0.8820960698689956\n", "Dice score of sample4 0.8627450980392157\n", "Dice score on the mean map 0.889763779527559\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAbsAAAG7CAYAAABaaTseAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjUuMywgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy/NK7nSAAAACXBIWXMAAA9hAAAPYQGoP6dpAAAIHUlEQVR4nO3dS3LiSBRAUdHhHZk1wZpgTbAm9aBraGXLKj721TlDBK60B3UjI14mh3me5wkAwv559wIA4NnEDoA8sQMgT+wAyBM7APLEDoA8sQMgT+wAyPtY+8bD4fDMdQDAJmvuRrGzAyBP7ADIEzsA8sQOgDyxAyBP7ADIEzsA8sQOgDyxAyBP7ADIEzsA8sQOgDyxAyBP7ADIEzsA8sQOgDyxAyBP7ADIEzsA8sQOgDyxAyBP7ADIEzsA8sQOgDyxAyBP7ADIEzsA8sQOgDyxAyBP7ADIEzsA8sQOgDyxAyBP7ADIEzsA8sQOgDyxAyBP7ADIEzsA8sQOgDyxAyBP7ADIEzsA8sQOgDyxAyBP7ADIEzsA8sQOgDyxAyBP7ADIEzsA8sQOgDyxAyBP7ADIEzsA8sQOgDyxAyBP7ADIEzsA8sQOgDyxAyBP7ADIEzsA8sQOgDyxAyBP7ADIEzsA8sQOgDyxAyBP7ADIEzsA8sQOgDyxAyBP7ADIEzsA8sQOgDyxAyBP7ADIEzsA8sQOgDyxAyBP7ADIEzsA8sQOgDyxAyBP7ADIEzsA8sQOgDyxAyBP7ADIEzsA8sQOgDyxAyBP7ADIEzsA8sQOgDyxAyBP7ADIEzsA8sQOgDyxAyBP7ADIEzsA8sQOgDyxAyBP7ADIEzsA8sQOgDyxAyBP7ADIEzsA8sQOgDyxAyBP7ADIEzsA8sQOgDyxAyBP7ADIEzsA8sQOgDyxAyBP7ADIEzsA8sQOgDyxAyBP7ADIEzsA8sQOgDyxAyBP7ADIEzsA8sQOgDyxAyBP7ADIEzsA8sQOgDyxAyBP7ADIEzsA8sQOgDyxAyBP7ADIEzsA8sQOgDyxAyBP7ADIEzsA8sQOgDyxAyBP7ADIEzsA8sQOgDyxAyBP7ADIEzsA8sQOgDyxAyBP7ADIEzsA8sQOgDyxAyBP7ADIEzsA8sQOgDyxAyBP7ADIEzsA8sQOgDyxAyBP7ADIEzsA8sQOgDyxAyBP7ADIEzsA8sQOgDyxAyBP7ADIEzsA8sQOgDyxAyBP7ADIEzsA8sQOgDyxAyBP7ADIEzsA8sQOgDyxAyBP7ADIEzsA8sQOgDyxAyBP7ADIEzsA8sQOgDyxAyBP7ADIEzsA8sQOgDyxAyBP7ADIEzsA8sQOgDyxAyBP7ADIEzsA8sQOgDyxAyBP7ADIEzsA8j7evQBgvz4/Pxef3e/3F66EOjs7APLEDoA8sQMgT+wAyBM7APLEDoA8Rw9gp06n07efjY4DjI4RXK/Xb/88eCQ7OwDyxA6APLEDIE/sAMgTOwDyDvM8z6veeDg8ey3AtDwJeblcXryS9zqfz1++vjTZyX6tyZidHQB5YgdAntgBkCd2AOSJHQB5YgdAnoug4S8tHRUYXbQ8ujSZ//gb8Uh2dgDkiR0AeWIHQJ7YAZAndgDkuQgaVhhNVu7tguZ3Ox6Pi8/u9/sLV8JP4SJoAJjEDoAdEDsA8sQOgDyxAyBP7ADIcxE0rDAaab9er1++Pjqu8Og1LF2avOUzr7Tl7+p4AVvY2QGQJ3YA5IkdAHliB0Ce2AGQJ3YA5Dl6AH9sHcVfGpH/CaP9I48e4R/9vKVnWz4DW9jZAZAndgDkiR0AeWIHQJ7YAZB3mOd5XvXGw+HZa4GHud1ui8+2TEkuTVyOjP6d4qTm6Hfaclm2/3NYa03G7OwAyBM7APLEDoA8sQMgT+wAyBM7APIcPeDH2zLCf7lcnrUcHmh0xOF4PL5wJfxmjh4AwCR2AOyA2AGQJ3YA5IkdAHliB0Dex7sXAP9nNJ7+07894N22fHvB6HNb/95L33qw5dskYAs7OwDyxA6APLEDIE/sAMgTOwDyXARN0u12W3xWnOBcmp4cTTtumdTcOt0Jz+QiaACYxA6AHRA7APLEDoA8sQMgT+wAyHMRNEnH43Hx2dLRg8vl8u3P/Gaj33fpyIKjB/xWdnYA5IkdAHliB0Ce2AGQJ3YA5LkIGv7S6XT61uvT9PjpzvP5/OXro4ugR2swdclv4iJoAJjEDoAdEDsA8sQOgDyxAyBP7ADIc/QAnmTr8YKlC5pHxwGWjh7AHjh6AACT2AGwA2IHQJ7YAZAndgDkiR0AeY4eAPCrOXoAAJPYAbADYgdAntgBkCd2AOSJHQB5YgdAntgBkCd2AOSJHQB5YgdAntgBkCd2AOSJHQB5YgdAntgBkCd2AOSJHQB5YgdAntgBkCd2AOSJHQB5YgdAntgBkCd2AOSJHQB5YgdAntgBkCd2AOSJHQB5YgdAntgBkCd2AOSJHQB5YgdAntgBkCd2AOSJHQB5YgdAntgBkCd2AOSJHQB5YgdAntgBkCd2AOSJHQB5YgdAntgBkCd2AOSJHQB5YgdAntgBkCd2AOSJHQB5YgdAntgBkCd2AOSJHQB5YgdAntgBkCd2AOSJHQB5YgdAntgBkCd2AOSJHQB5YgdAntgBkCd2AOSJHQB5YgdAntgBkCd2AOSJHQB5YgdAntgBkCd2AOSJHQB5YgdAntgBkCd2AOSJHQB5YgdAntgBkCd2AOSJHQB5YgdAntgBkCd2AOSJHQB5YgdAntgBkCd2AOSJHQB5YgdAntgBkCd2AOSJHQB5YgdAntgBkCd2AOSJHQB5YgdAntgBkCd2AOSJHQB5YgdAntgBkCd2AOSJHQB5YgdAntgBkCd2AOSJHQB5YgdAntgBkCd2AOSJHQB5H2vfOM/zM9cBAE9jZwdAntgBkCd2AOSJHQB5YgdAntgBkCd2AOSJHQB5YgdA3r8AHsUqzMqIOwAAAABJRU5ErkJggg==\n", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAbsAAAG7CAYAAABaaTseAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjUuMywgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy/NK7nSAAAACXBIWXMAAA9hAAAPYQGoP6dpAAAIDElEQVR4nO3dPXLrNhhAUTLj5b3iLcIL4UKyiFd4f0iVEpDCUP65PKeUKBl2cwczH+B9j<PERSON>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\n", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["for i in range(len(ensemble)):\n", "    prediction = torch.where(ensemble[i] > 0.5, 1, 0).float()  # a binary mask is obtained via thresholding\n", "    score = dice_coeff(\n", "        prediction[0, 0].cpu(), inputlabel.cpu()\n", "    )  # we compute the dice scores for all samples separately\n", "    print(\"Dice score of sample\" + str(i), score)\n", "\n", "\n", "E = torch.where(torch.cat(ensemble) > 0.5, 1, 0).float()\n", "var = torch.var(E, dim=0)  # pixel-wise variance map over the ensemble\n", "mean = torch.mean(E, dim=0)  # pixel-wise mean map over the ensemble\n", "mean_prediction = torch.where(mean > 0.5, 1, 0).float()\n", "\n", "score = dice_coeff(mean_prediction[0, ...].cpu(), inputlabel.cpu())  # Here we predict the Dice score for the mean map\n", "print(\"Dice score on the mean map\", score)\n", "\n", "plt.style.use(\"default\")\n", "plt.imshow(mean[0, ...].cpu(), vmin=0, vmax=1, cmap=\"gray\")  # We plot the mean map\n", "plt.tight_layout()\n", "plt.axis(\"off\")\n", "plt.show()\n", "plt.style.use(\"default\")\n", "plt.imshow(var[0, ...].cpu(), vmin=0, vmax=1, cmap=\"jet\")  # We plot the variance map\n", "plt.tight_layout()\n", "plt.axis(\"off\")\n", "plt.show()"]}], "metadata": {"jupytext": {"formats": "ipynb,py:light"}, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.13"}}, "nbformat": 4, "nbformat_minor": 4}