"""
可视化工具
用于训练过程中的图像对比展示和保存
"""

import os
import torch
import numpy as np
import matplotlib.pyplot as plt
from typing import List, Optional, Tuple
import torchvision.utils as vutils
from PIL import Image


def denormalize_for_display(image: torch.Tensor) -> torch.Tensor:
    """
    将图像从[-1,1]或[0,1]范围转换为显示范围[0,1]
    
    Args:
        image: 输入图像张量
    
    Returns:
        显示用的图像张量
    """
    if image.min() < 0:
        # 从[-1,1]转换到[0,1]
        image = (image + 1) / 2
    
    # 确保在[0,1]范围内
    image = torch.clamp(image, 0, 1)
    return image


def create_comparison_grid(
    bus_images: torch.Tensor,
    target_images: torch.Tensor,
    generated_images: torch.Tensor,
    num_samples: int = 4,
    figsize: Tuple[int, int] = (15, 12)
) -> plt.Figure:
    """
    创建BUS-目标-生成图像的对比网格
    
    Args:
        bus_images: BUS图像 [B, C, H, W]
        target_images: 目标CEUS图像 [B, C, H, W]
        generated_images: 生成的CEUS图像 [B, C, H, W]
        num_samples: 显示的样本数量
        figsize: 图像尺寸
    
    Returns:
        matplotlib图像对象
    """
    # 限制样本数量
    num_samples = min(num_samples, bus_images.shape[0])
    
    # 准备显示的图像
    bus_display = denormalize_for_display(bus_images[:num_samples])
    target_display = denormalize_for_display(target_images[:num_samples])
    generated_display = denormalize_for_display(generated_images[:num_samples])
    
    # 创建图像网格
    fig, axes = plt.subplots(num_samples, 3, figsize=figsize)
    if num_samples == 1:
        axes = axes.reshape(1, -1)
    
    for i in range(num_samples):
        # BUS图像
        if bus_display.shape[1] == 1:  # 灰度图像
            axes[i, 0].imshow(bus_display[i, 0].cpu().numpy(), cmap='gray', vmin=0, vmax=1)
        else:  # 彩色图像
            axes[i, 0].imshow(bus_display[i].permute(1, 2, 0).cpu().numpy())
        axes[i, 0].set_title(f'BUS Image {i+1}')
        axes[i, 0].axis('off')
        
        # 目标CEUS图像
        if target_display.shape[1] == 1:  # 灰度图像
            axes[i, 1].imshow(target_display[i, 0].cpu().numpy(), cmap='gray', vmin=0, vmax=1)
        else:  # 彩色图像
            axes[i, 1].imshow(target_display[i].permute(1, 2, 0).cpu().numpy())
        axes[i, 1].set_title(f'Target CEUS {i+1}')
        axes[i, 1].axis('off')
        
        # 生成的CEUS图像
        if generated_display.shape[1] == 1:  # 灰度图像
            axes[i, 2].imshow(generated_display[i, 0].cpu().numpy(), cmap='gray', vmin=0, vmax=1)
        else:  # 彩色图像
            axes[i, 2].imshow(generated_display[i].permute(1, 2, 0).cpu().numpy())
        axes[i, 2].set_title(f'Generated CEUS {i+1}')
        axes[i, 2].axis('off')
    
    plt.tight_layout()
    return fig


def save_comparison_images(
    bus_images: torch.Tensor,
    target_images: torch.Tensor,
    generated_images: torch.Tensor,
    save_path: str,
    epoch: int,
    num_samples: int = 8
):
    """
    保存对比图像
    
    Args:
        bus_images: BUS图像
        target_images: 目标CEUS图像
        generated_images: 生成的CEUS图像
        save_path: 保存路径
        epoch: 当前epoch
        num_samples: 保存的样本数量
    """
    os.makedirs(save_path, exist_ok=True)
    
    # 创建对比图像
    fig = create_comparison_grid(
        bus_images, target_images, generated_images, 
        num_samples=num_samples, figsize=(20, num_samples * 3)
    )
    
    # 保存图像
    save_file = os.path.join(save_path, f'comparison_epoch_{epoch:04d}.png')
    fig.savefig(save_file, dpi=150, bbox_inches='tight')
    plt.close(fig)
    
    print(f"Comparison images saved to {save_file}")


def create_training_progress_plot(
    train_losses: List[float],
    val_losses: List[float],
    train_metrics: dict,
    val_metrics: dict,
    save_path: str
):
    """
    创建训练进度图
    
    Args:
        train_losses: 训练损失列表
        val_losses: 验证损失列表
        train_metrics: 训练指标字典
        val_metrics: 验证指标字典
        save_path: 保存路径
    """
    fig, axes = plt.subplots(2, 3, figsize=(18, 10))
    
    epochs = range(1, len(train_losses) + 1)
    
    # 损失曲线
    axes[0, 0].plot(epochs, train_losses, 'b-', label='Train Loss')
    axes[0, 0].plot(epochs, val_losses, 'r-', label='Val Loss')
    axes[0, 0].set_title('Training and Validation Loss')
    axes[0, 0].set_xlabel('Epoch')
    axes[0, 0].set_ylabel('Loss')
    axes[0, 0].legend()
    axes[0, 0].grid(True)
    
    # PSNR曲线
    if 'psnr' in train_metrics:
        axes[0, 1].plot(epochs, train_metrics['psnr'], 'b-', label='Train PSNR')
        axes[0, 1].plot(epochs, val_metrics['psnr'], 'r-', label='Val PSNR')
        axes[0, 1].set_title('PSNR')
        axes[0, 1].set_xlabel('Epoch')
        axes[0, 1].set_ylabel('PSNR (dB)')
        axes[0, 1].legend()
        axes[0, 1].grid(True)
    
    # SSIM曲线
    if 'ssim' in train_metrics:
        axes[0, 2].plot(epochs, train_metrics['ssim'], 'b-', label='Train SSIM')
        axes[0, 2].plot(epochs, val_metrics['ssim'], 'r-', label='Val SSIM')
        axes[0, 2].set_title('SSIM')
        axes[0, 2].set_xlabel('Epoch')
        axes[0, 2].set_ylabel('SSIM')
        axes[0, 2].legend()
        axes[0, 2].grid(True)
    
    # MAE曲线
    if 'mae' in train_metrics:
        axes[1, 0].plot(epochs, train_metrics['mae'], 'b-', label='Train MAE')
        axes[1, 0].plot(epochs, val_metrics['mae'], 'r-', label='Val MAE')
        axes[1, 0].set_title('MAE')
        axes[1, 0].set_xlabel('Epoch')
        axes[1, 0].set_ylabel('MAE')
        axes[1, 0].legend()
        axes[1, 0].grid(True)
    
    # MSE曲线
    if 'mse' in train_metrics:
        axes[1, 1].plot(epochs, train_metrics['mse'], 'b-', label='Train MSE')
        axes[1, 1].plot(epochs, val_metrics['mse'], 'r-', label='Val MSE')
        axes[1, 1].set_title('MSE')
        axes[1, 1].set_xlabel('Epoch')
        axes[1, 1].set_ylabel('MSE')
        axes[1, 1].legend()
        axes[1, 1].grid(True)
    
    # 学习率曲线（如果有的话）
    axes[1, 2].text(0.5, 0.5, 'Learning Rate\n(if available)', 
                   ha='center', va='center', transform=axes[1, 2].transAxes)
    axes[1, 2].set_title('Learning Rate')
    
    plt.tight_layout()
    
    # 保存图像
    os.makedirs(os.path.dirname(save_path), exist_ok=True)
    fig.savefig(save_path, dpi=150, bbox_inches='tight')
    plt.close(fig)
    
    print(f"Training progress plot saved to {save_path}")


def save_individual_images(
    images: torch.Tensor,
    save_dir: str,
    prefix: str = "image",
    epoch: Optional[int] = None
):
    """
    保存单独的图像文件
    
    Args:
        images: 图像张量 [B, C, H, W]
        save_dir: 保存目录
        prefix: 文件名前缀
        epoch: epoch编号（可选）
    """
    os.makedirs(save_dir, exist_ok=True)
    
    # 准备显示的图像
    images_display = denormalize_for_display(images)
    
    for i, img in enumerate(images_display):
        if epoch is not None:
            filename = f"{prefix}_epoch_{epoch:04d}_sample_{i:02d}.png"
        else:
            filename = f"{prefix}_sample_{i:02d}.png"
        
        save_path = os.path.join(save_dir, filename)
        
        # 转换为PIL图像并保存
        if img.shape[0] == 1:  # 灰度图像
            img_pil = Image.fromarray((img[0].cpu().numpy() * 255).astype(np.uint8), mode='L')
        else:  # 彩色图像
            img_pil = Image.fromarray((img.permute(1, 2, 0).cpu().numpy() * 255).astype(np.uint8))
        
        img_pil.save(save_path)


class TrainingVisualizer:
    """
    训练可视化器
    """
    
    def __init__(self, save_dir: str):
        self.save_dir = save_dir
        self.train_losses = []
        self.val_losses = []
        self.train_metrics = {'psnr': [], 'ssim': [], 'mae': [], 'mse': []}
        self.val_metrics = {'psnr': [], 'ssim': [], 'mae': [], 'mse': []}
        
        # 创建保存目录
        os.makedirs(save_dir, exist_ok=True)
        os.makedirs(os.path.join(save_dir, 'comparisons'), exist_ok=True)
        os.makedirs(os.path.join(save_dir, 'plots'), exist_ok=True)
    
    def update_metrics(
        self, 
        train_loss: float, 
        val_loss: float,
        train_metric_dict: dict,
        val_metric_dict: dict
    ):
        """更新指标"""
        self.train_losses.append(train_loss)
        self.val_losses.append(val_loss)
        
        for key in self.train_metrics:
            if key in train_metric_dict:
                self.train_metrics[key].append(train_metric_dict[key])
            if key in val_metric_dict:
                self.val_metrics[key].append(val_metric_dict[key])
    
    def save_comparison(
        self,
        bus_images: torch.Tensor,
        target_images: torch.Tensor,
        generated_images: torch.Tensor,
        epoch: int,
        num_samples: int = 8
    ):
        """保存对比图像"""
        save_comparison_images(
            bus_images, target_images, generated_images,
            os.path.join(self.save_dir, 'comparisons'),
            epoch, num_samples
        )
    
    def save_progress_plot(self):
        """保存训练进度图"""
        create_training_progress_plot(
            self.train_losses, self.val_losses,
            self.train_metrics, self.val_metrics,
            os.path.join(self.save_dir, 'plots', 'training_progress.png')
        )


if __name__ == "__main__":
    # 测试可视化功能
    device = "cuda" if torch.cuda.is_available() else "cpu"
    
    # 创建测试图像
    batch_size = 4
    bus_images = torch.randn(batch_size, 1, 256, 256).to(device)
    target_images = torch.randn(batch_size, 1, 256, 256).to(device)
    generated_images = torch.randn(batch_size, 1, 256, 256).to(device)
    
    # 测试对比图像创建
    fig = create_comparison_grid(bus_images, target_images, generated_images)
    plt.show()
    plt.close()
    
    # 测试可视化器
    visualizer = TrainingVisualizer("test_output")
    
    # 模拟训练指标
    for epoch in range(5):
        train_loss = 1.0 - epoch * 0.1
        val_loss = 1.2 - epoch * 0.1
        train_metrics = {'psnr': 20 + epoch, 'ssim': 0.5 + epoch * 0.1}
        val_metrics = {'psnr': 18 + epoch, 'ssim': 0.4 + epoch * 0.1}
        
        visualizer.update_metrics(train_loss, val_loss, train_metrics, val_metrics)
        
        if epoch % 2 == 0:
            visualizer.save_comparison(bus_images, target_images, generated_images, epoch)
    
    visualizer.save_progress_plot()
    
    print("Visualization test completed successfully!")
