"""
图像质量评价指标
包括PSNR、SSIM等指标的计算
"""

import torch
import torch.nn.functional as F
import numpy as np
from typing import Tuple, Union
from generative.metrics.ssim import SSIMMetric
import math


def calculate_psnr(
    img1: torch.Tensor, 
    img2: torch.Tensor, 
    data_range: float = 1.0
) -> torch.Tensor:
    """
    计算PSNR (Peak Signal-to-Noise Ratio)
    
    Args:
        img1: 预测图像 [B, C, H, W]
        img2: 真实图像 [B, C, H, W]
        data_range: 数据范围
    
    Returns:
        PSNR值 [B]
    """
    # 确保输入在相同设备上
    img1 = img1.to(img2.device)
    
    # 计算MSE
    mse = F.mse_loss(img1, img2, reduction='none')
    mse = mse.view(mse.shape[0], -1).mean(dim=1)  # [B]
    
    # 避免除零
    mse = torch.clamp(mse, min=1e-10)
    
    # 计算PSNR
    psnr = 20 * torch.log10(data_range / torch.sqrt(mse))
    
    return psnr


def calculate_ssim(
    img1: torch.Tensor, 
    img2: torch.Tensor, 
    data_range: float = 1.0,
    spatial_dims: int = 2
) -> torch.Tensor:
    """
    计算SSIM (Structural Similarity Index)
    
    Args:
        img1: 预测图像 [B, C, H, W]
        img2: 真实图像 [B, C, H, W]
        data_range: 数据范围
        spatial_dims: 空间维度
    
    Returns:
        SSIM值 [B]
    """
    # 确保输入在相同设备上
    img1 = img1.to(img2.device)
    
    # 创建SSIM计算器
    ssim_metric = SSIMMetric(
        spatial_dims=spatial_dims,
        data_range=data_range,
        kernel_type="gaussian",
        kernel_size=11,
        kernel_sigma=1.5,
        reduction="none"  # 不进行reduction，保持batch维度
    )
    
    # 计算SSIM
    ssim_values = ssim_metric(img1, img2)
    
    return ssim_values.squeeze()


def calculate_mae(
    img1: torch.Tensor, 
    img2: torch.Tensor
) -> torch.Tensor:
    """
    计算MAE (Mean Absolute Error)
    
    Args:
        img1: 预测图像 [B, C, H, W]
        img2: 真实图像 [B, C, H, W]
    
    Returns:
        MAE值 [B]
    """
    # 确保输入在相同设备上
    img1 = img1.to(img2.device)
    
    # 计算MAE
    mae = F.l1_loss(img1, img2, reduction='none')
    mae = mae.view(mae.shape[0], -1).mean(dim=1)  # [B]
    
    return mae


def calculate_mse(
    img1: torch.Tensor, 
    img2: torch.Tensor
) -> torch.Tensor:
    """
    计算MSE (Mean Squared Error)
    
    Args:
        img1: 预测图像 [B, C, H, W]
        img2: 真实图像 [B, C, H, W]
    
    Returns:
        MSE值 [B]
    """
    # 确保输入在相同设备上
    img1 = img1.to(img2.device)
    
    # 计算MSE
    mse = F.mse_loss(img1, img2, reduction='none')
    mse = mse.view(mse.shape[0], -1).mean(dim=1)  # [B]
    
    return mse


class MetricsCalculator:
    """
    图像质量评价指标计算器
    """
    
    def __init__(
        self, 
        data_range: float = 1.0,
        spatial_dims: int = 2
    ):
        self.data_range = data_range
        self.spatial_dims = spatial_dims
        
        # 初始化累积指标
        self.reset()
    
    def reset(self):
        """重置累积指标"""
        self.total_psnr = 0.0
        self.total_ssim = 0.0
        self.total_mae = 0.0
        self.total_mse = 0.0
        self.count = 0
    
    def update(
        self, 
        pred_images: torch.Tensor, 
        target_images: torch.Tensor
    ) -> dict:
        """
        更新指标
        
        Args:
            pred_images: 预测图像 [B, C, H, W]
            target_images: 真实图像 [B, C, H, W]
        
        Returns:
            当前批次的指标字典
        """
        batch_size = pred_images.shape[0]
        
        # 计算各项指标
        psnr_values = calculate_psnr(pred_images, target_images, self.data_range)
        ssim_values = calculate_ssim(pred_images, target_images, self.data_range, self.spatial_dims)
        mae_values = calculate_mae(pred_images, target_images)
        mse_values = calculate_mse(pred_images, target_images)
        
        # 累积指标
        self.total_psnr += psnr_values.sum().item()
        self.total_ssim += ssim_values.sum().item()
        self.total_mae += mae_values.sum().item()
        self.total_mse += mse_values.sum().item()
        self.count += batch_size
        
        # 返回当前批次的平均指标
        batch_metrics = {
            'psnr': psnr_values.mean().item(),
            'ssim': ssim_values.mean().item(),
            'mae': mae_values.mean().item(),
            'mse': mse_values.mean().item()
        }
        
        return batch_metrics
    
    def compute(self) -> dict:
        """
        计算累积的平均指标
        
        Returns:
            平均指标字典
        """
        if self.count == 0:
            return {
                'psnr': 0.0,
                'ssim': 0.0,
                'mae': 0.0,
                'mse': 0.0
            }
        
        avg_metrics = {
            'psnr': self.total_psnr / self.count,
            'ssim': self.total_ssim / self.count,
            'mae': self.total_mae / self.count,
            'mse': self.total_mse / self.count
        }
        
        return avg_metrics


def denormalize_image(
    image: torch.Tensor, 
    mean: float = 0.5, 
    std: float = 0.5
) -> torch.Tensor:
    """
    反归一化图像（从[-1,1]到[0,1]）
    
    Args:
        image: 归一化的图像 [B, C, H, W]
        mean: 归一化时使用的均值
        std: 归一化时使用的标准差
    
    Returns:
        反归一化的图像
    """
    return image * std + mean


def normalize_for_metrics(
    pred_images: torch.Tensor, 
    target_images: torch.Tensor
) -> Tuple[torch.Tensor, torch.Tensor]:
    """
    为指标计算准备图像（确保在[0,1]范围内）
    
    Args:
        pred_images: 预测图像
        target_images: 目标图像
    
    Returns:
        处理后的预测图像和目标图像
    """
    # 如果图像在[-1,1]范围内，转换到[0,1]
    if pred_images.min() < 0:
        pred_images = denormalize_image(pred_images)
    if target_images.min() < 0:
        target_images = denormalize_image(target_images)
    
    # 确保在[0,1]范围内
    pred_images = torch.clamp(pred_images, 0, 1)
    target_images = torch.clamp(target_images, 0, 1)
    
    return pred_images, target_images


if __name__ == "__main__":
    # 测试指标计算
    device = "cuda" if torch.cuda.is_available() else "cpu"
    
    # 创建测试图像
    batch_size = 4
    pred_images = torch.randn(batch_size, 1, 256, 256).to(device)
    target_images = torch.randn(batch_size, 1, 256, 256).to(device)
    
    # 归一化到[0,1]
    pred_images = torch.sigmoid(pred_images)
    target_images = torch.sigmoid(target_images)
    
    # 测试单独的指标计算
    psnr = calculate_psnr(pred_images, target_images)
    ssim = calculate_ssim(pred_images, target_images)
    mae = calculate_mae(pred_images, target_images)
    mse = calculate_mse(pred_images, target_images)
    
    print(f"PSNR: {psnr.mean().item():.4f}")
    print(f"SSIM: {ssim.mean().item():.4f}")
    print(f"MAE: {mae.mean().item():.4f}")
    print(f"MSE: {mse.mean().item():.4f}")
    
    # 测试指标计算器
    calculator = MetricsCalculator()
    
    # 更新指标
    batch_metrics = calculator.update(pred_images, target_images)
    print(f"\nBatch metrics: {batch_metrics}")
    
    # 计算累积指标
    avg_metrics = calculator.compute()
    print(f"Average metrics: {avg_metrics}")
    
    print("Metrics test completed successfully!")
