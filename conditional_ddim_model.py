"""
条件DDIM模型实现
用于BUS到CEUS的图像转换任务
"""

import torch
import torch.nn as nn
from typing import Optional, Tuple
from generative.networks.nets import DiffusionModelUNet
from generative.networks.schedulers import DDIMScheduler
from generative.inferers import DiffusionInferer


class ConditionalDiffusionModelUNet(nn.Module):
    """
    条件扩散模型，支持图像到图像的转换
    使用concat方式将条件图像与噪声图像连接作为输入
    """
    
    def __init__(
        self,
        spatial_dims: int = 2,
        in_channels: int = 1,
        out_channels: int = 1,
        num_channels: Tuple[int, ...] = (128, 256, 512),
        attention_levels: Tuple[bool, ...] = (False, True, True),
        num_res_blocks: int = 2,
        num_head_channels: int = 256,
        norm_num_groups: int = 32,
        **kwargs
    ):
        super().__init__()
        
        # 条件输入通道数 = 原始通道数 + 条件图像通道数
        conditional_in_channels = in_channels + in_channels  # 噪声图像 + 条件图像
        
        self.unet = DiffusionModelUNet(
            spatial_dims=spatial_dims,
            in_channels=conditional_in_channels,  # 输入包含噪声图像和条件图像
            out_channels=out_channels,
            num_channels=num_channels,
            attention_levels=attention_levels,
            num_res_blocks=num_res_blocks,
            num_head_channels=num_head_channels,
            norm_num_groups=norm_num_groups,
            **kwargs
        )
        
        self.in_channels = in_channels
        self.out_channels = out_channels
    
    def forward(
        self, 
        x: torch.Tensor, 
        timesteps: torch.Tensor, 
        condition: torch.Tensor,
        context: Optional[torch.Tensor] = None,
        **kwargs
    ) -> torch.Tensor:
        """
        前向传播
        
        Args:
            x: 噪声图像 [B, C, H, W]
            timesteps: 时间步 [B]
            condition: 条件图像 (BUS图像) [B, C, H, W]
            context: 额外的上下文信息（可选）
        
        Returns:
            预测的噪声 [B, C, H, W]
        """
        # 将噪声图像和条件图像在通道维度上连接
        conditional_input = torch.cat([x, condition], dim=1)
        
        # 通过UNet预测噪声
        noise_pred = self.unet(
            x=conditional_input,
            timesteps=timesteps,
            context=context,
            **kwargs
        )
        
        return noise_pred


class ConditionalDDIMInferer(DiffusionInferer):
    """
    条件DDIM推理器
    支持条件图像输入的扩散模型推理
    """
    
    def __init__(self, scheduler: DDIMScheduler):
        super().__init__(scheduler)
    
    def __call__(
        self,
        inputs: torch.Tensor,
        diffusion_model: ConditionalDiffusionModelUNet,
        condition: torch.Tensor,
        noise: Optional[torch.Tensor] = None,
        timesteps: Optional[torch.Tensor] = None,
        **kwargs
    ) -> torch.Tensor:
        """
        条件扩散模型的前向传播（训练时使用）

        Args:
            inputs: 目标图像 (CEUS图像) [B, C, H, W]
            diffusion_model: 条件扩散模型
            condition: 条件图像 (BUS图像) [B, C, H, W]
            noise: 随机噪声 [B, C, H, W]
            timesteps: 时间步 [B]

        Returns:
            预测的噪声
        """
        if noise is None:
            noise = torch.randn_like(inputs)

        if timesteps is None:
            timesteps = torch.randint(
                0, self.scheduler.num_train_timesteps, 
                (inputs.shape[0],), device=inputs.device
            ).long()
        
        # 添加噪声到目标图像
        noisy_inputs = self.scheduler.add_noise(
            original_samples=inputs, 
            noise=noise, 
            timesteps=timesteps
        )
        
        # 预测噪声
        noise_pred = diffusion_model(
            x=noisy_inputs,
            timesteps=timesteps,
            condition=condition,
            **kwargs
        )
        
        return noise_pred
    
    def sample(
        self,
        input_noise: torch.Tensor,
        diffusion_model: ConditionalDiffusionModelUNet,
        condition: torch.Tensor,
        scheduler: Optional[DDIMScheduler] = None,
        save_intermediates: bool = False,
        intermediate_steps: int = 100,
        conditioning: Optional[torch.Tensor] = None,
        verbose: bool = True,
        **kwargs
    ) -> Tuple[torch.Tensor, Optional[list]]:
        """
        条件DDIM采样
        
        Args:
            input_noise: 初始噪声 [B, C, H, W]
            diffusion_model: 条件扩散模型
            condition: 条件图像 (BUS图像) [B, C, H, W]
            scheduler: DDIM调度器
            save_intermediates: 是否保存中间结果
            intermediate_steps: 中间步骤间隔
            conditioning: 额外的条件信息
            verbose: 是否显示进度
        
        Returns:
            生成的图像和中间结果（如果保存）
        """
        if scheduler is None:
            scheduler = self.scheduler
        
        image = input_noise
        intermediates = []
        
        # 设置推理时间步
        scheduler.set_timesteps(num_inference_steps=len(scheduler.timesteps))
        
        if verbose:
            from tqdm import tqdm
            progress_bar = tqdm(scheduler.timesteps)
        else:
            progress_bar = scheduler.timesteps
        
        for i, t in enumerate(progress_bar):
            # 预测噪声
            with torch.no_grad():
                noise_pred = diffusion_model(
                    x=image,
                    timesteps=torch.tensor([t]).to(image.device).expand(image.shape[0]),
                    condition=condition,
                    context=conditioning,
                    **kwargs
                )
            
            # DDIM步骤
            image, _ = scheduler.step(
                model_output=noise_pred,
                timestep=t,
                sample=image
            )
            
            # 保存中间结果
            if save_intermediates and i % intermediate_steps == 0:
                intermediates.append(image.clone())
        
        if save_intermediates:
            return image, intermediates
        else:
            return image, None


def create_conditional_ddim_model(
    image_size: Tuple[int, int] = (256, 256),
    in_channels: int = 1,
    out_channels: int = 1,
    num_train_timesteps: int = 1000,
    device: str = "cuda"
) -> Tuple[ConditionalDiffusionModelUNet, DDIMScheduler, ConditionalDDIMInferer]:
    """
    创建条件DDIM模型、调度器和推理器
    
    Args:
        image_size: 图像尺寸
        in_channels: 输入通道数
        out_channels: 输出通道数
        num_train_timesteps: 训练时间步数
        device: 设备
    
    Returns:
        model, scheduler, inferer
    """
    # 创建条件扩散模型
    model = ConditionalDiffusionModelUNet(
        spatial_dims=2,
        in_channels=in_channels,  # 单个图像的通道数
        out_channels=out_channels,
        num_channels=(128, 256, 512),
        attention_levels=(False, True, True),
        num_res_blocks=2,
        num_head_channels=256,
        norm_num_groups=32
    ).to(device)
    
    # 创建DDIM调度器
    scheduler = DDIMScheduler(
        num_train_timesteps=num_train_timesteps,
        schedule="linear_beta",
        clip_sample=True,
        set_alpha_to_one=True,
        prediction_type="epsilon"
    )
    
    # 创建条件推理器
    inferer = ConditionalDDIMInferer(scheduler)
    
    return model, scheduler, inferer


if __name__ == "__main__":
    # 测试模型
    device = "cuda" if torch.cuda.is_available() else "cpu"
    
    model, scheduler, inferer = create_conditional_ddim_model(
        image_size=(256, 256),
        device=device
    )
    
    # 测试前向传播
    batch_size = 2
    bus_image = torch.randn(batch_size, 1, 256, 256).to(device)  # BUS图像（条件）
    ceus_image = torch.randn(batch_size, 1, 256, 256).to(device)  # CEUS图像（目标）
    
    # 训练时的前向传播
    noise_pred = inferer(
        inputs=ceus_image,
        diffusion_model=model,
        condition=bus_image
    )
    
    print(f"Model parameters: {sum(p.numel() for p in model.parameters()):,}")
    print(f"Input shape: {ceus_image.shape}")
    print(f"Condition shape: {bus_image.shape}")
    print(f"Noise prediction shape: {noise_pred.shape}")
    
    # 测试采样
    scheduler.set_timesteps(num_inference_steps=50)
    noise = torch.randn_like(ceus_image)
    
    generated_image, _ = inferer.sample(
        input_noise=noise,
        diffusion_model=model,
        condition=bus_image,
        verbose=False
    )
    
    print(f"Generated image shape: {generated_image.shape}")
    print("Model test completed successfully!")
