"""
BUS-CEUS配对图像数据集加载器
用于加载BUS（源模态）和CEUS（目标模态）配对图像数据
"""

import os
import glob
from typing import List, Tuple, Optional, Dict, Any
import torch
from torch.utils.data import Dataset, DataLoader
import numpy as np
from PIL import Image
import torchvision.transforms as transforms
from monai.transforms import (
    Compose, LoadImage, EnsureChannelFirst, ScaleIntensity, 
    Resize, RandRotate, RandFlip, RandGaussianNoise, 
    RandAdjustContrast, ToTensor, NormalizeIntensity
)


class BUSCEUSDataset(Dataset):
    """
    BUS-CEUS配对图像数据集
    
    Args:
        bus_dir: BUS图像文件夹路径
        ceus_dir: CEUS图像文件夹路径
        image_size: 图像尺寸 (height, width)
        augmentation: 是否使用数据增强
        normalize: 是否归一化到[-1, 1]
    """
    
    def __init__(
        self,
        bus_dir: str,
        ceus_dir: str,
        image_size: Tuple[int, int] = (256, 256),
        augmentation: bool = True,
        normalize: bool = True
    ):
        self.bus_dir = bus_dir
        self.ceus_dir = ceus_dir
        self.image_size = image_size
        self.augmentation = augmentation
        self.normalize = normalize
        
        # 获取配对的图像文件列表
        self.image_pairs = self._get_image_pairs()
        
        # 定义基础变换
        self.base_transforms = Compose([
            LoadImage(image_only=True),
            EnsureChannelFirst(),
            Resize(spatial_size=image_size),
            ScaleIntensity(minv=0.0, maxv=1.0),  # 先缩放到[0,1]
        ])
        
        # 定义数据增强变换
        if augmentation:
            self.aug_transforms = Compose([
                RandRotate(range_x=0.1, prob=0.5),
                RandFlip(spatial_axis=0, prob=0.5),
                RandFlip(spatial_axis=1, prob=0.5),
                RandGaussianNoise(prob=0.3, mean=0.0, std=0.05),
                RandAdjustContrast(gamma=(0.8, 1.2), prob=0.3),
            ])
        else:
            self.aug_transforms = None
            
        # 定义最终变换
        if normalize:
            self.final_transform = NormalizeIntensity(subtrahend=0.5, divisor=0.5)  # 转换到[-1,1]
        else:
            self.final_transform = None
    
    def _get_image_pairs(self) -> List[Tuple[str, str]]:
        """获取配对的BUS-CEUS图像文件路径"""
        bus_files = sorted(glob.glob(os.path.join(self.bus_dir, "*.png")))
        ceus_files = sorted(glob.glob(os.path.join(self.ceus_dir, "*.png")))
        
        # 根据文件名匹配配对图像
        pairs = []
        for bus_file in bus_files:
            bus_basename = os.path.basename(bus_file)
            # 将bus替换为ceus来找到对应的CEUS文件
            ceus_basename = bus_basename.replace("_bus_", "_ceus_")
            ceus_file = os.path.join(self.ceus_dir, ceus_basename)
            
            if os.path.exists(ceus_file):
                pairs.append((bus_file, ceus_file))
            else:
                print(f"Warning: No matching CEUS file found for {bus_file}")
        
        print(f"Found {len(pairs)} image pairs")
        return pairs
    
    def __len__(self) -> int:
        return len(self.image_pairs)
    
    def __getitem__(self, idx: int) -> Dict[str, torch.Tensor]:
        bus_path, ceus_path = self.image_pairs[idx]
        
        # 加载和预处理BUS图像（条件输入）
        bus_image = self.base_transforms(bus_path)
        
        # 加载和预处理CEUS图像（目标输出）
        ceus_image = self.base_transforms(ceus_path)
        
        # 应用数据增强（对两个图像应用相同的变换）
        if self.aug_transforms is not None:
            # 设置相同的随机种子确保两个图像应用相同的变换
            seed = np.random.randint(0, 2**32)
            
            # 对BUS图像应用增强
            np.random.seed(seed)
            torch.manual_seed(seed)
            bus_image = self.aug_transforms(bus_image)
            
            # 对CEUS图像应用相同的增强
            np.random.seed(seed)
            torch.manual_seed(seed)
            ceus_image = self.aug_transforms(ceus_image)
        
        # 应用最终变换
        if self.final_transform is not None:
            bus_image = self.final_transform(bus_image)
            ceus_image = self.final_transform(ceus_image)
        
        return {
            "bus": bus_image,      # 条件输入 (BUS图像)
            "ceus": ceus_image,    # 目标输出 (CEUS图像)
            "bus_path": bus_path,
            "ceus_path": ceus_path
        }


def create_data_loaders(
    train_bus_dir: str,
    train_ceus_dir: str,
    val_bus_dir: Optional[str] = None,
    val_ceus_dir: Optional[str] = None,
    batch_size: int = 8,
    image_size: Tuple[int, int] = (256, 256),
    num_workers: int = 4,
    train_split: float = 0.8
) -> Tuple[DataLoader, DataLoader]:
    """
    创建训练和验证数据加载器
    
    Args:
        train_bus_dir: 训练BUS图像目录
        train_ceus_dir: 训练CEUS图像目录
        val_bus_dir: 验证BUS图像目录（可选）
        val_ceus_dir: 验证CEUS图像目录（可选）
        batch_size: 批次大小
        image_size: 图像尺寸
        num_workers: 数据加载工作进程数
        train_split: 训练集比例（当没有单独验证集时使用）
    
    Returns:
        train_loader, val_loader
    """
    
    if val_bus_dir is not None and val_ceus_dir is not None:
        # 使用单独的验证集
        train_dataset = BUSCEUSDataset(
            train_bus_dir, train_ceus_dir, 
            image_size=image_size, 
            augmentation=True
        )
        val_dataset = BUSCEUSDataset(
            val_bus_dir, val_ceus_dir, 
            image_size=image_size, 
            augmentation=False
        )
    else:
        # 从训练集中分割验证集
        full_dataset = BUSCEUSDataset(
            train_bus_dir, train_ceus_dir, 
            image_size=image_size, 
            augmentation=True
        )
        
        # 分割数据集
        total_size = len(full_dataset)
        train_size = int(train_split * total_size)
        val_size = total_size - train_size
        
        train_dataset, val_dataset = torch.utils.data.random_split(
            full_dataset, [train_size, val_size]
        )
        
        # 为验证集创建无增强的数据集
        val_dataset_no_aug = BUSCEUSDataset(
            train_bus_dir, train_ceus_dir, 
            image_size=image_size, 
            augmentation=False
        )
        # 使用相同的索引
        val_dataset.dataset = val_dataset_no_aug
    
    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=num_workers,
        pin_memory=True,
        drop_last=True
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=True,
        drop_last=False
    )
    
    return train_loader, val_loader


if __name__ == "__main__":
    # 测试数据加载器
    train_loader, val_loader = create_data_loaders(
        train_bus_dir="train_mini/bus",
        train_ceus_dir="train_mini/ceus",
        batch_size=4,
        image_size=(256, 256)
    )
    
    print(f"Train batches: {len(train_loader)}")
    print(f"Val batches: {len(val_loader)}")
    
    # 测试一个批次
    for batch in train_loader:
        print(f"BUS shape: {batch['bus'].shape}")
        print(f"CEUS shape: {batch['ceus'].shape}")
        print(f"BUS range: [{batch['bus'].min():.3f}, {batch['bus'].max():.3f}]")
        print(f"CEUS range: [{batch['ceus'].min():.3f}, {batch['ceus'].max():.3f}]")
        break
