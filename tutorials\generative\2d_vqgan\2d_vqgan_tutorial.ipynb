{"cells": [{"cell_type": "code", "execution_count": null, "id": "a6bd7bd8", "metadata": {}, "outputs": [], "source": ["# Copyright (c) MONAI Consortium\n", "# Licensed under the Apache License, Version 2.0 (the \"License\");\n", "# you may not use this file except in compliance with the License.\n", "# You may obtain a copy of the License at\n", "#     http://www.apache.org/licenses/LICENSE-2.0\n", "# Unless required by applicable law or agreed to in writing, software\n", "# distributed under the License is distributed on an \"AS IS\" BASIS,\n", "# WITHOUT WAR<PERSON><PERSON>IES OR CONDITIONS OF ANY KIND, either express or implied.\n", "# See the License for the specific language governing permissions and\n", "# limitations under the License."]}, {"cell_type": "markdown", "id": "1fa35264", "metadata": {}, "source": ["# Vector Quantized Generative Adversarial Networks with MedNIST Dataset\n", "\n", "This tutorial illustrates how to use MONAI for training a Vector Quantized Generative Adversarial Network (VQGAN) on 2D images.\n", "\n", "\n", "## Setup environment"]}, {"cell_type": "code", "execution_count": 1, "id": "9ca87966", "metadata": {}, "outputs": [], "source": ["!python -c \"import monai\" || pip install -q \"monai-weekly[tqdm]\"\n", "!python -c \"import matplotlib\" || pip install -q matplotlib\n", "%matplotlib inline"]}, {"cell_type": "markdown", "id": "b4ed4352", "metadata": {}, "source": ["## Setup imports"]}, {"cell_type": "code", "execution_count": 2, "id": "62220d8d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["MONAI version: 1.1.dev2248\n", "Numpy version: 1.23.3\n", "Pytorch version: 1.8.0+cu111\n", "MONAI flags: HAS_EXT = False, USE_COMPILED = False, USE_META_DICT = False\n", "MONAI rev id: 3400bd91422ccba9ccc3aa2ffe7fecd4eb5596bf\n", "MONAI __file__: /media/walter/Storage/Projects/GenerativeModels/venv/lib/python3.8/site-packages/monai/__init__.py\n", "\n", "Optional dependencies:\n", "Pytorch Ignite version: 0.4.10\n", "Nibabel version: 4.0.2\n", "scikit-image version: NOT INSTALLED or UNKNOWN VERSION.\n", "Pillow version: 9.2.0\n", "Tensorboard version: 2.11.0\n", "gdown version: NOT INSTALLED or UNKNOWN VERSION.\n", "TorchVision version: 0.9.0+cu111\n", "tqdm version: 4.64.1\n", "lmdb version: NOT INSTALLED or UNKNOWN VERSION.\n", "psutil version: 5.9.3\n", "pandas version: NOT INSTALLED or UNKNOWN VERSION.\n", "einops version: 0.6.0\n", "transformers version: NOT INSTALLED or UNKNOWN VERSION.\n", "mlflow version: NOT INSTALLED or UNKNOWN VERSION.\n", "pynrrd version: NOT INSTALLED or UNKNOWN VERSION.\n", "\n", "For details about installing the optional dependencies, please visit:\n", "    https://docs.monai.io/en/latest/installation.html#installing-the-recommended-dependencies\n", "\n"]}], "source": ["import os\n", "import shutil\n", "import tempfile\n", "import time\n", "\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "import torch\n", "from monai import transforms\n", "from monai.apps import MedNISTDataset\n", "from monai.config import print_config\n", "from monai.data import CacheDataset, DataLoader\n", "from monai.utils import first, set_determinism\n", "from torch.nn import L1Loss\n", "from tqdm import tqdm\n", "\n", "from generative.losses import PatchAdversarialLoss, PerceptualLoss\n", "from generative.networks.nets import VQVAE, PatchDiscriminator\n", "\n", "print_config()"]}, {"cell_type": "markdown", "id": "ad6acc85", "metadata": {}, "source": ["## Setup data directory\n", "\n", "You can specify a directory with the MONAI_DATA_DIRECTORY environment variable.\n", "\n", "This allows you to save results and reuse downloads.\n", "\n", "If not specified a temporary directory will be used."]}, {"cell_type": "code", "execution_count": 3, "id": "a0235ef9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/tmp/tmp50p6i_af\n"]}], "source": ["directory = os.environ.get(\"MONAI_DATA_DIRECTORY\")\n", "root_dir = tempfile.mkdtemp() if directory is None else directory\n", "print(root_dir)"]}, {"cell_type": "markdown", "id": "9cc643ee", "metadata": {}, "source": ["## Set deterministic training for reproducibility"]}, {"cell_type": "code", "execution_count": 4, "id": "10439b02", "metadata": {}, "outputs": [], "source": ["set_determinism(42)"]}, {"cell_type": "markdown", "id": "d57842d2", "metadata": {}, "source": ["## Setup MedNIST Dataset and training and validation dataloaders\n", "In this tutorial, we will train our models on the MedNIST dataset available on MONAI\n", "(https://docs.monai.io/en/stable/apps.html#monai.apps.MedNISTDataset). In order to train faster, we will select just\n", "one of the available classes (\"HeadCT\")."]}, {"cell_type": "code", "execution_count": 5, "id": "03753646", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2022-12-02 11:01:43,400 - INFO - Downloaded: /tmp/tmp50p6i_af/MedNIST.tar.gz\n", "2022-12-02 11:01:43,469 - INFO - Verified 'MedNIST.tar.gz', md5: 0bc7306e7427e00ad1c5526a6677552d.\n", "2022-12-02 11:01:43,469 - INFO - Writing into directory: /tmp/tmp50p6i_af.\n"]}], "source": ["train_data = MedNISTDataset(root_dir=root_dir, section=\"training\", download=True, progress=False, seed=0)\n", "train_datalist = [{\"image\": item[\"image\"]} for item in train_data.data if item[\"class_name\"] == \"HeadCT\"]"]}, {"cell_type": "markdown", "id": "13c36566", "metadata": {}, "source": ["Here we use transforms to augment the training dataset:\n", "\n", "1. `LoadImaged` loads the hands images from files.\n", "1. `EnsureChannelFirstd` ensures the original data to construct \"channel first\" shape.\n", "1. `ScaleIntensityRanged` extracts intensity range [0, 255] and scales to [0, 1].\n", "1. `RandAffined` efficiently performs rotate, scale, shear, translate, etc. together based on PyTorch affine transform."]}, {"cell_type": "code", "execution_count": 6, "id": "8937ef5e", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Loading dataset: 100%|████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 7991/7991 [00:04<00:00, 1813.54it/s]\n"]}], "source": ["train_transforms = transforms.Compose(\n", "    [\n", "        transforms.LoadImaged(keys=[\"image\"]),\n", "        transforms.EnsureChannelFirstd(keys=[\"image\"]),\n", "        transforms.ScaleIntensityRanged(keys=[\"image\"], a_min=0.0, a_max=255.0, b_min=0.0, b_max=1.0, clip=True),\n", "        transforms.RandA<PERSON>ed(\n", "            keys=[\"image\"],\n", "            rotate_range=[(-np.pi / 36, np.pi / 36), (-np.pi / 36, np.pi / 36)],\n", "            translate_range=[(-1, 1), (-1, 1)],\n", "            scale_range=[(-0.05, 0.05), (-0.05, 0.05)],\n", "            spatial_size=[64, 64],\n", "            padding_mode=\"zeros\",\n", "            prob=0.5,\n", "        ),\n", "    ]\n", ")\n", "train_ds = CacheDataset(data=train_datalist, transform=train_transforms)\n", "train_loader = DataLoader(train_ds, batch_size=256, shuffle=True, num_workers=4, persistent_workers=True)"]}, {"cell_type": "code", "execution_count": 7, "id": "5fdcc691", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2022-12-02 11:02:06,119 - INFO - Verified 'MedNIST.tar.gz', md5: 0bc7306e7427e00ad1c5526a6677552d.\n", "2022-12-02 11:02:06,119 - INFO - File exists: /tmp/tmp50p6i_af/MedNIST.tar.gz, skipped downloading.\n", "2022-12-02 11:02:06,119 - INFO - Non-empty folder exists in /tmp/tmp50p6i_af/MedNIST, skipped extracting.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Loading dataset: 100%|████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 7991/7991 [00:04<00:00, 1848.41it/s]\n"]}], "source": ["val_data = MedNISTDataset(root_dir=root_dir, section=\"validation\", download=True, progress=False, seed=0)\n", "val_datalist = [{\"image\": item[\"image\"]} for item in val_data.data if item[\"class_name\"] == \"HeadCT\"]\n", "val_transforms = transforms.Compose(\n", "    [\n", "        transforms.LoadImaged(keys=[\"image\"]),\n", "        transforms.EnsureChannelFirstd(keys=[\"image\"]),\n", "        transforms.ScaleIntensityRanged(keys=[\"image\"], a_min=0.0, a_max=255.0, b_min=0.0, b_max=1.0, clip=True),\n", "    ]\n", ")\n", "val_ds = CacheDataset(data=val_datalist, transform=val_transforms)\n", "val_loader = DataLoader(val_ds, batch_size=256, shuffle=False, num_workers=4, persistent_workers=True)"]}, {"cell_type": "markdown", "id": "0ad073d0", "metadata": {}, "source": ["### Visualization of the training images"]}, {"cell_type": "code", "execution_count": 8, "id": "dcea1aa6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["batch shape: torch.Size([256, 1, 64, 64])\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAABKUAAAE4CAYAAACKfUBxAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjYuMCwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy89olMNAAAACXBIWXMAAA9hAAAPYQGoP6dpAAB12ElEQVR4nO3dZ9hd1Xnm8ceFjkBCBXUhoQJqSEIFUU0z1dgYYgK4YOMW49jJTGZi44mTK/YwSQZ7EhdsbGPHmBBiTCemS1QJIToIFVDvvSGa8Xi+Zt3Pjc7KO2LrfaX/79va1/Oes8vaa699pH3v9/zhD3/4QwAAAAAAAAANeu/OXgEAAAAAAADsfvhRCgAAAAAAAI3jRykAAAAAAAA0jh+lAAAAAAAA0Dh+lAIAAAAAAEDj+FEKAAAAAAAAjeNHKQAAAAAAADSOH6UAAAAAAADQOH6UAgAAAAAAQOPeX1vYu3fvd3M9gJ1mjz32KNpvvPFGqtl7773Tsj333LNob9myJdXoZ+21114tv/93v/tdy++KiHj/+8vT9//+3/+bat56662i/fvf/77l57jv0s+JiHjf+9633bZbJ7eOf/jDH1rW6DJdZwC7Dzce7bfffmmZjhuvv/56qtGxxH3O22+/XbS3bduWatavX+9XFmjAe9+b/435Pe95T8saXbajat7N79fP3dXpPHLjxo0t/6ZXr17v1uoAEDoncfd6eq8Tke/J3P2Xjn9u/qNzFHc/5r5fl7mxtWaM1s9x37Vy5cq0LH1XywoAAAAAAABgB+NHKQAAAAAAADSOH6UAAAAAAADQOIJZsNvTnBGXu7Rp06a0TJ+ZdVkkPXr0KNouL0o/u3PnzqnmzTffTMt0vV3OkmZhuRpdJ5ep5Z6P1v3k/k5r3HPOmqnlvkufYXbZMAB2D927d0/LFi9enJbpWNK1a9dUs3Xr1qK9bt26VHPAAQcUbTdGkym1c7U1C2NH1TT5/btbptLuzvUJAO3HPvvs07LGZUG1JXfXjf8616nNlHLfp2quNzWfU4ORDgAAAAAAAI3jRykAAAAAAAA0jh+lAAAAAAAA0DgypbDb23PPPYu2y4ZyzwK/9tprRds9r7t58+ai7Z7N1XyUjRs3phrNXYqI2H///Vt+v2ZRbdu2LdXoOtU+i1zzd26Z0twpl5+lNZqVBWD34fKbNL8vIo9RLi9Q86Jcpp1m2Lkxamd7NzON9LNrPqfp7wd2VfR3oH3TTKW333471bj7SL22ub/TeYu71u+7774tP8fdx7llrdbR0c+p+VyHkQ4AAAAAAACN40cpAAAAAAAANI4fpQAAAAAAANA4fpQCAAAAAABA4wg6x25PQ2tdGN3vfve7tKxTp05Fe5999kk1K1euLNouaLdv375F24WDv/HGG2mZhu9q0J77LA3De6fvU1u2bEnLNHxTA+MjckC7C8zTfeLWpyawGMDuwY0R7mUQ+tIIffFERETXrl2Ltr5AIqIuxFMD0yPevRBxgo+B3QfnO9C+abC4m6N07tw5LdOXNrl7zQ0bNhRt96KVtoSRu7+rmX/U3DO2FSMdAAAAAAAAGsePUgAAAAAAAGgcP0oBAAAAAACgcWRKYbenz8vqM74RPq9EnyF2uUv6d0cccUSqOffcc4v22LFjU43LQlm4cGHRfuGFF1KNLlu2bFmqefXVV4u2e17Y5ay4DKtWNS4LqiZTSvcjmVLA7suNx8uXL0/LNC9K2xERK1asKNou06FLly5FWzMeIiL2228/v7IA8P+BTCmgfdNsJr0/jPDZwHrf6O5/NAt42LBhqWbo0KFFe9KkSamm5p7t/e/PPwvpOtVkY7r8qhqMdAAAAAAAAGgcP0oBAAAAAACgcfwoBQAAAAAAgMbxoxQAAAAAAAAa954/VKZR9e7d+91eF2Cn0GC3PffcM9Vs27YtLdPQur322ivV6OnlQnT171zQujv/NDR93LhxqUaDfRctWpRq7rvvvqI9ffr0lusYkbdFg+4i8r50Nfo5LiBQg/XcMQKwe3CB5SeffHJa9tJLLxXthx56KNXo2OpCPPUFEd27d081NSGiAPD/a9WqVWmZzjV79erV1OoAuz19+ZK7Z3Ih5jX3iG+++WbR3meffVJNt27dWn5OW18QVfOihZr5z+zZs1t/V9UaAQAAAAAAADsQP0oBAAAAAACgcfwoBQAAAAAAgMaRKYXd3muvvVa0XabTHnvskZbpM7vuVHJ/pzSbyn2Oez5Yn+Hdb7/9Uk3//v2L9qhRo1LN6NGji3bfvn1TzZVXXpmWrV69umhv3rw51Whe17777ptqNGfKPfes+6Stz0YD6PhcfsHkyZPTshNOOKFoL1myJNX85je/Kdpbt25NNT169Cja69evTzXk3AFowpo1a9IynRORKQU056233iraNRnDEfm8bWuNzj/cvaebN+lnuc92WcCtPtt9jt4zOvxPKQAAAAAAADSOH6UAAAAAAADQOH6UAgAAAAAAQOP4UQoAAAAAAACNI+gcu70DDjigaK9bt67q7zS0uyagW4O/I3JAnAuje9/73tdyfd588820TMP3XIh7t27dinaXLl1SzV/+5V+mZdOmTSvaU6ZMSTULFy4s2m+//Xaq0W1773vzb+UatFcTvAdg1+RCPDdu3JiWderUqWife+65qUZf/nDbbbelmvvvv79o9+vXL9W8/vrrfmUBYAdyc1R9GQ5B50Bz9J7E3bPp/VhEvieqedGWe/GVfo67j3I/9+gyd2+ln1Xz2e67li9fnpal72pZAQAAAAAAAOxg/CgFAAAAAACAxvGjFAAAAAAAABrHj1IAAAAAAABoHEHnuzgNza4JiHY1LrRN61z4mga7afBsRA7odoHhLiBcv9/9nW6/C2jTYDkXGO7CxzV8zu033SduHXWdakLNm+ZCfC+++OKifcwxx6Sa++67r2jffffdqUb3kduPe+65Z9F2xwPA7qGt1wgXhj5p0qSireNaRMScOXOK9tVXX51q9t9/f7+yALADbdiwIS3TeStB5x2TuyXX61jNbbu7j3D3aDqXdkHb+llvvPFGqtF1cnP0rl27pmWbNm0q2vvtt1+q2bJlS1qmal485dZb962bR6jaEPEdpeaztcat4862YsWKljXtb60BAAAAAACwy+NHKQAAAAAAADSOH6UAAAAAAADQODKldnGaxeMO99tvv73d9jv9nT5n7J7F1eda3Wfrs7/uWdi99tqr5We756X1+9x2uGePa7TlOW89HhF521x+087mnvNeunRp0R42bFiq+frXv160lyxZkmq++93vFm23H7VvvZvPbwNo31xehsuw0PH/oIMOSjWac9C/f/9Uc8kllxTt9evXp5p//ud/dquKXdCOvP7U5HwC/5Hm8ETkeSOZUh1TzXjg7rV0TGpr7qLLXdK8Mvc53bp1K9rr1q1r+V0R+RrdpUuXVKPXdreOWuPGaJeXpfdf7h7V5QzjP49MKQAAAAAAALRL/CgFAAAAAACAxvGjFAAAAAAAABrHj1IAAAAAAABoHEHnu7itW7cW7T322CPV6DIXIutoaJ37O13mQuTeeuutljWOBp27gPSaMHYNyHPb4QL6NEjQBe3q6VUTxuvC0He2jRs3pmUaCOwC2rX/feUrX0k1/fr1K9p/93d/l2pWrlxZtA888MB3XlkAu7R99tknLdu2bVvLujVr1rSscddIDUP9whe+kGq+//3v+5VFu1ETIlwzJSboHDvTli1b0jId/wg675hqXrzkrlE6JrkXP7l7C71veu2111KNBoS779c+6e7H3AuTdP5/9tlnp5opU6YU7VmzZqUavbdy+9Hdf+k9Yc39b0e4RrTHl0ERdA4AAAAAAIB2iR+lAAAAAAAA0Dh+lAIAAAAAAEDjyJTazbhnU/XZ35r8pIicheSe19XPcs8ia417Ftktq1nHmtwnXVa7/brMPa+tWSTu+zWLSf+mPagZJtwx0ufT991331TzqU99qmgfcsghqeaKK64o2ppDBmD34bIB3RitmXadO3dONTpubd68OdXUjONundC+7KhMqR35d2RK4T/r1VdfTct0rCNTqmNymbp6b1GTO+Wykd588820TK9/Xbp0STV6j+Jyp/bff/+i7fqo27bhw4cX7YkTJ6aaG2+8sWi7vCx3b6Fc7qTuE5dXqfvf7ccdlUVVw10zdBmZUgAAAAAAAEAlfpQCAAAAAABA4/hRCgAAAAAAAI3jRykAAAAAAAA0jqDzXZyGtrmANg2NqwlRc8tc0KsGm9cEjbsQO7dM/84FzekyF8at6+221QX01YRta0CeC3pX7hjtbJ06dUrLNPxwy5YtqUb7nwsa3HvvvYv2+eefn2pOP/30ov3FL37xnVcWwC7NhZG7YN833nijaLvpjl4TXIisBp27sa49vqACwK7HzaN0TCLovGNy9yg6j3Yh4vp3bs6+Zs2atEzvkSZMmJBq9Ho7ffr0VDNw4MCivWnTplQzePDgtKxnz55Fe9asWalm3bp1RfvAAw9MNRq+7u619ttvv7RM7yPdfZ3WuDmC3ke6uYa7/9U693d6T1rzcoz2+AINgs4BAAAAAADQLvGjFAAAAAAAABrHj1IAAAAAAABoHD9KAQAAAAAAoHE5mRq7FA0/dAFtGnSn4dzvRAPhXECcfrYGz0bkQDoX9OfUBLnp9rrA9K1btxZtF1irQYMROTTPfXbNPqoJP9/ZXECiHqe+ffumGg0o3H///VONBgTedtttqaYj7CMAzXDXKHdt03F7wYIFqUbHLTdGzZkzp2iPGTMm1axevdquKwDsSLVzZHQ87jqm9xbuXkPvtdzLmVyIts6t3fVv3rx5RXvQoEGpZunSpUW7X79+qebggw9Oy2bOnFm0x40bl2o+8pGPFO0pU6akmmnTphVtF7Tuvl/vP/SexTnggAPSMv2+ynfIpfDzmr+rCUPvqBjZAAAAAAAA0Dh+lAIAAAAAAEDj+FEKAAAAAAAAjSNTahfn8pGUPlPrnk19/fXX07I333xzu58TEbHvvvsWbfcscq9evYp2//79U03Xrl3TMn2G+rXXXks1a9euLdobN25MNStXrtxuOyJi/fr1aZk+1+9yTnRf1jwv7vbjzqb5WRE5r2Xu3LmpRo+b27YNGzYU7e7du6eaa665pmi7Z7oB7B7c9WDbtm1pmeY8DBkyJNXoNcHl540cObJoz549O9UcdNBBdl3RftTmfKgdldfhPqet64TdF5lSuxe9/9J7H7fM3Q+5vKgePXoUbb2vi8jZxL179041Xbp0KdoTJkxINW7ervcW5557bqo57rjjirbLJtb7qGXLlqUat/2vvvpqWqZ0jHbfr/d/mhUV4cd/vSesuUbsKvlRDiMbAAAAAAAAGsePUgAAAAAAAGgcP0oBAAAAAACgcfwoBQAAAAAAgMa95w+VKYsu2Aztn4aouaDtGi5YT4NdNQw2IuLwww8v2h/5yEdSjYbfHXjggalm7733Tss0xN0FjdfQoLvrr78+1fzqV79Ky+bPn1+0a8IH33777VTjAvHaG7eOb731VtF24cMamujCFzXo0PVRDfZ0+xHA7stNZfRFG+6FHRoa6kJEddxyL2PYunVr1Xpi59lRoeK1QbM1dbty0HlNQO/uFOK7o2ioc0TEunXrira+QAgdg16zIvL1p1OnTqlGzxt3PXL38QMHDiza7hr52GOPtVzHa6+9tmgPHTo01TzzzDNpmd5bjB07NtX069evaLsxQl+Y5F58oi++ioh46aWXirYLSNeXoaxatSrVPPHEE0Xb3TO5+xZd5u5/aq4Ruk/a433lihUrWtbwP6UAAAAAAADQOH6UAgAAAAAAQOP4UQoAAAAAAACN69CZUpop5J6F1Uybmmwil3vjnvPULCT3DKt+9vvf//5U88YbbxRtl03k6Hq6LKY+ffoUbfcs8v7771+09fndiIijjjoqLdMMqZ49e6aaLl26FO3169enmkWLFhXt5cuXpxrNFIqI2GeffYq22299+/Yt2kOGDEk1NfR5/YiI2267rWjfcsstqWbu3LlF2z3nq1lY+vxyRM5visjHW/tRRMQee+xRtF0WQU0/IsNp1+DGSO2Tel5F5H7kxkPXt3W8c/1Pv8+No/p3OvZG+Ofu9bxx45+eN2479BxxeR0ur0DXSXP4IvIYNWvWrFSjuWuagxfhM/V037qxRdfR7Vs3/gAdTdOZUjpvcfM//SyX16I1mp8S4cdW/SydD7u/c+uo47+2I/y8udXnROS5hZvroeSuv2vWrCna7TFTqj3mp+n9z8KFC1ONXqPdHEn3v5truL/TOZmr0f3mjv+AAQOK9vjx41ON3o9F5HmDu9f/1re+VbTdPrrggguK9o9+9KNU4+YRup+2bNmSanRO4vqR7hM3RtfcW7t9q2OU3tdH5PvYn/3sZ6nmjjvuSMt0LK/J1K3JvXTXEXeN0M9281/9O/fZet1wmWbuPjqtT8sKAAAAAAAAYAfjRykAAAAAAAA0jh+lAAAAAAAA0Dh+lAIAAAAAAEDjOnTQuYaPuRBFDTbTwNiIHFDrwtgOPvjgtExDu12wnYaGucBoDbpzYWwuoL1z585FW4PuIiKOPfbYoj1hwoRUM2zYsJbf5Wgg3cqVK1PNpk2bivb8+fNTjQYNu6A7F76mYXMufE6PiQv66969e9F2Qe+DBg1Ky3Q/bd68OdVMnz69aN96662p5qGHHtru50b48NHnnnuuaGuofUQOjXZ9S/uRO44u2A7tn4YYuuNfE3So4Yu1Qee6zH2/hii6cVSXucB2N2649WzFnWs6briXMdSEP9bUuO3QYEt3jXJB57q/3f7Qz27LPgOcHRVsrJ/jzlEX4qpciLCeS+7c0u9380g3t9O5hXvRgap5YcycOXNSjRuT9PrvXpBQEyKsIbpuru3GHz0mLsRcxx93HUHJHaNVq1YVbYLO62j4v9tvGtrsXmqi57o7R2pCvDV4PSLPf9wLC3SdTjzxxFRz6KGHpmUrVqwo2ieffHKq+eEPf9jy+6+88sqiPXny5FTj/k77hLv/0b7txlE3/qialzG47695+YKO/+4aNWPGjLTs6quvLtp6PxiR+5/rWzq3dC/DcNuh981ujq7HzV3r9O/cOi5btiwtS+vYsgIAAAAAAADYwfhRCgAAAAAAAI3jRykAAAAAAAA0rkNnSmn2hXteUp+zdM+0aj6Je6bXfbb+Xbdu3VKNPgvq8jo0L6Bv376pxi0bPHhw0Xa5R5qhcPzxx6cafaZ57ty5qebBBx9MyxYuXFi0XRaDPlfr9qPub7ePXBaCPrOq2xGR8wpc7oSuo3sW1mWKjRgxomhrNldE7n9r1qxJNfpM9ze/+c1U8/jjj6dlmiGmx8N9v3sWWPeJy91wf4f2T4d317e1xp3Hyj2/785tPZddXoieo+6SpLlz7nPcuK3b68Z/zWtwY41+n2blRfhMP81rc7kvuk5u2/R5fZd74M5Rvf64nAP9LLePXF4fOh53bdXz1p3bNZlmNXll7tyuyb3Ta5RbR9e3a7JAdGxxn61zPTePcFl4mk/jsqh0v7lxpEePHi2/3523ui/d/tAxyeV+rl69umjrPovwY0RNpqqukzuOSvsVchbo7pYp1dY+ofPdmkw510c1v83dM7ucJ82Q03MtIm+byx1at25d0b7ssstSzWGHHZaWPfDAA0Xb5XWeccYZRXvkyJGpZsiQIUXb5Te5ewvNwnLbpvnNOq+JyPffLr/TjX+aT9W/f/9Uo+eSm2u21YIFC4r2t7/97VRz3333Fe2abOq2ZmO566/2PzdH1nNE574R/h5V8T+lAAAAAAAA0Dh+lAIAAAAAAEDj+FEKAAAAAAAAjeNHKQAAAAAAADSuQwedayCXC3rUYMlVq1alGg2ac2GUc+bMSctGjRpVtDWMLSIHpI0ePTrVjBs3ruX3u2BJDXJzAZ3nn39+0dYwvoi8bTNnzkw1GqIYkcP+XECaBrK5ED0NjXNBg1u2bEnL9Ptc0LkGu7mA0D59+hRtF6LnQvv0+12wm4bmaTh6RA6od+vowu+uvfbaon3IIYekmqeeeqpouzBW7W+bN29ONTXho2h/9PxzIdrKHWvt6+6yUbPMBS3qOrrzX/uoO9fd2KbLXEClfn/37t1TjYZxuhDJjRs3pmUa/usC0vUlFm6M1H3iAutd+KV+f81+c+Ofu7ag/dPzz52jNQHB+nfub9z1v9XnRNQFXWuN6+suRFdDxF3f1jHRbYeeo24+5mj4rguI7dq1a9F2Qec6/vTr1y/VuDm6bpsbN3RMdPNonRPMmzcv1TzzzDNpmY53bvzXvuS+X2tqAnt3NxqQ7V7Os7O1Neh8RwXbu8/R+za9H4jI8393jp5yyilFe+jQoanGzb91THAvGtCXKLnzb8yYMUX7wx/+cKpxHn744aL9+c9/PtX84z/+Y9E+/fTTqz5b1byg4fLLL081GzZsKNrjx49PNTomuBcvuHtrXebGSB3HXRi61rg5k3sZiF5v3Pzve9/7XtG+6qqrUo1e22pe/BWR53/uHkH3kbv+6nF01+OlS5emZYqRHQAAAAAAAI3jRykAAAAAAAA0jh+lAAAAAAAA0Dh+lAIAAAAAAEDjOnR6sYamuYAyDaR04V/KBT2PHTs2LTv77LNb1hx00EFF24Vha4iYBhZG+GCx4cOHF20NPo+ImDZtWtFetmxZqlmyZEnRdmGcLnxU93dNQLnb/xp+6MLgXGicBsS5faThd+5z1qxZU7RdGJzrWxoIp58TkYM+XRizBiRrgH5ExN/+7d+mZUceeWTR/uxnP5tqNKDVBb3qOeJCXF1ANNq/mvBhDYR0Id7aJ9yLB9w5oiG+LkRUxz8X9Pjyyy8X7UWLFqUaHWvdMvcyCh033fhTE2x84IEHpmV6/rvxT0M8XUCnHkd3jFyIuu5L98IGvY66/ejCh9G+uBBhvUa567j+nQuj1Rr3OS4gteb79Zrszn+tcUHX7u+GDBlStF1ArXKfrd/vXmrgtl+vt3quR+TwY7f/dW7j5ohz585Ny3QsGzlyZKo54ogjivbhhx+earp06VK03QuD3L7Vl+a4ddSxtSYMHdnuHv5e00dcjb7YxN3H6Dlx7rnnphp9iZUbj2rGtokTJ6aa6667rmi7+dfHPvaxtEy5uc0xxxxTtN19xJNPPlm0XdC5zhHc9cjNW1asWFG0Fy5cmGpOPfXUou3G2pr7SHePqPdkrkbvid19nL7EqvZFA7qeLiD8q1/9atF226/h5+5ezwW96zXZ9X9dx5rrf1vnjLv3KAYAAAAAAICdgh+lAAAAAAAA0Dh+lAIAAAAAAEDj3vMH9+Cn0bt373d7Xf7T9Nl7l9ekz7737Nkz1ejzuW5bf/zjH6dlkyZNKtpuV+ozzO55TZdzpGryGh588MFUM2XKlJafrZkqLofGZSjoc7buOVPNR3HP4iq3re6zdT1rnoV1zzT36NGjaLtnit2x1XVyz6vrOrrnhTWv6cwzz2z5ORH5+eA77rgj1Xzta18r2m7bNOfi0EMPbVmDjkH7qHvOXrnzT3M/XD92eSVjxowp2poxFZHHEZdfphlSv/3tb1ONe4Zdv89l6rkxQel+dOPBpz71qbTssMMOK9ru3JoxY0bR1vyIiIgFCxYUbZdf5cYfPd4ud0Svf+466sZWtH/aJ2qyOFw/0v7vPsdlIWl/c+e/nn8uL0mz0DQHJiJi3LhxadlFF11UtDW/KSLnHrksDh2j3PlwyCGHpGX3339/0Xa5n7reLtN06dKlRdtl07gsOs3wmzBhQqrR8999jo43Lj/Tjdt63bj33ntTzVNPPbXdv4nw4y1KmpfozrWdreY4tjUbakfljrm5zec+97mirfOaiHyvOXjw4FSjOcARub/PmTMn1fzsZz8r2i4baMCAAUXbHf+LL744LdPz9sYbb0w1Om5oxlxEHiPcvYYbN/X8f+SRR1KNHhN3rGvyot3Yotc2N/7p9c5d63Su566RRx99dFqm9//r1q1LNd26dSva7hrx53/+50X73/7t31KNm+vW5AXr3Lrm/HP7aO3atS2/i/8pBQAAAAAAgMbxoxQAAAAAAAAax49SAAAAAAAAaBw/SgEAAAAAAKBxOT26A9ljjz2KtgvW0hAxF2Kpwa4XXHBBqtFQ84gcIuvCqJUGZkbk8EsX6u2WaZDc888/n2o02Mxtv4ZYuzBCF9qmYXsuRFe/zwXd1QSGO/p9bv/XBNTVbL9bJ91+Fxin/c/tR/3+3/zmN6nmpJNOSssOPvjgon3hhRemGu1bX/3qV1t+jusj6Jhqgs31nHTnqIZYulBzN0ZqsLc7/xYvXtzy+ydOnFi0XRilC3acN29e0XZhxHrePvvss6nmmGOOKdpf/OIXU40LWp8/f37Rvuuuu1LNqaeeWrRvuummVPPlL3+5aM+aNSvVuH2r66QvdYjI4517qYULVkXJje3umtgknSNpYHhEPv4u6LcmxNSdkwcddFDRdiH6eo66fabrdPrpp6caN28bNWpU0dYXBkTkbXEvntFgXxc0616Qc/bZZxdt94ICDW3X8TAi7xP3Xe7cXrlyZdF2L0PRlz+4sV3H8Zp1jMhhz2PHjk01Oo996aWXUo2OSTVz7d3Nzh5r2pva+wjtW5deemmq0ZcRuBetDBkypGi7+dAzzzyTlukLqm655ZZUo+OPC8zW4++uR9OmTUvLlixZUrTdy2D+6I/+qGi7uY7ex6xYsSLVaBh4RB4jXnzxxVSjIf5u22rmsS58Xed/LjBdv88FpuuczAXdL1y4MC3Ta6J7iYdygeV/8zd/U7RfeOGFVKMvDIrIL81w26/70l3/dZ3aOh4xigEAAAAAAKBx/CgFAAAAAACAxvGjFAAAAAAAABrXoTOl1JYtW9Iyzct5+eWXU40+L3zeeeelGn3uNyLi3nvvLdqa3xORsw/c8/r6LKZ7XtPR7INevXqlmpkzZxZt9yy+ZkG452XdOmnOg3uGVJ/hdc8C12RK1ewTzc9wn+UydjRnw2VquLwMzeLYtm1bqtFnr102mK6jy/14/PHH07IZM2YUbZfX8OEPf3i73xUR8aUvfSktw65BnwV3Y5SOCS4/aOjQoUV7zJgxqaZTp05pmWYquXPkoosuKtr9+vVLNbpOn/70p1PNJz/5ybTsn/7pn4r2c889l2o0Z6Zbt26pRveRy1367ne/m5b9r//1v4r27NmzU42O20888UTLddT8ioic3xORr1tr165NNZqp4HJvXBYDSu0xU6omU64mi0THEVfj+o3mU7i8EuXySnr27Fm0zzrrrFSj+VEReY6k+SkReY6k88GIiBNOOKHld7lxU7fFZTHpMXKZVlrj5gguQ0S3bc6cOalG5zarVq1KNTrXOfzww1ONy8t79NFHi7bLWdF8LJdp8tRTTxXtdevWpRo3R9O5rOu3Ov92NTr+uXlUbYbRu2VnjzXvJt23bd3X7u+039x///2pxo1tSnPmnnzyyVQzZcqUtEznBMcff3yqGT58eNF290O6bZpDFRFx3333pWWaM6X3zBF5HNMcooiIhx9+uGi7/GSX6adjq8vm1W1z36/738013Wfr8XdZVMqNUXpM3Djirj+6TwYOHJhqdNx0Y13//v2L9nHHHZdq1qxZk5bpeO/uEXRscWON3tu2dc64645iAAAAAAAAaLf4UQoAAAAAAACN40cpAAAAAAAANI4fpQAAAAAAANC4Dh10roGQLlhLg72GDRuWajS09vLLL081GuIWkcPOXIjaNddcU7T/23/7b6nmkksuKdpuO1yIt4Y4uu9X7rPXr19ftF2Imwv602A5pyYgTYPlXBi7CwivCUjXZS58TkM8XUDf1q1b0zLdFvf9WuOOo4bWuaDBTZs2pWV6/N066j4644wzUs23v/3tov31r3891WgYqOP6Vs3+14BAd/w7Anf8tW9pu62f7faR+34NqHQBmdon3TqOHz++aLtQc3duf+YznynaLgxYA7pdP/7EJz5RtDX4M8IHW/bp06doa2ByRMR1111XtN1+XLhwYdF+5plnUs03vvGNtGzq1KnbXZ+IiHvuuado33LLLalGj8n06dNTzYknnpiW6bXlkUceSTUaRu/6lgaLuhBV94IM5fqWjv+ub2nQsxtHdrb2uE4111bt7y5oXLlj5AK69brlzm3tE+4aOWLEiKLtXhjjrj8a2qpznYiIiRMnFu0PfehDqebFF18s2hogHhFxzjnnpGU6l3L7SMc/Df52f/fss8+mGtf/9KUN7mVA+mIXt46LFi0q2u5FDy7EXscE/ZyIiB49ehRt9xIN/RwXGO36ls6t3DxG+7sbo/Ta1tbr+LtJ19Gd6+7FB7qsZo7qwqB1mfsc9/06R3X9WI+/O45a4+4ZjjnmmLRs1qxZRduFUf/jP/5j0XZB2zr+uDB+d/+pY5t7iYC+IMHdo+m+femll1KNXusj8th24YUXphrt7+5+RO+bVqxYkWrcCyLmzZtXtFevXp1q9N62JujdHX/3ggg9/12/1THS9VH9HNePXL/V+z13/6nj5iGHHJJqdHs///nPp5qbbropLdN7e7dtOrd221H7grZW+J9SAAAAAAAAaBw/SgEAAAAAAKBx/CgFAAAAAACAxnXoTCl9Ztc9i6vPdbpnoZcuXVq058yZk2rGjh2blumzmO4ZWn0W9L777ks1X/jCF4q25iC8E32u02UR6XPG7plufRbU7SP3nK0+e+qes9fvc8+i6rOw7rtcXo3Lfmn12S4vQdfRZWq4vAp9Zt89w1zzObpsw4YNqcZ9ds3263PlLq/i4osvLtouG0Kz0SLy89nu2Orz0S73TPuf60d63HbU88s7Uk1ek+vbNZ9Tc466/qD7240tOv65vKDbb7+9aPfv3z/VuEwhzTQ577zzUo3mE7zwwgupRs+Rl19+OdVo7lNEfhbejSN6brlMA82ZcZlWbv8/+uijRXv58uWpRo+RyxTQ9R40aFCqefLJJ9OyVatWFW2XO9W1a9ei/dhjj6Ua7Teu/7kMk5r+rvu/Jq9EM07ag/aYKaVjqTtGut7uGqUZUpoDFOHzOrT/ueuvHlt3HdHzeOXKlammc+fOaZn2v49+9KMta2rygtz2u5wXHdvcua2ZUjpmunV02XSaDRORz22XhaPXe5cVqsvmzp2balxeoM6b+/btm2q6d+9etN0YPXTo0KKt/SoiZ9NE5L7l+p/uWzdHVjX5kU3T/ea2w+1bXW93/muN++ya+wG3/3XccOex9j83/ut54861wYMHp2U63ri8Rr2Pc5l2Ov9w13o91yPyWDJ79uyWf+cynfT73DzerdPAgQOL9lFHHZVqNENTM5YiIo4++ujt/k1ERL9+/dKy3/zmN0Xb5RXq8XdzBO2T7hytubd0f6fzb9e3ajKt3DXCbYvSe3v32Xreunu9ww47LC3TfEJ33tbMt3Q/tvUejf8pBQAAAAAAgMbxoxQAAAAAAAAax49SAAAAAAAAaBw/SgEAAAAAAKBxHTroXEMcn3vuuVQzatSooq2hihERTz31VNF2QWsuIE+Dzl3Q7wc/+MGi/Ud/9EepRgPCXBhlTfji1q1b0zINn3Qhhhrs7ULNXECcLnMBcfp9LmhRl7U11NsFzdaEkWtNTdCd+ywX4qjr5I6t7v8tW7akGtcndd+6EL3FixcX7ZkzZ6aaM844o2j/8R//cap5+OGH0zINMnR9REP8XI3bJ6rm+O9sLuhVt99tq26bO/9csKRyY4SOSa5vtWWMcGHY7u9qtl9f/uCCjj/1qU8VbRc0/H/+z/9Jy2677bai7cZxDeN1L4yYPHly0V6zZk2quemmm9Iy3Scu6FNDS/W6FpH7yCuvvJJqXECsvnzj1ltvTTV6jXQhytq39CUjEXV91PUR5cZx/eyOECreHmi/ceOoXn/dGK3njQvsdS8ocKHdSscftx81aPiv/uqvUo0bNz/xiU8U7bPOOivV6LzJfY6+jMCN9e4FEWvXri3a7iUmOrdwn6PrNGDAgFTjxna9/n/2s59t+Xd33XVXqpkyZUrRdv3IvSBGx3IdayLy/MvNYzXo3I11LqB62rRpRduNWzqPd9dR3TY313MvEWiSHhM3Z3XXVl1v93c6brvjr/NhN9d390g1L7rQMcmdI/ryFXcd07luRJ7buhc2jBkzpmi7l2FpP3JzpFNPPTUt033p5gi6bddff32qefrpp7f7uRF1AeE333xzqjnppJOK9pFHHtnyc9w14oknnkjLdF+649aWMH7HzRu0n7rzWPel+xzt2y5UvuZlML169Uo17v5P6Xnk7gfHjRuXlunvH+781/HOjfXufG8L/qcUAAAAAAAAGsePUgAAAAAAAGgcP0oBAAAAAACgcfwoBQAAAAAAgMZ16KBzDTt0IYYaiOcCEjWgTAPrInKIXEQOP6sJUdRQXfc5LmjUBSvefvvtRXv27NmpRsPOXNCdhg+773chZhqs5kLENVjRhVhqaJ0LmnQBcTXBam0JsasNsdT95LZft80F9GlonjtGjta58Dk9/i6gUUP0jj766FTz0Y9+NC1buHBhy3WsCcPX7Xf9z4XvdQQ1/a8mxFG33+0Pt0wDet3+1xBZ9zIIDdp89tlnU83GjRvTspUrVxbtL3/5y6nm8ssvL9pnnnlmqnn11VeL9i9/+ctUM3fu3LRMt+XEE09MNT/60Y+K9gMPPJBqli5dWrRdYKQLH1cuaFbHGxcYriHSLozZBbTqmOT+Tvfb8OHDU42+1MCNNS78Wfu7G9v1HHHjn35Oeww6b4/rpMe/JsTZjRE6/ru+7vrfsGHDirYL8dX+P3/+/FSj5587R9x84Gc/+1nR1uDxiLxPxo8fn2r23Xffou3CeN3LD3Rsd0HnOv9047iuo9tW3dcReU7qXnSiY7kG30bkebPuj4g8RkfkOakb/zS03QX06ssgXF9z4e+6/S+99FKq0eut2zYd22rCwJumfcIFP9e8MMbV6PhbM49x3PHXMUrD+SMiJk2aVLTPPffcVKPzVjePdXRMci/6+dM//dOi7eb6+qIVNz92c6Szzz67aLtQax3vXF/TY+LGWhc+rueWe2GAnm9ujjhy5Mii7eYa27ZtS8t0bD/ssMNSjQsNb8Vd69x5q+vp7v/ctig9Jrpd7/TZui9d31JurNW5vnP44YenZbpP3Hms563rfzputPXFD/xPKQAAAAAAADSOH6UAAAAAAADQOH6UAgAAAAAAQOM6dKbU888/X7SPOuqoVKPPXnbr1i3V6DO87ple95y7PovpnmHVZ/FdjT576p6pduukdTWZWu5ZUF2n2vwe/Sz3vKwuc5lSNXk5NTlbLmdBl7nn5TVTy6nJtKrZt+5zNK9AMybeaZl+/+bNm1ONe/ZbzZo1q2gfe+yxqeYjH/lIWnbzzTcX7Xnz5qUafRbZHVvNYnH7SHNP2vq88rtJn82PyFkALlNBn+F2x1q58cBlYUyePLloazZURH6G/0Mf+lCq0W1bvXp1qrnjjjvSsieeeKJouyyIM844o2ivWrUq1UydOrVoT5kyJdW4vALdly73SDOk9LsiIi6++OKirfkJ77Ts3//934u2y2vRceORRx5JNTpGuYyDRYsWpWW9evUq2qeffnqq0THK7X89b91Y785tPU/ddVS5TKktW7YUbZcptLO1x0ypmrFFr61nnXVWqtHxxuW+uXFD+6nLtNB1dP3/vvvuK9ou082NbX/1V39VtK+88spUc9xxxxXtU045JdXo2OpyNxYsWJCW3XnnnUXb5X5ozp2bR2h/d3MWd23RLJ5f/OIXqUbzwdw6Dh48uGifdtppqUbn4xH5WE6bNi3VaF7MkUcemWo0C0zHgwifhfexj32saOv1KCLi3nvvLdour0WvG27/uzlqk3Q+WJNNE5H7spsj1+S3ar9142FN7urAgQNTzfnnn1+0P/nJT6aarl27Fm2X8fYXf/EXaZlmP7ls3vXr1xdtl830ne98p2hfcMEFqea//Jf/kpZpztl3v/vdVKPZxC6vSMdWN/d382Zd5vabnhNu/qH9yPUZvdeIyP2tZvxzc23tWzV91n1fzf2vu4/X7ajJYY6ou0dQNZmy7li7c6smL0u5619N7mEN/qcUAAAAAAAAGsePUgAAAAAAAGgcP0oBAAAAAACgcfwoBQAAAAAAgMZ16KDzQYMGFe0ZM2akGg3E+7M/+7NUo8uOOOKIVOOCzTQQsiag3H2OrrcL+nShYRpk58Jn2xLi7QLSXPhhTUCiBru5dVRuHzk1wbK631yom4b4uTA6t0/0+134nIbouc/RgEwXBqxBnxE5fNMF3Wv4ar9+/VKNBgu7oEcXUDt+/PiiPWfOnFSjx9v1R93fLkSvI3Ah2npsXYijBnTXBNZq8HaED5/Vz2prGKsGy7rzaMSIEWnZypUri7YbIzT88qGHHko1Oka6fuTCRzW0050jDz74YNHesGFDqhkyZEjR/slPfpJqJkyYkJZ9+MMfLtoujFeD1YcOHZpqbrjhhqLtguZdQLCe/7fddlvLddTg+4gc9OrGMQ1sjcj7v2fPnqlG+6iGukbUv3xjZ+oIQecuIP7UU08t2u6lFvqCmEMPPTTVuOOmLzZx44Yuc/1YazTUOMKHWL/22mtpmRo9enTRdkH7ut/uvvvuVPPkk0+mZTpuffCDH0w1NS8jqQmxdtcWvSZ95jOfSTW6j1zQ8uOPP160XRjyIYcckpaNHTu2aLug4xdffLFouxe2aIi9vvgkwo9JBx98cNF2Qb89evQo2suWLUs1Ov64a3TtvPXdon2k9lpfM25pjetrWuO+343/em5dcsklqeb4448v2u4+QseaH//4x6nmxhtvTMv0PvKyyy5LNb179y7azz77bKq58MILi7Z78Ymj/d+9MEGDzd0cRcdoF9jvxmh9GYqbx+k44uYIyo0RM2fOTMv0JRJuHNXxzo1/+lIxd466+WfNi770muCuKzqPdy8jcvtf+39NQLi+wMxx91Hu/NP7TTe26j6quf64fV2D/ykFAAAAAACAxvGjFAAAAAAAABrHj1IAAAAAAABoXIfOlNJnZocNG5ZqLr/88qL9ne98J9WsWrWqaP/oRz9KNfpMcUTEkiVLirZ7hvree+8t2vpsfkR+ztQ9C7p8+fK0TJ99dc9w6nPeLtOhJnfGPUOuz9W6Z9NdPoPS9XbPq7rPds9Mt+Jyf2qe6a3JlKrhPlv3t3te3u2Tmv2m+Qxu+/X4P//886nGZUpphtEdd9yRat54442irc/9u3VyeUG6T9pj7pTLa9FMJZc7MXny5KKtOXgREWeeeWbRdpkq7jn3mlwJ7SNujNJxY8WKFalGc4cicj6ey0LS7IWa/CCXF7Bx48a0TPuJO0c00+HYY49NNbr9Lj/FZVppzoPLi9PsgXvuuSfVaBaWy69yGQ6bNm1quY6aj6M5LBE508WNB5o7FpHz6Vwf1fNd91lEzm+sua40rSNkSml+R0TOB3F9S/uom0e4a6T295rcyb59+6YazQtxfc2NPzqWuOuG5rPomB0RMWXKlKJ98803pxqXhaRZgCeddFKq0fPN7Vvdb25cd9cWPbf0WhORj5vLy9Fx2+VOuiwmvSa6bdNMLZ2PR+T5p+7XCD9v0pzLRYsWpRodW9w1QueaLhumJnfVactcxn2Xzi3d57o+onVujqo1bqzTscXl17gsOj3+Rx99dMsaN9bo97tsqj/5kz9p+dk6H4jI58Rf/uVfphrlrrXuHNG8Sp0zR+RMKbf9WuP6sctU03mDW++zzjqraLt7Lz23XTaXG7d1jB4wYECq0bymmkw5Nx93543OJdx+0/PG7X8d/90c3Z03uv3uXl/HNs0BczVuW928WY+b27e6zO1b7bduH9Xgf0oBAAAAAACgcfwoBQAAAAAAgMbxoxQAAAAAAAAax49SAAAAAAAAaFyHDjrXQLJzzjkn1WggpAaPR0RceOGFRduF0bnwsQcffLBouxBBDZFzQWMaIuaC1oYOHZqWaWi0C5HVsDMXPqYhhi4g0X22BoTWhHi7EL0aLthWt9+Fses61QQNO26f6PfVBFa6Y6vrqOHE7/R3GiyngY0ROfzOhdhp0J0LmnbrNHHixKLtgv3Wrl2blrX6fvddGqznjvXOpudDRD4mLiD6Ax/4QNF257oGRC5YsCDVuHFL+7sL6J4/f37RrglDHT16dKp59NFH0zINxHRB+08//XTRdkHbxxxzTNF2YZjPPPNMWqahpS4MXYPlzz///FSj2/HBD34w1bjPVi6MV4OmXdCvjlHuOuZCTPXlH+7c0uPtwog1fHz48OGpxvVJDSh147iGKLvxuCb8fmdrj0Hn2t9GjRqVao444oii7fZ1zQsTHP07F76q/c+96EK5oFV3TdBzUsfaiBys7sYoDQh35/qsWbPSsvHjxxdtN/7WBG3r/Mtda1yIvc4RXn755VSj56jbR/PmzSvajz32WKpx/UbHTTf/1NBeN0fUebS++CHCB+Tr9rvP1j7q9r/O210/dsvUu/mCFp1HuvPBfb/+XVuDnrt06VK0XV//0z/907RMx/s+ffqkGhcsrzTo272MxX22zlvctVVffuDmH0cddVTR1heIRPiA9Jo5ss7/3Iu3Fi9eXLTddVRfGBGRr9tubNPzxr1UQucWOmZERIwYMSIt0/3m5kg6R3Avo9B+W3Ov5Za5+Y+OEe76o+Of2/8uxF7HdjdG674cO3ZsqtF5pF5XIvyYoPMWN47rvM3dR9dct2vwP6UAAAAAAADQOH6UAgAAAAAAQOP4UQoAAAAAAACN69CZUvos5tFHH51qpk6dWrTds/hnnnlm0R40aFCquf/++9MyfYayJlNCn993XO6GPlMfkZ/rds956z5yz5Tq8/IuP8o9Z6rPGbssGv07zSaJyNurz09H+JwHzZ5xx1af19aMg4icxeKeF3bL9Nlr92y4Pnvrno3XPuG21R1/XSf3nK9mCNRsm9v/Nc9iu7wGzcdxfVv7pHum231/e+PWUZ/z1vyiiJwh9Q//8A+pRsc214979uyZls2ePbto//rXv041et645961xj0/7vLKNPfNjVH9+vUr2i4bS/MKNCspIuLiiy9OyzRn4Re/+EWq0XHbjX/u+Xzl+q1yeQH9+/cv2qecckqq+fSnP120v/Wtb6Ual0WlXKaE5jO4a5RmOrhjNGnSpLRM+8TMmTNTjY4/rv9ppoMbR3a29pgppWPyaaedlmp0bHF5Ibq/3TXK5dXoPnFjpGbxaDZMRO5vLj/Nnbeas9SjR49Uo+etywLRecuhhx6aalxeqM4l3fVX19uNkTreu/wqzQaMiHjyySeLtht/9PvHjRuXanSu5fajuyZpzpM7ttrfajLl3Hx87ty5admJJ55YtF1eo2YPuXXUcczl3rjrn5sTN8V9tztv9Vrusnh029xYpxlSLmPU9Vsdo0aOHJlq9Dr+1FNPpRrNlKzJWIvIY5s7t3Uep3OWiHyN0rlXhM9Z0nPJ7Vud27trtG6by9Ry90iDBw8u2mPGjEk1yh1bHaNPPvnkVOOuLTr/cMdIrxEuv03HMVfjrj96LrvzRj/bjXV6brmxviZT2m2/3iPp+RCRzyM3jrpsbJ2ju+u4nkuu/9XkbtVo/3d6AAAAAAAA2OXwoxQAAAAAAAAax49SAAAAAAAAaBw/SgEAAAAAAKBxHTroXIO1NAw3ImLixIlF+zOf+Uyqufbaa4v2T37yk1TzwQ9+MC3T8MXp06enGg3Rc0FrGn7mwghd+KB+vwsf02BbF2KmQWebN29ONS58rSYgVLfFhXHqOrqAOhe0rgFxLlhNP9uFCOpnu8BUFz6qx8QFjevfuf2oNW7/u+Omy1z4b01AuJ5HbjvcMdHjr0GXETlY1QX96f53YfztMURYuaD37t27F20XkHrEEUcU7cmTJ6eau+++u2i7UFc3Rmggo57rEfmYuDB+7be1Yfi6va6P6gsD3Gc/+OCDRdudD26faLDjFVdckWpmzJhRtEePHp1qNOi3S5cuqcYFq953331F+6abbko1Gj569dVXpxoNo3bXiJqXOLhzW9d70aJFqeaoo47a7t9E+PNWX9DgrtHaR2rGeh2z2oP2OEbpNVFD9SMiXnjhhaI9YMCAVKP9zYXBumuk1rkavba4lwpof9MxI8KHKGtA8ZFHHtnysx0df/7lX/4l1bhrpPZtFxCuwcpu3+q1tk+fPqnGzT/1XHZzG+23GhgdkcOPNcA8wgcr63nr9pHWuPFfj5E7/9336zK9Hkfka6I7j/Xa5sa6Jrnrn+5b16/d/lfuPkK3t+aFOUOGDEk1Tz/9dFr2la98pWjfdtttqeZXv/pVy3XUYHF3Paw5bjUvY7nuuutafo671rp+q9vijq2OI66P6vF2wftu/vO9732vaH/gAx9INcrNI/U64l5q0qtXr7Ssd+/eRVtfqhKRt23BggWpRsc6dxzdi170vtX1bR1/3VxXa9z+d0Hver1z36/9tuY6WvPisYi6sa0mDF63t60vp+J/SgEAAAAAAKBx/CgFAAAAAACAxvGjFAAAAAAAABrHj1IAAAAAAABo3C4VdK6BkRE57M4FPWr4+fPPP59q7rzzzrRMQ9tcQPDrr79etF1AnQstq6HBZu5zaoL99HNcYLJbpn/nQtTU2rVr0zINUXMBae64adiaC3bUGg0Vjojo2bPndv/mnWggndtHGsjqtkPDT933u2Dlmn6j54iG8UXkgG4NR47wwcpKAwsjckC2CzpX7vjXHpOdacmSJWnZqaeeWrRdiKEeRxc0qeGfbhxxQa/aJ10fVS5EVMM33ee4EE8NkXTjkQYkDh48ONXoOOrGaNf/NPz0n//5n1ONhh+7EN+a/r9s2bK07Be/+EXRduOP7tvHHnss1Zx99tlF+7jjjks1U6dOTcs0NN/1m3vuuSctU7ptLuh00KBBaZkbt5ReR2pequHCYHc2t2913Kp50YMLSK0JWnUvEdDrrQufveCCC4q2hvq773Pb6s5tPU7uuOlnuXNt1KhRRfvJJ59MNe7c0oBu9xIJHVvcefzcc88V7ZUrV6YaDfWPiOjWrVvRPu2001JNzTVR95v7Ljf+L126tGi77dfj/eKLL7b8bDf+u7mNvtjA/Z2GD7ugczcmKBeirHM7N0fUdXLnUc1LZWpCxJvkxpGacdPN4/WcdGOE9nV9OYarichBz9dcc02qmTJlynb/JiLPW9z9mBu39Fi681FDtKdNm5Zq9EU/l156aapx8xZ9aYLro9r/3dxft+2VV15JNePHj0/L9B7Znf8jR44s2u6lVvrCnnHjxqUa9xIVHVvcXF/3ieujOta4oHl3bPW8dee2jhE1QeduPHSfrfcEbhzR4+3u4/SYuL7urht63a59iUkrbb1n439KAQAAAAAAoHH8KAUAAAAAAIDG8aMUAAAAAAAAGtehM6X02UuXaaLP8F5//fWp5t577y3aV155ZaoZPXp0y79zz0u757qVPq/qshn0uV/HPYuqz7C6Z/r1OVtX457h1WXuGVL9/n79+qUaffa3JgcrIj9n7J7F1ud63TP1usw9i+7oc7ZuH2k+gjtG+ny+yx1yORv6fW6/6Tq6c0Sfj3bPXbtnqFt9V0R+zttlASi3ju0xQ0ZpfkVEfq57+fLlqUZzP1w/1gwf92y6+zt99tw9i655BS7TQ3Pfao6jW0/3nLt+nzvW2idXrVqVajSbLSLnjLhswDPPPLNo67ZG5NwZd46680/37dixY1PN6tWri7bLxlHnnHNOWubyGnRf6nZE5JwBl6k1a9asou3yu9x+0+Pvzm1d5vKqNC+h5rraUeg54fajjq0u981laAwfPrxou0ypT3ziEy2/vyYv0qnJAtNlbjv03HLZGC7DQ3POXL/Rc3T+/PmpRseomtyziHyeuCwe5cYR7SNuH7mcOV1PN0ddv3590XZjq/Y/N49x45b2Jfd3Nde2mvxOlymlx8QdN820ctc2XUe3He1tjuLmcW6OrvvIjb96/N0x0rmNu2d56qmn0jLtky53tiabUrfXHQ/Xb7Ru3bp1Lb/fnUc6Rrq5jl5H3d+5uUVNpmfNXH/ChAlpmc4/vv/976eayy+/vGgPGDAg1ehYp3mqERHf/va30zLtN24dtS9pDm5EvkdxeZZu2ebNm7f7ORF537oanVu564H7u5o5mvZtN/537dq15XfNmTMnLdMx0fWbGtpv2zoe8j+lAAAAAAAA0Dh+lAIAAAAAAEDj+FEKAAAAAAAAjeNHKQAAAAAAADSuQweda0DZtGnTUs348eOLtgv/04DY2bNnp5rTTjstLdNgSQ2Mi8ghYi5oUYMVXWC0CzatCXvVsDEXPqbhhzVhpBE5SM2F7+kyF/6n2+G+y4Wv1gTEaWibC+itCairCWiuCSh1NMTPfY7bt3rcXIilLnP7aOXKlS0/R0P0InJooOuPNduvQY8uRFSXtbdQ0Qgf/nnXXXcVbQ1+j8ihkSeeeGKqGTNmTNF2genu+/VYuhrdty7oVc8jd1xd39Jj6z67e/fuRVvHdfd97jx2dH9r8HNEDo10QY9u/FEuoFXHexdiqjUuxFJDhF2osTtvtJ88+OCDqUYD8l3QvY6JM2bMSDUuIFlDo90YoS+WqHlhR0ehfanmHHUvLFDuGuH2m37W+eefn2r69OlTtF3Q75IlS4q2C+x3Y4Kuk6vR7XdztI0bNxZtfYFNhB9bp06dWrRdQG/N/O/JJ5/c7vpE+HNbg4315TgREWeffXbRdmOknjc1L8yIyPMGF3SvXIizXutdGLyb2+p1uqbfHnrooalGt98F72uofUSeS7vjVvNiHf3+mpcRRPi5VFPc8XDnn87j3f7Q/e1eKqDXDXc9dH1bX340cuTIVPP000+nZWro0KFFW8esCH+O6rF0L/q46KKLivZHP/rRluszd+7ctOyhhx5Ky/T73AsD9EVHbv/379+/aH/4wx9ONe7vJk+eXLTdGHnHHXcUbXcd0XFDx7WIiGXLlqVlTzzxRNEeMWJEqnnllVeKts4ZI/x1Q7nzX++/3PinQffu/kM/W/8mwo8ROm928099iY97qVLNy6hc0L6e7+4c1Ro3/urfufGnBv9TCgAAAAAAAI3jRykAAAAAAAA0jh+lAAAAAAAA0LgOnSmlz1nedtttqeZzn/tc0XbPC2teRufOnVPN6NGj07LDDjusaL/00kupRpe9/PLLqUafRa3Ny9Hnw11ehWZK1Hy2e+7V0eflXaaFPufqnuluS+5QRMSaNWuKtsui0efF3bHVfeT2Y80y9yywe/ZZ6ba576rJ63I1yuUF6DPV69evTzUuw0fPCZfpoc9V12QRue3QPtkeM6XcOukz/C5355FHHinaRx11VKr52te+VrSPPPLIVKPP5kfkDJEVK1akGu0T7ll47ZO1fbQmi0qXuc/RPjpx4sRUc+qpp6ZlmgXjxnEdI9w5otvhskIWLFiQlunxd7kvuv3umf6DDjqoaLtMkz/+4z9Oy5S7/uj4585/zVlw+WGub2k/ceeI5iW6bAQ9/2tyYNoD7Sdu2/S66bIYdPtd7pTL69LsL9f/9RgtXLgw1fzyl78s2i6vRLNJ3Hq67de+7LZf50iaseK+KyKfk/fcc0+qmTJlStF2Y5vmvLnroVtvne+4OYr2ZZcfquPInDlzUo3LsPnGN75RtF1ejV6T3GfrMvc5NXkpLi9P5+RubNu6dWvRdtcRt281Z8fNUXUdXe6OjltuPlqT6dIkt45u+7XfuuOoy1zuquaOuWPksnB0PbXPRkRMmDChaN95552pRs9bN0a4+bhui+YQR0R8/OMfT8uUZvG5vu7ysrRv6bU+Iud1udw/Hf/ddrj7H71Hdet9++23F23NOI3IOXdujqr5kRF+vqN0vHX3Grr9bjx28zb9frf/9dx2Y7T2N3ev6+Y/ek663ygGDx5ctPW3D/fZLj/LZQHqeOvGMT1Ha/KT2zpHa1+jKAAAAAAAAHYL/CgFAAAAAACAxvGjFAAAAAAAABrHj1IAAAAAAABoXIcOOtewNxeQuGjRoqJ9/fXXp5p58+YVbRfi6YJd1aRJk9KycePGFe1Zs2almunTpxdtF4a79957p2W6vRqGF5ED4VyImYb4usBuF9CmQXIuWFHDz1wYpda4EEUXvqiBzC6gT8MXXdCfhmi6gDbXt3Q93T6qCZHT/ei23wX01QSba79126HHW0Ol3+n7dT1dQK6G+LkQWa1x+6gmDL890hBFd27/6le/KtpuH33qU58q2l/84hdTzSWXXJKW6fi3du3aVKMBwS54Uv9uyZIlqebRRx9Ny2699daiXRN+6MIwjz322KJ9xhlnpBoX4qzjjQvoPf/884u2G/********************************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\n", "text/plain": ["<Figure size 1200x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["check_data = first(train_loader)\n", "print(f\"batch shape: {check_data['image'].shape}\")\n", "image_visualization = torch.cat(\n", "    [check_data[\"image\"][0, 0], check_data[\"image\"][1, 0], check_data[\"image\"][2, 0], check_data[\"image\"][3, 0]], dim=1\n", ")\n", "plt.figure(\"training images\", (12, 6))\n", "plt.imshow(image_visualization, vmin=0, vmax=1, cmap=\"gray\")\n", "plt.axis(\"off\")\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "id": "6b37d378", "metadata": {}, "source": ["### Define network, scheduler and optimizer\n", "At this step, we instantiate the MONAI components to create a VQVAE and a Discriminator model. We are using the\n", "Discriminator to train the Autoencoder with a Generative Adversarial loss, where the VQVAE works as a Generator.\n", "The VQVAE is trained to minimize the reconstruction error, a perceptual loss using AlexNet as the embedding model\n", "and an adversarial loss versus the performance of the Discriminator."]}, {"cell_type": "code", "execution_count": 9, "id": "6d0495a9", "metadata": {}, "outputs": [], "source": ["device = torch.device(\"cuda\")\n", "\n", "model = VQVAE(\n", "    spatial_dims=2,\n", "    in_channels=1,\n", "    out_channels=1,\n", "    num_channels=(256, 512),\n", "    num_res_channels=512,\n", "    num_res_layers=2,\n", "    downsample_parameters=((2, 4, 1, 1), (2, 4, 1, 1)),\n", "    upsample_parameters=((2, 4, 1, 1, 0), (2, 4, 1, 1, 0)),\n", "    num_embeddings=256,\n", "    embedding_dim=32,\n", ")\n", "model.to(device)\n", "\n", "discriminator = PatchDiscriminator(spatial_dims=2, in_channels=1, num_layers_d=3, num_channels=64)\n", "discriminator.to(device)\n", "\n", "perceptual_loss = PerceptualLoss(spatial_dims=2, network_type=\"alex\")\n", "perceptual_loss.to(device)\n", "\n", "optimizer_g = torch.optim.Adam(params=model.parameters(), lr=1e-4)\n", "optimizer_d = torch.optim.Adam(params=discriminator.parameters(), lr=5e-4)"]}, {"cell_type": "code", "execution_count": 10, "id": "af25d880", "metadata": {"lines_to_next_cell": 2}, "outputs": [], "source": ["l1_loss = L1Loss()\n", "adv_loss = PatchAdversarialLoss(criterion=\"least_squares\")\n", "adv_weight = 0.01\n", "perceptual_weight = 0.001"]}, {"cell_type": "markdown", "id": "3692e3e9", "metadata": {}, "source": ["### Model training\n", "Here, we are training our model for 100 epochs (training time: ~50 minutes)."]}, {"cell_type": "code", "execution_count": 11, "id": "a12f8385", "metadata": {"lines_to_next_cell": 0}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Epoch 0: 100%|█████████████| 32/32 [00:54<00:00,  1.70s/it, recons_loss=0.148, gen_loss=1.35, disc_loss=0.694]\n", "Epoch 1: 100%|████████████| 32/32 [00:55<00:00,  1.73s/it, recons_loss=0.0794, gen_loss=0.719, disc_loss=0.36]\n", "Epoch 2: 100%|████████████| 32/32 [00:56<00:00,  1.76s/it, recons_loss=0.0568, gen_loss=0.592, disc_loss=0.34]\n", "Epoch 3: 100%|███████████| 32/32 [00:56<00:00,  1.76s/it, recons_loss=0.0474, gen_loss=0.464, disc_loss=0.284]\n", "Epoch 4: 100%|█████████████| 32/32 [00:56<00:00,  1.78s/it, recons_loss=0.0428, gen_loss=0.38, disc_loss=0.26]\n", "Epoch 5: 100%|████████████| 32/32 [00:57<00:00,  1.79s/it, recons_loss=0.0398, gen_loss=0.49, disc_loss=0.315]\n", "Epoch 6: 100%|███████████| 32/32 [00:57<00:00,  1.79s/it, recons_loss=0.0363, gen_loss=0.441, disc_loss=0.273]\n", "Epoch 7: 100%|███████████| 32/32 [00:57<00:00,  1.79s/it, recons_loss=0.0366, gen_loss=0.363, disc_loss=0.282]\n", "Epoch 8: 100%|███████████| 32/32 [00:57<00:00,  1.79s/it, recons_loss=0.0351, gen_loss=0.295, disc_loss=0.247]\n", "Epoch 9: 100%|████████████| 32/32 [00:57<00:00,  1.79s/it, recons_loss=0.033, gen_loss=0.291, disc_loss=0.238]\n", "Epoch 10: 100%|███████████| 32/32 [00:57<00:00,  1.80s/it, recons_loss=0.0323, gen_loss=0.27, disc_loss=0.273]\n", "Epoch 11: 100%|███████████| 32/32 [00:57<00:00,  1.80s/it, recons_loss=0.0307, gen_loss=0.289, disc_loss=0.23]\n", "Epoch 12: 100%|██████████| 32/32 [00:57<00:00,  1.80s/it, recons_loss=0.0306, gen_loss=0.265, disc_loss=0.258]\n", "Epoch 13: 100%|██████████| 32/32 [00:57<00:00,  1.80s/it, recons_loss=0.0288, gen_loss=0.302, disc_loss=0.239]\n", "Epoch 14: 100%|██████████| 32/32 [00:57<00:00,  1.81s/it, recons_loss=0.0285, gen_loss=0.287, disc_loss=0.246]\n", "Epoch 15: 100%|██████████| 32/32 [00:57<00:00,  1.80s/it, recons_loss=0.0282, gen_loss=0.291, disc_loss=0.249]\n", "Epoch 16: 100%|██████████| 32/32 [00:57<00:00,  1.79s/it, recons_loss=0.0276, gen_loss=0.267, disc_loss=0.243]\n", "Epoch 17: 100%|███████████| 32/32 [00:57<00:00,  1.80s/it, recons_loss=0.0274, gen_loss=0.27, disc_loss=0.247]\n", "Epoch 18: 100%|██████████| 32/32 [00:57<00:00,  1.80s/it, recons_loss=0.0265, gen_loss=0.298, disc_loss=0.247]\n", "Epoch 19: 100%|██████████| 32/32 [00:57<00:00,  1.80s/it, recons_loss=0.0264, gen_loss=0.283, disc_loss=0.243]\n", "Epoch 20: 100%|███████████| 32/32 [00:57<00:00,  1.80s/it, recons_loss=0.0261, gen_loss=0.28, disc_loss=0.244]\n", "Epoch 21: 100%|██████████| 32/32 [00:57<00:00,  1.80s/it, recons_loss=0.0259, gen_loss=0.301, disc_loss=0.245]\n", "Epoch 22: 100%|██████████| 32/32 [00:57<00:00,  1.80s/it, recons_loss=0.0255, gen_loss=0.291, disc_loss=0.243]\n", "Epoch 23: 100%|██████████| 32/32 [00:57<00:00,  1.80s/it, recons_loss=0.0254, gen_loss=0.292, disc_loss=0.248]\n", "Epoch 24: 100%|███████████| 32/32 [00:57<00:00,  1.79s/it, recons_loss=0.0251, gen_loss=0.29, disc_loss=0.239]\n", "Epoch 25: 100%|██████████| 32/32 [00:57<00:00,  1.80s/it, recons_loss=0.0251, gen_loss=0.278, disc_loss=0.243]\n", "Epoch 26: 100%|██████████| 32/32 [00:57<00:00,  1.79s/it, recons_loss=0.0257, gen_loss=0.308, disc_loss=0.252]\n", "Epoch 27: 100%|████████████| 32/32 [00:57<00:00,  1.80s/it, recons_loss=0.0249, gen_loss=0.3, disc_loss=0.252]\n", "Epoch 28: 100%|██████████| 32/32 [00:57<00:00,  1.80s/it, recons_loss=0.0241, gen_loss=0.299, disc_loss=0.252]\n", "Epoch 29: 100%|███████████| 32/32 [00:57<00:00,  1.80s/it, recons_loss=0.024, gen_loss=0.299, disc_loss=0.244]\n", "Epoch 30: 100%|██████████| 32/32 [00:57<00:00,  1.79s/it, recons_loss=0.0239, gen_loss=0.294, disc_loss=0.248]\n", "Epoch 31: 100%|██████████| 32/32 [00:57<00:00,  1.80s/it, recons_loss=0.0239, gen_loss=0.289, disc_loss=0.251]\n", "Epoch 32: 100%|██████████| 32/32 [00:57<00:00,  1.80s/it, recons_loss=0.0237, gen_loss=0.288, disc_loss=0.254]\n", "Epoch 33: 100%|██████████| 32/32 [00:57<00:00,  1.80s/it, recons_loss=0.0232, gen_loss=0.314, disc_loss=0.236]\n", "Epoch 34: 100%|██████████| 32/32 [00:57<00:00,  1.80s/it, recons_loss=0.0233, gen_loss=0.292, disc_loss=0.247]\n", "Epoch 35: 100%|██████████| 32/32 [00:57<00:00,  1.80s/it, recons_loss=0.0231, gen_loss=0.294, disc_loss=0.244]\n", "Epoch 36: 100%|███████████| 32/32 [00:57<00:00,  1.80s/it, recons_loss=0.0232, gen_loss=0.295, disc_loss=0.26]\n", "Epoch 37: 100%|██████████| 32/32 [00:57<00:00,  1.80s/it, recons_loss=0.0238, gen_loss=0.292, disc_loss=0.237]\n", "Epoch 38: 100%|██████████| 32/32 [00:57<00:00,  1.80s/it, recons_loss=0.0245, gen_loss=0.292, disc_loss=0.245]\n", "Epoch 39: 100%|██████████| 32/32 [00:57<00:00,  1.80s/it, recons_loss=0.0237, gen_loss=0.286, disc_loss=0.241]\n", "Epoch 40: 100%|██████████| 32/32 [00:57<00:00,  1.80s/it, recons_loss=0.0232, gen_loss=0.294, disc_loss=0.237]\n", "Epoch 41: 100%|██████████| 32/32 [00:57<00:00,  1.80s/it, recons_loss=0.0236, gen_loss=0.285, disc_loss=0.247]\n", "Epoch 42: 100%|██████████| 32/32 [00:57<00:00,  1.80s/it, recons_loss=0.0229, gen_loss=0.305, disc_loss=0.242]\n", "Epoch 43: 100%|██████████| 32/32 [00:57<00:00,  1.80s/it, recons_loss=0.0233, gen_loss=0.289, disc_loss=0.245]\n", "Epoch 44: 100%|██████████| 32/32 [00:57<00:00,  1.79s/it, recons_loss=0.0231, gen_loss=0.283, disc_loss=0.245]\n", "Epoch 45: 100%|███████████| 32/32 [00:57<00:00,  1.80s/it, recons_loss=0.0229, gen_loss=0.29, disc_loss=0.245]\n", "Epoch 46: 100%|██████████| 32/32 [00:57<00:00,  1.80s/it, recons_loss=0.0224, gen_loss=0.287, disc_loss=0.237]\n", "Epoch 47: 100%|██████████| 32/32 [00:57<00:00,  1.80s/it, recons_loss=0.0221, gen_loss=0.293, disc_loss=0.242]\n", "Epoch 48: 100%|███████████| 32/32 [00:57<00:00,  1.80s/it, recons_loss=0.0228, gen_loss=0.319, disc_loss=0.26]\n", "Epoch 49: 100%|███████████| 32/32 [00:57<00:00,  1.80s/it, recons_loss=0.0227, gen_loss=0.306, disc_loss=0.24]\n", "Epoch 50: 100%|██████████| 32/32 [00:57<00:00,  1.80s/it, recons_loss=0.0225, gen_loss=0.308, disc_loss=0.245]\n", "Epoch 51: 100%|███████████| 32/32 [00:58<00:00,  1.83s/it, recons_loss=0.022, gen_loss=0.308, disc_loss=0.243]\n", "Epoch 52: 100%|██████████| 32/32 [00:58<00:00,  1.81s/it, recons_loss=0.0222, gen_loss=0.324, disc_loss=0.251]\n", "Epoch 53: 100%|████████████| 32/32 [00:57<00:00,  1.81s/it, recons_loss=0.0227, gen_loss=0.31, disc_loss=0.24]\n", "Epoch 54: 100%|██████████| 32/32 [00:59<00:00,  1.85s/it, recons_loss=0.0224, gen_loss=0.296, disc_loss=0.242]\n", "Epoch 55: 100%|██████████| 32/32 [00:58<00:00,  1.82s/it, recons_loss=0.0219, gen_loss=0.315, disc_loss=0.235]\n", "Epoch 56: 100%|██████████| 32/32 [00:57<00:00,  1.81s/it, recons_loss=0.0226, gen_loss=0.331, disc_loss=0.261]\n", "Epoch 57: 100%|██████████| 32/32 [00:58<00:00,  1.82s/it, recons_loss=0.0232, gen_loss=0.319, disc_loss=0.242]\n", "Epoch 58: 100%|███████████| 32/32 [00:57<00:00,  1.80s/it, recons_loss=0.022, gen_loss=0.314, disc_loss=0.237]\n", "Epoch 59: 100%|██████████| 32/32 [00:58<00:00,  1.81s/it, recons_loss=0.0218, gen_loss=0.287, disc_loss=0.241]\n", "Epoch 60: 100%|██████████| 32/32 [00:57<00:00,  1.80s/it, recons_loss=0.0216, gen_loss=0.321, disc_loss=0.235]\n", "Epoch 61: 100%|███████████| 32/32 [00:57<00:00,  1.81s/it, recons_loss=0.022, gen_loss=0.304, disc_loss=0.235]\n", "Epoch 62: 100%|██████████| 32/32 [00:59<00:00,  1.86s/it, recons_loss=0.0222, gen_loss=0.306, disc_loss=0.239]\n", "Epoch 63: 100%|██████████| 32/32 [00:58<00:00,  1.84s/it, recons_loss=0.0215, gen_loss=0.333, disc_loss=0.249]\n", "Epoch 64: 100%|██████████| 32/32 [00:58<00:00,  1.81s/it, recons_loss=0.0219, gen_loss=0.308, disc_loss=0.231]\n", "Epoch 65: 100%|███████████| 32/32 [00:59<00:00,  1.85s/it, recons_loss=0.022, gen_loss=0.322, disc_loss=0.245]\n", "Epoch 66: 100%|███████████| 32/32 [00:58<00:00,  1.83s/it, recons_loss=0.0217, gen_loss=0.329, disc_loss=0.24]\n", "Epoch 67: 100%|██████████| 32/32 [00:58<00:00,  1.82s/it, recons_loss=0.0219, gen_loss=0.335, disc_loss=0.244]\n", "Epoch 68: 100%|███████████| 32/32 [00:58<00:00,  1.81s/it, recons_loss=0.0218, gen_loss=0.32, disc_loss=0.244]\n", "Epoch 69: 100%|██████████| 32/32 [00:58<00:00,  1.83s/it, recons_loss=0.0217, gen_loss=0.325, disc_loss=0.238]\n", "Epoch 70: 100%|██████████| 32/32 [00:58<00:00,  1.82s/it, recons_loss=0.0219, gen_loss=0.289, disc_loss=0.249]\n", "Epoch 71: 100%|██████████| 32/32 [00:58<00:00,  1.83s/it, recons_loss=0.0216, gen_loss=0.323, disc_loss=0.242]\n", "Epoch 72: 100%|██████████| 32/32 [00:58<00:00,  1.82s/it, recons_loss=0.0217, gen_loss=0.301, disc_loss=0.237]\n", "Epoch 73: 100%|███████████| 32/32 [00:58<00:00,  1.82s/it, recons_loss=0.0221, gen_loss=0.293, disc_loss=0.24]\n", "Epoch 74: 100%|██████████| 32/32 [00:58<00:00,  1.82s/it, recons_loss=0.0218, gen_loss=0.313, disc_loss=0.247]\n", "Epoch 75: 100%|██████████| 32/32 [00:58<00:00,  1.82s/it, recons_loss=0.0221, gen_loss=0.308, disc_loss=0.245]\n", "Epoch 76: 100%|██████████| 32/32 [00:58<00:00,  1.84s/it, recons_loss=0.0217, gen_loss=0.294, disc_loss=0.234]\n", "Epoch 77: 100%|██████████| 32/32 [00:58<00:00,  1.83s/it, recons_loss=0.0215, gen_loss=0.311, disc_loss=0.245]\n", "Epoch 78: 100%|██████████| 32/32 [00:58<00:00,  1.84s/it, recons_loss=0.0216, gen_loss=0.297, disc_loss=0.248]\n", "Epoch 79: 100%|██████████| 32/32 [00:58<00:00,  1.83s/it, recons_loss=0.0213, gen_loss=0.315, disc_loss=0.239]\n", "Epoch 80: 100%|██████████| 32/32 [00:57<00:00,  1.81s/it, recons_loss=0.0212, gen_loss=0.346, disc_loss=0.255]\n", "Epoch 81: 100%|████████████| 32/32 [00:58<00:00,  1.82s/it, recons_loss=0.021, gen_loss=0.325, disc_loss=0.24]\n", "Epoch 82: 100%|██████████| 32/32 [00:57<00:00,  1.81s/it, recons_loss=0.0216, gen_loss=0.295, disc_loss=0.239]\n", "Epoch 83: 100%|██████████| 32/32 [00:57<00:00,  1.79s/it, recons_loss=0.0214, gen_loss=0.303, disc_loss=0.231]\n", "Epoch 84: 100%|███████████| 32/32 [00:57<00:00,  1.79s/it, recons_loss=0.0215, gen_loss=0.32, disc_loss=0.239]\n", "Epoch 85: 100%|██████████| 32/32 [00:57<00:00,  1.79s/it, recons_loss=0.0215, gen_loss=0.321, disc_loss=0.236]\n", "Epoch 86: 100%|██████████| 32/32 [00:57<00:00,  1.79s/it, recons_loss=0.0215, gen_loss=0.307, disc_loss=0.236]\n", "Epoch 87: 100%|██████████| 32/32 [00:57<00:00,  1.79s/it, recons_loss=0.0218, gen_loss=0.335, disc_loss=0.218]\n", "Epoch 88: 100%|███████████| 32/32 [00:57<00:00,  1.79s/it, recons_loss=0.0219, gen_loss=0.337, disc_loss=0.23]\n", "Epoch 89: 100%|██████████| 32/32 [00:57<00:00,  1.79s/it, recons_loss=0.0215, gen_loss=0.331, disc_loss=0.245]\n", "Epoch 90: 100%|██████████| 32/32 [00:57<00:00,  1.80s/it, recons_loss=0.0214, gen_loss=0.313, disc_loss=0.236]\n", "Epoch 91: 100%|██████████| 32/32 [00:57<00:00,  1.79s/it, recons_loss=0.0212, gen_loss=0.349, disc_loss=0.219]\n", "Epoch 92: 100%|███████████| 32/32 [00:57<00:00,  1.79s/it, recons_loss=0.022, gen_loss=0.336, disc_loss=0.231]\n", "Epoch 93: 100%|██████████| 32/32 [00:57<00:00,  1.79s/it, recons_loss=0.0221, gen_loss=0.365, disc_loss=0.217]\n", "Epoch 94: 100%|██████████| 32/32 [00:57<00:00,  1.79s/it, recons_loss=0.0221, gen_loss=0.341, disc_loss=0.226]\n", "Epoch 95: 100%|███████████| 32/32 [00:57<00:00,  1.79s/it, recons_loss=0.022, gen_loss=0.384, disc_loss=0.215]\n", "Epoch 96: 100%|██████████| 32/32 [00:57<00:00,  1.79s/it, recons_loss=0.0221, gen_loss=0.369, disc_loss=0.217]\n", "Epoch 97: 100%|██████████| 32/32 [00:57<00:00,  1.79s/it, recons_loss=0.0224, gen_loss=0.355, disc_loss=0.217]\n", "Epoch 98: 100%|███████████| 32/32 [00:57<00:00,  1.79s/it, recons_loss=0.022, gen_loss=0.334, disc_loss=0.252]\n", "Epoch 99: 100%|██████████| 32/32 [00:57<00:00,  1.79s/it, recons_loss=0.0223, gen_loss=0.343, disc_loss=0.217]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["train completed, total time: 5914.227425098419.\n"]}], "source": ["n_epochs = 100\n", "val_interval = 10\n", "epoch_recon_loss_list = []\n", "epoch_gen_loss_list = []\n", "epoch_disc_loss_list = []\n", "val_recon_epoch_loss_list = []\n", "intermediary_images = []\n", "n_example_images = 4\n", "\n", "total_start = time.time()\n", "for epoch in range(n_epochs):\n", "    model.train()\n", "    discriminator.train()\n", "    epoch_loss = 0\n", "    gen_epoch_loss = 0\n", "    disc_epoch_loss = 0\n", "    progress_bar = tqdm(enumerate(train_loader), total=len(train_loader), ncols=110)\n", "    progress_bar.set_description(f\"Epoch {epoch}\")\n", "    for step, batch in progress_bar:\n", "        images = batch[\"image\"].to(device)\n", "        optimizer_g.zero_grad(set_to_none=True)\n", "\n", "        # Generator part\n", "        reconstruction, quantization_loss = model(images=images)\n", "        logits_fake = discriminator(reconstruction.contiguous().float())[-1]\n", "\n", "        recons_loss = l1_loss(reconstruction.float(), images.float())\n", "        p_loss = perceptual_loss(reconstruction.float(), images.float())\n", "        generator_loss = adv_loss(logits_fake, target_is_real=True, for_discriminator=False)\n", "        loss_g = recons_loss + quantization_loss + perceptual_weight * p_loss + adv_weight * generator_loss\n", "\n", "        loss_g.backward()\n", "        optimizer_g.step()\n", "\n", "        # Discriminator part\n", "        optimizer_d.zero_grad(set_to_none=True)\n", "\n", "        logits_fake = discriminator(reconstruction.contiguous().detach())[-1]\n", "        loss_d_fake = adv_loss(logits_fake, target_is_real=False, for_discriminator=True)\n", "        logits_real = discriminator(images.contiguous().detach())[-1]\n", "        loss_d_real = adv_loss(logits_real, target_is_real=True, for_discriminator=True)\n", "        discriminator_loss = (loss_d_fake + loss_d_real) * 0.5\n", "\n", "        loss_d = adv_weight * discriminator_loss\n", "\n", "        loss_d.backward()\n", "        optimizer_d.step()\n", "\n", "        epoch_loss += recons_loss.item()\n", "        gen_epoch_loss += generator_loss.item()\n", "        disc_epoch_loss += discriminator_loss.item()\n", "\n", "        progress_bar.set_postfix(\n", "            {\n", "                \"recons_loss\": epoch_loss / (step + 1),\n", "                \"gen_loss\": gen_epoch_loss / (step + 1),\n", "                \"disc_loss\": disc_epoch_loss / (step + 1),\n", "            }\n", "        )\n", "    epoch_recon_loss_list.append(epoch_loss / (step + 1))\n", "    epoch_gen_loss_list.append(gen_epoch_loss / (step + 1))\n", "    epoch_disc_loss_list.append(disc_epoch_loss / (step + 1))\n", "\n", "    if (epoch + 1) % val_interval == 0:\n", "        model.eval()\n", "        val_loss = 0\n", "        with torch.no_grad():\n", "            for val_step, batch in enumerate(val_loader, start=1):\n", "                images = batch[\"image\"].to(device)\n", "\n", "                reconstruction, quantization_loss = model(images=images)\n", "\n", "                # get the first sammple from the first validation batch for visualization\n", "                # purposes\n", "                if val_step == 1:\n", "                    intermediary_images.append(reconstruction[:n_example_images, 0])\n", "\n", "                recons_loss = l1_loss(reconstruction.float(), images.float())\n", "\n", "                val_loss += recons_loss.item()\n", "\n", "        val_loss /= val_step\n", "        val_recon_epoch_loss_list.append(val_loss)\n", "\n", "total_time = time.time() - total_start\n", "print(f\"train completed, total time: {total_time}.\")"]}, {"cell_type": "markdown", "id": "1dd875b8", "metadata": {}, "source": ["### Learning curves"]}, {"cell_type": "code", "execution_count": 12, "id": "f06c0fc2", "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 800x550 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.style.use(\"seaborn-v0_8\")\n", "plt.title(\"Learning Curves\", fontsize=20)\n", "plt.plot(np.linspace(1, n_epochs, n_epochs), epoch_recon_loss_list, color=\"C0\", linewidth=2.0, label=\"Train\")\n", "plt.plot(\n", "    np.linspace(val_interval, n_epochs, int(n_epochs / val_interval)),\n", "    val_recon_epoch_loss_list,\n", "    color=\"C1\",\n", "    linewidth=2.0,\n", "    label=\"Validation\",\n", ")\n", "plt.yticks(fontsize=12)\n", "plt.xticks(fontsize=12)\n", "plt.xlabel(\"Epochs\", fontsize=16)\n", "plt.ylabel(\"Loss\", fontsize=16)\n", "plt.legend(prop={\"size\": 14})\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 13, "id": "4d01492a", "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 800x550 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.title(\"Adversarial Training Curves\", fontsize=20)\n", "plt.plot(np.linspace(1, n_epochs, n_epochs), epoch_gen_loss_list, color=\"C0\", linewidth=2.0, label=\"Generator\")\n", "plt.plot(np.linspace(1, n_epochs, n_epochs), epoch_disc_loss_list, color=\"C1\", linewidth=2.0, label=\"Discriminator\")\n", "plt.yticks(fontsize=12)\n", "plt.xticks(fontsize=12)\n", "plt.xlabel(\"Epochs\", fontsize=16)\n", "plt.ylabel(\"Loss\", fontsize=16)\n", "plt.legend(prop={\"size\": 14})\n", "plt.show()"]}, {"cell_type": "markdown", "id": "4bfeecae", "metadata": {}, "source": ["### Checking reconstructions"]}, {"cell_type": "code", "execution_count": 21, "id": "4cfbd6cd", "metadata": {"lines_to_next_cell": 2}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 800x550 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Plot first 4 evaluations\n", "val_samples = np.linspace(val_interval, n_epochs, int(n_epochs / val_interval))\n", "fig, ax = plt.subplots(nrows=4, ncols=1, sharey=True)\n", "for image_n in range(4):\n", "    reconstructions = torch.reshape(intermediary_images[image_n], (64 * n_example_images, 64)).T\n", "    ax[image_n].imshow(reconstructions.cpu(), cmap=\"gray\")\n", "    ax[image_n].set_xticks([])\n", "    ax[image_n].set_yticks([])\n", "    ax[image_n].set_ylabel(f\"Epoch {val_samples[image_n]:.0f}\")"]}, {"cell_type": "code", "execution_count": 15, "id": "9a364126", "metadata": {"lines_to_next_cell": 2}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 800x550 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, ax = plt.subplots(nrows=1, ncols=2)\n", "ax[0].imshow(images[0, 0].detach().cpu(), vmin=0, vmax=1, cmap=\"gray\")\n", "ax[0].axis(\"off\")\n", "ax[0].title.set_text(\"Inputted Image\")\n", "ax[1].imshow(reconstruction[0, 0].detach().cpu(), vmin=0, vmax=1, cmap=\"gray\")\n", "ax[1].axis(\"off\")\n", "ax[1].title.set_text(\"Reconstruction\")\n", "plt.show()"]}, {"cell_type": "markdown", "id": "25d53d14", "metadata": {}, "source": ["### Cleanup data directory\n", "\n", "Remove directory if a temporary was used."]}, {"cell_type": "code", "execution_count": 16, "id": "20b1e100", "metadata": {}, "outputs": [], "source": ["if directory is None:\n", "    shutil.rmtree(root_dir)"]}], "metadata": {"jupytext": {"formats": "ipynb,py:percent"}, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.6"}}, "nbformat": 4, "nbformat_minor": 5}