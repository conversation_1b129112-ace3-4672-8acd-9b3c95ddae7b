{"cells": [{"cell_type": "code", "execution_count": null, "id": "70eef519", "metadata": {}, "outputs": [], "source": ["# Copyright (c) MONAI Consortium\n", "# Licensed under the Apache License, Version 2.0 (the \"License\");\n", "# you may not use this file except in compliance with the License.\n", "# You may obtain a copy of the License at\n", "#     http://www.apache.org/licenses/LICENSE-2.0\n", "# Unless required by applicable law or agreed to in writing, software\n", "# distributed under the License is distributed on an \"AS IS\" BASIS,\n", "# WITHOUT WAR<PERSON><PERSON>IES OR CONDITIONS OF ANY KIND, either express or implied.\n", "# See the License for the specific language governing permissions and\n", "# limitations under the License."]}, {"cell_type": "markdown", "id": "63d95da6", "metadata": {}, "source": ["# Weakly Supervised Anomaly Detection with Implicit Guidance\n", "\n", "This tutorial illustrates how to use MONAI Generative Models for training a 2D anomaly detection using DDIMs [1]. By leveraging recent advances in generative diffusion probabilistic models, we synthesize counterfactuals of \"How would a patient appear if X pathology was not present?\". The difference image between the observed patient state and the healthy counterfactual can be used for inferring the location of pathology. We generate counterfactuals that correspond to the minimal change of the input such that it is transformed to healthy domain. We create these counterfactual diffusion models by manipulating the generation process with implicit guidance.\n", "\n", "In summary, the tutorial will cover the following:\n", "1. Loading and preprocessing a dataset (we extract the brain MRI dataset 2D slices from 3D volumes from the BraTS dataset)\n", "2. Training a 2D diffusion model\n", "3. Anomaly detection with the trained model\n", "\n", "This method results in anomaly heatmaps. It is weakly supervised. The information about labels is not fed to the model as segmentation masks but as a scalar signal (is there an anomaly or not), which is used to guide the diffusion process.\n", "\n", "During inference, the model generates a counterfactual image, which is then compared to the original image. The difference between the two images is used to generate an anomaly heatmap.\n", "\n", "[1] - <PERSON> et al. [What is Healthy? Generative Counterfactual Diffusion for Lesion Localization](https://arxiv.org/abs/2207.12268). DGM 4 MICCAI 2022"]}, {"cell_type": "markdown", "id": "6b766027", "metadata": {}, "source": ["## Setup imports"]}, {"cell_type": "code", "execution_count": 9, "id": "972ed3f3", "metadata": {"jupyter": {"outputs_hidden": false}, "lines_to_next_cell": 2}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["MONAI version: 1.1.0\n", "Numpy version: 1.23.5\n", "Pytorch version: 1.13.1+cu117\n", "MONAI flags: HAS_EXT = False, USE_COMPILED = False, USE_META_DICT = False\n", "MONAI rev id: a2ec3752f54bfc3b40e7952234fbeb5452ed63e3\n", "MONAI __file__: /remote/rds/users/s2086085/miniconda3/envs/pytorch_monai/lib/python3.10/site-packages/monai/__init__.py\n", "\n", "Optional dependencies:\n", "Pytorch Ignite version: 0.4.10\n", "Nibabel version: 5.0.1\n", "scikit-image version: 0.19.3\n", "Pillow version: 9.4.0\n", "Tensorboard version: 2.12.0\n", "gdown version: 4.6.4\n", "TorchVision version: 0.14.1+cu117\n", "tqdm version: 4.64.1\n", "lmdb version: 1.4.0\n", "psutil version: 5.9.4\n", "pandas version: 1.5.3\n", "einops version: 0.6.0\n", "transformers version: 4.21.3\n", "mlflow version: 2.1.1\n", "pynrrd version: 1.0.0\n", "\n", "For details about installing the optional dependencies, please visit:\n", "    https://docs.monai.io/en/latest/installation.html#installing-the-recommended-dependencies\n", "\n"]}], "source": ["import os\n", "import tempfile\n", "import time\n", "import os\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "import torch\n", "import torch.nn.functional as F\n", "import sys\n", "from monai import transforms\n", "from monai.apps import DecathlonDataset\n", "from monai.config import print_config\n", "from monai.data import DataLoader\n", "from monai.utils import set_determinism\n", "from torch.cuda.amp import GradScaler, autocast\n", "from tqdm import tqdm\n", "\n", "\n", "from generative.inferers import DiffusionInferer\n", "from generative.networks.nets.diffusion_model_unet import DiffusionModelUNet\n", "from generative.networks.schedulers.ddim import DDIMScheduler\n", "\n", "torch.multiprocessing.set_sharing_strategy(\"file_system\")\n", "os.environ[\"CUDA_VISIBLE_DEVICES\"] = \"0\"\n", "\n", "print_config()"]}, {"cell_type": "markdown", "id": "7d4ff515", "metadata": {}, "source": ["## Setup data directory"]}, {"cell_type": "code", "execution_count": 3, "id": "8b4323e7", "metadata": {"jupyter": {"outputs_hidden": false}}, "outputs": [], "source": ["directory = os.environ.get(\"MONAI_DATA_DIRECTORY\")\n", "root_dir = tempfile.mkdtemp() if directory is None else directory"]}, {"cell_type": "markdown", "id": "99175d50", "metadata": {}, "source": ["## Set deterministic training for reproducibility"]}, {"cell_type": "code", "execution_count": 4, "id": "34ea510f", "metadata": {"jupyter": {"outputs_hidden": false}}, "outputs": [], "source": ["set_determinism(42)"]}, {"cell_type": "markdown", "id": "c3f70dd1-236a-47ff-a244-575729ad92ba", "metadata": {}, "source": ["## Setup BRATS Dataset  - Transforms for extracting 2D slices from 3D volumes\n", "\n", "We now download the BraTS dataset and extract the 2D slices from the 3D volumes. The `slice_label` is used to indicate whether the slice contains an anomaly or not."]}, {"cell_type": "markdown", "id": "6986f55c", "metadata": {}, "source": ["Here we use transforms to augment the training dataset, as usual:\n", "\n", "1. `LoadImaged` loads the brain images from files.\n", "2. `EnsureChannelFirstd` ensures the original data to construct \"channel first\" shape.\n", "3.  The first `Lambdad` transform chooses the first channel of the image, which is the T1-weighted image.\n", "4. `Spacingd` resamples the image to the specified voxel spacing, we use 3,3,2 mm to match the original paper.\n", "5. `ScaleIntensityRangePercentilesd` Apply range scaling to a numpy array based on the intensity distribution of the input. Transform is very common with MRI images.\n", "6. `RandSpatialCropd` randomly crop out a 2D patch from the 3D image.\n", "6. The last `Lambdad` transform obtains `slice_label` by summing up the label to have a single scalar value (healthy `=1` or not `=2` )."]}, {"cell_type": "code", "execution_count": 5, "id": "c68d2d91-9a0b-4ac1-ae49-f4a64edbd82a", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["<class 'monai.transforms.utility.array.AddChannel'>: Class `AddChannel` has been deprecated since version 0.8. please use MetaTensor data type and monai.transforms.EnsureChannelFirst instead.\n"]}], "source": ["channel = 0  # 0 = Flair\n", "assert channel in [0, 1, 2, 3], \"Choose a valid channel\"\n", "\n", "train_transforms = transforms.Compose(\n", "    [\n", "        transforms.LoadImaged(keys=[\"image\", \"label\"]),\n", "        transforms.EnsureChannelFirstd(keys=[\"image\", \"label\"]),\n", "        transforms.Lambdad(keys=[\"image\"], func=lambda x: x[channel, :, :, :]),\n", "        transforms.AddChanneld(keys=[\"image\"]),\n", "        transforms.EnsureTyped(keys=[\"image\", \"label\"]),\n", "        transforms.Orientationd(keys=[\"image\", \"label\"], axcodes=\"RAS\"),\n", "        transforms.Spacingd(keys=[\"image\", \"label\"], pixdim=(3.0, 3.0, 2.0), mode=(\"bilinear\", \"nearest\")),\n", "        transforms.CenterSpatialCropd(keys=[\"image\", \"label\"], roi_size=(64, 64, 44)),\n", "        transforms.ScaleIntensityRangePercentilesd(keys=\"image\", lower=0, upper=99.5, b_min=0, b_max=1),\n", "        transforms.RandSpatialCropd(keys=[\"image\", \"label\"], roi_size=(64, 64, 1), random_size=False),\n", "        transforms.Lambdad(keys=[\"image\", \"label\"], func=lambda x: x.squeeze(-1)),\n", "        transforms.CopyItemsd(keys=[\"label\"], times=1, names=[\"slice_label\"]),\n", "        transforms.Lambdad(keys=[\"slice_label\"], func=lambda x: 2.0 if x.sum() > 0 else 1.0),\n", "    ]\n", ")"]}, {"cell_type": "markdown", "id": "9d378ac6", "metadata": {}, "source": ["### Load Training and Validation Datasets"]}, {"cell_type": "code", "execution_count": 6, "id": "da1927b0", "metadata": {"jupyter": {"outputs_hidden": false}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Loading dataset: 100%|██████████| 388/388 [02:41<00:00,  2.40it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Lenght of training data: 388\n", "Train image shape torch.Size([1, 64, 64])\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Loading dataset: 100%|██████████| 96/96 [00:39<00:00,  2.42it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Lenght of training data: 96\n", "Validation Image shape torch.Size([1, 64, 64])\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["train_ds = DecathlonDataset(\n", "    root_dir=root_dir,\n", "    task=\"Task01_BrainTumour\",\n", "    section=\"training\",\n", "    cache_rate=1.0,  # you may need a few Gb of RAM... Set to 0 otherwise\n", "    num_workers=4,\n", "    download=False,  # Set download to True if the dataset hasnt been downloaded yet\n", "    seed=0,\n", "    transform=train_transforms,\n", ")\n", "print(f\"Length of training data: {len(train_ds)}\")\n", "print(f'Train image shape {train_ds[0][\"image\"].shape}')\n", "\n", "val_ds = DecathlonDataset(\n", "    root_dir=root_dir,\n", "    task=\"Task01_BrainTumour\",\n", "    section=\"validation\",\n", "    cache_rate=1.0,  # you may need a few Gb of RAM... Set to 0 otherwise\n", "    num_workers=4,\n", "    download=False,  # Set download to True if the dataset hasnt been downloaded yet\n", "    seed=0,\n", "    transform=train_transforms,\n", ")\n", "print(f\"Length of training data: {len(val_ds)}\")\n", "print(f'Validation Image shape {val_ds[0][\"image\"].shape}')"]}, {"cell_type": "markdown", "id": "08428bc6", "metadata": {}, "source": ["## Define network, scheduler, optimizer, and inferer\n", "\n", "At this step, we instantiate the MONAI components to create a DDIM, the UNET with conditioning, the noise scheduler, and the inferer used for training and sampling. We are using\n", "the deterministic DDIM scheduler containing 1000 timesteps, and a 2D UNET with attention mechanisms.\n", "\n", "The `attention` mechanism is essential for ensuring good conditioning and images manipulation here.\n", "\n", "An `embedding layer`, which is also optimised during training, is used in the original work because it was empirically shown to improve conditioning compared to a single scalar information.\n"]}, {"cell_type": "code", "execution_count": 7, "id": "bee5913e", "metadata": {"jupyter": {"outputs_hidden": false}, "lines_to_next_cell": 2}, "outputs": [], "source": ["device = torch.device(\"cuda\")\n", "embedding_dimension = 64\n", "model = DiffusionModelUNet(\n", "    spatial_dims=2,\n", "    in_channels=1,\n", "    out_channels=1,\n", "    num_channels=(64, 64, 64),\n", "    attention_levels=(False, True, True),\n", "    num_res_blocks=1,\n", "    num_head_channels=16,\n", "    with_conditioning=True,\n", "    cross_attention_dim=embedding_dimension,\n", ").to(device)\n", "embed = torch.nn.Embedding(num_embeddings=3, embedding_dim=embedding_dimension, padding_idx=0).to(device)\n", "\n", "scheduler = DDIMScheduler(num_train_timesteps=1000)\n", "optimizer = torch.optim.Adam(params=list(model.parameters()) + list(embed.parameters()), lr=1e-5)\n", "\n", "inferer = DiffusionInferer(scheduler)"]}, {"cell_type": "markdown", "id": "f815ff34", "metadata": {}, "source": ["## Training a diffusion model with classifier-free guidance"]}, {"cell_type": "code", "execution_count": 10, "id": "9a4fc901", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Train Loss 0.8078, Interval Loss 0.9115, Interval Loss Val 0.8100\n", "Train Loss 0.6174, Interval Loss 0.7103, Interval Loss Val 0.6154\n", "Train Loss 0.4603, Interval Loss 0.5313, Interval Loss Val 0.4571\n", "Train Loss 0.3323, Interval Loss 0.3887, Interval Loss Val 0.3288\n", "Train Loss 0.2358, Interval Loss 0.2822, Interval Loss Val 0.2356\n", "Train Loss 0.1771, Interval Loss 0.2034, Interval Loss Val 0.1660\n", "Train Loss 0.1237, Interval Loss 0.1467, Interval Loss Val 0.1185\n", "Train Loss 0.0802, Interval Loss 0.1054, Interval Loss Val 0.0855\n", "Train Loss 0.0731, Interval Loss 0.0770, Interval Loss Val 0.0699\n", "Train Loss 0.0528, Interval Loss 0.0570, Interval Loss Val 0.0512\n", "Train Loss 0.0383, Interval Loss 0.0434, Interval Loss Val 0.0311\n", "Train Loss 0.0268, Interval Loss 0.0343, Interval Loss Val 0.0483\n", "Train Loss 0.0330, Interval Loss 0.0292, Interval Loss Val 0.0255\n", "Train Loss 0.0359, Interval Loss 0.0250, Interval Loss Val 0.0293\n", "Train Loss 0.0235, Interval Loss 0.0233, Interval Loss Val 0.0318\n", "Train Loss 0.0241, Interval Loss 0.0224, Interval Loss Val 0.0303\n", "Train Loss 0.0171, Interval Loss 0.0211, Interval Loss Val 0.0217\n", "Train Loss 0.0304, Interval Loss 0.0207, Interval Loss Val 0.0098\n", "Train Loss 0.0269, Interval Loss 0.0211, Interval Loss Val 0.0128\n", "Train Loss 0.0163, Interval Loss 0.0194, Interval Loss Val 0.0233\n", "Train Loss 0.0319, Interval Loss 0.0195, Interval Loss Val 0.0389\n", "Train Loss 0.0200, Interval Loss 0.0194, Interval Loss Val 0.0214\n", "Train Loss 0.0190, Interval Loss 0.0203, Interval Loss Val 0.0235\n", "Train Loss 0.0291, Interval Loss 0.0194, Interval Loss Val 0.0178\n", "Train Loss 0.0263, Interval Loss 0.0196, Interval Loss Val 0.0248\n", "Train Loss 0.0229, Interval Loss 0.0198, Interval Loss Val 0.0246\n", "Train Loss 0.0183, Interval Loss 0.0195, Interval Loss Val 0.0239\n", "Train Loss 0.0147, Interval Loss 0.0188, Interval Loss Val 0.0216\n", "Train Loss 0.0255, Interval Loss 0.0181, Interval Loss Val 0.0150\n", "Train Loss 0.0161, Interval Loss 0.0183, Interval Loss Val 0.0211\n", "Train Loss 0.0239, Interval Loss 0.0182, Interval Loss Val 0.0097\n", "Train Loss 0.0163, Interval Loss 0.0188, Interval Loss Val 0.0173\n", "Train Loss 0.0188, Interval Loss 0.0177, Interval Loss Val 0.0121\n", "Train Loss 0.0120, Interval Loss 0.0189, Interval Loss Val 0.0191\n", "Train Loss 0.0201, Interval Loss 0.0182, Interval Loss Val 0.0130\n", "Train Loss 0.0118, Interval Loss 0.0187, Interval Loss Val 0.0309\n", "Train Loss 0.0114, Interval Loss 0.0181, Interval Loss Val 0.0221\n", "Train Loss 0.0197, Interval Loss 0.0180, Interval Loss Val 0.0118\n", "Train Loss 0.0229, Interval Loss 0.0176, Interval Loss Val 0.0278\n", "Train Loss 0.0242, Interval Loss 0.0188, Interval Loss Val 0.0126\n", "Train Loss 0.0166, Interval Loss 0.0182, Interval Loss Val 0.0157\n", "Train Loss 0.0162, Interval Loss 0.0195, Interval Loss Val 0.0170\n", "Train Loss 0.0124, Interval Loss 0.0179, Interval Loss Val 0.0261\n", "Train Loss 0.0151, Interval Loss 0.0183, Interval Loss Val 0.0223\n", "Train Loss 0.0308, Interval Loss 0.0188, Interval Loss Val 0.0151\n", "Train Loss 0.0210, Interval Loss 0.0177, Interval Loss Val 0.0193\n", "Train Loss 0.0175, Interval Loss 0.0184, Interval Loss Val 0.0232\n", "Train Loss 0.0270, Interval Loss 0.0184, Interval Loss Val 0.0125\n", "Train Loss 0.0128, Interval Loss 0.0181, Interval Loss Val 0.0224\n", "Train Loss 0.0170, Interval Loss 0.0188, Interval Loss Val 0.0199\n", "Train Loss 0.0203, Interval Loss 0.0176, Interval Loss Val 0.0145\n", "Train Loss 0.0248, Interval Loss 0.0176, Interval Loss Val 0.0149\n", "Train Loss 0.0213, Interval Loss 0.0188, Interval Loss Val 0.0120\n", "Train Loss 0.0324, Interval Loss 0.0181, Interval Loss Val 0.0310\n", "Train Loss 0.0302, Interval Loss 0.0183, Interval Loss Val 0.0119\n", "Train Loss 0.0206, Interval Loss 0.0168, Interval Loss Val 0.0060\n", "Train Loss 0.0117, Interval Loss 0.0176, Interval Loss Val 0.0208\n", "Train Loss 0.0213, Interval Loss 0.0172, Interval Loss Val 0.0178\n", "Train Loss 0.0156, Interval Loss 0.0176, Interval Loss Val 0.0201\n", "Train Loss 0.0134, Interval Loss 0.0180, Interval Loss Val 0.0116\n", "Train Loss 0.0305, Interval Loss 0.0176, Interval Loss Val 0.0194\n", "Train Loss 0.0253, Interval Loss 0.0171, Interval Loss Val 0.0230\n", "Train Loss 0.0198, Interval Loss 0.0173, Interval Loss Val 0.0246\n", "Train Loss 0.0139, Interval Loss 0.0175, Interval Loss Val 0.0146\n", "Train Loss 0.0154, Interval Loss 0.0168, Interval Loss Val 0.0269\n", "Train Loss 0.0086, Interval Loss 0.0174, Interval Loss Val 0.0132\n", "Train Loss 0.0156, Interval Loss 0.0171, Interval Loss Val 0.0170\n", "Train Loss 0.0292, Interval Loss 0.0174, Interval Loss Val 0.0165\n", "Train Loss 0.0220, Interval Loss 0.0179, Interval Loss Val 0.0171\n", "Train Loss 0.0132, Interval Loss 0.0175, Interval Loss Val 0.0116\n", "Train Loss 0.0158, Interval Loss 0.0174, Interval Loss Val 0.0165\n", "Train Loss 0.0325, Interval Loss 0.0172, Interval Loss Val 0.0235\n", "Train Loss 0.0105, Interval Loss 0.0169, Interval Loss Val 0.0171\n", "Train Loss 0.0082, Interval Loss 0.0173, Interval Loss Val 0.0146\n", "Train Loss 0.0232, Interval Loss 0.0180, Interval Loss Val 0.0132\n", "Train Loss 0.0120, Interval Loss 0.0170, Interval Loss Val 0.0275\n", "Train Loss 0.0211, Interval Loss 0.0182, Interval Loss Val 0.0107\n", "Train Loss 0.0247, Interval Loss 0.0169, Interval Loss Val 0.0162\n", "Train Loss 0.0196, Interval Loss 0.0178, Interval Loss Val 0.0365\n", "Train Loss 0.0247, Interval Loss 0.0173, Interval Loss Val 0.0185\n", "Train Loss 0.0174, Interval Loss 0.0180, Interval Loss Val 0.0153\n", "Train Loss 0.0203, Interval Loss 0.0185, Interval Loss Val 0.0208\n", "Train Loss 0.0112, Interval Loss 0.0172, Interval Loss Val 0.0296\n", "Train Loss 0.0215, Interval Loss 0.0165, Interval Loss Val 0.0155\n", "Train Loss 0.0144, Interval Loss 0.0172, Interval Loss Val 0.0194\n", "Train Loss 0.0192, Interval Loss 0.0179, Interval Loss Val 0.0195\n", "Train Loss 0.0175, Interval Loss 0.0178, Interval Loss Val 0.0092\n", "Train Loss 0.0082, Interval Loss 0.0180, Interval Loss Val 0.0323\n", "Train Loss 0.0234, Interval Loss 0.0168, Interval Loss Val 0.0118\n", "Train Loss 0.0234, Interval Loss 0.0172, Interval Loss Val 0.0192\n", "Train Loss 0.0088, Interval Loss 0.0172, Interval Loss Val 0.0262\n", "Train Loss 0.0189, Interval Loss 0.0179, Interval Loss Val 0.0313\n", "Train Loss 0.0081, Interval Loss 0.0182, Interval Loss Val 0.0181\n", "Train Loss 0.0195, Interval Loss 0.0168, Interval Loss Val 0.0164\n", "Train Loss 0.0280, Interval Loss 0.0166, Interval Loss Val 0.0132\n", "Train Loss 0.0198, Interval Loss 0.0179, Interval Loss Val 0.0125\n", "Train Loss 0.0182, Interval Loss 0.0167, Interval Loss Val 0.0208\n", "Train Loss 0.0099, Interval Loss 0.0171, Interval Loss Val 0.0119\n", "Train Loss 0.0271, Interval Loss 0.0169, Interval Loss Val 0.0156\n", "Train Loss 0.0119, Interval Loss 0.0164, Interval Loss Val 0.0114\n", "Train Loss 0.0172, Interval Loss 0.0165, Interval Loss Val 0.0228\n", "Train Loss 0.0198, Interval Loss 0.0167, Interval Loss Val 0.0179\n", "Train Loss 0.0165, Interval Loss 0.0164, Interval Loss Val 0.0110\n", "Train Loss 0.0172, Interval Loss 0.0167, Interval Loss Val 0.0076\n", "Train Loss 0.0140, Interval Loss 0.0171, Interval Loss Val 0.0172\n", "Train Loss 0.0090, Interval Loss 0.0176, Interval Loss Val 0.0115\n", "Train Loss 0.0120, Interval Loss 0.0167, Interval Loss Val 0.0202\n", "Train Loss 0.0137, Interval Loss 0.0166, Interval Loss Val 0.0169\n", "Train Loss 0.0113, Interval Loss 0.0171, Interval Loss Val 0.0131\n", "Train Loss 0.0187, Interval Loss 0.0171, Interval Loss Val 0.0072\n", "Train Loss 0.0302, Interval Loss 0.0158, Interval Loss Val 0.0169\n", "Train Loss 0.0220, Interval Loss 0.0172, Interval Loss Val 0.0233\n", "Train Loss 0.0238, Interval Loss 0.0169, Interval Loss Val 0.0193\n", "Train Loss 0.0077, Interval Loss 0.0173, Interval Loss Val 0.0176\n", "Train Loss 0.0221, Interval Loss 0.0172, Interval Loss Val 0.0221\n", "Train Loss 0.0106, Interval Loss 0.0161, Interval Loss Val 0.0170\n", "Train Loss 0.0179, Interval Loss 0.0167, Interval Loss Val 0.0281\n", "Train Loss 0.0147, Interval Loss 0.0164, Interval Loss Val 0.0167\n", "Train Loss 0.0223, Interval Loss 0.0171, Interval Loss Val 0.0150\n", "Train Loss 0.0206, Interval Loss 0.0176, Interval Loss Val 0.0159\n", "Train Loss 0.0138, Interval Loss 0.0168, Interval Loss Val 0.0175\n", "Train Loss 0.0202, Interval Loss 0.0165, Interval Loss Val 0.0108\n", "Train Loss 0.0154, Interval Loss 0.0166, Interval Loss Val 0.0123\n", "Train Loss 0.0225, Interval Loss 0.0175, Interval Loss Val 0.0133\n", "Train Loss 0.0146, Interval Loss 0.0161, Interval Loss Val 0.0172\n", "Train Loss 0.0166, Interval Loss 0.0166, Interval Loss Val 0.0120\n", "Train Loss 0.0140, Interval Loss 0.0169, Interval Loss Val 0.0137\n", "Train Loss 0.0165, Interval Loss 0.0171, Interval Loss Val 0.0142\n", "Train Loss 0.0245, Interval Loss 0.0162, Interval Loss Val 0.0301\n", "Train Loss 0.0207, Interval Loss 0.0162, Interval Loss Val 0.0135\n", "Train Loss 0.0183, Interval Loss 0.0162, Interval Loss Val 0.0112\n", "Train Loss 0.0124, Interval Loss 0.0161, Interval Loss Val 0.0128\n", "Train Loss 0.0168, Interval Loss 0.0169, Interval Loss Val 0.0119\n", "Train Loss 0.0042, Interval Loss 0.0180, Interval Loss Val 0.0175\n", "Train Loss 0.0207, Interval Loss 0.0161, Interval Loss Val 0.0143\n", "Train Loss 0.0195, Interval Loss 0.0157, Interval Loss Val 0.0217\n", "Train Loss 0.0231, Interval Loss 0.0169, Interval Loss Val 0.0113\n", "Train Loss 0.0193, Interval Loss 0.0152, Interval Loss Val 0.0221\n", "Train Loss 0.0206, Interval Loss 0.0166, Interval Loss Val 0.0152\n", "Train Loss 0.0133, Interval Loss 0.0161, Interval Loss Val 0.0078\n", "Train Loss 0.0172, Interval Loss 0.0162, Interval Loss Val 0.0109\n", "Train Loss 0.0204, Interval Loss 0.0160, Interval Loss Val 0.0247\n", "Train Loss 0.0113, Interval Loss 0.0159, Interval Loss Val 0.0142\n", "Train Loss 0.0196, Interval Loss 0.0167, Interval Loss Val 0.0183\n", "Train Loss 0.0088, Interval Loss 0.0156, Interval Loss Val 0.0139\n", "Train Loss 0.0205, Interval Loss 0.0170, Interval Loss Val 0.0124\n", "Train Loss 0.0149, Interval Loss 0.0175, Interval Loss Val 0.0309\n", "Train Loss 0.0115, Interval Loss 0.0165, Interval Loss Val 0.0147\n", "Train Loss 0.0133, Interval Loss 0.0166, Interval Loss Val 0.0148\n", "Train Loss 0.0225, Interval Loss 0.0160, Interval Loss Val 0.0166\n", "Train Loss 0.0147, Interval Loss 0.0163, Interval Loss Val 0.0147\n", "Train Loss 0.0107, Interval Loss 0.0169, Interval Loss Val 0.0105\n", "Train Loss 0.0108, Interval Loss 0.0159, Interval Loss Val 0.0139\n", "Train Loss 0.0079, Interval Loss 0.0161, Interval Loss Val 0.0176\n", "Train Loss 0.0101, Interval Loss 0.0168, Interval Loss Val 0.0085\n", "Train Loss 0.0210, Interval Loss 0.0178, Interval Loss Val 0.0146\n", "Train Loss 0.0146, Interval Loss 0.0174, Interval Loss Val 0.0203\n", "Train Loss 0.0151, Interval Loss 0.0156, Interval Loss Val 0.0164\n", "Train Loss 0.0092, Interval Loss 0.0163, Interval Loss Val 0.0126\n", "Train Loss 0.0140, Interval Loss 0.0160, Interval Loss Val 0.0118\n", "Train Loss 0.0164, Interval Loss 0.0168, Interval Loss Val 0.0174\n", "Train Loss 0.0089, Interval Loss 0.0166, Interval Loss Val 0.0143\n", "Train Loss 0.0165, Interval Loss 0.0177, Interval Loss Val 0.0122\n", "Train Loss 0.0244, Interval Loss 0.0171, Interval Loss Val 0.0151\n", "Train Loss 0.0148, Interval Loss 0.0163, Interval Loss Val 0.0209\n", "Train Loss 0.0162, Interval Loss 0.0160, Interval Loss Val 0.0166\n", "Train Loss 0.0175, Interval Loss 0.0173, Interval Loss Val 0.0167\n", "Train Loss 0.0246, Interval Loss 0.0172, Interval Loss Val 0.0165\n", "Train Loss 0.0150, Interval Loss 0.0168, Interval Loss Val 0.0151\n", "Train Loss 0.0307, Interval Loss 0.0169, Interval Loss Val 0.0063\n", "Train Loss 0.0121, Interval Loss 0.0170, Interval Loss Val 0.0179\n", "Train Loss 0.0113, Interval Loss 0.0156, Interval Loss Val 0.0127\n", "Train Loss 0.0139, Interval Loss 0.0156, Interval Loss Val 0.0155\n", "Train Loss 0.0109, Interval Loss 0.0165, Interval Loss Val 0.0148\n", "Train Loss 0.0090, Interval Loss 0.0168, Interval Loss Val 0.0269\n", "Train Loss 0.0246, Interval Loss 0.0169, Interval Loss Val 0.0161\n", "Train Loss 0.0228, Interval Loss 0.0160, Interval Loss Val 0.0166\n", "Train Loss 0.0124, Interval Loss 0.0156, Interval Loss Val 0.0124\n", "Train Loss 0.0120, Interval Loss 0.0160, Interval Loss Val 0.0134\n", "Train Loss 0.0214, Interval Loss 0.0167, Interval Loss Val 0.0192\n", "Train Loss 0.0194, Interval Loss 0.0165, Interval Loss Val 0.0142\n", "Train Loss 0.0092, Interval Loss 0.0153, Interval Loss Val 0.0148\n", "Train Loss 0.0198, Interval Loss 0.0165, Interval Loss Val 0.0115\n", "Train Loss 0.0139, Interval Loss 0.0172, Interval Loss Val 0.0144\n", "Train Loss 0.0069, Interval Loss 0.0165, Interval Loss Val 0.0155\n", "Train Loss 0.0109, Interval Loss 0.0160, Interval Loss Val 0.0197\n", "Train Loss 0.0243, Interval Loss 0.0165, Interval Loss Val 0.0154\n", "Train Loss 0.0124, Interval Loss 0.0172, Interval Loss Val 0.0245\n", "Train Loss 0.0234, Interval Loss 0.0160, Interval Loss Val 0.0198\n", "Train Loss 0.0129, Interval Loss 0.0163, Interval Loss Val 0.0144\n", "Train Loss 0.0201, Interval Loss 0.0168, Interval Loss Val 0.0098\n", "Train Loss 0.0203, Interval Loss 0.0157, Interval Loss Val 0.0135\n", "Train Loss 0.0161, Interval Loss 0.0159, Interval Loss Val 0.0194\n", "Train Loss 0.0147, Interval Loss 0.0160, Interval Loss Val 0.0232\n", "Train Loss 0.0165, Interval Loss 0.0166, Interval Loss Val 0.0133\n", "Train Loss 0.0175, Interval Loss 0.0165, Interval Loss Val 0.0261\n", "Train Loss 0.0184, Interval Loss 0.0161, Interval Loss Val 0.0124\n", "Train Loss 0.0114, Interval Loss 0.0170, Interval Loss Val 0.0103\n", "Train Loss 0.0224, Interval Loss 0.0160, Interval Loss Val 0.0199\n", "Train Loss 0.0227, Interval Loss 0.0154, Interval Loss Val 0.0085\n", "train diffusion completed, total time: 5660.153350830078.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["The seaborn styles shipped by Matplotlib are deprecated since 3.6, as they no longer correspond to the styles shipped by seaborn. However, they will remain available as 'seaborn-v0_8-<style>'. Alternatively, directly use the seaborn API instead.\n"]}, {"data": {"image/png": "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*********************************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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["condition_dropout = 0.15\n", "n_iterations = 2e4\n", "batch_size = 64\n", "val_interval = 100\n", "iter_loss_list = []\n", "val_iter_loss_list = []\n", "iterations = []\n", "iteration = 0\n", "iter_loss = 0\n", "\n", "train_loader = DataLoader(\n", "    train_ds, batch_size=batch_size, shuffle=True, num_workers=4, drop_last=True, persistent_workers=True\n", ")\n", "val_loader = DataLoader(\n", "    val_ds, batch_size=batch_size, shuffle=False, num_workers=4, drop_last=True, persistent_workers=True\n", ")\n", "\n", "scaler = GradScaler()\n", "total_start = time.time()\n", "\n", "while iteration < n_iterations:\n", "    for batch in train_loader:\n", "        iteration += 1\n", "        model.train()\n", "        images, classes = batch[\"image\"].to(device), batch[\"slice_label\"].to(device)\n", "        # 15% of the time, class conditioning dropout\n", "        classes = classes * (torch.rand_like(classes) > condition_dropout)\n", "        # cross attention expects shape [batch size, sequence length, channels]\n", "        class_embedding = embed(classes.long().to(device)).unsqueeze(1)\n", "        optimizer.zero_grad(set_to_none=True)\n", "        # pick a random time step t\n", "        timesteps = torch.randint(0, 1000, (len(images),)).to(device)\n", "\n", "        with autocast(enabled=True):\n", "            # Generate random noise\n", "            noise = torch.randn_like(images).to(device)\n", "            # Get model prediction\n", "            noise_pred = inferer(\n", "                inputs=images, diffusion_model=model, noise=noise, timesteps=timesteps, condition=class_embedding\n", "            )\n", "            loss = F.mse_loss(noise_pred.float(), noise.float())\n", "\n", "        scaler.scale(loss).backward()\n", "        scaler.step(optimizer)\n", "        scaler.update()\n", "        iter_loss += loss.item()\n", "        sys.stdout.write(f\"Iteration {iteration}/{n_iterations} - train Loss {loss.item():.4f}\" + \"\\r\")\n", "        sys.stdout.flush()\n", "\n", "        if (iteration) % val_interval == 0:\n", "            model.eval()\n", "            val_iter_loss = 0\n", "            for val_step, val_batch in enumerate(val_loader):\n", "                images, classes = val_batch[\"image\"].to(device), val_batch[\"slice_label\"].to(device)\n", "                # cross attention expects shape [batch size, sequence length, channels]\n", "                class_embedding = embed(classes.long().to(device)).unsqueeze(1)\n", "                timesteps = torch.randint(0, 1000, (len(images),)).to(device)\n", "                with torch.no_grad():\n", "                    with autocast(enabled=True):\n", "                        noise = torch.randn_like(images).to(device)\n", "                        noise_pred = inferer(\n", "                            inputs=images,\n", "                            diffusion_model=model,\n", "                            noise=noise,\n", "                            timesteps=timesteps,\n", "                            condition=class_embedding,\n", "                        )\n", "                        val_loss = F.mse_loss(noise_pred.float(), noise.float())\n", "                val_iter_loss += val_loss.item()\n", "            iter_loss_list.append(iter_loss / val_interval)\n", "            val_iter_loss_list.append(val_iter_loss / (val_step + 1))\n", "            iterations.append(iteration)\n", "            iter_loss = 0\n", "            print(\n", "                f\"Train Loss {loss.item():.4f}, Interval Loss {iter_loss_list[-1]:.4f}, Interval Loss Val {val_iter_loss_list[-1]:.4f}\"\n", "            )\n", "\n", "\n", "total_time = time.time() - total_start\n", "\n", "print(f\"train diffusion completed, total time: {total_time}.\")\n", "\n", "plt.style.use(\"seaborn-bright\")\n", "plt.title(\"Learning Curves Diffusion Model\", fontsize=20)\n", "plt.plot(iterations, iter_loss_list, color=\"C0\", linewidth=2.0, label=\"Train\")\n", "plt.plot(\n", "    iterations, val_iter_loss_list, color=\"C1\", linewidth=2.0, label=\"Validation\"\n", ")  # np.linspace(1, n_iterations, len(val_iter_loss_list))\n", "plt.yticks(fontsize=12), plt.xticks(fontsize=12)\n", "plt.xlabel(\"Iterations\", fontsize=16), plt.ylabel(\"Loss\", fontsize=16)\n", "plt.legend(prop={\"size\": 14})\n", "plt.show()"]}, {"cell_type": "markdown", "id": "fd2b79a4", "metadata": {}, "source": ["## Generate synthetic data\n", "(Only for verifying the model and classifier guidance is working)"]}, {"cell_type": "code", "execution_count": 14, "id": "98e17f78", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 100/100 [00:02<00:00, 43.69it/s]\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAbsAAAG7CAYAAABaaTseAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjcuMCwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy88F64QAAAACXBIWXMAAA9hAAAPYQGoP6dpAAArJElEQVR4nO3dW4ydVd3H8T/STjttp+fT9DDttFNajqWVFgGRg4igJgpRDpIYTzFegOHCIDGKN3pNSFSi8RSJ0UQTJYCiQggBApSWgpaWnqSn6bTT6fR8mh54L8xr1Hf9fuy9+nS/e9Z8P5drde397Od59v53kt/6P+e9++677wYAAAV73//3AQAAcK5R7AAAxaPYAQCKR7EDABSPYgcAKB7FDgBQPIodAKB4FDsAQPGG1foPzzvvvHN5HGdt+PDhyfGTJ082+EjQSOeff35y/PTp0w0+krK8733p/wefOXNGrnG/Eap3Rc4adWwR/vgaJecz4ezUcl75yw4AUDyKHQCgeBQ7AEDxKHYAgOJR7AAAxaPYAQCKd16tz7NTEe+IxsV9B2ukN/e4c+LfOcfRDOcu9/5Sn8mtafbtClVf9yqpLT7vpVFbgIYN07upTp06Vel7qevkvk/N8F1zGvUbUfU2lVq+G/xlBwAoHsUOAFA8ih0AoHgUOwBA8Sh2AIDi1dwIOicJ1tLSIudU8s0l4gZrGjP32NQ5zz0PjTpHLrGnUnnuuuc0/q36Xhms956T85lcorEZzkNO4rLqa+vWqN9El1Y92xRiPRp1DXPex/0O1LT+rFYDADAIUOwAAMWj2AEAikexAwAUj2IHACgexQ4AULyaG0G7+Gszyz1uFXOtullwM0Tac85RI2Pmrkm04iLZzRCRbwaNvPdUg+aqmzPjveVcd/cdzPlNrHo7EY2gAQAIih0AYAig2AEAikexAwAUj2IHACheJWnMRj3KvRlU/Tj5wXqOcj+TSmFV3dAWZ6fE73ROIlStea91g1WzJ7PP5hj4yw4AUDyKHQCgeBQ7AEDxKHYAgOJR7AAAxaPYAQCKV/PWA9e4U71ETrPPXIM1Ku2ivo1qRl21Rl73HIM1Xt3s57VqzfCdbtQ5b5YtDqrhc7P/5rD1AACAoNgBAIYAih0AoHgUOwBA8Sh2AIDiUewAAMXTedf/0gzRa0dFd0+ePNngI6mPO685cd+cqHTVT2XIiWRXHfF2r6fmmr2Lfc55aOQTNxoV08/ZOhKRt0VKRfEjqr0euffe8OHDk+Pud899pirviaqv09niLzsAQPEodgCA4lHsAADFo9gBAIpHsQMAFK/mRtC5yZoqqeRRRHOnLodaA1+nGZr75miGVKO7VwbreW2kwdrk2KUnc469paVFzg0MDNT9ejnc9ymn1tRyHvjLDgBQPIodAKB4FDsAQPEodgCA4lHsAADFo9gBAIo3qLYeODlx7UZRTaoj/PHlNLd2MeVmb3Scw51bJec8jBw5Us4dP348Od4MW06q3jKR+5lyGhYPVjnNo911qvpeaeQ2mkap5bj5yw4AUDyKHQCgeBQ7AEDxKHYAgOJR7AAAxaPYAQCKV8nWAzWXE6d10WY3N5Ri9SV+Vqfqru+DVU7sfzDHzHOe5FD1do9m2D5StZxtNM3APa3hxIkT77mev+wAAMWj2AEAikexAwAUj2IHACgexQ4AULxB1QhaNZON0A1lc5N8OY2l1Xs1MjGYk74bNWqUXHP06NG63yfnGKpOBjZ7CrHZj09phnTiYD1350JOYrVENIIGACAodgCAIYBiBwAoHsUOAFA8ih0AoHgUOwBA8dKdhhNc5LhRcXL3ejmx/6ob6zZDY1h3fOoc5Vwn10z22LFjdR+DuxZqW4mzcOFCOdff358c7+3tlWtGjBgh5wYGBpLj7rw2Khre1dUl5zZt2iTnWltbk+Pu2jbKUNum4jT78TUT/rIDABSPYgcAKB7FDgBQPIodAKB4FDsAQPHOaSPonDW56aKcxs05chtLN7Ovfe1rcu7tt99Ojl9++eVyTUtLi5xTqUaXuBwzZkzdcz09PXJNe3t7cnz37t1yza9+9Ss5p7i0Y9XUeTh8+HDW611xxRXJ8ZUrV2a9HvI1QxK+alU3FKcRNAAAQbEDAAwBFDsAQPEodgCA4lHsAADFo9gBAIpX89YDF7lvhgbIQ0lHR4ecu+aaa+TcpZdemhwfPXq0XDN16tTk+Nq1a+Uad3xq3WWXXSbX/OMf/5BzbW1tyfHp06fLNbt27UqOq+bHERF79+6Vc7NmzUqOf/e735Vrbr31Vjn32GOPJcfvueceueapp55KjrvjXr58uZy78cYbk+Pf+c535Bp3/qpsIJ3buDmnWfxQo7YENENT87Ntws9fdgCA4lHsAADFo9gBAIpHsQMAFI9iBwAoHsUOAFC8c7r1IDci3AxynqKgzpE7dy7KfeDAgeT4oUOH5BoXJz9+/Hhd4xH62Ldt2ybXqO0KERETJ05MjrvP5Dr3jxs3Ljnuzrl68sKOHTvkGrXFISJi1KhRyfGRI0fKNf39/XJuzpw5yXF3jtSTJtyTHH7605/KObWFZcuWLXJNZ2ennPvRj36UHHfnVX3ewfy7ouQ+TUWdi2Z/ekwOd45OnTr1nuv5yw4AUDyKHQCgeBQ7AEDxKHYAgOJR7AAAxas5jekSUMrw4cPrXnPy5Mm61zgqXRThE0ZqnUsEqWN352727Nly7pZbbkmOu+N2TZ1VOnDy5MlyzbBhw5LjR48elWu6urrkXG9vb3LcfaYTJ07IOXV8KiHpvPXWW3Ju8eLFck4lCn/zm9/INR//+MflnEqLqs8aEbFu3brk+Ny5c+WaRYsWybknnngiOe7O686dO+Wcaub9sY99TK55+OGHk+MuPazurwidUHQ/ge43rOrfqhzNkJ5shmOopYzxlx0AoHgUOwBA8Sh2AIDiUewAAMWj2AEAikexAwAUr5KtB1U3I62S23rgjk99pgkTJsg1+/fvT45/5StfkWtcA1PVWNc1hh0YGJBzkyZNSo4fOXJErlFUPD7CN4JW7+WOwV1DxUXkVTPjGTNmyDWuGbXahnHw4EG5xh2firtfdNFFdR+DO3fjx4+Xc2vWrEmO33jjjXLNAw88IOceeuih5HhfX59co7YRuHv82LFjck5F4V2zbHefz5o1Kzm+evVquebXv/61nFNyt081StVbD9T2Lve7x9YDAACCYgcAGAIodgCA4lHsAADFo9gBAIpHsQMAFE+3Uf8vLS0tck5Fgd0TAlSM1G1xyNnK4Na4uZtuuik5fskll8g1U6ZMSY63t7fLNa6TfU70eu/evXJObZtw8eqZM2cmx7u7u+Ua13lefV639WDatGlyTp0LF1NWT5ro6emRa9x2CvWZ1LmL0NtAIiJefvnl5PiBAwfkGnUN3RMCXNf+a6+9Njm+bdu2utdE6Bi6i6dPnDgxOe7uf3cvL1iwIDnutv+op0lE6O0oS5YskWs2b96cHHdPjHD3pbqP3O9AzlaGnDW5v+Xuu3s2+MsOAFA8ih0AoHgUOwBA8Sh2AIDiUewAAMWrpBG0SupUnZ7MaUbt0l4PPvignJs8eXJy3CWFxo0bV/cxuNdTTW1Vo+D3otKx7vVUw2LVTDkiYt68eXJu06ZNyXGXWG1ra5NzKqnm0mMnTpxIjrvmzC6xp9Knn/zkJ+WaN998U87lNNBV96s63xERY8aMkXPTp09PjrsUrmqM7I7DNVZXx+caN992221yTqVPXbpz3759ck41+nbJYnX+Dh06JNc8/PDDck6lT91xuxSuSha7+1/99ro0vrvHW1tbk+PuvNIIGgCAoNgBAIYAih0AoHgUOwBA8Sh2AIDiUewAAMWruRG0kxOVVlwM3kVmlS996UtybvTo0XKur68vOT5+/Hi5RkVt3XG7iLyKI6stDu9Fxbzd9VPnoaurS65xrzd27NjkuGqQGxFx2WWXyTkVR3bnSF0P10TYxfTXr1+fHHfNeN02h6NHjybHXURebc/o7OyUa1Tj8ggdXVex8Aj/3bjhhhvqep8I3Xz7tddek2tWrVol59S5cMf9+uuvyzm3bUKZP39+clxth4nQDawj9JYA14zdUa+X0wjabVdw2xLcFoOzwV92AIDiUewAAMWj2AEAikexAwAUj2IHACheJY2gm8HNN9+cHL/yyivlGpcWUuk7lyJSKT/X7Dmn8bVLrLpmvCrx5c6DWjNx4kS5xqUQ1XltaWmRa1yTXHUu3DlX6TF3bV0zapUodCk/93nfeeed5Li7tur4VLPiCJ8wVek7d21dalDd5y5hqhK1y5Ytk2ueffZZOafuleXLl8s1rpG2a4auqKStu07u+/nNb34zOX748GG5xt3nx48fl3P1yklwRujfPfdbSSNoAACCYgcAGAIodgCA4lHsAADFo9gBAIpHsQMAFK+YrQdPP/10cvyZZ56Razo6OuScirsPG6Z7Z+/fvz857hqbqsbIERHTpk2Tc4qLtKsovIs2q7j2yJEj5ZoDBw7IObWV4dixY3KNoxoqu2NQn8k1Z3aRexWR7+/vl2suueQSOafOrdv+oLa9dHd3yzWuEbraujFz5ky5Jie27rbRqPtVNVOOiPjd734n51Tk/uKLL5ZrLrroIjn35JNPyrl67dq1S8657/S2bduS44888ohc4+7lGsvBOcXWAwAAMlHsAADFo9gBAIpHsQMAFI9iBwAoHsUOAFC8ptt64Dpyuwju4sWLk+Ous/ull14q51Ts38W1Vedy14FfRfEjIqZPn54cHzFihFzjzt/cuXOT4yq2HqE7+rv3cZ9JbXNwt6Hr4D5hwoTkuDruCP1UAfVaEX4biIrPu6ceuAi/ur6zZ8+Wa9RTKHp7e+Ua9zQJdT1cJ3v3G6GuYWtrq1yjnvKwdu1aucZ9P6dMmSLnFBXtj4jYvHlzctzdy+r3aPv27XKN2xqkruGqVavkGve0hhUrVsg5Rf0WuN+9qrH1AACAoNgBAIYAih0AoHgUOwBA8Sh2AIDi6a7GdVAprKoTnD//+c/lnEpNuYbFW7durfsY+vr65NzRo0eT45MnT5ZrXHpMJdhcw2LXWHfjxo3J8QULFsg1Klnpmlu71KxKfroG2y6NqRJ2PT09co1KNaqGzhERZ86ckXMf+MAHkuPuHLmUsDpHzz77rFyjGgl/+MMflmtcolZ9b9y1dY2g1bmdMWOGXKPuidxjUMlPl+TbsWOHnFOpWff9VGlW1+RbNZiPiFi6dGly/LrrrpNrvvjFL8o5dXzut1ylLt39lZPUdL9tteAvOwBA8Sh2AIDiUewAAMWj2AEAikexAwAUj2IHAChe0zWCdu644w45p5r4zpkzR65xTW1V/NvFaVVU2q1x8fT29nY5p+zcuVPOLVq0KDm+YcMGuUZF+937uAbIkyZNSo678+C2JagIs9sqMHbs2OT4wMCAXOMaS6stJ24rg2vqrI7PbSN47bXXkuPLli2Ta9RxR0T09/cnx13s38XJ1VYG19RcRfhd43LXhFxF15988km55sCBA3JOnT/XNFxtQ3JbmtzWJfWZ5s+fL9e8+uqrcu5zn/tcctxt/6mxhJxTNIIGACAodgCAIYBiBwAoHsUOAFA8ih0AoHgUOwBA8Sp56kGV3BaHxYsX170upwu64zqaqyiyi2SrKH5ExKlTp5LjEydOlGvcdgoVn7/88svlGrX1wEXGN2/eLOfU53VPAXDx77179ybHp06dKteoLvIuin/o0CE5p7aWuG71bivDkiVLkuPqqRUReouBehpCRMT06dPlnLvPG0Vtf+ju7pZrHn/88brfx22VcU9G2b59e3LcfZ/U9Zg1a5Zc47awqC0d7t5zv6Nq+42L9qvtKG4rTw63hasW/GUHACgexQ4AUDyKHQCgeBQ7AEDxKHYAgOI1XSPor3/963LONQQePXp0ctwleFzCTjXqPXjwYN3H4BKh7ryqBrAu5ZTTdNo1ee3s7EyO9/b2yjWqkbF7L5U8jfDXXaVP1bWI0OlOdx5cg2F1PdwxqBRdhL5fXGLvs5/9bHLcNex2x+CSmlVyCdiVK1cmxzdt2iTXuOSzOhdtbW1yjbvPVeLRJZWvv/765Lg7bpeMXbp0aXI89/q9+OKLyfFrr71WrlG/YY1sEE0jaAAAgmIHABgCKHYAgOJR7AAAxaPYAQCKR7EDABSv5kbQrsHwmTNn6n7j4cOHJ8ddQ+D169fLOdVg2DVRdQ2VVXNT93oqun7y5Em5xjWjVuvcdgW3zUHF593xqbj2jBkz5Bp3P6jjc8141ZaJCH/sioopT5s2Ta7ZsmWLnFPXwx2325aguAj6tm3bkuOqWXGEj9z39PQkx1Vj8Ah/LdT33Z1XFe3v6OiQa9asWSPn1HfabTlx22jU79Hy5cvlGvWb47YMua0M7vhyqC0Q7nfPNWpvJvxlBwAoHsUOAFA8ih0AoHgUOwBA8Sh2AIDi1ZzGdHIagaoU4rp16+SaBQsW1P16EyZMkGtcE9rdu3fLOUUlzlzi0jXjVefVpcdcQ2WVAHTXSb2eSxru3btXzrnPq7j0qToOl1xUqdS+vj65Rl3biIipU6cmx12jcXf+1Ou5lGt/f39y3N17ak2ETi5u2LBBrlHN0yP0vecabKs05qpVq+Qa931XjdXdd90131bJT3evqNSsSwK///3vl3Pu+ub485//nBz/4Q9/KNfcc889lR6DSv7npP7/43XPajUAAIMAxQ4AUDyKHQCgeBQ7AEDxKHYAgOJR7AAAxat560FO7NM1Nz148GByvLOzU65xEXTVSNg1wnVNbRV3HoYNS59OF7d3WwXU+XORdtfcWkW5VRPtCB3LVo2CIyIGBgbknDrnLvbvtjKoxuEuVq+a3boYd07jZhdBV9H+iIh9+/Ylx919tHXr1rrXqPs1QjcAd42HN23aJOfUuXVNhF988cXk+OrVq+Uad93Vd6O3t1euue++++Sc+u66e1ldj/nz58s17hypJtGucb/banHnnXcmxx999FG5puqtAme7xUDhLzsAQPEodgCA4lHsAADFo9gBAIpHsQMAFI9iBwAoXs1bD1yUVXXNd1sFlD/84Q9y7stf/rKcU9Fm94SAv//973Lu05/+dHL82LFjco36vO7pCu4cqYi8i5OfPHlSzqlr6M6Riva77Q8zZ86Uc+ozuScvzJ07V86peL+La6vO+C6SPWPGDDmnoubt7e1yjbuPcu7lnKd+qO0F7vW2bNki1+zZs0fOqevutgqoLQFq21JExLJly+TcJz7xieS42uoR4bcGqe/aSy+9JNd84QtfSI67LR1u+5S6j9zvgNsSo87tY489Jtfk/M47OU/RqQV/2QEAikexAwAUj2IHACgexQ4AUDyKHQCgeOe0EbRLMilz5syRc66xtEqqzZo1S65xjXBfe+215HhXV1fdr+fSky7lqlJJ7vVc0qqtrS057poSq8+kXivCp7NU6mzHjh1yjfu8qrmvSzuq9KRr9uwSe6optmrSG+GbD6uG3S7VqxKAixcvlmtcyvWVV15JjrvvjHsvlWadOnWqXDN9+vTk+JVXXinXuHvPvZdy4sQJOXf33Xcnx5955hm5Rn3fv//978s1KsEZoX9jn3rqKbnG3ctLlixJjrt7T/1+qATue1G/8zn15N/xlx0AoHgUOwBA8Sh2AIDiUewAAMWj2AEAikexAwAU77x3a+yu6SK9Khp+/Pjxug/IRfEfeOABOTd58uTk+Lhx4+Sa8ePHyzl1WlQsPEI3X3VNXl2U+8iRI8lxd47++te/yrmrr746Oe5uAdXM2J2HSy+9VM6pLSI//vGP5ZqrrrpKzj3//PPJ8QsvvFCuueKKK5Lj7h7/xS9+IefUdg+1JSEiYt26dXJObb9xjaDVlhh3HjZv3iznVKPvCy64QK5x76W+A93d3XKNipq7bRvqOxOhtz8sXLhQrnHbUdR1HxgYqHuN2+Jw++23yzkV+3dNvl9//XU5973vfS85vmLFCrkmZ1uao7YeuK0MtZQx/rIDABSPYgcAKB7FDgBQPIodAKB4FDsAQPEodgCA4lWy9aBKl19+uZxznfEffPDB5Ljrfu+6/av4cM52Bfc+7ikFap2L+k6bNk3OqVi22yKiYsDuM6ktGO71XFf1/v5+Oac647vPpK6tO24X+1+wYEFy3G0rcVti1Od138G9e/cmxw8ePCjXuOP729/+Vvfrudi/+kzuSQlqS5M7d277g/q8aptFhH9qilq3a9euul9P3ccR+v6K8Ocix9NPP50cV/dDRMQ3vvGNSo9Bcfd/Ldsf+MsOAFA8ih0AoHgUOwBA8Sh2AIDiUewAAMWrJI2p5txL33XXXcnxiy++uJbD+T9USrKvr0+ucZ9p1KhRyXHVnDZCNyp1jaBdA1j1mVRKLSLiwIEDck4lltwadY7cMbgkpEqPqZRm7nu1trbKNSqh6+6HiRMnyrk9e/Ykx13C1H0m1eh4xIgRco26L1Xz4wifnlTJZ3fcLoWovhsuTai+ux/96EflGpesVN81d21dWlqlO13KVaUx3XG7NLI6vuuvv16uaWtrk3PqnLsmzLNnz06Ou++0e70cNIIGACAodgCAIYBiBwAoHsUOAFA8ih0AoHgUOwBA8c5pI2gX21Ux+DvuuEOucU1j1VYB1SA3wkdwc7ZTqGi4azDsotz79u1Ljk+YMEGucVst1Od111ZFhF282m21UI2E3Xl1EXl1j7nGuuq91Pl+r2OYNGlSctxtK8lpapvTNNxtA3FN0lUcf8qUKXKNa9S+adMmOaeoOL67V7Zv3y7n1PGp346IiNtvv13OLV26NDnumqSr75P7HXBNyFVT8xtuuEGucb97ygsvvCDnPvShDyXH2XoAAECDUewAAMWj2AEAikexAwAUj2IHACjeOU1jVp3GeeSRR+ScSvm55rluTiW3XGNdlepyjZZdqlE1981JuUb4lFi9x/C+9+n/J7nzqs6RS/KptGOE/rwularOn/tM7l5W58g1Dc9J7LnzqowePVrOuddTx+BSqe477a6vor4bq1evlmtcGlOdc3d/fepTn5JzKpnqGoCrxtcusa0Sl057e7ucc785KkHsmm/n1IYc7rjd+ftf/GUHACgexQ4AUDyKHQCgeBQ7AEDxKHYAgOJR7AAAxdNZznpeRERCXfRauffee+Wci7i6RqqKiwir6K6LtKvmw+59VMPdCB2rd41c3XupXSZujXovFwN2EX51DW+++Wa5ZuPGjXJOHfsHP/hBuUZdQ3e/qqbEEfq8uvPgGjSrc+vufxVpf+655+Qa1/haxfSXLFki17j7cv369cnxq6++Wq4ZP358ctw1bn7ppZfknGrqvHv3brnGbUtQ251cA3B13d295677vHnzkuO5W67WrFmTHL/lllvkGiVnW5WTs+bf8ZcdAKB4FDsAQPEodgCA4lHsAADFo9gBAIpHsQMAFK+Spx6ouRpf+j98+9vflnNTp06VcyoaPmbMmLrXROhO4677fWtra3JcRZQjfDxdfV63/cHFfVVk250jtaXDrXHd79W94ta4zv2qC797MkRO/NvF3Xt7e+s+BvckDHWPdXd31/168+fPl2vc8eVsf3DnSJ3bCRMmyDWzZs1Kjrv7wd3/f/zjH5PjbtuS206hrpP6HYjQWzrctXXXSW0jcE8BWLZsmZxTWwzc8eVw91FO3ahlDX/ZAQCKR7EDABSPYgcAKB7FDgBQPIodAKB4laQxq/Twww/LOZfCcs1cc15PNXNVzWkjIqZNm5Ycd8fmUlMq1aUSXRE6nRihU6HquCP0OXLH4BoMq8SeO+7JkyfLuWPHjiXHXTNelbTt6enJOgbFpTtdAlA1t3Zf1bFjxybH3f2lGo1HRMyYMSM57n4H1q1bJ+fU8bk0prrH3HfQJQ137NiRHF+xYoVc4865SlIfPnxYrlFykqwROlHu0s0XXnihnJsyZUpyfPny5XJN1XJqjbuX/xd/2QEAikexAwAUj2IHACgexQ4AUDyKHQCgeBQ7AEDxmm7rgeOaRKsIs4teu3iuihXPmTNHrnnqqaeS4wsWLJBrVMw8ImLRokXJ8b1798o1rnGtahbsYvDjxo1Ljruor2vGq5pYd3V1yTWq0XKEbursIu27du1Kjrt73DUAV/eYi8i7667uPddgWEXX3T3uztEbb7yRHJ87d65c4z6Tei+3pqOjIzmurl+E3yKitiW4xuo/+9nP5JzaluPuf7WNwDW5d1sPJk6cWNexRUSsXLlSzv3kJz9Jjm/btk2uyUEjaAAAzgGKHQCgeBQ7AEDxKHYAgOJR7AAAxaPYAQCKV8nWgxEjRiTHXef5HK7TvnpaguqKH+Gj16qbuHu9rVu3JsfVFoKIiEmTJsm57u7u5Lg63xH+nKttBHv27JFrOjs7k+Oqg3yEj15v2bIlOe661ec8RcFtz1DH567F6tWr5dzSpUuT48OHD5dr3H2kuFi9iqe77+3mzZvlnNo24Z6QsXDhQjm3cePG5Ljb9nLJJZckx9euXSvXOOo7rbZ6RPhtDurpBup7FqEj/G7rgdpeEKG3vbhtKnfeeaeca2buOrktNv9aX+XBAADQjCh2AIDiUewAAMWj2AEAikexAwAUT8fm6qASgFU3+3QNUVVS7eDBg3KNS3eqRJV7PZVcdGtcI1zVfNidV5fYU1SiMSJi9+7dyXGXNHTNt6dPn54cV0m5iIiBgQE5p86fS6yqFN2GDRvkmuuuu07OqWbUrnm0S8sdOXIkOd7f3y/XzJw5MznuzoO7hi+//HJyfNOmTXKNOz71G+HuPZXCdfeX+z6pFK67v8aMGSPnVOLXfd/b29uT4+48uGTx4sWLk+OvvvqqXJPzu+zu5VqSkPVQx+eaz9eCv+wAAMWj2AEAikexAwAUj2IHACgexQ4AUDyKHQCgeDU3gnbNfVX0NDeenuPuu+9Ojs+aNUuumT9/vpxTx+ca+KpGwjt37pRrRo8eLedUBNdFpd3WAxXvd6+njsHFjd11V7ebO69qu4Jb55pHX3TRRXWvcdsSrrrqqrrXuKbTKu6utjhE6O+n+565rTxtbW3J8fXr18s17jOpxtJuu4Lijrujo0POqfdyP4FuS0Bvb29yfOzYsXKN2kYwb948ucY1gr755pvlXJVcw253XypVb2WopYzxlx0AoHgUOwBA8Sh2AIDiUewAAMWj2AEAildzGtM1D1VzOc2eqzZt2jQ5d//998u5hQsXJse3b98u16hUknucvEuwqfSkS1y+/fbbcu6CCy5Ijru0qEr5uZSaa9iqEnHu9VyiUJ1bt0YlDVWT3oiI559/Xs5ddtllyXHVRDvCJ0xV8s3dR6rRsjsG993o6+tLjrvGyOoYIvQ5d78rqpmxuo8jfHN39Xvk7j2XDFTXw50HldRU5yfCf6cfeeSR5Li77o46F67BdtXUeXW/K6QxAQAIih0AYAig2AEAikexAwAUj2IHACgexQ4AULxKth40Sk4zavfxXPxbNfe955575BrVGNY1Sn3iiSfk3I033pgcnzlzplzjmuSqSLs67ggd93XR/qlTp8q5np6e5LhqFBwRceDAATmnPpNrLK22GLgtGG7LiYqGv/HGG3KNuyfuvffe5Lhrntva2poc7+zslGtcE/K33norOe62vbgG4Oq93L2nzpH6rBG+mbfaTuTur8OHD8u5nN/EuXPnJsfV9yIi4k9/+pOc+8tf/lL3MZSIrQcAAATFDgAwBFDsAADFo9gBAIpHsQMAFI9iBwAons7yn2M5T0pwHchznrCwa9cuOXfNNdfUfQx79+5Njo8YMUKuufvuu+VcV1dXctxF2l38W0WsXQxeRaX3798v16iO+RERAwMDyXEX43Yd4bdu3Zoc7+jokGtUB3f3VIGJEyfKOdWd/6GHHpJrfvCDH8g59SQM9T4ReivIc889J9d85jOfkXNqm4/b2uLm1HdA3Q8R+gkZ7tq6bTnqnh03bpxcs2fPHjk3ZcqU5LjbRrNo0aLk+Oc//3m5xn0/1TYM9+QFxz1ZQMl5SsH/B/6yAwAUj2IHACgexQ4AUDyKHQCgeBQ7AEDxmq4RtEvENUO659Zbb5Vzt912W3LcNU12DYtVk9w5c+bINTmpxpUrV8o1qhn17t275Ro3p5KGLj3mUn7qMx06dEiuUfeRuxYuUas+0/r16+WaDRs2yLlrr722rveJ0InCr371q3LNo48+KufefPPN5PjYsWPlGpVOjNDpzh07dsg16rq7tKNL7o4aNSo5fuTIEbnGJSHV67l75a677pJzivtNzEmhuzXNnKx0v221HB9/2QEAikexAwAUj2IHACgexQ4AUDyKHQCgeBQ7AEDxmm7rQSO5SK+KSi9ZskSuUY16f//738s1LnqtjsFFr11MX22BUA13IyKmTp2aHHcNp9WWiYiI9vb25PjGjRvlmpytB47a5uCaPbv7XzXYdnFoF2lXX0nX5PjgwYPJ8e3bt8s13d3dck7dY5MnT5Zrli1bJufUfd7T0yPXzJgxIzmuIv8REe+8846cmzBhQnLcfafvv/9+OdfZ2Zkcf/zxx+WaZlD19q6cpv5Vq+W9+MsOAFA8ih0AoHgUOwBA8Sh2AIDiUewAAMWj2AEAindOtx5UvV2hkVFWpaOjQ85t27at0vf65S9/mRwfOXKkXOPOuerG7uLaKqb8wgsvyDXuyRD79+9PjqttFhERhw8flnPqM7nu/Oeff35yPGc7QITeeuC66bsnV6hzntOd/8ILL5Rr3HVXTzBYvXq1XLNq1So59+yzz8q5eqknPET466SuuzuvAwMDci7nCQE5a9x3uhl+E9V5PX36tFzjvu9qq5HbMuHe61/r3/NfAAAwyFHsAADFo9gBAIpHsQMAFI9iBwAo3pBuBF2iO++8U86pJKlLOalUo0uPrVixQs7ddNNNdR+DamDt5tz9OmnSpOR4a2urXHP06NG6jyFXS0tLXeMROsG2YcMGuea3v/2tnFNNp1955RW5xjUHVz8z7ryqNe5eUencCN3U3KUx+/r65BzOjZzEKo2gAQAIih0AYAig2AEAikexAwAUj2IHACgexQ4AULxBtfXARY5VLLUZmqi6Y3BzLmpbpfvuu0/OPffcc8nxj3zkI3KNavYcoZtYT548Wa5xTZMfe+yx5PjVV18t14waNSo57qLzJ06ckHNbtmxJjrvYv2sorprkusbNa9asSY67a+G2MrgGyM0s5zeiao38zVFNmM82pv/fcj6T2wbivk852HoAAEBQ7AAAQwDFDgBQPIodAKB4FDsAQPEqSWOquWZ4ZHyuRn2mnJRr1cfgEmzf+ta3kuNtbW1yzY4dO+p+r+eff16umT17tpzr7u5OjrsGw2vXrk2OuzTmvHnz5Fxvb29yfN++fXKNakrsXm+oUY25jx071rBjUGnHiIjTp0837Diq5D6T+m1xvzkqPezWqMbljjvuWl6Pv+wAAMWj2AEAikexAwAUj2IHACgexQ4AUDyKHQCgeIOqEXQjqRj6yZMnG3wk/1czNLeuWldXl5zbtGlT3a83f/58Obdz587kuGt+nBMzz71Og3UrT4n3ZYmfyVHbCKreKlD1tg0aQQMAEBQ7AMAQQLEDABSPYgcAKB7FDgBQPIodAKB4g2rrgYrFRuRFY3Nixe4JAWfOnKn7GHJUHYduhs+US50L95mavVt9znctZ7tCzr1S4lMAcjXqiSWDdftDI4+brQcAAATFDgAwBFDsAADFo9gBAIpHsQMAFK/mNKZLYamXKLHZbbMjuTW4j2EwG8yp3hzq85b4WZsdaUwAAIJiBwAYAih2AIDiUewAAMWj2AEAikexAwAUT3dW/i9Vx2mHUkPURhqs56EZrm3u+5S4jUZtNWqGz+TuFTenfsNy772cc6Ga2bsm2u59qt7+UOXrDR8+XM6dPHmyIcfwH697VqsBABgEKHYAgOJR7AAAxaPYAQCKR7EDABSPYgcAKF7NTz1w8dycNWw9+CfO0T81w3lwT/Zw0fAcjfq8VW+ncJr5/nKa4d6rWjMcQy619cA9VcNtZfjX+uwjAgBgkKDYAQCKR7EDABSPYgcAKB7FDgBQvJrTmC4J0wzpnpzGtVU3tz5XDUz/m0sNuvdS58Jd26qPPSc1mHN8VafRGnmOGmWwJvaa/bibId2Z24R59OjRyfEjR47INY2S0+T73/GXHQCgeBQ7AEDxKHYAgOJR7AAAxaPYAQCKR7EDABTvnDaCbgaDOTKujt1tPailIWqt7xORd45ytqk0smGxWtPI+2Gw3peD9biHmmHDhsm5U6dOybmc7VNVNyHPUct78ZcdAKB4FDsAQPEodgCA4lHsAADFo9gBAIp3TtOYOYmgZm/yWqLc5FaVhlrKr+r7XCV0T58+XfdrnYvXy1F1yk/d5+4z5TQob1SD+Yi8dLNLc1d5fd375DR1zmly/+/4yw4AUDyKHQCgeBQ7AEDxKHYAgOJR7AAAxaPYAQCKV/PWg5z4a9WqjuA2+zaHlpaW5PjAwECDj2Twybm2udsf1HvlxKudZrhfc2PrzbCVoWrN0AA5RzPcR07OeWXrAQAAQbEDAAwBFDsAQPEodgCA4lHsAADFo9gBAIpX89YD1xlfxYddTFm9bW7HcBUbz43SqnVVx3ZzXq8ZosMupu+oY29kBF3dy+4JD43qFF81d6+4z1T10y5ynhAwWKP9JT7Bo+ptX1Vj6wEAAEGxAwAMARQ7AEDxKHYAgOJR7AAAxTunacycVFLVzXgbmQjKSYTmNAtu9rRXI9OizXDdqz6GZm6a3Az3XjMcQ9VyvzM5955L4arzV3XS3MlJoddy3fnLDgBQPIodAKB4FDsAQPEodgCA4lHsAADFo9gBAIpX89YDAAAGK/6yAwAUj2IHACgexQ4AUDyKHQCgeBQ7AEDxKHYAgOJR7AAAxaPYAQCKR7EDABTvfwBw7DKXFj68RgAAAABJRU5ErkJggg==", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["model.eval()\n", "scheduler.clip_sample = True\n", "guidance_scale = 3\n", "conditioning = torch.cat([torch.zeros(1).long(), 2 * torch.ones(1).long()], dim=0).to(\n", "    device\n", ")  # 2*torch.ones(1).long() is the class label for the UNHEALTHY (tumor) class\n", "class_embedding = embed(conditioning).unsqueeze(\n", "    1\n", ")  # cross attention expects shape [batch size, sequence length, channels]\n", "noise = torch.randn((1, 1, 64, 64))\n", "noise = noise.to(device)\n", "scheduler.set_timesteps(num_inference_steps=100)\n", "progress_bar = tqdm(scheduler.timesteps)\n", "for t in progress_bar:\n", "    with autocast(enabled=True):\n", "        with torch.no_grad():\n", "            noise_input = torch.cat([noise] * 2)\n", "            model_output = model(noise_input, timesteps=torch.Tensor((t,)).to(noise.device), context=class_embedding)\n", "            noise_pred_uncond, noise_pred_text = model_output.chunk(2)\n", "            noise_pred = noise_pred_uncond + guidance_scale * (noise_pred_text - noise_pred_uncond)\n", "\n", "    noise, _ = scheduler.step(noise_pred, t, noise)\n", "\n", "plt.style.use(\"default\")\n", "plt.imshow(noise[0, 0].cpu(), vmin=0, vmax=1, cmap=\"gray\")\n", "plt.tight_layout()\n", "plt.axis(\"off\")\n", "plt.show()"]}, {"cell_type": "markdown", "id": "a676b3fe", "metadata": {}, "source": ["# Image-to-Image Translation to a Healthy Subject\n", "We pick a diseased subject of the validation set as input image. We want to translate it to its healthy reconstruction."]}, {"cell_type": "code", "execution_count": 15, "id": "fe0d9eac-1477-4d6d-a885-d3c4acb4a781", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["input label:  tensor(2., dtype=torch.float64)\n"]}], "source": ["\n", "idx_unhealthy = np.argwhere(val_batch[\"slice_label\"].numpy() == 2).squeeze()\n", "\n", "idx = idx_unhealthy[4]  # Pick a random slice of the validation set to be transformed\n", "inputting = val_batch[\"image\"][idx]  # Pick an input slice of the validation set to be transformed\n", "inputlabel = val_batch[\"slice_label\"][idx]  # Check whether it is healthy or diseased\n", "\n", "plt.figure(\"input\" + str(inputlabel))\n", "plt.imshow(inputting[0], vmin=0, vmax=1, cmap=\"gray\")\n", "plt.axis(\"off\")\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "model.eval()\n", "print(\"input label: \", inputlabel)"]}, {"cell_type": "markdown", "id": "a7c8346a-6296-4800-b978-c10fcdf09779", "metadata": {}, "source": ["\n", "### The image-to-image translation has two steps\n", "\n", "1. Encoding the input image into a latent space with the reversed DDIM sampling scheme\n", "2. Sampling from the latent space using gradient guidance towards the desired class label `y=1` (healthy)\n", "\n", "In order to sample using gradient guidance, we first need to encode the input image in noise by using the reversed DDIM sampling scheme.\n", "We define the number of steps in the noising and denoising process by `L`."]}, {"cell_type": "code", "execution_count": 22, "id": "ca28e70c", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 125/125 [00:04<00:00, 29.37it/s, timestep input=124]\n", "100%|██████████| 125/125 [00:05<00:00, 24.68it/s, timestep input=1] \n"]}], "source": ["model.eval()\n", "\n", "guidance_scale = 3.0\n", "total_timesteps = 500\n", "latent_space_depth = int(total_timesteps * 0.25)\n", "\n", "current_img = inputting[None, ...].to(device)\n", "scheduler.set_timesteps(num_inference_steps=total_timesteps)\n", "\n", "## Encoding\n", "\n", "scheduler.clip_sample = False\n", "class_embedding = embed(torch.zeros(1).long().to(device)).unsqueeze(1)\n", "progress_bar = tqdm(range(latent_space_depth))\n", "for i in progress_bar:  # go through the noising process\n", "    t = i\n", "    with torch.no_grad():\n", "        model_output = model(current_img, timesteps=torch.Tensor((t,)).to(current_img.device), context=class_embedding)\n", "    current_img, _ = scheduler.reversed_step(model_output, t, current_img)\n", "    progress_bar.set_postfix({\"timestep input\": t})\n", "\n", "latent_img = current_img\n", "\n", "## Decoding\n", "conditioning = torch.cat([torch.zeros(1).long(), torch.ones(1).long()], dim=0).to(device)\n", "class_embedding = embed(conditioning).unsqueeze(1)\n", "\n", "progress_bar = tqdm(range(latent_space_depth))\n", "for i in progress_bar:  # go through the denoising process\n", "    t = latent_space_depth - i\n", "    current_img_double = torch.cat([current_img] * 2)\n", "    with torch.no_grad():\n", "        model_output = model(\n", "            current_img_double, timesteps=torch.Tensor([t, t]).to(current_img.device), context=class_embedding\n", "        )\n", "    noise_pred_uncond, noise_pred_text = model_output.chunk(2)\n", "    noise_pred = noise_pred_uncond + guidance_scale * (noise_pred_text - noise_pred_uncond)\n", "    current_img, _ = scheduler.step(noise_pred, t, current_img)\n", "    progress_bar.set_postfix({\"timestep input\": t})\n", "    torch.cuda.empty_cache()"]}, {"cell_type": "markdown", "id": "188fe33b", "metadata": {}, "source": ["### Visualize anomaly map"]}, {"cell_type": "code", "execution_count": 23, "id": "502ba4f5", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x3000 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["def visualize(img):\n", "    _min = img.min()\n", "    _max = img.max()\n", "    normalized_img = (img - _min) / (_max - _min)\n", "    return normalized_img\n", "\n", "\n", "diff = abs(inputting.cpu() - current_img[0].cpu()).detach().numpy()\n", "row = 4\n", "plt.style.use(\"default\")\n", "\n", "fig = plt.figure(figsize=(10, 30))\n", "\n", "ax = plt.subplot(1, row, 2)\n", "ax.imshow(latent_img[0, 0].cpu().detach().numpy(), vmin=0, vmax=1, cmap=\"gray\")\n", "ax.set_title(\"Latent Image\"), plt.tight_layout(), plt.axis(\"off\")\n", "\n", "ax = plt.subplot(1, row, 3)\n", "ax.imshow(current_img[0, 0].cpu().detach().numpy(), vmin=0, vmax=1, cmap=\"gray\")\n", "ax.set_title(\"Reconstructed \\n Image\"), plt.tight_layout(), plt.axis(\"off\")\n", "\n", "\n", "ax = plt.subplot(1, row, 4)\n", "ax.imshow(diff[0], cmap=\"inferno\")\n", "ax.set_title(\"Anomaly Map\"), plt.tight_layout(), plt.axis(\"off\")\n", "\n", "ax = plt.subplot(1, row, 1)\n", "ax.imshow(inputting[0], vmin=0, vmax=1, cmap=\"gray\")\n", "ax.set_title(\"Original Image\"), plt.tight_layout(), plt.axis(\"off\")\n", "plt.show()"]}], "metadata": {"jupytext": {"formats": "py:percent,ipynb"}, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.6"}, "vscode": {"interpreter": {"hash": "4f1513a79f82193cb81c96943579af15c6a44d6347609348bde584197ab7b1ab"}}}, "nbformat": 4, "nbformat_minor": 5}