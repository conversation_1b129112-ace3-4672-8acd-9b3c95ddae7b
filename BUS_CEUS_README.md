# BUS到CEUS图像转换的DDIM模型

这个项目实现了基于DDIM（Denoising Diffusion Implicit Models）的BUS（B超）到CEUS（造影超声）图像转换模型。该模型可以将BUS图像作为条件输入，生成对应的CEUS图像。

## 功能特性

- ✅ **条件扩散模型**: 基于MONAI GenerativeModels实现的条件DDIM模型
- ✅ **完整训练流程**: 支持训练、验证、模型保存和断点续训
- ✅ **评价指标**: 自动计算PSNR、SSIM、MAE、MSE等图像质量指标
- ✅ **可视化功能**: 训练过程中的对比图像和指标曲线可视化
- ✅ **模型保存**: 支持每个epoch保存、最优模型保存和最终模型保存
- ✅ **测试推理**: 完整的测试脚本，支持批量推理和结果评估

## 项目结构

```
├── bus_ceus_dataset.py          # 数据加载器
├── conditional_ddim_model.py     # 条件DDIM模型实现
├── metrics.py                    # 评价指标计算
├── visualization.py              # 可视化工具
├── train_bus_to_ceus.py         # 训练脚本
├── test_bus_to_ceus.py          # 测试脚本
├── run_example.py               # 简单运行示例
├── BUS_CEUS_README.md           # 说明文档
└── train_mini/                  # 数据目录
    ├── bus/                     # BUS图像（源模态）
    └── ceus/                    # CEUS图像（目标模态）
```

## 环境要求

### 基础依赖
```bash
pip install torch torchvision torchaudio
pip install monai[all]
pip install matplotlib tqdm pillow numpy
```

### 完整依赖列表
- Python >= 3.8
- PyTorch >= 1.12
- MONAI >= 1.0
- NumPy
- Matplotlib
- Pillow
- tqdm

## 快速开始

### 1. 数据准备

确保你的数据按以下结构组织：
```
train_mini/
├── bus/
│   ├── image_001_bus_0.png
│   ├── image_001_bus_1.png
│   └── ...
└── ceus/
    ├── image_001_ceus_0.png
    ├── image_001_ceus_1.png
    └── ...
```

**重要**: BUS和CEUS图像需要按文件名配对，文件名中的`_bus_`和`_ceus_`用于匹配对应的图像对。

### 2. 简单运行

```bash
python run_example.py
```

这个脚本会自动检查依赖项和数据，并提供交互式菜单来选择训练或测试。

### 3. 手动训练

```bash
python train_bus_to_ceus.py \
    --bus_dir train_mini/bus \
    --ceus_dir train_mini/ceus \
    --epochs 100 \
    --batch_size 8 \
    --learning_rate 1e-4 \
    --image_size 256 256 \
    --output_dir outputs \
    --mixed_precision
```

### 4. 手动测试

```bash
python test_bus_to_ceus.py \
    --checkpoint outputs/bus_to_ceus_YYYYMMDD_HHMMSS/checkpoint_best.pth \
    --bus_dir train_mini/bus \
    --ceus_dir train_mini/ceus \
    --output_dir test_results \
    --save_images \
    --save_comparisons
```

## 训练参数说明

### 主要参数

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `--epochs` | 100 | 训练轮数 |
| `--batch_size` | 8 | 批次大小 |
| `--learning_rate` | 1e-4 | 学习率 |
| `--image_size` | [256, 256] | 图像尺寸 |
| `--num_timesteps` | 1000 | 扩散时间步数 |

### 保存和评估

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `--save_every` | 10 | 每N个epoch保存模型 |
| `--eval_every` | 5 | 每N个epoch进行评估 |
| `--vis_every` | 10 | 每N个epoch保存可视化 |

### 断点续训

```bash
python train_bus_to_ceus.py \
    --continue_train outputs/bus_to_ceus_YYYYMMDD_HHMMSS/checkpoint_epoch_0050.pth \
    --start_epoch 50 \
    [其他参数...]
```

## 输出结果

### 训练输出

训练完成后，在输出目录中会生成：

```
outputs/bus_to_ceus_YYYYMMDD_HHMMSS/
├── config.json                    # 训练配置
├── checkpoint_epoch_0010.pth      # 定期保存的检查点
├── checkpoint_epoch_0020.pth
├── ...
├── checkpoint_best.pth            # 最优模型
├── final_model.pth                # 最终模型
├── comparisons/                   # 对比图像
│   ├── comparison_epoch_0010.png
│   └── ...
└── plots/
    └── training_progress.png      # 训练进度图
```

### 测试输出

测试完成后，在测试目录中会生成：

```
test_results/
├── metrics.json                   # 评价指标
├── test_config.json              # 测试配置
├── comparison_grid.png           # 对比网格图
├── bus_images/                   # BUS图像
├── target_images/                # 目标CEUS图像
└── generated_images/             # 生成的CEUS图像
```

## 评价指标

模型会自动计算以下图像质量指标：

- **PSNR** (Peak Signal-to-Noise Ratio): 峰值信噪比，越高越好
- **SSIM** (Structural Similarity Index): 结构相似性指数，越高越好
- **MAE** (Mean Absolute Error): 平均绝对误差，越低越好
- **MSE** (Mean Squared Error): 均方误差，越低越好

## 模型架构

- **扩散模型**: 基于UNet的条件扩散模型
- **调度器**: DDIM调度器，支持快速采样
- **条件机制**: 通过通道连接的方式将BUS图像作为条件输入
- **损失函数**: MSE损失用于噪声预测

## 注意事项

1. **内存需求**: 建议使用至少8GB显存的GPU进行训练
2. **数据配对**: 确保BUS和CEUS图像正确配对
3. **图像格式**: 支持PNG格式的灰度或彩色图像
4. **训练时间**: 根据数据量和硬件配置，完整训练可能需要数小时到数天

## 故障排除

### 常见问题

1. **CUDA内存不足**: 减小batch_size或image_size
2. **数据加载错误**: 检查图像文件路径和命名格式
3. **模型加载失败**: 确保检查点文件完整且路径正确

### 性能优化

- 使用`--mixed_precision`启用混合精度训练
- 调整`--num_workers`优化数据加载速度
- 使用更大的`batch_size`提高GPU利用率

## 许可证

本项目基于Apache 2.0许可证开源。

## 引用

如果这个项目对你的研究有帮助，请考虑引用相关论文：

```bibtex
@article{song2020denoising,
  title={Denoising diffusion implicit models},
  author={Song, Jiaming and Meng, Chenlin and Ermon, Stefano},
  journal={arXiv preprint arXiv:2010.02502},
  year={2020}
}
```
