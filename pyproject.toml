[tool.black]
line-length = 120
target-version = ['py37', 'py38', 'py39', 'py310']
include = '\.pyi?$'
exclude = '''
(
  /(
    # exclude a few common directories in the root of the project
      \.eggs
    | \.git
    | \.hg
    | \.mypy_cache
    | \.tox
    | \.venv
    | venv
    | \.pytype
    | _build
    | buck-out
    | build
    | dist
  )/
  # also separately exclude a file named versioneer.py
  | generative/_version.py
)
'''

[tool.pycln]
all = true

[tool.pytype]
# Space-separated list of files or directories to exclude.
exclude = ["versioneer.py", "_version.py", "tutorials/"]
# Space-separated list of files or directories to process.
inputs = ["generative"]
# Keep going past errors to analyze as many files as possible.
keep_going = true
# Run N jobs in parallel.
jobs = 8
# All pytype output goes here.
output = ".pytype"
# Paths to source code directories, separated by ':'.
pythonpath = "."
# Check attribute values against their annotations.
check_attribute_types = true
# Check container mutations against their annotations.
check_container_types = true
# Check parameter defaults and assignments against their annotations.
check_parameter_types = true
# Check variable values against their annotations.
check_variable_types = true
# Comma or space separated list of error names to ignore.
disable = ["pyi-error"]
# Report errors.
report_errors = true
# Experimental: Infer precise return types even for invalid function calls.
precise_return = true
# Experimental: solve unknown types to label with structural types.
protocols = true
# Experimental: Only load submodules that are explicitly imported.
strict_import = false
