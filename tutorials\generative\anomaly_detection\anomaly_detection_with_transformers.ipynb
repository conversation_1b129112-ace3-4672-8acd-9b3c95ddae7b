{"cells": [{"cell_type": "code", "execution_count": 1, "id": "73121f87-6d4c-413e-a9e9-ed5da13e2a33", "metadata": {}, "outputs": [], "source": ["# Copyright (c) MONAI Consortium\n", "# Licensed under the Apache License, Version 2.0 (the \"License\");\n", "# you may not use this file except in compliance with the License.\n", "# You may obtain a copy of the License at\n", "#     http://www.apache.org/licenses/LICENSE-2.0\n", "# Unless required by applicable law or agreed to in writing, software\n", "# distributed under the License is distributed on an \"AS IS\" BASIS,\n", "# WITHOUT WAR<PERSON><PERSON>IES OR CONDITIONS OF ANY KIND, either express or implied.\n", "# See the License for the specific language governing permissions and\n", "# limitations under the License."]}, {"cell_type": "markdown", "id": "f6090d00", "metadata": {}, "source": ["# Anomaly Detection with Transformers\n", "\n", "This tutorial illustrates how to use MONAI to perform image-wise and localised anomaly detection with transformers based on the method proposed in <PERSON><PERSON> et al.[1].\n", "\n", "Here, we will work with the [MedNIST dataset](https://docs.monai.io/en/stable/apps.html#monai.apps.MedNISTDataset) available on MONAI, and similar to \"Experiment 2 – image-wise anomaly detection on 2D synthetic data\" from [1], we will train a general-purpose VQ-VAE (using all MEDNIST classes), and then a generative models (i.e., Transformer) on `HeadCT` images.\n", "\n", "We will compute the log-likelihood of images from the same class (in-distribution class) and images from other classes (out-of-distribution). We will also provide an example of performing localised anomaly detection with these trained models.\n", "\n", "[1] - [<PERSON><PERSON><PERSON> et al. \"Unsupervised brain imaging 3D anomaly detection and segmentation with transformers\"](https://doi.org/10.1016/j.media.2022.102475)"]}, {"cell_type": "markdown", "id": "8b27924f", "metadata": {}, "source": ["### Setup environment"]}, {"cell_type": "code", "execution_count": 2, "id": "01787b4b", "metadata": {}, "outputs": [], "source": ["!python -c \"import seaborn\" || pip install -q seaborn\n", "!python -c \"import monai\" || pip install -q \"monai-weekly[tqdm]\"\n", "!python -c \"import matplotlib\" || pip install -q matplotlib\n", "%matplotlib inline"]}, {"cell_type": "markdown", "id": "56afab18", "metadata": {}, "source": ["### Setup imports"]}, {"cell_type": "code", "execution_count": 3, "id": "b6b0c79f", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/media/walter/Storage/Projects/GenerativeModels/venv/lib/python3.10/site-packages/tqdm/auto.py:22: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}, {"name": "stdout", "output_type": "stream", "text": ["2023-04-09 11:49:50,403 - A matching Triton is not available, some optimizations will not be enabled.\n", "Error caught was: No module named 'triton'\n", "MONAI version: 1.2.dev2304\n", "Numpy version: 1.23.5\n", "Pytorch version: 1.13.1+cu117\n", "MONAI flags: HAS_EXT = False, USE_COMPILED = False, USE_META_DICT = False\n", "MONAI rev id: 9a57be5aab9f2c2a134768c0c146399150e247a0\n", "MONAI __file__: /media/walter/Storage/Projects/GenerativeModels/venv/lib/python3.10/site-packages/monai/__init__.py\n", "\n", "Optional dependencies:\n", "Pytorch Ignite version: 0.4.10\n", "ITK version: 5.3.0\n", "Nibabel version: 4.0.2\n", "scikit-image version: 0.19.3\n", "Pillow version: 9.3.0\n", "Tensorboard version: 2.11.0\n", "gdown version: 4.6.0\n", "TorchVision version: 0.14.1+cu117\n", "tqdm version: 4.64.1\n", "lmdb version: 1.4.0\n", "psutil version: 5.9.4\n", "pandas version: 1.5.3\n", "einops version: 0.6.0\n", "transformers version: 4.21.3\n", "mlflow version: 2.1.1\n", "pynrrd version: 1.0.0\n", "\n", "For details about installing the optional dependencies, please visit:\n", "    https://docs.monai.io/en/latest/installation.html#installing-the-recommended-dependencies\n", "\n"]}], "source": ["import os\n", "import tempfile\n", "import time\n", "\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "import seaborn as sns\n", "import torch\n", "import torch.nn.functional as F\n", "from monai import transforms\n", "from monai.apps import MedNISTDataset\n", "from monai.config import print_config\n", "from monai.data import DataLoader, Dataset\n", "from monai.utils import first, set_determinism\n", "from torch.nn import CrossEntropyLoss, L1Loss\n", "from tqdm import tqdm\n", "from generative.inferers import VQVAETransformerInferer\n", "from generative.networks.nets import VQVAE, DecoderOnlyTransformer\n", "from generative.utils.enums import OrderingType\n", "from generative.utils.ordering import Ordering\n", "\n", "print_config()"]}, {"cell_type": "code", "execution_count": 4, "id": "de0ed372", "metadata": {}, "outputs": [], "source": ["# for reproducibility purposes set a seed\n", "set_determinism(42)"]}, {"cell_type": "markdown", "id": "ad40db27", "metadata": {}, "source": ["### Setup a data directory and download dataset\n", "\n", "Specify a `MONAI_DATA_DIRECTORY` variable, where the data will be downloaded. If not\n", "specified a temporary directory will be used."]}, {"cell_type": "code", "execution_count": 5, "id": "42fa255d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/tmp/tmpglemb0j3\n"]}], "source": ["directory = os.environ.get(\"MONAI_DATA_DIRECTORY\")\n", "root_dir = tempfile.mkdtemp() if directory is None else directory\n", "print(root_dir)"]}, {"cell_type": "markdown", "id": "10054720", "metadata": {}, "source": ["### Download training data"]}, {"cell_type": "code", "execution_count": 6, "id": "7db7ac32", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["MedNIST.tar.gz: 59.0MB [00:02, 24.5MB/s]                                                                                                                                                                                                "]}, {"name": "stdout", "output_type": "stream", "text": ["2023-04-09 11:49:52,990 - INFO - Downloaded: /tmp/tmpglemb0j3/MedNIST.tar.gz\n", "2023-04-09 11:49:53,061 - INFO - Verified 'MedNIST.tar.gz', md5: 0bc7306e7427e00ad1c5526a6677552d.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["2023-04-09 11:49:53,062 - INFO - Writing into directory: /tmp/tmpglemb0j3.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Loading dataset: 100%|██████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 47164/47164 [00:26<00:00, 1798.73it/s]\n"]}], "source": ["train_transforms = transforms.Compose(\n", "    [\n", "        transforms.LoadImaged(keys=[\"image\"]),\n", "        transforms.EnsureChannelFirstd(keys=[\"image\"]),\n", "        transforms.ScaleIntensityRanged(keys=[\"image\"], a_min=0.0, a_max=255.0, b_min=0.0, b_max=1.0, clip=True),\n", "        transforms.RandA<PERSON>ed(\n", "            keys=[\"image\"],\n", "            rotate_range=[(-np.pi / 36, np.pi / 36), (-np.pi / 36, np.pi / 36)],\n", "            translate_range=[(-1, 1), (-1, 1)],\n", "            scale_range=[(-0.01, 0.01), (-0.01, 0.01)],\n", "            spatial_size=[64, 64],\n", "            padding_mode=\"zeros\",\n", "            prob=0.5,\n", "        ),\n", "    ]\n", ")\n", "train_data = MedNISTDataset(root_dir=root_dir, section=\"training\", download=True, seed=0, transform=train_transforms)\n", "train_loader = DataLoader(train_data, batch_size=256, shuffle=True, num_workers=4, persistent_workers=True)"]}, {"cell_type": "markdown", "id": "ec356258", "metadata": {}, "source": ["### Visualise some examples from the dataset"]}, {"cell_type": "code", "execution_count": 7, "id": "33d7c3dc", "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 640x480 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Plot 3 examples from the training set\n", "check_data = first(train_loader)\n", "fig, ax = plt.subplots(nrows=1, ncols=3)\n", "for image_n in range(3):\n", "    ax[image_n].imshow(check_data[\"image\"][image_n, 0, :, :], cmap=\"gray\")\n", "    ax[image_n].axis(\"off\")"]}, {"cell_type": "markdown", "id": "d860d83a", "metadata": {}, "source": ["### Download Validation Data"]}, {"cell_type": "code", "execution_count": 8, "id": "ec954b77", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2023-04-09 11:50:24,235 - INFO - Verified 'MedNIST.tar.gz', md5: 0bc7306e7427e00ad1c5526a6677552d.\n", "2023-04-09 11:50:24,235 - INFO - File exists: /tmp/tmpglemb0j3/MedNIST.tar.gz, skipped downloading.\n", "2023-04-09 11:50:24,236 - INFO - Non-empty folder exists in /tmp/tmpglemb0j3/MedNIST, skipped extracting.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Loading dataset: 100%|████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 5895/5895 [00:03<00:00, 1789.90it/s]\n"]}], "source": ["val_transforms = transforms.Compose(\n", "    [\n", "        transforms.LoadImaged(keys=[\"image\"]),\n", "        transforms.EnsureChannelFirstd(keys=[\"image\"]),\n", "        transforms.ScaleIntensityRanged(keys=[\"image\"], a_min=0.0, a_max=255.0, b_min=0.0, b_max=1.0, clip=True),\n", "    ]\n", ")\n", "val_data = MedNISTDataset(root_dir=root_dir, section=\"validation\", download=True, seed=0, transform=val_transforms)\n", "val_loader = DataLoader(val_data, batch_size=256, shuffle=False, num_workers=4, persistent_workers=True)"]}, {"cell_type": "markdown", "id": "09da3d54", "metadata": {}, "source": ["## Vector Quantized Variational Autoencoder\n", "\n", "The first step is to train a Vector Quantized Variation Autoencoder (VQ-VAE). This network is responsible for creating a compressed version of the inputted data. Once its training is done, we can use the encoder to obtain smaller and discrete representations of the 2D images to generate the inputs required for our autoregressive transformer.\n", "\n", "For its training, we will use the L1 loss, and we will update its codebook using a method based on Exponential Moving Average (EMA)."]}, {"cell_type": "markdown", "id": "2c7a91c3", "metadata": {}, "source": ["### Define network, optimizer and losses"]}, {"cell_type": "code", "execution_count": 9, "id": "757d00ff", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Using cuda\n"]}, {"data": {"text/plain": ["VQVAE(\n", "  (encoder): Encoder(\n", "    (blocks): ModuleList(\n", "      (0): Convolution(\n", "        (conv): Conv2d(1, 256, kernel_size=(4, 4), stride=(2, 2), padding=(1, 1))\n", "        (adn): ADN(\n", "          (A): ReLU()\n", "        )\n", "      )\n", "      (1): VQVAEResidualUnit(\n", "        (conv1): Convolution(\n", "          (conv): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          (adn): ADN(\n", "            (D): Dropout(p=0.0, inplace=False)\n", "            (A): ReLU()\n", "          )\n", "        )\n", "        (conv2): Convolution(\n", "          (conv): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "        )\n", "      )\n", "      (2): VQVAEResidualUnit(\n", "        (conv1): Convolution(\n", "          (conv): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          (adn): ADN(\n", "            (D): Dropout(p=0.0, inplace=False)\n", "            (A): ReLU()\n", "          )\n", "        )\n", "        (conv2): Convolution(\n", "          (conv): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "        )\n", "      )\n", "      (3): Convolution(\n", "        (conv): Conv2d(256, 256, kernel_size=(4, 4), stride=(2, 2), padding=(1, 1))\n", "        (adn): ADN(\n", "          (D): Dropout(p=0.0, inplace=False)\n", "          (A): ReLU()\n", "        )\n", "      )\n", "      (4): VQVAEResidualUnit(\n", "        (conv1): Convolution(\n", "          (conv): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          (adn): ADN(\n", "            (D): Dropout(p=0.0, inplace=False)\n", "            (A): ReLU()\n", "          )\n", "        )\n", "        (conv2): Convolution(\n", "          (conv): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "        )\n", "      )\n", "      (5): VQVAEResidualUnit(\n", "        (conv1): Convolution(\n", "          (conv): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          (adn): ADN(\n", "            (D): Dropout(p=0.0, inplace=False)\n", "            (A): ReLU()\n", "          )\n", "        )\n", "        (conv2): Convolution(\n", "          (conv): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "        )\n", "      )\n", "      (6): Convolution(\n", "        (conv): Conv2d(256, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "      )\n", "    )\n", "  )\n", "  (decoder): Decoder(\n", "    (blocks): ModuleList(\n", "      (0): Convolution(\n", "        (conv): Conv2d(64, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "      )\n", "      (1): VQVAEResidualUnit(\n", "        (conv1): Convolution(\n", "          (conv): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          (adn): ADN(\n", "            (D): Dropout(p=0.0, inplace=False)\n", "            (A): ReLU()\n", "          )\n", "        )\n", "        (conv2): Convolution(\n", "          (conv): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "        )\n", "      )\n", "      (2): VQVAEResidualUnit(\n", "        (conv1): Convolution(\n", "          (conv): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          (adn): ADN(\n", "            (D): Dropout(p=0.0, inplace=False)\n", "            (A): ReLU()\n", "          )\n", "        )\n", "        (conv2): Convolution(\n", "          (conv): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "        )\n", "      )\n", "      (3): Convolution(\n", "        (conv): ConvTranspose2d(256, 256, kernel_size=(4, 4), stride=(2, 2), padding=(1, 1))\n", "        (adn): ADN(\n", "          (D): Dropout(p=0.0, inplace=False)\n", "          (A): ReLU()\n", "        )\n", "      )\n", "      (4): VQVAEResidualUnit(\n", "        (conv1): Convolution(\n", "          (conv): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          (adn): ADN(\n", "            (D): Dropout(p=0.0, inplace=False)\n", "            (A): ReLU()\n", "          )\n", "        )\n", "        (conv2): Convolution(\n", "          (conv): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "        )\n", "      )\n", "      (5): VQVAEResidualUnit(\n", "        (conv1): Convolution(\n", "          (conv): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "          (adn): ADN(\n", "            (D): Dropout(p=0.0, inplace=False)\n", "            (A): ReLU()\n", "          )\n", "        )\n", "        (conv2): Convolution(\n", "          (conv): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "        )\n", "      )\n", "      (6): Convolution(\n", "        (conv): ConvTranspose2d(256, 1, kernel_size=(4, 4), stride=(2, 2), padding=(1, 1))\n", "      )\n", "    )\n", "  )\n", "  (quantizer): VectorQuantizer(\n", "    (quantizer): EMAQuantizer(\n", "      (embedding): Embedding(16, 64)\n", "    )\n", "  )\n", ")"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "print(f\"Using {device}\")\n", "\n", "vqvae_model = VQVAE(\n", "    spatial_dims=2,\n", "    in_channels=1,\n", "    out_channels=1,\n", "    num_res_layers=2,\n", "    downsample_parameters=((2, 4, 1, 1), (2, 4, 1, 1)),\n", "    upsample_parameters=((2, 4, 1, 1, 0), (2, 4, 1, 1, 0)),\n", "    num_channels=(256, 256),\n", "    num_res_channels=(256, 256),\n", "    num_embeddings=16,\n", "    embedding_dim=64,\n", ")\n", "vqvae_model.to(device)"]}, {"cell_type": "code", "execution_count": 10, "id": "7611f596", "metadata": {}, "outputs": [], "source": ["optimizer = torch.optim.Adam(params=vqvae_model.parameters(), lr=5e-4)\n", "l1_loss = L1Loss()"]}, {"cell_type": "markdown", "id": "f1d81a89", "metadata": {}, "source": ["### VQ-VAE Model training\n", "We will train our VQ-VAE for 20 epochs."]}, {"cell_type": "code", "execution_count": 11, "id": "fe7459e4", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Epoch 0: 100%|████████████████| 185/185 [02:47<00:00,  1.10it/s, recons_loss=0.152, quantization_loss=1.05e-5]\n", "Epoch 1: 100%|███████████████| 185/185 [02:59<00:00,  1.03it/s, recons_loss=0.0358, quantization_loss=7.67e-6]\n", "Epoch 2: 100%|███████████████| 185/185 [03:00<00:00,  1.02it/s, recons_loss=0.0296, quantization_loss=1.27e-5]\n", "Epoch 3: 100%|███████████████| 185/185 [02:59<00:00,  1.03it/s, recons_loss=0.0273, quantization_loss=1.68e-5]\n", "Epoch 4: 100%|███████████████| 185/185 [03:00<00:00,  1.03it/s, recons_loss=0.0274, quantization_loss=2.47e-5]\n", "Epoch 5: 100%|███████████████| 185/185 [03:00<00:00,  1.03it/s, recons_loss=0.0273, quantization_loss=2.43e-5]\n", "Epoch 6: 100%|███████████████| 185/185 [03:00<00:00,  1.02it/s, recons_loss=0.0246, quantization_loss=2.37e-5]\n", "Epoch 7: 100%|███████████████| 185/185 [03:00<00:00,  1.02it/s, recons_loss=0.0254, quantization_loss=2.47e-5]\n", "Epoch 8: 100%|███████████████| 185/185 [03:00<00:00,  1.02it/s, recons_loss=0.0254, quantization_loss=3.03e-5]\n", "Epoch 9: 100%|███████████████| 185/185 [03:00<00:00,  1.02it/s, recons_loss=0.0247, quantization_loss=3.23e-5]\n", "Epoch 10: 100%|██████████████| 185/185 [03:00<00:00,  1.02it/s, recons_loss=0.0245, quantization_loss=2.94e-5]\n", "Epoch 11: 100%|██████████████| 185/185 [03:01<00:00,  1.02it/s, recons_loss=0.0239, quantization_loss=3.89e-5]\n", "Epoch 12: 100%|██████████████| 185/185 [03:01<00:00,  1.02it/s, recons_loss=0.0233, quantization_loss=2.87e-5]\n", "Epoch 13: 100%|██████████████| 185/185 [03:01<00:00,  1.02it/s, recons_loss=0.0236, quantization_loss=3.18e-5]\n", "Epoch 14: 100%|██████████████| 185/185 [03:01<00:00,  1.02it/s, recons_loss=0.0226, quantization_loss=3.43e-5]\n", "Epoch 15: 100%|██████████████| 185/185 [03:01<00:00,  1.02it/s, recons_loss=0.0232, quantization_loss=3.53e-5]\n", "Epoch 16: 100%|██████████████| 185/185 [03:01<00:00,  1.02it/s, recons_loss=0.0224, quantization_loss=3.22e-5]\n", "Epoch 17: 100%|██████████████| 185/185 [03:01<00:00,  1.02it/s, recons_loss=0.0224, quantization_loss=3.32e-5]\n", "Epoch 18: 100%|██████████████| 185/185 [03:01<00:00,  1.02it/s, recons_loss=0.0221, quantization_loss=3.46e-5]\n", "Epoch 19: 100%|██████████████| 185/185 [03:00<00:00,  1.02it/s, recons_loss=0.0218, quantization_loss=3.59e-5]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["train completed, total time: 3630.9891188144684.\n"]}], "source": ["n_epochs = 20\n", "val_interval = 5\n", "epoch_losses = []\n", "val_epoch_losses = []\n", "\n", "total_start = time.time()\n", "for epoch in range(n_epochs):\n", "    vqvae_model.train()\n", "    epoch_loss = 0\n", "    progress_bar = tqdm(enumerate(train_loader), total=len(train_loader), ncols=110)\n", "    progress_bar.set_description(f\"Epoch {epoch}\")\n", "    for step, batch in progress_bar:\n", "        images = batch[\"image\"].to(device)\n", "        optimizer.zero_grad(set_to_none=True)\n", "\n", "        # model outputs reconstruction and the quantization error\n", "        reconstruction, quantization_loss = vqvae_model(images=images)\n", "        recons_loss = l1_loss(reconstruction.float(), images.float())\n", "        loss = recons_loss + quantization_loss\n", "\n", "        loss.backward()\n", "        optimizer.step()\n", "\n", "        epoch_loss += recons_loss.item()\n", "\n", "        progress_bar.set_postfix(\n", "            {\"recons_loss\": epoch_loss / (step + 1), \"quantization_loss\": quantization_loss.item() / (step + 1)}\n", "        )\n", "    epoch_losses.append(epoch_loss / (step + 1))\n", "\n", "    if (epoch + 1) % val_interval == 0:\n", "        vqvae_model.eval()\n", "        val_loss = 0\n", "        with torch.no_grad():\n", "            for val_step, batch in enumerate(val_loader, start=1):\n", "                images = batch[\"image\"].to(device)\n", "                reconstruction, quantization_loss = vqvae_model(images=images)\n", "                recons_loss = l1_loss(reconstruction.float(), images.float())\n", "                val_loss += recons_loss.item()\n", "\n", "        val_loss /= val_step\n", "        val_epoch_losses.append(val_loss)\n", "\n", "total_time = time.time() - total_start\n", "print(f\"train completed, total time: {total_time}.\")"]}, {"cell_type": "markdown", "id": "8dfa3270", "metadata": {}, "source": ["### Plot reconstructions of final trained vqvae model"]}, {"cell_type": "code", "execution_count": 12, "id": "0789cfcc", "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 300x400 with 8 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["images = first(val_loader)[\"image\"].to(device)\n", "reconstruction, quantization_loss = vqvae_model(images=images)\n", "nrows = 4\n", "fig, ax = plt.subplots(nrows=4, ncols=2, figsize=(3, 4))\n", "for i in range(nrows):\n", "    ax.flat[i * 2].imshow(images[i + 20, 0].detach().cpu(), vmin=0, vmax=1, cmap=\"gray\")\n", "    ax.flat[i * 2].axis(\"off\")\n", "    ax.flat[i * 2 + 1].imshow(reconstruction[i + 20, 0].detach().cpu(), vmin=0, vmax=1, cmap=\"gray\")\n", "    ax.flat[i * 2 + 1].axis(\"off\")\n", "ax.flat[0].title.set_text(\"Image\")\n", "ax.flat[1].title.set_text(\"Reconstruction\")\n", "plt.show()"]}, {"cell_type": "markdown", "id": "773f5f43", "metadata": {}, "source": ["# Autoregressive Transformer\n", "\n", "Now that our VQ-VAE model has been trained, we can use this model to encode the data into its discrete latent representations. Then, to be able to input it into the autoregressive Transformer, it is necessary to transform this 2D latent representation into a 1D sequence.\n", "\n", "In order to train it in an autoregressive manner, we will use the CrossEntropy Loss as the Transformer will try to predict the next token value for each position of the sequence.\n", "\n", "Here we will use the MONAI's `VQVAETransformerInferer` class to help with the forward pass and to get the predicted likelihood from the VQ-VAE + Transformer models."]}, {"cell_type": "markdown", "id": "83352d19", "metadata": {}, "source": ["### Datasets\n", "To train the transformer, we only use the `HeadCT` class."]}, {"cell_type": "code", "execution_count": 13, "id": "2b3c3a82", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Loading dataset: 100%|██████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 47164/47164 [00:13<00:00, 3404.41it/s]\n", "Loading dataset: 100%|████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 5895/5895 [00:01<00:00, 3433.64it/s]\n"]}], "source": ["in_distribution_class = \"HeadCT\"\n", "\n", "train_data = MedNISTDataset(root_dir=root_dir, section=\"training\", seed=0)\n", "train_datalist = [{\"image\": item[\"image\"]} for item in train_data.data if item[\"class_name\"] == in_distribution_class]\n", "train_transforms = transforms.Compose(\n", "    [\n", "        transforms.LoadImaged(keys=[\"image\"]),\n", "        transforms.EnsureChannelFirstd(keys=[\"image\"]),\n", "        transforms.ScaleIntensityRanged(keys=[\"image\"], a_min=0.0, a_max=255.0, b_min=0.0, b_max=1.0, clip=True),\n", "        transforms.RandA<PERSON>ed(\n", "            keys=[\"image\"],\n", "            rotate_range=[(-np.pi / 36, np.pi / 36), (-np.pi / 36, np.pi / 36)],\n", "            translate_range=[(-1, 1), (-1, 1)],\n", "            scale_range=[(-0.01, 0.01), (-0.01, 0.01)],\n", "            spatial_size=[64, 64],\n", "            padding_mode=\"zeros\",\n", "            prob=0.5,\n", "        ),\n", "    ]\n", ")\n", "train_ds = Dataset(data=train_datalist, transform=train_transforms)\n", "train_loader = DataLoader(train_ds, batch_size=32, shuffle=True, num_workers=4, persistent_workers=True)\n", "\n", "val_data = MedNISTDataset(root_dir=root_dir, section=\"validation\", seed=0)\n", "val_datalist = [{\"image\": item[\"image\"]} for item in val_data.data if item[\"class_name\"] == in_distribution_class]\n", "val_transforms = transforms.Compose(\n", "    [\n", "        transforms.LoadImaged(keys=[\"image\"]),\n", "        transforms.EnsureChannelFirstd(keys=[\"image\"]),\n", "        transforms.ScaleIntensityRanged(keys=[\"image\"], a_min=0.0, a_max=255.0, b_min=0.0, b_max=1.0, clip=True),\n", "    ]\n", ")\n", "val_ds = Dataset(data=val_datalist, transform=val_transforms)\n", "val_loader = DataLoader(val_ds, batch_size=32, shuffle=False, num_workers=4, persistent_workers=True)"]}, {"cell_type": "markdown", "id": "b0f5a3cd", "metadata": {}, "source": ["### 2D latent representation -> 1D sequence\n", "We need to define an ordering of which we convert our 2D latent space into a 1D sequence. For this we will use a simple raster scan."]}, {"cell_type": "code", "execution_count": 14, "id": "f91086e3", "metadata": {"lines_to_next_cell": 2}, "outputs": [], "source": ["# Get spatial dimensions of data\n", "test_data = next(iter(train_loader))[\"image\"].to(device)\n", "spatial_shape = vqvae_model.encode_stage_2_inputs(test_data).shape[2:]\n", "\n", "ordering = Ordering(ordering_type=OrderingType.RASTER_SCAN.value, spatial_dims=2, dimensions=(1,) + spatial_shape)"]}, {"cell_type": "markdown", "id": "ace09890", "metadata": {}, "source": ["### Define network, inferer, optimizer and loss function"]}, {"cell_type": "code", "execution_count": 15, "id": "aab1891a", "metadata": {}, "outputs": [], "source": ["device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "\n", "transformer_model = DecoderOnlyTransformer(\n", "    num_tokens=16 + 1,\n", "    max_seq_len=spatial_shape[0] * spatial_shape[1],\n", "    attn_layers_dim=128,\n", "    attn_layers_depth=16,\n", "    attn_layers_heads=16,\n", ")\n", "transformer_model.to(device)\n", "\n", "inferer = VQVAETransformerInferer()"]}, {"cell_type": "code", "execution_count": 16, "id": "fa3cd231", "metadata": {}, "outputs": [], "source": ["optimizer = torch.optim.Adam(params=transformer_model.parameters(), lr=1e-4)\n", "ce_loss = CrossEntropyLoss()"]}, {"cell_type": "markdown", "id": "0921fcfb", "metadata": {}, "source": ["### Transformer Training\n", "We will train the Transformer for 20 epochs."]}, {"cell_type": "code", "execution_count": 17, "id": "9c32f0a9", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Epoch 0: 100%|████████████████████████████████████████████████| 250/250 [00:41<00:00,  6.02it/s, ce_loss=1.81]\n", "Epoch 1: 100%|████████████████████████████████████████████████| 250/250 [00:42<00:00,  5.95it/s, ce_loss=1.58]\n", "Epoch 2: 100%|█████████████████████████████████████████████████| 250/250 [00:41<00:00,  5.97it/s, ce_loss=1.5]\n", "Epoch 3: 100%|████████████████████████████████████████████████| 250/250 [00:41<00:00,  5.97it/s, ce_loss=1.44]\n", "Epoch 4: 100%|████████████████████████████████████████████████| 250/250 [00:41<00:00,  5.98it/s, ce_loss=1.39]\n", "100%|████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 256/256 [00:01<00:00, 137.34it/s]\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["Epoch 5: 100%|████████████████████████████████████████████████| 250/250 [00:42<00:00,  5.84it/s, ce_loss=1.34]\n", "Epoch 6: 100%|████████████████████████████████████████████████| 250/250 [00:44<00:00,  5.66it/s, ce_loss=1.31]\n", "Epoch 7: 100%|████████████████████████████████████████████████| 250/250 [00:43<00:00,  5.70it/s, ce_loss=1.28]\n", "Epoch 8: 100%|████████████████████████████████████████████████| 250/250 [00:43<00:00,  5.73it/s, ce_loss=1.25]\n", "Epoch 9: 100%|████████████████████████████████████████████████| 250/250 [00:43<00:00,  5.81it/s, ce_loss=1.23]\n", "100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 256/256 [00:01<00:00, 137.37it/s]\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["Epoch 10: 100%|███████████████████████████████████████████████| 250/250 [00:43<00:00,  5.80it/s, ce_loss=1.21]\n", "Epoch 11: 100%|███████████████████████████████████████████████| 250/250 [00:42<00:00,  5.88it/s, ce_loss=1.19]\n", "Epoch 12: 100%|███████████████████████████████████████████████| 250/250 [00:43<00:00,  5.75it/s, ce_loss=1.18]\n", "Epoch 13: 100%|███████████████████████████████████████████████| 250/250 [00:42<00:00,  5.88it/s, ce_loss=1.16]\n", "Epoch 14: 100%|███████████████████████████████████████████████| 250/250 [00:42<00:00,  5.94it/s, ce_loss=1.16]\n", "100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 256/256 [00:01<00:00, 139.20it/s]\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["Epoch 15: 100%|███████████████████████████████████████████████| 250/250 [00:42<00:00,  5.92it/s, ce_loss=1.14]\n", "Epoch 16: 100%|███████████████████████████████████████████████| 250/250 [00:42<00:00,  5.87it/s, ce_loss=1.14]\n", "Epoch 17: 100%|███████████████████████████████████████████████| 250/250 [00:42<00:00,  5.84it/s, ce_loss=1.13]\n", "Epoch 18: 100%|███████████████████████████████████████████████| 250/250 [00:41<00:00,  5.95it/s, ce_loss=1.12]\n", "Epoch 19: 100%|███████████████████████████████████████████████| 250/250 [00:42<00:00,  5.93it/s, ce_loss=1.11]\n", "100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 256/256 [00:01<00:00, 139.69it/s]\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAaAAAAGzCAYAAABpdMNsAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjYuMiwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8o6BhiAAAACXBIWXMAAA9hAAAPYQGoP6dpAABPFklEQVR4nO3de3hU1dk//O+e<PERSON><PERSON><PERSON>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\n", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["train completed, total time: 868.6071045398712.\n"]}], "source": ["n_epochs = 20\n", "val_interval = 5\n", "epoch_losses = []\n", "val_epoch_losses = []\n", "vqvae_model.eval()\n", "\n", "total_start = time.time()\n", "for epoch in range(n_epochs):\n", "    transformer_model.train()\n", "    epoch_loss = 0\n", "    progress_bar = tqdm(enumerate(train_loader), total=len(train_loader), ncols=110)\n", "    progress_bar.set_description(f\"Epoch {epoch}\")\n", "    for step, batch in progress_bar:\n", "\n", "        images = batch[\"image\"].to(device)\n", "\n", "        optimizer.zero_grad(set_to_none=True)\n", "\n", "        logits, target, _ = inferer(images, vqvae_model, transformer_model, ordering, return_latent=True)\n", "        logits = logits.transpose(1, 2)\n", "\n", "        loss = ce_loss(logits, target)\n", "\n", "        loss.backward()\n", "        optimizer.step()\n", "\n", "        epoch_loss += loss.item()\n", "\n", "        progress_bar.set_postfix({\"ce_loss\": epoch_loss / (step + 1)})\n", "    epoch_losses.append(epoch_loss / (step + 1))\n", "\n", "    if (epoch + 1) % val_interval == 0:\n", "        transformer_model.eval()\n", "        val_loss = 0\n", "        with torch.no_grad():\n", "            for val_step, batch in enumerate(val_loader, start=1):\n", "\n", "                images = batch[\"image\"].to(device)\n", "\n", "                logits, quantizations_target, _ = inferer(\n", "                    images, vqvae_model, transformer_model, ordering, return_latent=True\n", "                )\n", "                logits = logits.transpose(1, 2)\n", "\n", "                loss = ce_loss(logits[:, :, :-1], quantizations_target[:, 1:])\n", "\n", "                val_loss += loss.item()\n", "        # get sample\n", "        sample = inferer.sample(\n", "            vqvae_model=vqvae_model,\n", "            transformer_model=transformer_model,\n", "            ordering=ordering,\n", "            latent_spatial_dim=(spatial_shape[0], spatial_shape[1]),\n", "            starting_tokens=vqvae_model.num_embeddings * torch.ones((1, 1), device=device),\n", "        )\n", "        plt.imshow(sample[0, 0, ...].cpu().detach())\n", "        plt.title(f\"Sample epoch {epoch}\")\n", "        plt.show()\n", "        val_loss /= val_step\n", "        val_epoch_losses.append(val_loss)\n", "        val_loss /= val_step\n", "        val_epoch_losses.append(val_loss)\n", "\n", "total_time = time.time() - total_start\n", "print(f\"train completed, total time: {total_time}.\")"]}, {"cell_type": "markdown", "id": "29a35d4b", "metadata": {}, "source": ["## Image-wise anomaly detection\n", "\n", "To verify the performance of the VQ-VAE + Transformer performing unsupervised anomaly detection, we will use the images from the test set of the MedNIST dataset. We will consider images from the `HeadCT` class as in-distribution images."]}, {"cell_type": "code", "execution_count": 18, "id": "aa3938fe", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2023-04-09 13:05:44,866 - INFO - Verified 'MedNIST.tar.gz', md5: 0bc7306e7427e00ad1c5526a6677552d.\n", "2023-04-09 13:05:44,867 - INFO - File exists: /tmp/tmpglemb0j3/MedNIST.tar.gz, skipped downloading.\n", "2023-04-09 13:05:44,867 - INFO - Non-empty folder exists in /tmp/tmpglemb0j3/MedNIST, skipped extracting.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Loading dataset: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 5895/5895 [00:01<00:00, 3332.94it/s]\n", "In-distribution data: 100%|███████████████████████████████████████████████████| 17/17 [00:02<00:00,  7.67it/s]\n"]}], "source": ["vqvae_model.eval()\n", "transformer_model.eval()\n", "\n", "test_data = MedNISTDataset(root_dir=root_dir, section=\"test\", download=True, seed=0)\n", "\n", "in_distribution_datalist = [\n", "    {\"image\": item[\"image\"]} for item in test_data.data if item[\"class_name\"] == in_distribution_class\n", "]\n", "in_distribution_ds = Dataset(data=in_distribution_datalist, transform=val_transforms)\n", "in_distribution_loader = DataLoader(\n", "    in_distribution_ds, batch_size=64, shuffle=False, num_workers=4, persistent_workers=True\n", ")\n", "\n", "in_likelihoods = []\n", "\n", "progress_bar = tqdm(enumerate(in_distribution_loader), total=len(in_distribution_loader), ncols=110)\n", "progress_bar.set_description(f\"In-distribution data\")\n", "for step, batch in progress_bar:\n", "    images = batch[\"image\"].to(device)\n", "\n", "    log_likelihood = inferer.get_likelihood(\n", "        inputs=images, vqvae_model=vqvae_model, transformer_model=transformer_model, ordering=ordering\n", "    )\n", "    in_likelihoods.append(log_likelihood.sum(dim=(1, 2)).cpu().numpy())\n", "\n", "in_likelihoods = np.concatenate(in_likelihoods)"]}, {"cell_type": "markdown", "id": "19541717", "metadata": {}, "source": ["We will use all other classes for the out-of-distribution examples."]}, {"cell_type": "code", "execution_count": 19, "id": "f3e714ee", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["out-of-distribution data CXR: 100%|███████████████████████████████████████████| 16/16 [00:02<00:00,  7.61it/s]\n", "out-of-distribution data AbdomenCT: 100%|█████████████████████████████████████| 16/16 [00:02<00:00,  7.32it/s]\n", "out-of-distribution data Hand: 100%|██████████████████████████████████████████| 16/16 [00:02<00:00,  7.37it/s]\n", "out-of-distribution data ChestCT: 100%|███████████████████████████████████████| 16/16 [00:02<00:00,  7.42it/s]\n", "out-of-distribution data BreastMRI: 100%|█████████████████████████████████████| 15/15 [00:01<00:00,  7.69it/s]\n"]}], "source": ["all_classes = {item[\"class_name\"] for item in test_data.data}\n", "all_classes.remove(in_distribution_class)\n", "\n", "all_likelihoods = {}\n", "for c in all_classes:\n", "    ood_datalist = [{\"image\": item[\"image\"]} for item in test_data.data if item[\"class_name\"] == c]\n", "    ood_ds = Dataset(data=ood_datalist, transform=val_transforms)\n", "    ood_loader = DataLoader(ood_ds, batch_size=64, shuffle=False, num_workers=4, persistent_workers=True)\n", "\n", "    ood_likelihoods = []\n", "\n", "    progress_bar = tqdm(enumerate(ood_loader), total=len(ood_loader), ncols=110)\n", "    progress_bar.set_description(f\"out-of-distribution data {c}\")\n", "    for step, batch in progress_bar:\n", "        images = batch[\"image\"].to(device)\n", "\n", "        log_likelihood = inferer.get_likelihood(\n", "            inputs=images, vqvae_model=vqvae_model, transformer_model=transformer_model, ordering=ordering\n", "        )\n", "        ood_likelihoods.append(log_likelihood.sum(dim=(1, 2)).cpu().numpy())\n", "\n", "    ood_likelihoods = np.concatenate(ood_likelihoods)\n", "    all_likelihoods[c] = ood_likelihoods"]}, {"cell_type": "markdown", "id": "5aa92638", "metadata": {}, "source": ["## Log-likelihood plot\n", "\n", "Here, we plot the log-likelihood of the images. In this case, the lower the log-likelihood, the more unlikely the image belongs to the training set."]}, {"cell_type": "code", "execution_count": 20, "id": "cd456a7c", "metadata": {}, "outputs": [{"data": {"text/plain": ["Text(0.5, 0, 'Log-likelihood')"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.set_style(\"whitegrid\", {\"axes.grid\": False})\n", "sns.kdeplot(in_likelihoods, bw_adjust=1, label=\"In-distribution\", fill=True, cut=True)\n", "for c, l in all_likelihoods.items():\n", "    sns.kdeplot(l, bw_adjust=1, label=f\"OOD {c}\", cut=True, fill=True)\n", "plt.legend(loc=\"upper right\")\n", "plt.xlabel(\"Log-likelihood\")\n", "# plt.xlim([-200,10])\n", "# plt.ylim([0,10])"]}, {"cell_type": "markdown", "id": "60ab6571-6f85-4c1b-8be8-896cee74bf48", "metadata": {}, "source": ["# Localised anomaly detection\n", "First we create a synthetic corruption of an in-distribution image"]}, {"cell_type": "code", "execution_count": 21, "id": "884b152e-54a5-41b8-9d59-327991c58a20", "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 640x480 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["input_image = first(in_distribution_loader)\n", "image_clean = input_image[\"image\"][0, ...]\n", "plt.subplot(1, 2, 1)\n", "plt.imshow(image_clean[0, ...], cmap=\"gray\")\n", "plt.axis(\"off\")\n", "plt.title(\"Clean image\")\n", "image_corrupted = image_clean.clone()\n", "image_corrupted[0, 25:40, 40:50] = 1\n", "plt.subplot(1, 2, 2)\n", "plt.imshow(image_corrupted[0, ...], cmap=\"gray\")\n", "plt.axis(\"off\")\n", "plt.title(\"Corrupted image\")\n", "plt.show()"]}, {"cell_type": "markdown", "id": "dc3b8023-731c-4ac7-b335-9835e85bbabd", "metadata": {}, "source": ["Get the log-likelihood and convert into a mask of the 5% lowest-likelihood tokens"]}, {"cell_type": "code", "execution_count": 22, "id": "3faeed43-59c1-4062-8180-2c8445b9e118", "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 640x480 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["log_likelihood = inferer.get_likelihood(\n", "    inputs=image_corrupted[None, ...].to(device),\n", "    vqvae_model=vqvae_model,\n", "    transformer_model=transformer_model,\n", "    ordering=ordering,\n", ")\n", "likelihood = torch.exp(log_likelihood)\n", "plt.subplot(1, 2, 1)\n", "plt.imshow(likelihood.cpu()[0, ...])\n", "plt.axis(\"off\")\n", "plt.title(\"Log-likelihood\")\n", "plt.subplot(1, 2, 2)\n", "mask = log_likelihood.cpu()[0, ...] < torch.quantile(log_likelihood, 0.03).item()\n", "plt.imshow(mask)\n", "plt.axis(\"off\")\n", "plt.title(\"Healing mask\")\n", "plt.show()"]}, {"cell_type": "markdown", "id": "ec053da6-8f95-4460-bbe5-577a4a956266", "metadata": {}, "source": ["Use this mask and the trained transformer to 'heal' the sequence"]}, {"cell_type": "code", "execution_count": 23, "id": "dd08553f-0b51-48ff-860d-fc1fabadf77c", "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# flatten the mask\n", "mask_flattened = mask.reshape(-1)\n", "mask_flattened = mask_flattened[ordering.get_sequence_ordering()]\n", "\n", "latent = vqvae_model.index_quantize(image_corrupted[None, ...].to(device))\n", "latent = latent.reshape(latent.shape[0], -1)\n", "latent = latent[:, ordering.get_sequence_ordering()]\n", "latent = F.pad(latent, (1, 0), \"constant\", vqvae_model.num_embeddings)\n", "latent = latent.long()\n", "latent_healed = latent.clone()\n", "\n", "# heal the sequence\n", "# loop over tokens\n", "for i in range(1, latent.shape[1]):\n", "    if mask_flattened[i - 1]:\n", "        # if token is low probability, replace with tranformer's most likely token\n", "        logits = transformer_model(latent_healed[:, :i])\n", "        probs = <PERSON>.softmax(logits, dim=-1)\n", "        # don't sample beginning of sequence token\n", "        probs[:, :, vqvae_model.num_embeddings] = 0\n", "        index = torch.argmax(probs[0, -1, :])\n", "        latent_healed[:, i] = index\n", "\n", "\n", "# reconstruct\n", "latent_healed = latent_healed[:, 1:]\n", "latent_healed = latent_healed[:, ordering.get_revert_sequence_ordering()]\n", "latent_healed = latent_healed.reshape((16, 16))\n", "\n", "image_healed = vqvae_model.decode_samples(latent_healed[None, ...]).cpu().detach()\n", "plt.imshow(image_healed[0, 0, ...], cmap=\"gray\")\n", "plt.axis(\"off\")\n", "plt.title(\"Healed image\")\n", "plt.show()"]}, {"cell_type": "markdown", "id": "082dd314-e328-4666-956e-2c831739537a", "metadata": {}, "source": ["## Create anomaly maps"]}, {"cell_type": "code", "execution_count": 24, "id": "d629c1b6-0a66-4fb5-8722-3006404afe30", "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAABFEAAAEWCAYAAACufAjUAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjYuMiwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8o6BhiAAAACXBIWXMAAA9hAAAPYQGoP6dpAABN4UlEQVR4nO3deXiU1f3//xeEnRAgQFiNgJhACYHIJrssbixlaSuyWgUVWzeUj4JYW61i3UqLuKCAqCiiBfoTFFABEQFR1AotKiKyyw5JIEAg3L8/+M7tOSeTyR0IWfT5uC6u65w598yc+x7mzMzJeb9PCc/zPAEAAAAAACCikoXdAQAAAAAAgOKASRQAAAAAAIAAmEQBAAAAAAAIgEkUAAAAAACAAJhEAQAAAAAACIBJFAAAAAAAgACYRAEAAAAAAAiASRQAAAAAAIAAmEQBAAAAAAAIgEmUAtCtWzeNHTu2sLuRox07digxMVFz584t7K4A+IUbO3asunXrlutxw4YN07BhwwqgR0Dxl5iYqKeffrqwu5Gjp59+WomJiTp48GBhd6XICl2jn6Nhw4apd+/ehd0N5ML9PbNmzRolJiZqzZo1hdgrW1H/zVWQfq7XIvS7ddq0aYXaj1KF+uzF3LZt2zR16lStXLlSe/fuVenSpZWQkKCrr75aAwcOVLly5Qq7iwAK0c91jNi0aZMWLlyo/v37q169eoXdHSBfbN++XS+99JJWrlyp3bt3S5Lq1q2rtm3bauDAgWrcuHEh9/D8GDt2rObNm5frcf3799ff/va3c3qu559/Xo0aNVKPHj3O6XGAX5q5c+dq3Lhxfr1MmTKqU6eOOnTooD/84Q+qXr16IfYub5YvX65169bptttuK+yuAGeNSZSz9OGHH+qOO+5QmTJl1LdvXyUkJOjkyZP6/PPP9cQTT2jTpk3661//WtjdDKRu3bpat26dSpXivwOQX35OY4Rr06ZNmjx5stq0aVNokyiF/RcI/LwsW7ZMo0ePVlRUlPr06aPGjRurZMmS2rx5s9577z3NmjVLS5YsUd26dQu7q/lu4MCBateunV/fsWOHJk2apIEDB6ply5b+7fHx8ef8XFOmTNGVV17JJApwlm6//XbVq1dPmZmZ+vzzzzVr1iwtX75cCxYsUPny5Qu0L61bt9a6detUunTpPN1v+fLleu2115hEQbHGr+azsH37do0ePVp16tTRyy+/rLi4OL9tyJAh2rp1qz788MPC62AelShRQmXLli3sbgA/GwU1RniepxMnToRd0XLixAmVLl1aJUv+PKM2y5QpU9hdwM/Etm3bdNddd6lOnTqaMWOG9X6VpDFjxuj111/P9b2UkZGhChUqnM+unhcpKSlKSUnx6+vXr9ekSZPUokUL9e3bN8f7FdfzBYqzzp07q1mzZpKk3/3ud6pSpYpeeuklLVmyJMeQqPP1Xi1ZsiS/H/CL9fP8dn2eTZ06VRkZGXrkkUeyfdmSpAsvvFDXXXddxMdIS0vTI488oi5duigpKUmXX365XnjhBZ0+fdo6btq0abr22mvVtm1bJScna8CAAVq0aFG2x0tMTNRDDz2kDz74QL1791ZSUpJ69eqljz76KNfzCZcTZezYsUpJSdGuXbt08803KyUlRZ06ddJrr70mSfr22281fPhwtWjRQl27dtX8+fOtxzx8+LAee+wx9enTRykpKbrkkks0cuRIffPNN9mef+fOnRo1apRatGihdu3aacKECVqxYkXYOMuvvvpKI0aMUMuWLdW8eXMNHTpUn3/+ea7nCBSkvI4Rp06d0jPPPKMePXooKSlJ3bp109///ndlZmZa9+vWrZtuvvlmrVixQgMGDFBycrLeeOMNPy75nXfe0cSJE9WpUyc1b95cR44cyTGOfu7cuUpMTNSOHTuyPf7HH3+svn37qlmzZurZs6fee+8963533HGHJGn48OFKTEzM9l5dvny5Bg8erBYtWiglJUU33XSTvvvuu2x9CI1XzZo1U+/evfX+++8HvsZuTpTQNXj33Xc1efJkderUSSkpKbr99tuVnp6uzMxMPfLII2rXrp1SUlI0bty4bNd3zpw5Gj58uNq1a6ekpCT17NlTr7/+erbnPn36tJ5++ml17NhRzZs317Bhw7Rp06aw8cdBx3oUntD79dFHHw37fi1VqpSGDx+u2rVr+7eFPiO3bdumG2+8USkpKRozZoykMz9Y/va3v/mv+ZVXXqlp06bJ8zz//pFykbn5S0Lv4a1bt2rs2LFq1aqVWrZsqXHjxunYsWPWfTMzMzVhwgRdeumlSklJ0ahRo/zQpHMRGi8+/fRT/eUvf1G7du3UpUsX/1qEy2Pkjj2JiYnKyMjQvHnz/HHDfb+kp6fneo7hrF27Vrfffrsuu+wyJSUlqUuXLpowYYKOHz9uHRd63fbs2aM//OEPSklJ0aWXXqrHHntMWVlZ1rFBXsfQeT300ENauHChevbsqeTkZA0cOFDffvutJOmNN97Q5ZdfrmbNmmnYsGHWmJuXvruGDh2qX//612HbrrzySo0YMSLi/UPj/Zo1a/zPkz59+vhj+Xvvvac+ffqoWbNmGjBggDZs2GDd/5tvvtHYsWPVvXt3NWvWTB06dNC4ceN06NAh67gjR47okUceUbdu3ZSUlKR27drp+uuv1//+97+I/fv444/VvHlz3XXXXTp16lTEY3/JLr30Ukny/19FGptOnz6tGTNmqFevXmrWrJnat2+vBx54QKmpqdZjep6nZ599Vp07d/Y/48J9hueUE+Wrr77SjTfeqNatW6tFixbq06ePXn75Zb9/od8SoXHAHCfyu485OR+/rzZs2KCRI0fqkksuUUpKiq677jr95z//sY4JjaVr167Vww8/rEsvvVStWrXSAw88oMzMTKWlpemee+5R69at1bp1az3++OPZxpygfTdt375diYmJmjFjRra2L774QomJiVqwYEGO9y/I71jr16/XiBEj/PPr1q2bFcoWjud5+tOf/qSkpCTrO+v5xEqUs7Bs2TJdcMEFuuSSS87q/seOHdPQoUO1Z88eXXvttapdu7a+/PJL/f3vf9e+ffs0fvx4/9hXXnlF3bp1U58+fXTy5Em98847uuOOOzRlyhRddtll1uN+/vnneu+99zR48GBVrFhRr776qm6//XYtW7ZMVatWzXM/s7KydOONN6pVq1YaM2aM5s+fr4ceekjly5fXxIkT1adPH11xxRV64403dO+996pFixa64IILJJ15s37wwQe66qqrVK9ePe3fv1+zZ8/W0KFD9c4776hmzZqSznxJue6667Rv3z4NHz5c1atX14IFC8ImqVq9erVuvPFGJSUl6dZbb1WJEiU0d+5cXXfddXr99deVnJyc53MEzoe8jhH333+/5s2bpyuvvFLXX3+91q1bpylTpuj777/XM888Yx37ww8/6O6779bAgQN1zTXXqEGDBn7bs88+q9KlS2vEiBHKzMzM8xJbSdqyZYtGjx6ta6+9Vv3799ecOXN0xx13aOrUqerQoYNat26tYcOG6dVXX9WoUaPUsGFDSdJFF10kSfr3v/+tsWPHqmPHjhozZoyOHTumWbNmafDgwZo3b54f/vPxxx/rtttuU6NGjXT33Xfr0KFDGjdunGrVqpXnPpteeOEFlStXTjfddJO2bt2qmTNnqlSpUipRooTS0tJ066236quvvtLcuXNVt25d3Xrrrf59Z82apYsvvljdunVTqVKltGzZMj344IPyPE9Dhgzxj3vqqac0depUde3aVZ06ddI333yjESNG6MSJE1Zf8jLWo/AsW7ZMF154oZo3b56n+506dcqf1L/33ntVrlw5eZ6nW265RWvWrNFvf/tbNWnSRCtWrNDjjz+uPXv26L777jvrft55552qV6+e7rrrLm3YsEFvvfWWYmNj9X//93/+MePHj9fbb7+t3r1765JLLtEnn3yim2666ayf0/Xggw8qNjZWf/zjH5WRkZGn+z7++OO6//77lZycrGuuuUZS9hChIOcYzqJFi3T8+HENGjRIVapU0bp16zRz5kzt3r1bkyZNso7NysrSiBEjlJycrHvuuUerV6/W9OnTdcEFF2jw4MGSlOfXce3atVq6dKl//xdeeEGjRo3SyJEj9frrr2vw4MFKTU3V1KlTdd999+mVV145q76b+vbtq/vvv18bN25UQkKCf/u6deu0ZcsW3XLLLRGvmSRt3bpVd999t6699lr9+te/1vTp0zVq1Cg9+OCDmjhxogYNGuSfz5133qlFixb5K7JWrVql7du3a8CAAapRo4a+++47vfnmm9q0aZPefPNNlShRQpL05z//WYsXL9bQoUN10UUX6fDhw/r888/1/fffq2nTpmH7tWzZMt1+++3q2bOnJkyYoKioqFzP5Zdq27ZtkqQqVar4t4UbmyTpgQce0Lx58zRgwAB/Qu+1117Thg0bNGvWLP87wz//+U8999xz6tKli7p06aL//e9/uuGGG3Ty5Mlc+7Ny5UrdfPPNiouL87/Xf//99/rwww913XXXaeDAgdq7d69Wrlypxx9/PNv9C6KPUv7/vvruu+80ZMgQVaxYUSNHjlSpUqU0e/ZsDRs2TDNnzsz2+fLwww+revXquu222/TVV19p9uzZqlSpkr788kvVrl1bo0eP1kcffaRp06YpISFB/fr1O6u+h4S+k7799tv6/e9/b7XNnz9fFStWVPfu3XO9buf7O9aBAwc0YsQIVa1aVTfddJNiYmK0Y8eOiH9ky8rK0n333edP8OR0DfKdhzxJT0/3EhISvFtuuSXwfbp27erde++9fv2ZZ57xWrRo4f3www/WcU8++aTXpEkTb9euXf5tx44ds47JzMz0evfu7Q0fPty6PSEhwWvatKm3detW/7avv/7aS0hI8F599dWI/du+fbuXkJDgzZkzx7/t3nvv9RISErznn3/evy01NdVLTk72EhMTvXfeece//fvvv/cSEhK8SZMm+bedOHHCy8rKyvY8SUlJ3uTJk/3bpk+f7iUkJHjvv/++f9vx48e9q666yktISPA++eQTz/M87/Tp094VV1zh3XDDDd7p06et69OtWzfv+uuvj3iOQEHJ6xgRep+OHz/euv1vf/ubl5CQ4K1evdq/rWvXrl5CQoL30UcfWcd+8sknXkJCgte9e/dsY8akSZO8hISEbM87Z84cLyEhwdu+fXu2x1+8eLF1Ph06dPD69evn37Zw4ULr/Rly5MgRr1WrVt79999v3b5v3z6vZcuW1u19+/b1OnTo4KWlpfm3ffzxx15CQoLXtWvX7BfKMXToUG/o0KHZrkHv3r29zMxM//a77rrLS0xM9EaOHGndf+DAgdmex712nud5N9xwg9e9e3frXH71q195f/jDH6zjnn76aS8hIeGsx3oUjtD71X09Pe/MZ96BAwf8f+b/j9Bn5JNPPmnd5/333/cSEhK8Z5991rr9tttu8xITE/3P6HCfuyHu52noPTxu3DjruD/+8Y9emzZt/HpoLPnLX/5iHXfXXXdle8xI1q1bl61vofFi0KBB3qlTp6zj77333rDv2XBjT4sWLaz3SF7PMSfh3rtTpkzxEhMTvZ07d1p9TUhIsL6HeJ7n9evXz+vfv79fD/o6et6Z1yspKckaS9944w0vISHB69Chg5eenu7f/tRTT2Ubd4P23b2eaWlpXrNmzbwnnnjCuu9f//pXr0WLFt7Ro0ezPa4pNN5/8cUX/m0rVqzwEhISvOTkZOu5Q+djjvnh+r1gwQIvISHB++yzz/zbWrZs6T344IMR+zJ06FCvV69enud53uLFi72mTZt6999/f7bvkb9koffgqlWrvAMHDng//vij984773ht2rTxkpOTvd27d3uel/PY9Nlnn3kJCQne22+/bd3+0UcfWbcfOHDAa9q0qXfTTTdZ37f//ve/Z/uMC33uhv5fnDp1yuvWrZvXtWtXLzU11Xoe87EefPDBsN9Lzkcfc5Lfv6/+8Ic/eE2bNvW2bdvm37Znzx4vJSXFGzJkiH9b6HV0f88MHDjQS0xM9B544AH/tlOnTnmdO3e2vuvkpe/u78/Q+3jTpk3Wfdu2bZvrNSuo71ihsXfdunU59iX0+Tl16lTv5MmT3p133uklJyd7K1asiHgO+Y1wnjw6cuSIJKlixYpn/RiLFi1Sy5YtFRMTo4MHD/r/2rdvr6ysLH322Wf+sWaug9TUVKWnp6tly5bZllVKUvv27a2/6jRu3FjR0dHavn37Wff1d7/7nV+OiYlRgwYNVL58eV199dX+7Q0bNlRMTIz1PGXKlPH/WpGVlaVDhw6pQoUKatCggdX3FStWqGbNmtbsZ9myZf2/UoV8/fXX2rJli/r06aNDhw751ywjI0Pt2rXTZ599xvJ4FAl5HSOWL18uSbr++uut22+44QarPaRevXrq1KlT2Mfq16/fOe/4ExcXp8svv9yvR0dHq1+/ftqwYYP27dsX8b6rVq1SWlqaevXqZY1tJUuWVPPmzf0VZnv37tXXX3+t/v37q1KlSv79O3TooEaNGp1T//v27WutwElOTpbnefrNb35jHZecnKwff/zRWiZuXrv09HQdPHhQbdq00fbt25Weni7pzIq4U6dO+X9xDhk6dGi2vuRlrEfhCL1fw+ULGDZsmNq1a+f/Cy1BN4X+Uh/y0UcfKSoqKtv22zfccIM8zwsUYpuTa6+91qq3atVKhw8f9s8hNFa4z51beHFeXHPNNed1VUBu55gT872bkZGhgwcPKiUlRZ7nhf2+5L5uLVu2tMJs8vo6tmvXzkqyHfqr8xVXXKHo6Gj/9tCKWfP7Ul77HlKpUiV1795d77zzjr/cPysrSwsXLlT37t0D5cBo1KiRlQ8n1O9LL71UderUyXZ7Tv0+ceKEDh486B9nhurExMToq6++0p49e3Ltz4IFCzR69GgNHDhQDz300M82p9e5+P3vf++H040ePVoVK1bU5MmT/RXeIe7/8UWLFqlSpUrq0KGD9XnUtGlTVahQwf98XrVqlU6ePKmhQ4f6q4mkYOPIhg0btGPHDg0fPlwxMTFWm/lYOSmIPobk5++rrKwsrVy5Uj169PBX5Etnvk/17t1bn3/+ebYx7Le//a3V99B3ld/+9rf+bVFRUUpKSsr2Oy4vfTddffXVKlu2rJWC4eOPP9ahQ4dyDA10ne/vWKHvhB9++GGuq4pOnjypO+64Qx9++KFeeOEFdezYMdA55BfCefIo9GF49OjRs36MrVu36ttvv7Wy4ZsOHjzol5ctW6bnnntOX3/9tRVbFm4wMuO1QypXrqy0tLSz6mfZsmUVGxtr3VapUiXVqlUr2/NXqlTJep7Tp0/rlVde0euvv64dO3ZYscbmksOdO3cqPj4+2+O5S3y3bNkiSbr33ntz7G96eroqV64c6NyA8yWvY8TOnTtVsmTJbP/na9SooZiYGO3cudO6PdJuOPmxU86FF16Y7f1Yv359v681atTI8b6h92lOX2RC12bXrl3+c7ncida8Mr/4Sz99ILvjY6VKlXT69Gmlp6f7y3E///xzPf300/rPf/6TLQ9Denq6KlWq5Pfdfb2qVKmSbfzJy1iPwhGa7AwXmvLQQw/p6NGj2r9/f9hwklKlSmULP9u5c6fi4uKsH87ST+Fu7vs5L9z/26EfKampqYqOjs5xLAmF3IVkZmZmyy8QGxsbaHLkfO/Glds55mTXrl2aNGmSli5dmu3c3B8v4b7bVK5c2bpfXl9Hd3wJ3c/9/xEaj8zvS3npu6tfv3569913tXbtWrVu3VqrVq3S/v37IyYEjtTvUP/cfofOx+z34cOHNXnyZL377rs6cOCAdXzoB5F0JjHz2LFjddlll6lp06bq0qWL+vXrZ/3YlM7k9Pi///s/XXXVVfrTn/4UqP+/RA888IAaNGigqKgoVa9eXQ0aNMg22RRubNq6davS09Nz/DwKvYahz7jQ535IbGxsrt+xQz/2zfCyvCiIPobk5++rgwcP6tixY1Z4dchFF12k06dP68cff9TFF1/s356X7yruuJCXvptiYmLUtWtXLViwQHfeeaekM6E8NWvW9HPr5OZ8f8dq06aNrrzySk2ePFkzZsxQmzZt1KNHD/Xp0yfbhgJTpkxRRkaGXnzxRbVt2zZQ//MTkyh5FB0drbi4uDwlL3KdPn1aHTp00MiRI8O2hwaFtWvX6pZbblHr1q315z//WTVq1FDp0qU1Z86csMl/cvoC5DkJiYLK6fGCPM/zzz+vf/7zn/rNb36jO+64Q5UrV1bJkiU1YcKEs+pP6D733HOPmjRpEvYYdglAUXC2Y0SQv9JIirjSJFxbTo/rJlHMD6H36eOPPx52sqUg4tpz+stlTreH+rxt2zb9/ve/V8OGDTV27FjVrl1bpUuX1vLlyzVjxoyzWukWdKxH4alUqZKfz8EV+qu6mwg0xFxxmVdn877M7f9wUF9++aWGDx9u3bZkyZJAEyThduLIzzHmbM4xKytL119/vVJTUzVy5Eg1bNhQFSpU0J49ezR27Nhs793zMQ6d7felvPbd1bFjR1WvXl1vv/22Wrdurbfffls1atRQ+/btz2u/pTP5a7788kuNGDFCTZo0UYUKFXT69GmNHDnSOq5nz55q1aqV3n//fa1cuVLTpk3Tiy++qKefftpPTiyd+cNBjRo1tHz5cq1fv97fgQa25OTkXK9NuLHp9OnTqlatmp588smw93EnFgtDQfWxsH9fSXn/rhKS1767+vXrp0WLFumLL75QQkKCli5dqkGDBgX+LDvf37FKlCihSZMm6T//+Y+WLVumFStW6L777tNLL72k2bNnW6u8O3XqpBUrVmjq1Klq27Ztge8UxSTKWejatatmz56tL7/80loGGVR8fLwyMjJy/ZBbvHixypYtq2nTplmzb3PmzMnzcxa0xYsXq23btpowYYJ1e1pampXktm7dutq0aZM8z7O+iIUSZYWE/mIRHR0d+MsBUFjyMkbUrVtXp0+f1tatW/2/ckrS/v37lZaWprp1655TX0J/yU1LS7OW14b+kuPaunVrtvdjaIVJqC85/WgKvU+rVasW8X0a+kvG1q1bs7X98MMPOd7vfFq6dKkyMzP13HPPWX9pcZNch9q2bdtm/SX10KFD2f5aFHSsR+G67LLL9NZbb2ndunXnnKC8bt26Wr16tY4cOWKtYti8ebPfLsn/a6m7UjSn92XQ5z59+rS2bdtmrT4JPXdI48aN9dJLL1m3RVphlpuYmJiwK17P5VzyYuPGjdqyZYsee+wxK/niypUrz/oxg76O5+pc+x4VFaXevXtr3rx5GjNmjD744IPzHnIlnVkZtHr1at12221W4sjQZ4UrLi5OQ4YM0ZAhQ3TgwAH1799fzz//vDWJUrZsWU2ZMkXXXXedRo4cqZkzZ1p/uce5iY+P1+rVq3XJJZdE/GNM6DNuy5Yt1mfcwYMHs33GuULHb9y4MeLnXk7fIQqij1L+/76KjY1V+fLlw35/2bx5s0qWLBl2NcvZONe+d+rUSbGxsZo/f76aN2+uY8eOBV65di6CfscKadGihVq0aKHRo0dr/vz5GjNmjN59910rzUTz5s117bXX6uabb9Ydd9yhyZMnq1SpgpvaINjwLIwcOVIVKlTQ/fffr/3792dr37Ztm7+VVzhXX321vvzyS61YsSJbW1pamh8/FhUVpRIlSlh/zdmxY4eWLFmSD2dxfkVFRWWboV24cGG2mNiOHTtqz5491jmdOHFCb775pnVcUlKS4uPjNX369LBhEiyLR1GSlzEi9CXSHTNCP3LML5lnI7S038y/kZGRoX//+99hj9+7d6+VBf3IkSP697//rSZNmvg/tMqXLy/JXrItnflwjo6O1pQpU8LGsobep3FxcWrSpInmzZtnPcbKlSu1adOmszjLcxf60WGOW+np6dm+mLRr106lSpXSrFmzrNvD5csIOtajcI0cOVLly5fXfffdF/b9mpe/Nnbu3FlZWVnZ/j/MmDFDJUqUUOfOnSWd+YNA1apVtXbtWuu4cNs95uW5JenVV1+1bnfHlsqVK6t9+/bWv3P5C158fLzS09P1zTff+Le540hIhQoVzjrEOCehv4Car5PnedYOOHkV9HU8V/nR9759+yo1NVUPPPCAMjIyAuc2OBc5TdK4/9eysrKyfU5Uq1ZNcXFx2bY/lc6sDJs6daqqVaum66+/Ptsf1HD2rr76amVlZenZZ5/N1nbq1Cn/fdm+fXuVLl1aM2fOtP5fRvpdE9K0aVPVq1dPr7zySrb3uflYoe8Q7jEF0Ucp/39fRUVFqUOHDlqyZIm1cnH//v1asGCBWrZsGTEcMa/PdS59L1WqlHr16qWFCxdq7ty5SkhIUOPGjfOlb5EE/Y6Vmpqa7TM3FIEQbsxo3769Jk6cqBUrVuiee+4p0PyYrEQ5C/Hx8XryySc1evRo9ezZU3379lVCQoIyMzP15ZdfatGiRRowYECO9x8xYoSWLl2qUaNGqX///mratKmOHTumjRs3avHixVqyZIliY2PVpUsXvfTSSxo5cqR69+6tAwcO6PXXX1d8fLy+/fbbAjzjvLvsssv0zDPPaNy4cUpJSdHGjRs1f/78bDGwAwcO1MyZM3X33Xdr+PDhqlGjhubPn+9/oQvNVpcsWVIPP/ywbrzxRvXu3VsDBgxQzZo1tWfPHq1Zs0bR0dF6/vnnC/w8gXDyMkY0btxY/fv31+zZs5WWlqbWrVtr/fr1mjdvnnr06BE4TjUnHTp0UJ06dTR+/Hht3rxZUVFRmjNnjqpWrRr2L8X169fX+PHjtX79elWrVk1z5szRgQMH9Oijj/rHNGnSRFFRUXrxxReVnp6uMmXK6NJLL1W1atX0l7/8Rffcc48GDBignj17KjY2Vrt27dLy5ct1ySWX6IEHHpAk3XXXXbr55ps1ePBg/eY3v9Hhw4f9vzzmdevU/NChQweVLl1ao0aN0rXXXqujR4/qrbfeUrVq1ayEutWrV9fw4cP9rUA7deqkb7/9Vh999JGqVq1q/YUt6FiPwlW/fn09+eSTuvvuu3XVVVepT58+aty4sTzP044dO7RgwQKVLFky0Pbb3bp1U9u2bTVx4kTt3LlTiYmJWrlypZYsWaLrrrvOylfyu9/9Ti+88ILGjx+vpKQkrV279pxWYjVp0kS9e/fW66+/rvT0dKWkpOiTTz4Ju+IrP/Xs2VNPPvmkbr31Vg0bNkzHjx/XrFmz1KBBAyvBqHTmR9bq1av10ksvKS4uTvXq1cvz1tKuhg0bKj4+Xo899pj27Nmj6OhoLV68+Jwma/LyOhZ233/1q18pISFBixYt0kUXXZTjtsH5KTo6Wq1bt9bUqVN18uRJ1axZUytXrswW+nb06FF16dJFV155pRo3bqwKFSpo1apVWr9+vcaOHRv2sWNjY/XSSy9p0KBB+v3vf69Zs2ZlS5qKvGvTpo0GDhyoKVOm6Ouvv/Y/87Zs2aJFixZp/PjxuuqqqxQbG6sbbrhBU6ZM0c0336wuXbpow4YN/mdcJCVLltRf/vIX3XLLLerXr5+//fXmzZu1adMmTZs2TZL8/6MPP/ywOnbsqKioKPXq1atA+ijpvPy+uvPOO7Vq1SoNHjxYgwcPVlRUlGbPnq3MzMxct2jPi/zoe79+/fTqq69qzZo1GjNmTL71LZKg37HmzZunWbNmqUePHoqPj9fRo0f15ptvKjo6OsfJ6x49emjChAm69957FR0drYceeqhAzolJlLPUvXt3vf3225o2bZqWLFmiWbNmqUyZMkpMTNTYsWOz7S5jKl++vF599VVNmTJFixYt0r///W9FR0erfv36uu222/wkPe3atdMjjzyiF198URMmTFC9evU0ZswY7dy5s8hPoowaNUrHjh3T/Pnz9e677+pXv/qVpkyZoqeeeso6rmLFinr55Zf18MMP65VXXlGFChXUr18/paSk6LbbbrP+Ota2bVvNnj1bzz77rGbOnKmMjAzVqFFDycnJGjhwYEGfIhBRXsaIhx9+WPXq1dO8efP0wQcfqHr16rr55putZdJnq3Tp0po8ebIefPBB/fOf/1SNGjV03XXXKSYmRuPGjct2fP369fWnP/1Jjz/+uH744QfVq1dPEydOtHYEqlGjhh588EFNmTJF48ePV1ZWll555RVVq1ZNffr0UVxcnF544QVNmzZNmZmZqlmzplq1amVNLnfu3Fn//Oc/9Y9//ENPPfWU4uPj9eijj2rJkiX69NNPz/m886phw4aaNGmS/vGPf+ixxx5T9erVNWjQIMXGxuq+++6zjh0zZozKlSunt956S6tXr1aLFi00bdo0DR482FpeG3SsR+Hr0aOH5s+fr+nTp2vlypWaM2eOSpQooTp16qhLly4aNGhQoL/WlSxZUs8995wmTZqkd999V3PnzlXdunV1zz33+Dtuhfzxj3/UwYMHtXjxYi1cuFCdO3fW1KlTc0yqGMSECRNUtWpVzZ8/X0uWLFHbtm31wgsvnPOKtkiqVq2qyZMn629/+5ueeOIJ1atXT3fddZe2bt2abRJl7NixeuCBB/SPf/xDx48fV//+/c95EqV06dJ6/vnn9fDDD2vKlCkqW7asLr/8cg0ZMuSsl6nn5XUsCn3v27evnnjiiQJZlh/y1FNP6a9//atef/11eZ6nDh066MUXX7Q+K8qVK6dBgwZp5cqVeu+99+R5nuLj4/XnP/852w5nppo1a2rGjBkaPHiwrr/+es2cOZMJ53zw0EMPKSkpSW+88YYmTpyoqKgo1a1bV7/+9a91ySWX+MfdeeedKlOmjN544w2tWbNGycnJmj59um6++eZcn6NTp056+eWX9cwzz2j69OnyPE8XXHCB9Z3niiuu0LBhw/TOO+/o7bfflud56tWrV4H18Xz8vrr44ov12muv6amnntKUKVPkeZ6Sk5P1xBNPnPMYl999T0pK0sUXX6zvv/++QFauScG/Y7Vp00br16/Xu+++q/3796tSpUpKTk7Wk08+me0P8aa+ffvq6NGjevDBB1WxYsWIG5HklxLeuWTFAc6TGTNm6NFHH9VHH33EXyCAAtKtWzddfPHFmjJlSmF3pdgJrSK68847dcsttxR2dwD8grz88st69NFHtXTp0my7ZwCAq1+/fqpcuXLgEChkR04UFLrjx49b9RMnTmj27NmqX78+EygAihx3zJJ+isVu06ZNQXcHwC+Y53n617/+pdatWzOBAiBX69ev19dff20ls0beEc6DQnfrrbeqTp06aty4sY4cOaK3335bmzdvznGLMwAoTO+++67mzZunzp07q0KFCvriiy+0YMECdezYUS1btizs7gH4BcjIyNDSpUu1Zs0abdy4MWwyTgAI2bhxo/73v/9p+vTpqlGjhnr27FnYXSrWmERBoevYsaP+9a9/af78+crKylKjRo00ceJE3twAiqTExERFRUVp6tSpOnr0qKpVq6bhw4frzjvvLOyuAfiFOHjwoO6++27FxMRo1KhR6t69e2F3CUARtnjxYj3zzDNq0KCB/v73v5/TrmwgJwoAAAAAAEAg5EQBAAAAAAAIgEkUAAAAAACAAJhEAQAAAAAACCBwYlm2TQOKj127dhV2F84aYw1QfBTnsaZEiRKF3QUAARXnFI6MNUDxEXSsYSUKAAAAAABAAEyiAAAAAAAABMAkCgAAAAAAQABMogAAAAAAAATAJAoAAAAAAEAATKIAAAAAAAAEwCQKAAAAAABAAEyiAAAAAAAABMAkCgAAAAAAQABMogAAAAAAAATAJAoAAAAAAEAATKIAAAAAAAAEwCQKAAAAAABAAEyiAAAAAAAABMAkCgAAAAAAQABMogAAAAAAAATAJAoAAAAAAEAATKIAAAAAAAAEwCQKAAAAAABAAEyiAAAAAAAABMAkCgAAAAAAQABMogAAAAAAAATAJAoAAAAAAEAATKIAAAAAAAAEwCQKAAAAAABAAEyiAAAAAAAABMAkCgAAAAAAQABMogAAAAAAAARQqrA7UBRFRUVZdc/z/HLJkva8k1vPysryyydPnrTaSpQoYdVLly7tl48cOWK1VahQIcdjJenUqVN++fTp01ZbqVL2y2r20TwXSUpPT7fq5cqV88tlypSx2sxzk6QTJ074ZfeaudfFPNblnpt7X/P8zPOWsp+P2Q/3cVzm+bnnlpmZGfhx3dfZ7K97boCJseYMxppgj8tYAwAAUPhYiQIAAAAAABAAkygAAAAAAAABMIkCAAAAAAAQADlRwnBj7s049GPHjlltbo4AMxeBm5fAZT5uXFyc1Xbw4EGrnpqaatXNfAJuXgI354EZg+8eGxMTY9UPHDjgl92cAJUqVbLqZpx/pOsg2XkX3NwCx48ft+pue9myZf2yed7h+mj2w80n4PbJzNHgtpUvXz7H53Gvr9k/SapSpYpfPnr0qICcMNacwVgT/nkYawAAAIoeVqIAAAAAAAAEwCQKAAAAAABAAEyiAAAAAAAABFDCc4PCc1CnTp3z3ZciIzMz06qbsf1uDgM3vt1sd+PkXWb+ADf/gRv77j7PiRMncmxzY/nNfpi5EcLVzT6ZeQgkqXTp0lbdzC/g5imoXLlyjnX3urh5CtxrYZ6re1+3bt7XzSdQtWrVHJ/XPTc3n0Ok6+Iycz1ER0dHPPZ82bVrV6E8b35grDmDseYnjDXhMdacm9xyCQEoOgL+XCmSGGuA4iPoWMNKFAAAAAAAgACYRAEAAAAAAAiASRQAAAAAAIAASuV+yC+PG/tuxqW7eQrcnAYZGRl+2Y2pcu9rcuPZU1NTrbobTxkTE+OX3Tj/3bt353hsw4YNrba4uDirnpKS4pdbt25ttdWoUcOqm7H8bg6AKlWqWHUzj4F5jcI9rpsH4ODBg37ZvS779u2z6l988YVf/uyzz6y2VatWWXUzp8GpU6esNve1M9vdnAaRXlcgEsaaMxhrFLadsQYAAKDoYSUKAAAAAABAAEyiAAAAAAAABMAWx2G4S6jN5eHuUmx3u01TbltkmnX3cd1tL93tQc2XrX79+lbb5ZdfbtWvuuoqv9ykSROrrVq1auG6Lin7ubl9MK9TblusmluHuluqutz/kmYIgXtd3NcqEncb0kmTJvnlWbNmWW179+616mZYgLkFqWSHMEhSpUqV/PLRo0cD9y8/FedtRxlrzmCs+QljzRmMNfmLbUeB4oMtjgEUBLY4BgAAAAAAyEdMogAAAAAAAATAJAoAAAAAAEAA5EQJw425N+Pk3baKFStadXPLTHc7UHe7TTM3gZsTIDY21qp37NjRql9zzTV+uWvXrlabu4WpyX0e93zM+PbDhw9bbe72mm7eAlOtWrWsevXq1XM81n0cNw9AuXLlcryvuXWoZJ+Pmw/B3SLWfK22bdtmtU2fPt2q/+tf//LLbg6D8uXL59gHMz9DQSrOeQoYa8K3MdaEx1hzBmNN3pGnACg+yIkCoCCQEwUAAAAAACAfMYkCAAAAAAAQAJMoAAAAAAAAAZATJQw3Ht+MNXfb3Fh4MzfB0aNHrbbKlStb9Xr16vnl9u3bW229evWy6t27d8+xv25cv5n/IFwfTRs3brTqc+fO9cuffvqp1RYXF2fVf/WrX/llNy+BmyvBjOWvVKmS1WbmC5CkmJgYq27mbHAf182zkF+OHDli1RcuXOiXH3/8catt7dq1Vt28Tu7/l4JSnPMUMNaEb2OsOYOx5ieMNeeGPAVA8UFOFAAFgZwoAAAAAAAA+YhJFAAAAAAAgABKFXYHiiJ32Z25TNpd0u1uZWkusXeXnQ8cONCqDxkyxC8nJCTk+JzhmMvo09PTrbZDhw5Z9e3bt4ctS9LOnTuturn9Zm7L2c1l3GlpaVabG05QoUIFv+xeM/dYd7vWAwcO+GX3mrrbsTZo0CDH53G3ADWX+rvbsbrn/rvf/c4vu1uoPvHEE1Z9+fLlftk9N8DEWHMGY81PGGsAAACKNlaiAAAAAAAABMAkCgAAAAAAQABMogAAAAAAAARATpQwKlasaNXNWH63zd3is1GjRn75hRdesNouueSSHJ/T3To0Nz/++KNfdre9/O6776y6Gfdfrlw5q82NzzfPzz3WzVNw7Ngxv1yqlP1fyb1vamqqX65SpYrV5m7x6fapdu3aftnMASBJhw8ftuqtWrXKsb/mNqmS/dq5uSncHAdm3oiuXbtabeaWqpI0atQov+xu6xofH2/V9+7da9WrVq3ql/fs2WO1XXDBBVbd/D/g9gHFA2NN+GMZa85grAEAACh6WIkCAAAAAAAQAJMoAAAAAAAAATCJAgAAAAAAEEAJz/O8IAfWqVPnfPelyNi3b59Vv/DCC/3y0aNHc2yTpJkzZ/plNya9TJkygftgxqBL2ePdf/jhB7984MABq83NnWDmEHDzCbhx/mbuATfXgHvfEydO5NjmnmtGRkaOx1aqVMmqu3kLYmJi/PK6deusNjdvhBnn7z5u9erVrXqnTp38splfIhzzXMuWLZtjmyQtWbLEL48fP95q++9//2vV3feVmaPBfWua+SYk+9zd3A7bt29XccVYcwZjzU8Ya7K3SYw158rNTwOg6Ar4c6VIYqwBio+gYw0rUQAAAAAAAAJgEgUAAAAAACAAtjgOw102b24NWbNmTatt4sSJVj23pdo5Wb9+vVXfunWrVd+0aZNVN5fG57blp7nk3g0RcJeLm9ufusuZTp48adXNrTndY0uXLp1j3d3S090e1G0367Vq1bLa0tPTrfru3bv9shtq4C6nNMMW3P67W3y652Nyr2HPnj398oYNG6y2xx57zKq752q+luZWp1L28zGvW163rUXRwFhzBmNN+P67GGsAAAAKHytRAAAAAAAAAmASBQAAAAAAIAAmUQAAAAAAAAIgJ0oY7raX5vaODz/8sNWWlJRk1RcvXuyXr7zySqvN3Z5y0aJFfnnnzp1Wmxt37sbRR0dH53isuz2lWXfj4s0tPd1291iXudWlmwPAPVezj+7juse6W2iax8fGxlptFSpUyLF/7pakbty/uTWnu3Wr24fExMQcn8dl5nO4/vrrrbZdu3ZZ9RdffNGqV6lSxS+7eQncbVTNLWLd/qJ4YKwJf6yLsSY8xhoAAICCx0oUAAAAAACAAJhEAQAAAAAACIBJFAAAAAAAgADIiRKGG3PfpUsXv9y3b1+rbeTIkVa9fPnyfrlz585W25o1a6z63r17/XJqaqrVVq5cOavuxtibsfvufc34dUkqVeqnl9l93P3791t1M2eAex3Kli2bYx9LlrTn444ePRqxbnJzMLgOHz7sl8uUKZNjHySpcuXKfnnPnj1Wm3m9JalJkyY5Pu6OHTusupkjoEaNGlZb6dKlrbp5LapVq2a13XDDDVZ92bJlVv2HH37wyxdccIHVtm/fPqtuXjfyFBRPjDVnMNb8hLEGAFBcuZ9bo0eP9stPPPGE1fbWW29Z9R49epy/jgH5jJUoAAAAAAAAATCJAgAAAAAAEADhPGGY20ZKUqtWrfzyRx99ZLXNnTvXqt94441+2VxuL0krVqyw6uaWme4ybXcrUXdrTnPJekZGhtXmbutpPpb7uO6x5laX7nVwjzWXdbtL1N0tM83l+u6xbp/MZfKSlJ6e7pcjbanqPvbmzZutNncpvxky4IYluK+V+Vjdu3e32ho2bGjV3XADU+PGja36NddcY9XNbW3r1KljtW3cuNGqm+3u/wEUD4w1ZzDW/ISxBgBQXA0ePNiqmyHCAwYMsNrczzigOGElCgAAAAAAQABMogAAAAAAAATAJAoAAAAAAEAA5EQJw9xiUpK6du3qlx955JGI961fv75fdmPo69WrZ9W/++67HJ/TjfN3cwSYOQTcuHh3i1KzH27uAXcrUTOW333OSPkQ3Mdx8y6Y7W6be66R4vzNLVQlO4eBFPm6uNuumc+blpZmte3evduqm+fqbknaoEGDHI91czCYuSkkqVevXlZ9xowZftm93hUrVrTq5uvsbhGL4oGxJvxzMtacwVgDAChOmjVrZtXnzJnjl6+44gqr7dlnny2QPgHnAytRAAAAAAAAAmASBQAAAAAAIAAmUQAAAAAAAAIgJ0oYbj6B8uXL++WlS5dabZdeeqlVHz16tF9+8803rTY3Pr9cuXJ+2Y1nz0ueAve+bt3MPWCWJenAgQNW3cwn4OY7iJQ/wM3J4NbN/rox9e6x7rkePXrUL1epUsVqO3LkiFU3r1v16tUjPu7+/fv9spsnIikpyaqb18LNaXDo0CGrHhsbq6Di4+OtekJCgl/+6quvrLYLL7zQqu/du9cvnz59OvBzouhgrDmDseYnjDUAgOLK/B4jSStXrvTLlStXtto++eSTAukTcD6wEgUAAAAAACAAJlEAAAAAAAACKNLhPO5S8UhLvCV7+bh7rFs3lyS7y9kHDhxo1T/88EO/7G7b2aZNmxz7Yy6BlqRjx47l2Ifclqi7y93N7TfdY93l1uZ1dJeku+dz4sQJv+wuhXdDBMqUKeOXzXABKfs2pGb/3cdxtxJ1lwLWrFnTL7vX0F3Obm5D6i4bjHQN3cc9fPiwVb/44otzPHbFihVWvW/fvgrKvcbt27f3y+7//127dll185rn9t5AZIw1P2GsOYOx5ieMNQCAIIYMGZJj2xtvvFGAPfl5ql27tlV3vydUrFjRqteoUcMvm99bpOzfSc3vMm7osRmWlVddu3b1y/Xr17fa3O+Rr776ao5tRQ3fhgAAAAAAAAJgEgUAAAAAACAAJlEAAAAAAAACKNI5UdwY7oLSoUMHq/7yyy/75YyMDKvNjbE3t7Z04+1dqampftmNoXfj/N3nMa+NGzPm1t3HMrmxdGauB/f6u30w8w3k1l8zjt69LlWrVs2xf5K9raebZ8HNj1ChQoUc+7B9+3ar3qhRI78cExNjtblbi5o5Atx4w6ZNm1p1M8bQ3erU7J+U/brFxcX55Vq1alltZlyjJH388cd+2dxCFXnHWJPz8xTnsWbixIk59qewTJo0yaoz1gDF3+rVq626uV16OGb+pWrVqllt7vecffv2+WUzl5SUfUz49NNP/XKfPn0i9gFA8TN9+nSr7uY1cXPBmfXo6GirLdL3Qfc7RcuWLfPe2f8nOTnZL5s57iRpw4YNVt3srzveFTWsRAEAAAAAAAiASRQAAAAAAIAAmEQBAAAAAAAIoEjnRHHllrfAjLHPC/d+Zg4OSdq2bVuO9z148KBV37Fjh192Y2LdnABmXJgb0+bGjLkxbmYuAvfYrKwsq27GvLlx8m5OkUh5CtzYOTMXgdsW6bVyz8Xtr5tnwYzhi42Njdgn8/XYsmWL1dasWbMc+2TmjJCyX1Oz7uZPqVKlilU3X3dzz/Vw3Hg/877Hjx+32tq0aWPVzThC9/8Wzg1jzU+K81hTFDHWAD8/bv4Adxw1czVJ9ti/c+dOq80dV+vWreuXW7RoYbUtWbIkz30FUHzllpfM/Z5p5sxzv3O6x5pj0ffff3+2XczGzPXmfuc5cOCAVTe/75ETBQAAAAAA4GeASRQAAAAAAIAAmEQBAAAAAAAIoEjnRMnPWHc37itSTgM3Z0CHDh388jvvvGO1paWlWfULLrjAL1944YVW27fffmvVT548meNzujFjLjNm1o2fdevmY7v5Rty43Ujc1yPS6+P2weRee/M6SNn7aOZAcK+Tuae5JDVv3twvDx482Gq7+OKLrfqqVav88vbt2602N37ZfB43f8CmTZusupm3oFKlSlabe83c53HrpkaNGll1M24w0v2QO8aanBXnsaYoqlOnjlVnrAGKP/f95uaAcnM5mWOyOwZnZGRY9cWLF/vlcePGWW2ffPKJVR84cGDAHgMojtzvgu5vKLdufma7+VR2795t1detW+eX3Vx75+KDDz7wyxdddJHVVr9+fat++PBhv/zNN9/kWx/OB74NAQAAAAAABMAkCgAAAAAAQABFOpzH5S6XdJfJm+2R2lzuse5Wdb179/bLM2bMsNrcJZrmknV3yZX7uOYS6fT09Ij9jdRHd9m8W48UTuAu+8oL99wjPafZJ3c5WaQtPiWpYsWKOT6nu+zVXJrmbg/63//+16qbWyC7S2/dZfRuu8kNKYqPj/fLkUKTpOwhDubzuv9/3D6Z9eK2zWtRx1gT/rGK21hTFLlbVTPWAMWfu/WwuxW4+13F3OrcHRvd7crN8Lp9+/ZZbd26dcuxH88//3zkTgMBuZ8n5neK+fPnW21XXHFFgfTplyq30Gh3PDly5IhffvTRR89Ln3Jjbq28du1aq82tFyesRAEAAAAAAAiASRQAAAAAAIAAmEQBAAAAAAAIoEjnRDmfsdeRYvdPnDhh1c2cAO4Wk2a+DsmOPXPj16Ojo6361q1b/bK77agrUi6C3GLf3fwIkZjXJbdcD+a2Wblte2k+lrudldt/M1ZYsl8PN1bYPVcz38DSpUutNjOGU7JzD7ivo3vu5rFVq1a12sxtRt1jc+NetwMHDvhld+ux9evXW3U3twXOHmPNT35OY01R5F5Dxhqg+HO/b7jjbFxcnFU3x/O9e/dabW5OInNb9BEjRlht7phrfi4AZ8v9vpyVlWXVK1Wq5JfJk1WwatSoYdVzy7Xn5lFC/mElCgAAAAAAQABMogAAAAAAAATAJAoAAAAAAEAARTonSm7cOLyzjYV377du3TqrnpiY6Jfr1q1rtbmx5Lt27fLLZsxguHp8fLxf3r59u9Xmxrq78Yjmubuxi27suxlf6+Y7cK+h2e4e6z5upDjISMdu2bLFaqtVq5ZVd/MsmP039xoP10ez7uaFcI81r7F7/atVq2bV3ZwTJjfHixmT7J6Le83c/AhmHLX7//Lrr7+26ocPH/bL7r7wyF+MNWcUt7GmKGKsAX5+WrRoYdXdnFZu/ivz+0nt2rWtNve9ao5xZt63cMeSEwX5wf1Mdj/7I+UfxPnlfmdwP/srVKhg1fOSqw55w0oUAAAAAACAAJhEAQAAAAAACKBYh/PkRV6W37vLrc3t5Ro1amS1ffDBB1bdXPbsLrn65ptvrLq5xMrdvrR8+fJW3d3yLhJ3GZ65/PrYsWM59kGyr5O7rN9dNm8u73OXk0Vajuo+p7u9n/s85tI0N/TADe8xHzsmJsZqc/8PmM+zZ88eq+2iiy6y6uY13bZtm9XWu3dvq+6GEZncpfBpaWlW3dyK1g3fcJUpU8Yvu1uhovAw1pxRFMaaooixBvj5ccfRBg0aWHV3jEtNTfXLbgiO+TngPvb+/futNne8c58HOBvu95hIYb4LFy4skD7hDDdcx93y2B0T3O9pyD9F+9smAAAAAABAEcEkCgAAAAAAQABMogAAAAAAAARQrHKi5JZrIC9bW5oxY+6Wku72jjt37vTLrVq1stqWLFli1c34/NjYWKvNjVszc4G4+QLc+HU3HjEjI8Mvu7Hv7lZkkbYSjZQnxM09EGk7YffamzH0kr2dptvm1t1zNbcGdF8rNz7fvI7uudWrV8+qf/fdd37Z3AJWyp5rwIxfdmOdI+UlcLmvjbvF7fr16/2y+7q629Zu3rzZL7vXEOeGseYnxXmsKYoYa4CfH3OcDFd33zdmbir3WHP7esnOieLmO9iwYYNVb9u2bcAeAzlzP68jfSf6z3/+c557A5M7BuSWA8XdFh35h5UoAAAAAAAAATCJAgAAAAAAEACTKAAAAAAAAAEUq5woBWXjxo1Wff78+X556NChVtsjjzxi1WfMmOGXn376aavt22+/teo7duzwy25OAzdG/fjx41bd3CfcjYdzcwYcO3bML7v5EM42t4N7XzM/Q27cWH0zNljKnh/BfF73vu75mP1wj929e7dV/+GHH/zyVVddZbW516V27dp++ZprrlEkZjyie82OHj1q1V977TWrfuTIEb9cs2ZNq83M4SLZ52rmjUHxwVgTXn6NNUXRxRdfbNUZa4Dizx1HDx8+bNXd942Ze8odK80cKO5ju8c2atTIqqekpATrMIBiyfxOFo772f/jjz+ez+78orESBQAAAAAAIAAmUQAAAAAAAAIgnCcMd1n0//f//X9++bbbbrPa3OXWW7ZsyfFxk5KSrPp///vfHI/NbStRc6tUdzm4uxzfXPLtLoV3l42a25i5z+leF5O7fV+kY902t0/uNrDm+blbd7nMUKBy5cpZbWb4jmQvq3dDiurUqWPVa9WqFfF5TeZSOner0JUrV1r1xYsXW3XzWrh9cv9vmds7u/9fUDww1oR/zvwaa4oixhrg58fdGt7dat3dxjjSWOkyQ4HMMDxJatGihVVfs2ZNrn0FUHzt27fPqrvhO27qAnfLdOSf4vXtEwAAAAAAoJAwiQIAAAAAABAAkygAAAAAAAABkBMlDHf7qK1bt/plcwtSSXrqqaes+rx58/yyu+3orbfeatWbNm3ql7/55hurzd3m180DYOYpcLe8c2NvzXjbypUrK5JIcbqR4nbd/kZqdx/HzWngxhab7W5OBjfvifnYbp/q169v1ZOTk/2yG2fsbkNqbjvq5oFw+2DmJvj++++ttueee86qu9semq+PG9fovnbmtcjL9rEoOhhrst8vXD1Sf/PaXtjc/C+MNUDxd/DgQavufo9x3/dmu5kfSsr+fcTMr2JuIy9JX331lVV///33A/YYQHH06aefWnV3vPjf//5n1detW3fe+/RLxUoUAAAAAACAAJhEAQAAAAAACIBJFAAAAAAAgADIiRLGoUOHrHrFihX98syZM622a665xqr/5je/8ctuTLob396uXTu/7MarHzhwwKq7MW9mnhAzZ4EkxcTEWPXo6Ogcj3Wf9+TJk345tzwFZkyvm9fErZt5CszrKWXPs+DGB5v3dfM1uPH5Zp/c6+3e17wuSUlJVltsbKxy4sY2m/HKbvuUKVOstg8//NCqV61a1aqb18LdCz4uLs6qm/ko3GuG4oGx5ozzNdYURZdeeqlVZ6wBir8dO3ZY9fLly1t197uKOR66eU7cfCrmdxczD1K4+7rjOYCfl7vvvruwu4D/h5UoAAAAAAAAATCJAgAAAAAAEADhPGG420iaSyk/++wzq23ChAlWfcyYMX75vvvus9rcZdBVqlTxy+YWpFL27Sq3bNli1c2loO7ydXeJvbmVqMsNcXFDa0zuclTzWPfc3GXo5vO4y+/dx3WX8pthAe65uo9lXhf3XJo0aWLVExIS/HL16tUjPq65nN3dltY1depUvzx79myrzQx3kLJfN3MZr/s6uudjXie2HS2eGGvCy6+xpiiqU6eOVWesAYo/NwTHHQvddjNs0n2/ucea7zn3M2PFihVW/ZNPPgnYYwDAuWAlCgAAAAAAQABMogAAAAAAAATAJAoAAAAAAEAA5EQJw90K0oxRP3r0qNU2ffp0q27Gu48cOTLi85jbVV5wwQVWm5sXxN0u7/Dhw37Z3TrUrZvxtu4WmZG2IXVj6N37mnU3/tfNS2A+j3k9pezn6m6BbNbdeHw37t+su/2//PLLlZP09HSr7m4jaOYmcB/3/ffft+qPPPKIX05LS7Pa6tata9UPHjxo1c38CO65uVvPmteYPAXFE2PNGedrrCmK3NeVsQYo/txx1B3D3O3szXHVzYHiHmu+d90cSu7W5gCAgsFKFAAAAAAAgACYRAEAAAAAAAiASRQAAAAAAIAAyIkShpsfw4xXrVy5stXmxuebMepu7PiIESOseqlSpcKWJalhw4ZWPT4+3qrv3r3bL+/YscNq27t3r1U3Y/DdGHv3ec24XvfczHwHbt2N6XXzFJh5Ftxj3T64eSLMnChuPHDt2rWtekJCQtjnlLKf+549e/xyrVq1FIn5f2L58uVW24033mjVzXOvXr261ebmJXCvqXl+J0+ejNgnM6cEeQqKJ8aaM87XWFMUuTlQXIw1QPHj5jpy328//vijVTfznrhj2nfffWfVV69e7Zfdzwz3/WiO599//31u3QYAnCVWogAAAAAAAATAJAoAAAAAAEAAJTx3zXUOzO00f+5Kly5t1c1tR90l6e6x5jaT7labvXr1suqDBg3yy507d7ba8rJNp7sU1F3eaS7VjrQlqWQvq9+8ebPVtnLlSqv+ww8/+OWYmBirrV69elbdbHdDZ2rWrBmxbi7PP5ftTN2QATc0yORuF2puI/jMM89YbWa4g2RvIesu8S0ou3btKpTnzQ+MNWcw1vyEseYMxpr8RWgSUHwE/LlSJDHWAMVH0LGGlSgAAAAAAAABMIkCAAAAAAAQAJMoAAAAAAAAAZATJYxjx45ZdXMrTnO7XUkqV66cVTdzGphb2EnZYyLN2P2+fftabe3atbPql112mVU3Y+Fdbjy+mccgt5fbzANw4MABq83ddi81NdUvu7kH3G1SzXN1Y/fdLUndXBBmn91rGCnO1N2203wdJen48eN++b333rPaJk6caNXNLQbj4uKstujoaKtu5ghw2wpKcc5TwFhzBmPNTxhrzmCsyV/kKQCKD3KiACgI5EQBAAAAAADIR0yiAAAAAAAABMAkCgAAAAAAQADkRAkjUpx8Zmam1Xbq1Cmrbsb5uzkM3PseOXLEL5u5BCSpQoUKVr1u3bpWPSUlxS9fccUVVlvnzp2teqTXzu2T2f/cmHH+ZcuWtdrc+M9IuQbyk5ljIi0tzWpbtGiRVX/11Vf98ieffGK1xcbGWnUzP4Wbv8G9hlWqVMmxraAU5zwFjDVnMNb8hLHmDMaa/EWeAqD4ICcKgIJAThQAAAAAAIB8xCQKAAAAAABAAEyiAAAAAAAABEBOlABKly7tl918AmasviSVLPnTvJSba8CNiTRj6k+ePGm1uTkOXBkZGX759OnTVpsbY9+gQQO/XKtWLautU6dOVr1mzZp+OS4uzmpzz8fsY/ny5a0297+V2V/3GprXTMp+LQ4dOuSXd+zYYbVt2LDBqn/xxRd+edOmTVZbamqqVTdfj9zyLJh9ioqKstrcvBbu+RWG4pyngLHmDMaa8H1krPkJY825IU8BUHyQEwVAQSAnCgAAAAAAQD5iEgUAAAAAACAAwnnCcJd4m0vszXJu93W3JHUvtbm8z12i7i7TdpfRm9wl6q4TJ074ZTckwF0ebp6f+7ju+ZhLzd2QgEjL5t3HceXl3N3+m9fUvd5uPdK5utfJ7LMbauD+nzBDJ9zl+AWlOC+xZ6zJXs7tvow1P2GsKVjFeaxhiT1QfBDOA6AgEM4DAAAAAACQj5hEAQAAAAAACIBJFAAAAAAAgABK5X7IL48bc2/GrJsx/5JUpkwZq27Gzbsx6m7djG83Y9ul7LH6kbaydGPs3dhLs+72NyYmxqqbcWB52T4zt9wCZt2NNXOvi9vHSH3Ky3Vx8yOY93Wfs3Llylb96NGjfjkzMzPH55Ts/z9uzgvAxFiT+3O6GGt+wlgDAABQ8FiJAgAAAAAAEACTKAAAAAAAAAEwiQIAAAAAABAAOVHCiBSH7sbUR9r73Y2hP3LkiFU3cx6UL1/eanNzJbh1Mwbf7a8bj2/et2zZslbb4cOHrbp5fpHyKkj2ubt5Cdz4fLNP7uPmJfdAbnt3m49t5oyQpJo1a1r13bt3++WDBw9abRUrVrTq5rm7r7l7/TMyMvyy+7oCJsaa7GWJsSaEsQYAAKDoYSUKAAAAAABAAEyiAAAAAAAABFDCy23N8v9Tp06d892XIsO9JOaydLfN3S7U5C6vjrQNZqRtOnPj9inSfd0+uNttmsvb3aXu7vOYS83dZefu85h1ty23LUsjhTFE+u/r3s8NJ4iOjvbL7mvlhgiY4RBumIK7lN/cptYNJygou3btKpTnzQ+MNeHbGGuyl8M9D2NNwSrOY02k1xtA0RLw50qRxFgDFB9BxxpWogAAAAAAAATAJAoAAAAAAEAATKIAAAAAAAAEQE4U4GeoOOcpYKwBio/iPNaQpwAoPsiJAqAgkBMFAAAAAAAgHzGJAgAAAAAAEACTKAAAAAAAAAEwiQIAAAAAABAAkygAAAAAAAABMIkCAAAAAAAQAJMoAAAAAAAAATCJAgAAAAAAEACTKAAAAAAAAAEwiQIAAAAAABAAkygAAAAAAAABMIkCAAAAAAAQAJMoAAAAAAAAATCJAgAAAAAAEACTKAAAAAAAAAEwiQIAAAAAABAAkygAAAAAAAABMIkCAAAAAAAQAJMoAAAAAAAAATCJAgAAAAAAEACTKAAAAAAAAAEwiQIAAAAAABAAkygAAAAAAAABMIkCAAAAAAAQAJMoAAAAAAAAATCJAgAAAAAAEACTKAAAAAAAAAEwiQIAAAAAABAAkygAAAAAAAABMIkCAAAAAAAQAJMoAAAAAAAAATCJAgAAAAAAEACTKAAAAAAAAAEwiQIAAAAAABAAkygAAAAAAAABMIkCAAAAAAAQAJMoAAAAAAAAATCJAgAAAAAAEACTKAAAAAAAAAEwiQIAAAAAABAAkygAAAAAAAABMIkCAAAAAAAQAJMoAAAAAAAAATCJAgAAAAAAEACTKAAAAAAAAAEwiQIAAAAAABAAkygAAAAAAAABMIkCAAAAAAAQAJMoAAAAAAAAATCJAgAAAAAAEACTKAAAAAAAAAEwiQIAAAAAABAAkygAAAAAAAABMIkCAAAAAAAQAJMoAAAAAAAAATCJAgAAAAAAEACTKAAAAAAAAAEwiQIAAAAAABBACc/zvMLuBAAAAAAAQFHHShQAAAAAAIAAmEQBAAAAAAAIgEkUAAAAAACAAJhEAQAAAAAACIBJFAAAAAAAgACYRAEAAAAAAAiASRQAAAAAAIAAmEQBAAAAAAAIgEkUAAAAAACAAP5/EfKJMy1ItjEAAAAASUVORK5CYII=\n", "text/plain": ["<Figure size 1400x800 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Get a naive anomaly map using the difference\n", "difference_map = torch.abs(image_healed[0, 0, ...] - image_corrupted[0, ...])\n", "\n", "# Further mask with the healing mask\n", "resizer = torch.nn.Upsample(size=(64, 64), mode=\"nearest\")\n", "mask_upsampled = resizer(mask[None, None, ...].float()).int()\n", "\n", "fig, ax = plt.subplots(1, 4, figsize=(14, 8))\n", "plt.subplot(1, 4, 1)\n", "plt.imshow(image_clean[0, ...], cmap=\"gray\")\n", "plt.axis(\"off\")\n", "plt.title(\"Clean image\")\n", "image_corrupted = image_clean.clone()\n", "image_corrupted[0, 25:40, 40:50] = 1\n", "plt.subplot(1, 4, 2)\n", "plt.imshow(image_corrupted[0, ...], cmap=\"gray\")\n", "plt.axis(\"off\")\n", "plt.title(\"Corrupted image\")\n", "plt.subplot(1, 4, 3)\n", "plt.imshow(image_corrupted[0, ...] - image_clean[0, ...], cmap=\"gray\")\n", "plt.axis(\"off\")\n", "plt.title(\"Ground-Truth anomaly mask\")\n", "plt.subplot(1, 4, 4)\n", "plt.imshow(mask_upsampled[0, 0, ...] * difference_map, cmap=\"gray\")\n", "plt.axis(\"off\")\n", "plt.title(\"Predicted anomaly mask\")\n", "plt.show()"]}], "metadata": {"jupytext": {"formats": "py:percent,ipynb"}, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.6"}}, "nbformat": 4, "nbformat_minor": 5}