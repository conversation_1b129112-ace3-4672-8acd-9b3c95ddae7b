# Full requirements for developments
-r requirements-min.txt
pytorch-ignite==0.4.10
gdown>=4.4.0
scipy
itk>=5.2
nibabel
pillow!=8.3.0  # https://github.com/python-pillow/Pillow/issues/5571
tensorboard>=2.6  # https://github.com/Project-MONAI/MONAI/issues/5776
scikit-image>=0.19.0
tqdm>=4.47.0
lmdb
flake8>=3.8.1
flake8-bugbear
flake8-comprehensions
flake8-executable
pylint!=2.13  # https://github.com/PyCQA/pylint/issues/5969
mccabe
pep8-naming
pycodestyle
pyflakes
black
isort
pytype>=2020.6.1; platform_system != "Windows"
types-pkg_resources
mypy>=0.790
ninja
torchvision
psutil
Sphinx==3.5.3
recommonmark==0.6.0
sphinx-autodoc-typehints==1.11.1
sphinx-rtd-theme==0.5.2
cucim==22.8.1; platform_system == "Linux"
openslide-python==1.1.2
imagecodecs; platform_system == "Linux" or platform_system == "Darwin"
tifffile; platform_system == "Linux" or platform_system == "Darwin"
pandas
requests
einops
transformers<4.22  # https://github.com/Project-MONAI/MONAI/issues/5157
mlflow
matplotlib!=3.5.0
tensorboardX
types-PyYAML
pyyaml
fire
jsonschema
pynrrd
pre-commit
pydicom
h5py
nni
optuna
git+https://github.com/Project-MONAI/MetricsReloaded@monai-support#egg=MetricsReloaded
lpips==0.1.4
xformers==0.0.16
