{"cells": [{"cell_type": "code", "execution_count": 1, "id": "bc11fdc9", "metadata": {}, "outputs": [], "source": ["# Copyright (c) MONAI Consortium\n", "# Licensed under the Apache License, Version 2.0 (the \"License\");\n", "# you may not use this file except in compliance with the License.\n", "# You may obtain a copy of the License at\n", "#     http://www.apache.org/licenses/LICENSE-2.0\n", "# Unless required by applicable law or agreed to in writing, software\n", "# distributed under the License is distributed on an \"AS IS\" BASIS,\n", "# WITHOUT WAR<PERSON><PERSON>IES OR CONDITIONS OF ANY KIND, either express or implied.\n", "# See the License for the specific language governing permissions and\n", "# limitations under the License."]}, {"cell_type": "markdown", "id": "95c08725", "metadata": {}, "source": ["# Super-resolution using Stable Diffusion v2 Upscalers\n", "\n", "Tutorial to illustrate the super-resolution task on medical images using Latent Diffusion Models (LDMs) [1]. For that, we will use an autoencoder to obtain a latent representation of the high-resolution images. Then, we train a diffusion model to infer this latent representation when conditioned on a low-resolution image.\n", "\n", "To improve the performance of our models, we will use a method called \"noise conditioning augmentation\" (introduced in [2] and used in Stable Diffusion v2.0 and Imagen Video [3]). During the training, we add noise to the low-resolution images using a random signal-to-noise ratio, and we condition the diffusion models on the amount of noise added. At sampling time, we use a fixed signal-to-noise ratio, representing a small amount of augmentation that aids in removing artefacts in the samples.\n", "\n", "\n", "[1] - <PERSON><PERSON><PERSON> et al. \"High-Resolution Image Synthesis with Latent Diffusion Models\" https://arxiv.org/abs/2112.10752\n", "\n", "[2] - <PERSON> et al. \"Cascaded diffusion models for high fidelity image generation\" https://arxiv.org/abs/2106.15282\n", "\n", "[3] - <PERSON> et al. \"High Definition Video Generation with Diffusion Models\" https://arxiv.org/abs/2210.02303"]}, {"cell_type": "markdown", "id": "b839bf2d", "metadata": {}, "source": ["## Set up environment using Colab\n"]}, {"cell_type": "code", "execution_count": 2, "id": "77f7e633", "metadata": {}, "outputs": [], "source": ["!python -c \"import monai\" || pip install -q \"monai-weekly[tqdm]\"\n", "!python -c \"import pytorch_lightning\" || pip install pytorch-lightning\n", "!python -c \"import matplotlib\" || pip install -q matplotlib\n", "%matplotlib inline"]}, {"cell_type": "markdown", "id": "214066de", "metadata": {}, "source": ["## Set up imports"]}, {"cell_type": "code", "execution_count": 3, "id": "de71fe08", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2023-05-12 17:22:32,886 - WARNING[XFORMERS]: xFormers can't load C++/CUDA extensions. xFormers was built for:\n", "    PyTorch 1.13.1+cu117 with CUDA 1107 (you have 1.12.1)\n", "    Python  3.8.16 (you have 3.8.16)\n", "  Please reinstall xformers (see https://github.com/facebookresearch/xformers#installing-xformers)\n", "  Memory-efficient attention, SwiGLU, sparse and more won't be available.\n", "  Set XFORMERS_MORE_DETAILS=1 for more details\n", "2023-05-12 17:22:35,069 - Created a temporary directory at /tmp/tmph4_v9gin\n", "2023-05-12 17:22:35,071 - Writing /tmp/tmph4_v9gin/_remote_module_non_scriptable.py\n", "MONAI version: 1.2.dev2304\n", "Numpy version: 1.23.5\n", "Pytorch version: 1.12.1\n", "MONAI flags: HAS_EXT = False, USE_COMPILED = False, USE_META_DICT = False\n", "MONAI rev id: 9a57be5aab9f2c2a134768c0c146399150e247a0\n", "MONAI __file__: /home/<USER>/miniconda3/envs/monai_generative/lib/python3.8/site-packages/monai_weekly-1.2.dev2304-py3.8.egg/monai/__init__.py\n", "\n", "Optional dependencies:\n", "Pytorch Ignite version: 0.4.10\n", "ITK version: 5.3.0\n", "Nibabel version: 5.1.0\n", "scikit-image version: 0.20.0\n", "Pillow version: 9.4.0\n", "Tensorboard version: 2.12.1\n", "gdown version: 4.7.1\n", "TorchVision version: 0.13.1\n", "tqdm version: 4.65.0\n", "lmdb version: 1.4.0\n", "psutil version: 5.9.4\n", "pandas version: 2.0.0\n", "einops version: 0.6.0\n", "transformers version: 4.21.3\n", "mlflow version: 2.2.2\n", "pynrrd version: 1.0.0\n", "\n", "For details about installing the optional dependencies, please visit:\n", "    https://docs.monai.io/en/latest/installation.html#installing-the-recommended-dependencies\n", "\n"]}], "source": ["import os\n", "import shutil\n", "import tempfile\n", "from pathlib import Path\n", "\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "import torch\n", "import torch.nn.functional as F\n", "from monai import transforms\n", "from monai.apps import MedNISTDataset\n", "from monai.config import print_config\n", "from monai.data import CacheDataset, ThreadDataLoader\n", "from monai.utils import first, set_determinism\n", "from torch.cuda.amp import GradScaler, autocast\n", "from torch import nn\n", "from tqdm.notebook import tqdm\n", "\n", "from generative.losses import PatchAdversarialLoss, PerceptualLoss\n", "from generative.networks.nets import AutoencoderKL, DiffusionModelUNet, PatchDiscriminator\n", "from generative.networks.schedulers import DDPMScheduler\n", "\n", "import pytorch_lightning as pl\n", "from pytorch_lightning.callbacks.model_checkpoint import ModelCheckpoint\n", "\n", "print_config()"]}, {"cell_type": "code", "execution_count": 4, "id": "9f0a17bc", "metadata": {}, "outputs": [], "source": ["# for reproducibility purposes set a seed\n", "set_determinism(42)"]}, {"cell_type": "markdown", "id": "c0dde922", "metadata": {}, "source": ["## Setup a data directory and download dataset\n", "Specify a MONAI_DATA_DIRECTORY variable, where the data will be downloaded. If not specified a temporary directory will be used."]}, {"cell_type": "code", "execution_count": 5, "id": "ded618a7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/tmp/tmphf3tvpfi\n"]}], "source": ["directory = os.environ.get(\"MONAI_DATA_DIRECTORY\")\n", "root_dir = tempfile.mkdtemp() if directory is None else directory\n", "print(root_dir)"]}, {"cell_type": "code", "execution_count": 6, "id": "298d964a", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["MedNIST.tar.gz: 59.0MB [00:01, 36.4MB/s]                                                                                                                               "]}, {"name": "stdout", "output_type": "stream", "text": ["2023-05-12 17:22:36,915 - INFO - Downloaded: /tmp/tmphf3tvpfi/MedNIST.tar.gz\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["2023-05-12 17:22:37,020 - INFO - Verified 'MedNIST.tar.gz', md5: 0bc7306e7427e00ad1c5526a6677552d.\n", "2023-05-12 17:22:37,022 - INFO - Writing into directory: /tmp/tmphf3tvpfi.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Loading dataset: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████| 47164/47164 [00:35<00:00, 1323.43it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["2023-05-12 17:23:21,457 - INFO - Verified 'MedNIST.tar.gz', md5: 0bc7306e7427e00ad1c5526a6677552d.\n", "2023-05-12 17:23:21,459 - INFO - File exists: /tmp/tmphf3tvpfi/MedNIST.tar.gz, skipped downloading.\n", "2023-05-12 17:23:21,460 - INFO - Non-empty folder exists in /tmp/tmphf3tvpfi/MedNIST, skipped extracting.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "Loading dataset: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████| 5895/5895 [00:04<00:00, 1319.58it/s]\n"]}], "source": ["train_data = MedNISTDataset(root_dir=root_dir, section=\"training\",\n", "                                download=True, seed=0)\n", "train_datalist = [{\"image\": item[\"image\"]} for item in train_data.data if item[\"class_name\"] == \"HeadCT\"]\n", "val_data = MedNISTDataset(root_dir=root_dir, section=\"validation\",\n", "                              download=True, seed=0)\n", "val_datalist = [{\"image\": item[\"image\"]} for item in val_data.data if item[\"class_name\"] == \"HeadCT\"]"]}, {"cell_type": "markdown", "id": "46bafb78", "metadata": {}, "source": ["## Setup utils functions"]}, {"cell_type": "code", "execution_count": 7, "id": "4f8eff03", "metadata": {}, "outputs": [], "source": ["def get_train_transforms():\n", "    image_size = 64\n", "    train_transforms = transforms.Compose(\n", "    [\n", "        transforms.LoadImaged(keys=[\"image\"]),\n", "        transforms.EnsureChannelFirstd(keys=[\"image\"]),\n", "        transforms.ScaleIntensityRanged(keys=[\"image\"], a_min=0.0, a_max=255.0, b_min=0.0,\n", "                                        b_max=1.0, clip=True),\n", "        transforms.RandA<PERSON>ed(\n", "            keys=[\"image\"],\n", "            rotate_range=[(-np.pi / 36, np.pi / 36), (-np.pi / 36, np.pi / 36)],\n", "            translate_range=[(-1, 1), (-1, 1)],\n", "            scale_range=[(-0.05, 0.05), (-0.05, 0.05)],\n", "            spatial_size=[image_size, image_size],\n", "            padding_mode=\"zeros\",\n", "            prob=0.5,\n", "        ),\n", "        transforms.CopyItemsd(keys=[\"image\"], times=1, names=[\"low_res_image\"]),\n", "        transforms.Resized(keys=[\"low_res_image\"], spatial_size=(16, 16)),\n", "    ]\n", "    )\n", "    return train_transforms\n", "\n", "def get_val_transforms():\n", "    val_transforms = transforms.Compose(\n", "        [\n", "            transforms.LoadImaged(keys=[\"image\"]),\n", "            transforms.EnsureChannelFirstd(keys=[\"image\"]),\n", "            transforms.ScaleIntensityRanged(keys=[\"image\"], a_min=0.0, a_max=255.0, b_min=0.0, \n", "                                            b_max=1.0, clip=True),\n", "            transforms.CopyItemsd(keys=[\"image\"], times=1, names=[\"low_res_image\"]),\n", "            transforms.Resized(keys=[\"low_res_image\"], spatial_size=(16, 16)),\n", "        ]\n", "    )\n", "    return val_transforms\n", "\n", "    \n", "def get_datasets():\n", "    train_transforms = get_train_transforms()\n", "    val_transforms = get_val_transforms()\n", "    train_ds = CacheDataset(data=train_datalist[:320], transform=train_transforms)\n", "    val_ds = CacheDataset(data=val_datalist[:32], transform=val_transforms)\n", "    return train_ds, val_ds\n", "\n"]}, {"cell_type": "markdown", "id": "d80e045b", "metadata": {}, "source": ["## Define the LightningModule for AutoEncoder (transforms, network, loaders, etc)\n", "The LightningModule contains a refactoring of your training code. The following module is a reformating of the code in 2d_stable_diffusion_v2_super_resolution.\n"]}, {"cell_type": "code", "execution_count": 8, "id": "d5d1caff", "metadata": {"scrolled": false}, "outputs": [], "source": ["class AutoEncoder(pl.LightningModule):\n", "    def __init__(self):\n", "        super().__init__()\n", "        self.data_dir = root_dir\n", "        self.autoencoderkl = AutoencoderKL(spatial_dims=2,\n", "                                           in_channels=1,\n", "                                           out_channels=1,\n", "                                           num_channels=(256, 512, 512),\n", "                                           latent_channels=3,\n", "                                           num_res_blocks=2,\n", "                                           norm_num_groups=32,\n", "                                           attention_levels=(False, <PERSON>alse, True))\n", "        self.discriminator = PatchDiscriminator(spatial_dims=2, in_channels=1,\n", "                                                num_layers_d=3, num_channels=64)\n", "        self.perceptual_loss = PerceptualLoss(spatial_dims=2, network_type=\"alex\")\n", "        self.perceptual_weight = 0.002\n", "        self.autoencoder_warm_up_n_epochs = 10\n", "        self.automatic_optimization = False\n", "        self.adv_loss = PatchAdversarialLoss(criterion=\"least_squares\")\n", "        self.adv_weight = 0.005\n", "        self.kl_weight = 1e-6\n", "        \n", "    def forward(self, z):\n", "        return self.autoencoderkl(z)\n", "\n", "    def prepare_data(self):\n", "        self.train_ds, self.val_ds = get_datasets()\n", "                \n", "    def train_dataloader(self):\n", "        return ThreadDataLoader(self.train_ds, batch_size=16, shuffle=True,\n", "                          num_workers=4, persistent_workers=True)\n", "        \n", "    def val_dataloader(self):\n", "        return ThreadDataLoader(self.val_ds, batch_size=16, shuffle=False,\n", "                          num_workers=4)\n", "                          \n", "    def _compute_loss_generator(self, images, reconstruction, z_mu, z_sigma):\n", "        recons_loss = F.l1_loss(reconstruction.float(), images.float())\n", "        p_loss = self.perceptual_loss(reconstruction.float(), images.float())\n", "        kl_loss = 0.5 * torch.sum(z_mu.pow(2) + z_sigma.pow(2) - torch.log(z_sigma.pow(2)) - 1, dim=[1, 2, 3])\n", "        kl_loss = torch.sum(kl_loss) / kl_loss.shape[0]\n", "        loss_g = recons_loss + (self.kl_weight * kl_loss) + (self.perceptual_weight * p_loss)\n", "        return loss_g,recons_loss\n", "    \n", "    def _compute_loss_discriminator(self, images, reconstruction):\n", "        logits_fake = self.discriminator(reconstruction.contiguous().detach())[-1]\n", "        loss_d_fake = self.adv_loss(logits_fake, target_is_real=False, for_discriminator=True)\n", "        logits_real = self.discriminator(images.contiguous().detach())[-1]\n", "        loss_d_real = self.adv_loss(logits_real, target_is_real=True, for_discriminator=True)\n", "        discriminator_loss = (loss_d_fake + loss_d_real) * 0.5\n", "        loss_d = self.adv_weight * discriminator_loss\n", "        return loss_d, discriminator_loss\n", "        \n", "    def training_step(self, batch, batch_idx):\n", "        optimizer_g, optimizer_d = self.optimizers()\n", "        images = batch[\"image\"]\n", "        reconstruction, z_mu, z_sigma = self.forward(images)\n", "        loss_g, recons_loss = self._compute_loss_generator(images, reconstruction, z_mu, z_sigma)\n", "        self.log(\"recons_loss\", recons_loss, batch_size=16, prog_bar=True)\n", "\n", "        if self.current_epoch > self.autoencoder_warm_up_n_epochs:\n", "            logits_fake = self.discriminator(reconstruction.contiguous().float())[-1]\n", "            generator_loss = self.adv_loss(logits_fake, target_is_real=True, for_discriminator=False)\n", "            loss_g += self.adv_weight * generator_loss\n", "            self.log(\"gen_loss\", generator_loss, batch_size=16, prog_bar=True)\n", "            \n", "                          \n", "\n", "        self.log(\"loss_g\", loss_g, batch_size=16, prog_bar=True)\n", "        self.manual_backward(loss_g)\n", "        optimizer_g.step()\n", "        optimizer_g.zero_grad()\n", "        self.untoggle_optimizer(optimizer_g)\n", "\n", "        if self.current_epoch > self.autoencoder_warm_up_n_epochs:\n", "            loss_d, discriminator_loss = self._compute_loss_discriminator(images, reconstruction)\n", "            self.log(\"disc_loss\", loss_d, batch_size=16, prog_bar=True)\n", "            self.log(\"train_loss_d\", loss_d, batch_size=16, prog_bar=True)\n", "            self.manual_backward(loss_d)\n", "            optimizer_d.step()\n", "            optimizer_d.zero_grad()\n", "            self.untoggle_optimizer(optimizer_d)\n", "\n", "                          \n", "                                    \n", "    def validation_step(self, batch, batch_idx):\n", "        images = batch[\"image\"]\n", "        reconstruction, z_mu, z_sigma = self.autoencoderkl(images)\n", "        recons_loss = F.l1_loss(images.float(), reconstruction.float())\n", "        self.log(\"val_loss_d\", recons_loss, batch_size=1, prog_bar=True)\n", "        self.images = images\n", "        self.reconstruction = reconstruction\n", "\n", "\n", "    def on_validation_epoch_end(self):\n", "        # ploting reconstruction\n", "        plt.figure(figsize=(2, 2))\n", "        plt.imshow(torch.cat([self.images[0, 0].cpu(), \n", "                              self.reconstruction[0, 0].cpu()],\n", "                             dim=1), vmin=0, vmax=1, cmap=\"gray\")\n", "        plt.tight_layout()\n", "        plt.axis(\"off\")\n", "        plt.show()\n", "        \n", "\n", "    def configure_optimizers(self):\n", "        optimizer_g = torch.optim.Adam(self.autoencoderkl.parameters(), lr=5e-5)\n", "        optimizer_d = torch.optim.Adam(self.discriminator.parameters(), lr=1e-4)\n", "        return [optimizer_g, optimizer_d], []\n", "                          \n"]}, {"cell_type": "markdown", "id": "c16de505", "metadata": {}, "source": ["## Train Autoencoder"]}, {"cell_type": "code", "execution_count": 9, "id": "9d903aaa", "metadata": {"scrolled": true}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["The parameter 'pretrained' is deprecated since 0.13 and will be removed in 0.15, please use 'weights' instead.\n", "Arguments other than a weight enum or `None` for 'weights' are deprecated since 0.13 and will be removed in 0.15. The current behavior is equivalent to passing `weights=AlexNet_Weights.IMAGENET1K_V1`. You can also use `weights=AlexNet_Weights.DEFAULT` to get the most up-to-date weights.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["2023-05-12 17:23:29,608 - GPU available: True (cuda), used: True\n", "2023-05-12 17:23:29,609 - TPU available: False, using: 0 TPU cores\n", "2023-05-12 17:23:29,610 - IPU available: False, using: 0 IPUs\n", "2023-05-12 17:23:29,611 - HPU available: False, using: 0 HPUs\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Loading dataset: 100%|██████████████████████████████████████████████████████████████████████████████████████████████████████████████| 320/320 [00:00<00:00, 858.75it/s]\n", "Loading dataset: 100%|████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 32/32 [00:00<00:00, 284.05it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["2023-05-12 17:23:30,118 - Missing logger folder: /tmp/tmphf3tvpfi/lightning_logs\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "Checkpoint directory /tmp/tmphf3tvpfi exists and is not empty.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["2023-05-12 17:23:31,637 - LOCAL_RANK: 0 - CUDA_VISIBLE_DEVICES: [0,1]\n", "2023-05-12 17:23:31,652 - \n", "  | Name            | Type                 | Params\n", "---------------------------------------------------------\n", "0 | autoencoderkl   | AutoencoderKL        | 75.1 M\n", "1 | discriminator   | PatchDiscriminator   | 2.8 M \n", "2 | perceptual_loss | PerceptualLoss       | 2.5 M \n", "3 | adv_loss        | PatchAdversarialLoss | 0     \n", "---------------------------------------------------------\n", "77.8 M    Trainable params\n", "2.5 M     Non-trainable params\n", "80.3 M    Total params\n", "321.225   Total estimated model params size (MB)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["The number of training batches (20) is smaller than the logging interval Trainer(log_every_n_steps=50). Set a lower value for log_every_n_steps if you want to see logs for the training epoch.\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "e345b86c597145adad868d37e0416041", "version_major": 2, "version_minor": 0}, "text/plain": ["Training: 0it [00:00, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "", "version_major": 2, "version_minor": 0}, "text/plain": ["Validation: 0it [00:00, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 200x200 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "", "version_major": 2, "version_minor": 0}, "text/plain": ["Validation: 0it [00:00, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 200x200 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "", "version_major": 2, "version_minor": 0}, "text/plain": ["Validation: 0it [00:00, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 200x200 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "", "version_major": 2, "version_minor": 0}, "text/plain": ["Validation: 0it [00:00, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 200x200 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "", "version_major": 2, "version_minor": 0}, "text/plain": ["Validation: 0it [00:00, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 200x200 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "", "version_major": 2, "version_minor": 0}, "text/plain": ["Validation: 0it [00:00, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 200x200 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "", "version_major": 2, "version_minor": 0}, "text/plain": ["Validation: 0it [00:00, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 200x200 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["2023-05-12 17:41:32,347 - `Trainer.fit` stopped: `max_epochs=75` reached.\n"]}], "source": ["n_epochs = 75 \n", "val_interval = 10\n", "\n", "                          \n", "# initialise the LightningModule\n", "ae_net = AutoEncoder()\n", "\n", "# set up checkpoints\n", "\n", "checkpoint_callback = ModelCheckpoint(dirpath=root_dir, filename=\"best_metric_model\")\n", "\n", "                         \n", "# initialise Lightning's trainer.\n", "trainer = pl.Trainer(devices=1,\n", "                     max_epochs=n_epochs,\n", "                     check_val_every_n_epoch=val_interval,\n", "                     num_sanity_val_steps=0,\n", "                     callbacks=checkpoint_callback,\n", "                     default_root_dir=root_dir)\n", "\n", "# train\n", "trainer.fit(ae_net)"]}, {"cell_type": "markdown", "id": "c7108b87", "metadata": {}, "source": ["## Rescaling factor\n", "\n", "As mentioned in <PERSON><PERSON><PERSON> et al. [1] Section 4.3.2 and D.1, the signal-to-noise ratio (induced by the scale of the latent space) became crucial in image-to-image translation models (such as the ones used for super-resolution). For this reason, we will compute the component-wise standard deviation to be used as scaling factor."]}, {"cell_type": "code", "execution_count": 10, "id": "ccb6ba9f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Scaling factor set to 0.5302040576934814\n"]}], "source": ["def get_scale_factor():\n", "    ae_net.eval()\n", "    device = torch.device(\"cuda:0\")\n", "    ae_net.to(device)\n", "\n", "    train_loader = ae_net.train_dataloader()\n", "    check_data = first(train_loader)\n", "    z = ae_net.autoencoderkl.encode_stage_2_inputs(check_data[\"image\"].to(ae_net.device))\n", "    print(f\"Scaling factor set to {1/torch.std(z)}\")\n", "    scale_factor = 1 / torch.std(z)\n", "    return scale_factor\n", "\n", "scale_factor = get_scale_factor()"]}, {"cell_type": "markdown", "id": "3baa2b0f", "metadata": {}, "source": ["## Define the LightningModule for DiffusionModelUnet (transforms, network, loaders, etc)\n", "The LightningModule contains a refactoring of your training code. The following module is a reformating of the code in 2d_stable_diffusion_v2_super_resolution."]}, {"cell_type": "code", "execution_count": 11, "id": "731034ec", "metadata": {"lines_to_next_cell": 2}, "outputs": [], "source": ["class DiffusionUNET(pl.LightningModule):\n", "    def __init__(self):\n", "        super().__init__()\n", "        self.data_dir = root_dir\n", "        self.unet = DiffusionModelUNet(\n", "                                spatial_dims=2,\n", "                                in_channels=4,\n", "                                out_channels=3,\n", "                                num_res_blocks=2,\n", "                                num_channels=(256, 256, 512, 1024),\n", "                                attention_levels=(False, False, True, True),\n", "                                num_head_channels=(0, 0, 64, 64),\n", "                            )\n", "        self.max_noise_level = 350\n", "        self.scheduler = DDPMScheduler(num_train_timesteps=1000, \n", "                          beta_schedule=\"linear\",\n", "                          beta_start=0.0015,\n", "                          beta_end=0.0195)\n", "        self.z = ae_net.autoencoderkl.eval()\n", "\n", "\n", "    def forward(self, x, timesteps, low_res_timesteps):\n", "        return self.unet(x=x, \n", "                         timesteps=timesteps,\n", "                         class_labels=low_res_timesteps)\n", "    \n", "           \n", "    def prepare_data(self):\n", "        self.train_ds, self.val_ds = get_datasets()\n", "        \n", "    def train_dataloader(self):\n", "        return ThreadDataLoader(self.train_ds, batch_size=16, shuffle=True,\n", "                          num_workers=4, persistent_workers=True)\n", "        \n", "    def val_dataloader(self):\n", "        return ThreadDataLoader(self.val_ds, batch_size=16, shuffle=False,\n", "                          num_workers=4)\n", "    \n", "    def _calculate_loss(self, batch, batch_idx, plt_image=False):\n", "        images = batch[\"image\"]\n", "        low_res_image = batch[\"low_res_image\"]  \n", "        with autocast(enabled=True):\n", "            with torch.no_grad():\n", "                latent = self.z.encode_stage_2_inputs(images) * scale_factor\n", "        \n", "            # Noise augmentation\n", "            noise = torch.randn_like(latent)\n", "            low_res_noise = torch.randn_like(low_res_image)\n", "            timesteps = torch.randint(0, self.scheduler.num_train_timesteps, (latent.shape[0],),\n", "                                      device=latent.device).long()\n", "            low_res_timesteps = torch.randint(\n", "                0, self.max_noise_level, (low_res_image.shape[0],), device=latent.device\n", "            ).long()\n", "\n", "            noisy_latent = self.scheduler.add_noise(original_samples=latent, \n", "                                               noise=noise, timesteps=timesteps)\n", "            noisy_low_res_image = self.scheduler.add_noise(\n", "                original_samples=low_res_image, noise=low_res_noise, \n", "                timesteps=low_res_timesteps\n", "            )\n", "\n", "            latent_model_input = torch.cat([noisy_latent, noisy_low_res_image], dim=1)\n", "\n", "            noise_pred = self.forward(latent_model_input, timesteps, low_res_timesteps)\n", "            loss = F.mse_loss(noise_pred.float(), noise.float())\n", "        \n", "        if plt_image:\n", "            # Sampling image during training\n", "            sampling_image = low_res_image[0].unsqueeze(0)\n", "            latents = torch.randn((1, 3, 16, 16)).to(sampling_image.device)\n", "            low_res_noise = torch.randn((1, 1, 16, 16)).to(sampling_image.device)\n", "            noise_level = 20\n", "            noise_level = torch.Tensor((noise_level,)).long().to(sampling_image.device)\n", "            \n", "            noisy_low_res_image = self.scheduler.add_noise(\n", "                original_samples=sampling_image,\n", "                noise=low_res_noise,\n", "                timesteps=noise_level,\n", "            )\n", "            self.scheduler.set_timesteps(num_inference_steps=1000)\n", "            for t in tqdm(self.scheduler.timesteps, ncols=110):\n", "                with autocast(enabled=True):\n", "                    with torch.no_grad():\n", "                        latent_model_input = torch.cat([latents, noisy_low_res_image], dim=1)\n", "                        noise_pred = self.forward(latent_model_input, \n", "                                                  torch.Tensor((t,)).to(sampling_image.device)\n", "                                                  , noise_level)\n", "                    latents, _ = self.scheduler.step(noise_pred, t, latents)\n", "            with torch.no_grad():\n", "                decoded = self.z.decode_stage_2_outputs(latents / scale_factor)\n", "            low_res_bicubic = nn.functional.interpolate(sampling_image, (64, 64), mode=\"bicubic\")\n", "            # plot images\n", "            \n", "            self.images = images\n", "            self.low_res_bicubic = low_res_bicubic\n", "            self.decoded = decoded\n", "            \n", "        return loss\n", "    \n", "    def _plot_image(self, images, low_res_bicubic, decoded):\n", "        plt.figure(figsize=(2, 2))\n", "        plt.style.use(\"default\")\n", "        plt.imshow(\n", "            torch.cat([images[0, 0].cpu(), low_res_bicubic[0, 0].cpu(), decoded[0, 0].cpu()], dim=1),\n", "            vmin=0,\n", "            vmax=1,\n", "            cmap=\"gray\",\n", "        )\n", "        plt.tight_layout()\n", "        plt.axis(\"off\")\n", "        plt.show()\n", "\n", "    def training_step(self, batch, batch_idx):\n", "        loss = self._calculate_loss(batch, batch_idx)\n", "        self.log(\"train_loss\", loss, batch_size=16, prog_bar=True)\n", "        return loss\n", "        \n", "    def validation_step(self, batch, batch_idx):\n", "        loss = self._calculate_loss(batch, batch_idx, plt_image=True)\n", "        self.log(\"val_loss\", loss, batch_size=16, prog_bar=True)\n", "        return loss\n", "    \n", "    def on_validation_epoch_end(self):\n", "        self._plot_image(self.images, self.low_res_bicubic, self.decoded)\n", "\n", "    def configure_optimizers(self):\n", "        optimizer = torch.optim.Adam(self.unet.parameters(), lr=5e-5)\n", "        return optimizer"]}, {"cell_type": "markdown", "id": "b386a0c2", "metadata": {}, "source": ["## Train Diffusion Model\n", "\n", "In order to train the diffusion model to perform super-resolution, we will need to concatenate the latent representation of the high-resolution with the low-resolution image. For this, we create a Diffusion model with `in_channels=4`. Since only the outputted latent representation is interesting, we set `out_channels=3`.\n", "\n", "As mentioned, we will use the conditioned augmentation (introduced in [2] section 3 and used on Stable Diffusion Upscalers and Imagen Video [3] Section 2.5) as it has been shown critical for cascaded diffusion models, as well for super-resolution tasks. For this, we apply Gaussian noise augmentation to the low-resolution images. We will use a scheduler low_res_scheduler to add this noise, with the t step defining the signal-to-noise ratio and use the t value to condition the diffusion model (inputted using class_labels argument)."]}, {"cell_type": "code", "execution_count": 12, "id": "936bbb9c", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2023-05-12 17:41:38,503 - GPU available: True (cuda), used: True\n", "2023-05-12 17:41:38,503 - TPU available: False, using: 0 TPU cores\n", "2023-05-12 17:41:38,504 - IPU available: False, using: 0 IPUs\n", "2023-05-12 17:41:38,504 - HPU available: False, using: 0 HPUs\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Loading dataset: 100%|██████████████████████████████████████████████████████████████████████████████████████████████████████████████| 320/320 [00:00<00:00, 773.44it/s]\n", "Loading dataset: 100%|████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 32/32 [00:00<00:00, 495.25it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["2023-05-12 17:41:39,194 - LOCAL_RANK: 0 - CUDA_VISIBLE_DEVICES: [0,1]\n", "2023-05-12 17:41:39,226 - \n", "  | Name      | Type               | Params\n", "-------------------------------------------------\n", "0 | unet      | DiffusionModelUNet | 266 M \n", "1 | scheduler | DDPMScheduler      | 0     \n", "2 | z         | AutoencoderKL      | 75.1 M\n", "-------------------------------------------------\n", "342 M     Trainable params\n", "0         Non-trainable params\n", "342 M     Total params\n", "1,368.189 Total estimated model params size (MB)\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "71e4d5d2e391477aac58bfb05ecefb85", "version_major": 2, "version_minor": 0}, "text/plain": ["Training: 0it [00:00, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "", "version_major": 2, "version_minor": 0}, "text/plain": ["Validation: 0it [00:00, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "be43a3c9e09a4405a067d9fc887e151a", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|                                                                                | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "bc6fe02c06e848a48a3cff1662788638", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|                                                                                | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 200x200 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "", "version_major": 2, "version_minor": 0}, "text/plain": ["Validation: 0it [00:00, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "5aa3448de1094d0f82f2808b8131bb9e", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|                                                                                | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "d3db0817d9674e5f9fb7027521b9e032", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|                                                                                | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 200x200 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "", "version_major": 2, "version_minor": 0}, "text/plain": ["Validation: 0it [00:00, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "9b9fad73a311409082600b576378c2b7", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|                                                                                | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "4fee43bf923247019574f67cc2aa7c0d", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|                                                                                | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAKIAAABDCAYAAAAf6t48AAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjcuMSwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy/bCgiHAAAACXBIWXMAAA9hAAAPYQGoP6dpAAAccElEQVR4nO1dW4wbV/n/2eO52B7fvfbed7PZJJsmDW1o2lKgQEq4CBBFlXjggac+VKpA8AQSBQkEEki88tBXnnhBPEBEBRGQVFWbtLlsm91ks8kmu856fVnfx/bMeGz/H1bf6fHE3vXm1u1f/iTL8ex45ptzfuf7ft/lOI52u93GQAbyCYvzk1ZgIAMBBkAcyB6RARAHsidkAMSB7AkZAHEge0IGQBzInpABEAeyJ2QAxIHsCXH1e+Lo6Oij1GMg24hpmnA6nWi32/e8eGm323A4HOyz/d/0sn/mz9tJep1Px+36WZaFbDa743X7BuJAcM/E36/sZuIBQJIkdn/67na60Hl0znb3489rt9twOp0dxwCg1Wqh3W6j1WrB4XDA6XR2vS7/ebdjNQBiH8JPFE2I/Xi3d6DT8jidTjaJ9Ld+hICxnbXj78tbvX6k1WrB6XTC7XYjHA4jEAjA6/XC6/XC4/FAEAQAgGVZTA/LsqDrOgqFAnK5HAqFAizLYtdsNBr3AHo7GQCxTyEA0qubm+zmMgmAgiCwCSWrcz862L/H348HbC9raL8GLSrSrVarQdd1OBwOuFwuyLIMSZIgiiL7Di0oAu+hQ4cgyzLq9TpWV1eRTCbZPej6O8kAiF2k2yp2OBwQBAEul4t95v/Wz/XsIO3Hdfa6Vjdr080i88DkLaf9mGVZaDabqNfrXcHDf5e36sAWMD0eD+LxOIaGhuB2u5FIJFCpVPoGoqPf7pvdBivdVulO5zKlugyynX9s9/lBhLdqzWYTzWYT7XabWQdFUaAoCkRRhMvlYu/0EgShY6Isy0KtVkO1WoWmaajX62g0GuweBPB+eFw34O40fb2saK954Y/bAxt+bOxzROeKogifzwdFUVAoFFAqlT75YKXZbEJRFOi6DpfLBYfDwQivZVnMHdAkejweSJKEQCCARqMBl8sFXddRq9VQKpWg63rX1dhoNCCK4q44yXZC7teyLJimyayAKIrw+/2IRCLweDxwu93wer0MnPSiZ22329B1HdlsFul0GhsbG0ilUrAsi1kgAm4/C7VXtLrTQuSBxF+H3CvvRrczAvbInc51Op2MrjQaDeTzeUiSBI/HA1VV+xrzRwZEesharQZFUdjkKoqCYDCI8fFxjI2NIRQKIR6PIxqNYmxsDOFwGIqiQBAEyLKMXC6HXC6HhYUF/Otf/8L8/Dwz9/QuyzIajcZ9cy+g07IQp5MkCV6vF6IoIhAIIBQKIRwOY2hoiAHQ7/fD7XZDURR2zOVyMV3q9TrS6TRCoRC8Xi9cLhcKhQIqlQpqtVrX+9+Pq6bv8e65W8qGpBvPpfHsNo481wXAPAUPUl4fXdeh63rfz/FIXDM9lCiKsCwLkiTB7/fjO9/5Dk6ePImRkREEg0GoqgqXywVJkuByudBqtdiDAmCWkwZ4Y2MDZ86cwe9//3vkcjkGcMuyGJm+H4tI+tLqdjqdkCQJoVAIkUgE0WgU8Xgcfr+frXJRFNliEUURkiRBlmXIstzhopvNJiqVCiqVCrLZLFZWVrC2toZkMol0Og1d1+9JjfRyl71054FmB2I3CiAIAtrtdofVcrvdaLVaLFih7zSbTWbB+XsSbeEBzf8d2AK7aZooFAo7zsEj44i0uvbt24eXX34ZP/zhDxEOh5nbymQyLDIrFossaQsA2WwWsVgMsiwjHA5DVVU0m03m2s+fP4/f/OY3+OCDD6CqKrO2hmFAluVd6QmAuZR2u81A5ff7MTs7iyeeeAIHDx7E5OQkJElik2KaJhqNBktpEIAITKIospckSZAkCY1GA4lEAktLS1hYWMCNGzeQz+eh6zoMw4DD4YAoivfQDztHpGP9uGN6PgDwer2YnJzE0aNHceDAAUxMTCAejyMYDDJaZBgGKpUKTNNkQNrc3MSVK1dw6dIlZDIZxnUNw4BlWR0BiX0B6LqOYrG44xw8EtdME/P888/jRz/6ET73uc+xpGyr1cLt27fx3nvvQdd1KIoCwzBQLpdRrVYRDAZhGAbC4TAymQzGxsbw2c9+Fo1GA7FYDKFQCMePH8dPf/pT/OEPf8DCwgJcLhfa7TYURblvi0gD6Ha7EQwGMTY2hqNHj+Lpp5/GoUOHMD4+jlarhVKphGKxCE3ToGkaA2Or1WIWAgCzlpSb8/l8HRaz3W4zS55Op2EYxo5JauBeq0fvdh7In6soCkZGRnDixAl88YtfxIEDB6CqKiRJYlaSd8+0mNxuN2KxGKampjA5OYn9+/fj4sWLWFhYQCqVYtbQPo52PfuRhwZEO1949tln8ctf/hKf+cxnmGKmaeL27dtYXFzE5uYmAKBcLjMX3Gw2YZomcrkcnE4n44eFQgGXLl3CyMgIfvzjHyMWi+HLX/4ydF3Hr371K+RyOWYx++VX3QbJ6XQiEAhgdnYWhw8fxvHjxzE7O4tYLAaXy8UsAAVQ9XodtVoNjUaDUQTiTpTqqdfr7Pp+vx8ulwtjY2NMV0EQUK1WUS6X7wGYPd1C0isK5kEkiiI8Hg9GR0dx4sQJPPPMM5idnYWqqjAMA8lkEqVSiUXxJLqus2MUnPn9fgQCARw8eBDj4+OYnZ3FmTNncPPmTQBgz99sNgF8nCDvNc7d5IGByFcZaHCnpqbwu9/9DocOHWLnbWxssJVUq9VYzso0TSiKwlxBtVqFZVlIJpMIBALIZrNIJpM4dOgQ7ty5g7/97W945ZVXEI1G8fWvfx2bm5v47W9/C0EQoOt6R+J1J6EVzPPCaDSKw4cP49lnn8XRo0ehKAqq1SoymQwqlQpKpRIKhQJqtRoMw4Cu6x0lMN4qAlvpjGKxiGQyCVVVMTQ0hHg8jrm5OQbujY0NZLPZrtWIXgur23lkhSORCI4cOYJnnnkGx44dQzQahcPhQKFQwJ07d7C5uQlN02AYBls4FPkSmICthVkqlSCKIlRVRTwex/DwMD7/+c8jEong9OnTmJ+fh6ZpbBHyi8dOJ7aTBwIiv3JdLhdLybz++us4cOAAHA4HDMPA4uIiFhYWUC6X4Xa7UavVWLLTsixomsYCG8rZtVotNsnFYpHdI5VK4cKFCzh16hTcbjdOnTqF//znPzh37lxHoNOPEIAodeT3+zE6Oop9+/ZhZmYG0WgUmqYhm81ifX0d5XKZ6W6aJht8fjx4UBIgaYF6PB60Wi3EYjHEYjG0222k02ksLS0hlUqhWq0yvrqddbdHwRTNyrKMEydO4OTJkzh27Bh8Ph8AIJPJIJvNYnNzkxkBCjjIgtJcEiCJ87ZaLTQaDWiaBgAwDAOxWAwzMzP4xje+gWq1imvXrrFgxm7F+/VQDwREeghSotls4uTJkzh16hSzUAsLC7h8+TIj99VqFbqus+DE5XIxrkVpGCLuwBbXotRMNBqFJEm4fv06pqenceTIEUxMTOB73/se3nnnHRal9iM0EcBWHjMUCmF4eBhjY2MIBoNwOp3QNA2bm5vY2NjA+vo6syK6rrPJtHOiXtaRAKZpGkzThMPhgNfrRTQaxejoKNLpNDKZDKrVKgzDAIB7gpZez0HR7XPPPYdXX30VU1NTKBQKWF9fZ8FCsVhkVRM+oLBXPvjP/DnkwSzLQr1ex9TUFA4fPoxvf/vbqFarWF5eZoaE9HrsHNHhcKDRaCAQCOCll15CKBRCrVbD6dOn8f7772NiYgKWZTGgEFGv1+sQRZGlCAAwwBIA6Xu6rsPtdgPYqocuLy9jenoabrcbL7zwAqamprCysrIrvQmIoigiGo1ienoaw8PDAIBkMomNjQ0UCgWkUinkcjkGQL74b5duFpGsDD1fsVhENptFs9mELMsYGRlBJpNBs9lkXgDYvmLEWxzLsjA3N4fXX38dR44cwcWLF3Hx4kUIgoBAIMCsmmEYTC8yIjR/fG6222cKyOg9n88jEongxIkTSKfTyOfzSKVSjG92C562kwd2zZT7a7VaCIfDmJiYgCiKuHv3LhYXF2EYBq5cuQK32w3TNNFsNuF2uxGJRNigkEul/BXxPCLMgiDAsixUq1UIggC32418Po90Oo19+/YhGo3iqaeewtLSEqsF96s7ueZwOIyxsTHE43GIoohSqYRqtcqsSbVa7SjNAff2+HXjbHxQIggCTNNklQfKnw4PDyOXy6FSqSCdTndwLbput3uQeDwefOlLX8KTTz6Ju3fv4vz58yyJ7nA4IElSx2InvWiBCILAxty+kMht8+VOh8OBWq0G0zQRjUYxNzeHWCyGzc1Ndo/dZi8e2CIS6qkSMTMzAwAsanM6nZiYmEAikYCqqsjn8ygUCsziKYoCj8fDLB+tOgAdOULeYrpcLqTTaayvr2NsbAySJHUERv0KDbogCFBVFdFolEXIFMXaO266iZ0P2fN+PKjIVQqCAL/fD1EUEQ6HEYvFcPfuXcaPe0XJPJ8jcEciEQwNDWF5eRnvvvsukskkJEmCaZqoVCqsOuT1ehn/pOoPn+ym8efHnHgwWVUCMRkVURQxMjKCkZER3Lhx477LrA/MEUmZWq2GmZkZBINBtNttxONxHDlyBEtLSwCAqakptFotqKqKWq3GkqfkjiRJYtZJURQ0m02WLqF6JZFyy7IQCoVw8eJFHDt2DLIs49ixYx2T1K3aYBeeoIuiCLfbDVVVoSgKc1v8hFDd2R4Zk/BVETv/ons1Gg1UKhUG3FAoBI/HA5/PxxbuThPJP6Msy4jFYqjX6zh37hxWVlZgGAZb6C6Xi3mhQCDALKNpmtB1nQWYlG4izk/0o1vCmreWHo+HcWtet93KA1tEKhfRipNlmQFmaGgIS0tLzDWUy2WIoghFUdh5VMIji8evUgIEDRgNKuUYDcPA2toannzySUxOTiISiSCfz9/3s9BCIAtClIMCFOBjMm8n+3xlhY9m7XVxIv2CIMDj8SAUCrHEtyRJfVsUCvSGh4cxOzsLWZaRz+eZhScLznNaSqYXCgVomsZSZbQQqWkD+LgtjF4013b9KOPgdrsZB6WEPY1TP/LAQOR5BhXxKS915MgR3LhxA4VCgQ0EJVGbzSYajQbjQzSAdD1yH7T6aLVSBw6580KhgGazCZ/P11eUyQvvQhuNBuOAVDd2OLaaFmjR2HmbvXGAeBd/rv079NyUN3U6nV27duw60r956xSLxfDVr34V4+PjAD4GOzW2kscyTZMFhpQNoGQ8DzJacPZcrL18SXNE7rndbjMQ2rnsYwMiPzCJRAL5fB6qqsKyLHi9XsTjcWxubrKUjP0hSAgE9OBkLQmEfNKbBiYWiyEej7O+wN0KP1m6rqNSqbBcnsfjAQDWKs+7JJ6489yRnyh+8ujvvPW09y/2UzsmaTabUFUVX/va1/Ctb30L1WoV+XyegZy8Co0fRcyapnWkYXiaYXe/pCNdi38uOsZXYcrlcldMPDYg8hFdJpPBlStXMD4+zoAxOTmJVCqFcrnMViopSCCgyeQT2gRSSZI60j7USEDWgZoJyuVyV97WS4igE5jK5TJSqRRSqRSi0ShUVYXH44HH44Esyx3dNN3GgI8wSbr1TtKi8ng8UBSFpb4Mw+hYjN2iZN7yBgIB7N+/H4FAgB2nwgBZJ+J+5FIJlMCWBaR2O3om0pfvZqLFAoA1AdvHTtd1aJq2bbppJ3loHNGyLJRKJZw5cwZf+cpX4PP50Gq1EI/HmWvjW6eok4WS2wRK4GN3xH/mI9dqtcqAEovFIAgC6wbejfADn8vlsLa2hng8jtHRUcTjcaiqylqkZFlmwRXpRHrS4BMY7WDl83XEpfngxDAMVu7sVZGwU4Bms4lsNgtd11lbWjab7bDABBoCEHFQ/t/kuqleTOkvvlpD40+unfo0iUu3Wi1UKpV7ehR3A8qHssGez9S/9dZbuHz5MjPntJehVquhVquxVAkNElkIenc6nQyYfNd1s9lk7VlDQ0NQVRXPPfccO+ejjz5igY+dl3UTPrfXaDSwubmJO3fuYGVlBel0mgUUZL0omCB3xW+I4iNOuifv7vgAR5Zl+Hw+hMNheL1eAICmaSiVSqjVah3Uo5feDocDxWIRZ8+exa1bt+BwbJUPg8EgW/SkH1EAWZbh9XoRCATgdrs7uuP5ZyRjwT+fKIqQZZn1LlKU7/f7IQgCS2bzKavHzhHJlRI3yefzePPNNzE9PY3R0VEIgoDh4WFEo1EGRsuyGJcEtuqX1MJFuSpyDRTVeb1elpyljunJyUm0222Yponz58/vOnXAb5M0DAOGYTAwxuNxAEC9XmcTTUAHPu5QJr5IfKybKyXXSGAIh8OIRCIQBAGVSgWZTIbRF3re7RYTZRmuXbuGt99+G0NDQ/D7/Uwn/ju8DqIoot1uM15nmiaATovdKygDPs678t7NNE3cunWLdVPdrzyUEh/lnShoOHv2LH7+85/jZz/7GY4ePYpIJAKXy4VqtQoAHbVU6nDmy2c8J2w2m/B4PCwnRhZjZmYG4XAYsizj/PnzeOedd3blCvhzKXiyLIt1CQmCgGKxCK/XC8uyEAwGmeWoVCqsmZWehcaBvybxXwIh9SZSF0utVkMqlUIikWBdMZTX284i0gLSNA0ffvghXnzxRQBbDcX8Ljw7b6UA0a47AZCsJwC2wIlr8taeP4eaZqkpwp6/fWwWkR6YuAIp+b///Q+JRAJvvPEGvvCFL+D48eP4+9//jlu3bkGSJBw8eJBxLkEQEAqFAGyBlM+/1et1VKtV1qSp6zqmpqZw4sQJCIKAUqmEf/zjH8jlch19cP0IP0ikd6VSwbVr11AsFpFKpbBv3z6MjIyw5lbal1KpVKBp2j0dNiSUmuItiKqqCIVCGBoaQiQSYUHW6uoq7ty5wzapE9B6JeX5CaayaKFQwNraGlvQ5J7JLfOWkKcwfN2Z3y7brWmD6AcFcfV6HfPz87h06dI9jRqPvcRHZp/cM1kup9OJpaUl/OIXv8Brr72G7373u3jttdfw/vvvI5fLQZZlrK6uotVqsYmljmEAjBPSXop6vY5MJoO5uTk8//zzLNi5cOEC3nrrLZRKJXi93r67b3jhrUy9XkcikUAmk0GpVGIpD+JSANjE0vf4hgByhVS9aLVarGITCAQYBdE0DZlMBuvr60gkEkilUjAMoyNKtetIwucv/X4/Go0G1tfXWUMx8HG2gTZ18ZUq0p3fJ8Rvi6V7EEDpb263m/Fbj8eDTCaDd999FxsbG/ck+rvpvZ08lKYH2vZJCtODSJKE1dVV/PGPf8R///tf/OQnP8GLL77IorSbN2/io48+QqFQYBt5KJ1AE0hRWqlUwszMDOvuabVaWFxcxBtvvIG1tTV4vV6YprnrnkQSGjAqfVFejKoE9XodPp+P1dBpgvnIm6yNvUGA3DKlaygYunbtGq5du4Z0Os3yl7xb7jWJBELSbX5+ntEFinKJ61Ggwe/5IWtJnJFASVaSr6AQfVIUhXHbUCiEUqmEK1eu4OrVq8wq87rt1iI+tM1Tdj7AR9J03Ov14pVXXsH3v/99zM3Nwenc2pO8srKCzc1N1h5FwQM1PcTjcezbtw/79++H1+vF5uYmTp8+jT/96U9IJBIMvP123vTSH0CHZSMLQFteg8EgQqEQpqenMTExwYIwirxN0+zgXgQCvlEgnU4jmUzi9u3buH79OtbX15n1pe8AO1sSsmTj4+MYGhpi7tLtdsPtdsPv97MIl34QwOFwsG2efHMDD1Lew1GFyev1QlVVRCIRqKqKUqmEc+fO4d///jcSiQTLW9r1A8AyEjvJQ9uzYh+4bqta0zT8+c9/xtmzZ/Hqq6/im9/8JsLhMI4dO4Z2u41yuYxEIsGsCu2BjsfjbNXOz8/jzTffxD//+U/UajU2cVRffVD9yQIDW/Qgl8shn88jk8nA7/djeHgYDoeD7WnmqxBAZ9JZURTWzEFtbHfv3sXCwgKuXr2K27dvM2ti30Zr183OEel4rVaDpmnMFVOahc/zURBJXej0yxNUYeG3hvI5W/56Ho8HzWYT6XQaly5dwttvv836D7vp1isf2kse+2/fWJaFW7du4de//jX++te/4gc/+AFeeOEFjI2NwefzYW5ujiXJiXQDWxHh6dOn8Ze//AVXrlxhFRaqFMiy/EBAJOErGhRh2rtViCNWq1Xkcjl4PB7mDvkmAQJno9GAruvI5XJYXl7GjRs3sLq6yn6Kg/KnO01at9QQBUWBQACHDx/G5OQkfD4fS/FQklxRFAQCAciyDE3TGD0gD0BA5OvdlC6jZolqtYqlpSVcuHAByWSSceBu+nWrDG0njx2IFJ212218+OGHeO+99xCPx/HUU0+xPcSxWIxZo0QigfPnz+Pq1avI5XKsDkx8jPgPBQy7bXzYSVdKYpPF1TQNq6urDFTBYJBF0pQQpomkyhFZH9p6UCgUGKe25+66gdHOufiqTq1WY/uGn376aTzxxBOM+xmGwXK3VL0plUqshEdWjvcGtA+bAiza82wYBpaXl/HBBx/g9u3bLB/cS+xWfCd5ZBvsuwndiiJNqh2TVaMJp4E2TbMj6cr/jQZQFEVWiXlQ99xLX5JuHJiIvKqqrGxHtXJqpNA0jeXu+Kh1N3lPOxDtnyORCF5++WW89NJLiEQiHbXlYrGItbU1LC8vo1gsQlEU1jBC21mpq8nn8zFuScGQpmm4efMmLl++jJWVFZZm4ru5+THhde6XI34iQORXNC8UcNi3NBJw6bu85SOORm1M9xs196M7uWsKTIg6UFRMTbX8LsRqtcra6vnFQ+5uN/fvllfkc67UCDE8PMx4rtPpRLVaRTabZS1z5Kanp6cxOTmJcDjMOuypKblSqSCZTOLu3btIpVLIZDJs304vXsjrRu+NRqOvXwN7rEDcSXaTiX8U39/p2gA6Ik0i9xRlUmBA4CDQ8hvv+eTxbhdNr8wE/ZsWJ78nhS+d8jlDeqcGDGrssCwL5XIZ5XK5oxRIVIPfqWcfG+D+gbinfqjzQUH0qEBI16bJ5qNkXrrtcwbQkSi+X7HXkHnXbs/d2bvd+e/Rv8mtEr/kmzb4PUL8uXauylMMex6RZM9GzZ9m2S6QsL9ogvhXr2vcj9itUrf6NHFpu/BWjUDEV0a6RcL8c/Hf7abL/TzjAIgPQXrVhOnYwwKh3e0BnZSh23E7WHiO3c3Nd7Nw9nc7JeB1o791+5nB7WQAxAeUXon8nY49rPvtRPF7uUo7UHnho2A7wPnrdgMyH0T6fL69m0f8/yqPkp/S9e3WEOj9syS9XGgvXteLdtiv180aknUlXURRxOTkJPtR935kAMRPofQCvR1QvawzDyo6ZgeZndvSMbtbJzdMOeHx8XFMTEygVCrh+vXrff1aLDAA4qdGurm4nY71cqm82Cs7/Dl2y0dA5Tu1JUlCMBjExMQEpqenAQCLi4tYXFxkxYh+ZADET5lsx+16nct/5oMbKudREYH/O5+movwo5R6pU11VVbaFQ9d1zM/PI5lMolardQQs/cgAiJ8i6ScqtgvPK6mqo6oqxsbGMDMzg+HhYdYMQW15fA2ZvmuaJvvZZtoxmUgk2K9GdGv0oMR9PzIA4qdAuiWl7WIPTnjrZv83dXRTyx25WOoC4vfwUHcP36HDu2a6H7l3/qebgcF/gfb/Srq5416WkcQeaBAYLctCpVJhx+g8qsJ0q9zw7wQ4e/RsT4Lz9+tH+q41D2Qgj1IG/4P9QPaEDIA4kD0hAyAOZE/IAIgD2RMyAOJA9oQMgDiQPSEDIA5kT8gAiAPZEzIA4kD2hPwfJXVbfjbTXFAAAAAASUVORK5CYII=", "text/plain": ["<Figure size 200x200 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "", "version_major": 2, "version_minor": 0}, "text/plain": ["Validation: 0it [00:00, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "323be58fabcb4ab4a9a128d9f6cc9b52", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|                                                                                | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "b80e5eb10ac04eb6a3f90ac7415ab681", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|                                                                                | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 200x200 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "", "version_major": 2, "version_minor": 0}, "text/plain": ["Validation: 0it [00:00, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "4b79fa6a38cd49f7bb65fc6ad327af73", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|                                                                                | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "05dfeb5eed1b44bfa4d3986dd0f97e1f", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|                                                                                | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAKIAAABDCAYAAAAf6t48AAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjcuMSwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy/bCgiHAAAACXBIWXMAAA9hAAAPYQGoP6dpAAAbGklEQVR4nO1dS2wb19X+yHlx+CZFkbIoyZSs+B0pdurYcZuideFFUPQRBDCKFu0i6CJA0aJdtUDTLoouWqDbLrLosmi7bo26adokBuJWcRrbTWRLsiPZkiWRlCiK75khh/Mv9J/ry/GQomw5Vf+fBxAkUzN37uO753znO3dkl2VZFnrWs/+wuf/THehZz4AeEHu2R6wHxJ7tCesBsWd7wnpA7NmesB4Qe7YnrAfEnu0J6wGxZ3vCxG4vHBwcfJL96FkHq9frEAQBLpcLneoPLper4890r2VZju1s17ZlWQ+1ZX8G/1zLsmCaJtbW1tq2S9Y1EHvWeaF2YvxidWOSJO34XifQ8mCyA3WnY6Pr2/WHniOK3UGsB8QuzO5Jms3mQ587fQfAFt3lcsHtdsPtdrf8bqd9sN/He6l217fzXvyY7J/x1zqBtFPfH2XD9oDYpREA6YsWsN0XGQFQEAQIggAAcLvdO/aKToDYaXildra7zh6Gd3IPf303/SHrAdHB2nkAQRBYqGnHxzq1ZwfpduFtuz7t1JwARYBrB2o+jLcL9bthTwyI/CS3Cx/2a8na8Rv++m7C06MY79VM04RpmrAsC6IoQlEUeDweeDweSJIEURTZd/oSBKEl/DYaDVSrVVQqFZTLZdRqNdTr9ZaQSIlIJ9tuTjqNh8DEP5MHmNvtdtwUPAXh27C33S752Yk9UY9omiY8Hg80TYMoinC5XGg2m3C5XGg0GixU0SJ6vV7IsoxQKIR6vQ5RFKFpGqrVKgqFAjRNa1lkYGsS6/U6JEnatR1K4bfRaMAwDFiWBbfbDUmSEAwG0dfXB6/XC1VV4fP5GDjpi8ZqWRY0TcPa2hoymQxWV1eRTqfRaDTQaDRgmiYDbjegsoOJjAcRP8c8N6Xr+H+73W40m0243W6YptnyHPp9Jzrg1OduwriTPTEgEjGvVqvweDxscT0eD8LhMIaGhpBMJhGJRJBIJBCLxZBMJhGNRuHxeCAIAhRFQS6XQy6Xw/T0NN544w3cuHGD7VT6rigK6vX6I3EvMn7iiNPJsgyfzwdJkhAKhRCJRBCNRtHf388AGAwGoaoqPB4P+0wURdaXWq2GTCaDSCQCn88HURSRz+dRKpVQrVYdn99pDE7ZLu/dyIPb54Lap3WQJAmqqsLr9cLr9bJrTNNErVZDpVKBpmkM1E7ctx1vfZQ1cHV7MHYnOiIRe0mS0Gg0IMsygsEgvvSlL+HcuXPYt28fwuEw/H4/RFGELMsQRRHNZpN5SQDMc9KAV1dX8eabb+IXv/gFcrkcA3ij0WASx6N4ROovLaDb7YYsy4hEIujr60MsFkMikUAwGITX64Xf74ckSWyzSJIEWZahKAoURWkJ0aZpolQqoVQqYW1tDfPz81hcXMTKygoymQw0TWPPJ2+1HY3hk4h2mbp9bOTdIpEIjhw5gomJCYyMjLB1kCQJkiSh2WyiUqkgl8theXkZKysrWF5extLSEjY2NqDrOur1+kPPcvLK9O+VlZVt1+CJABF44K1GR0fx1a9+Fd/61rcQjUZZ2Mpms0xn2tzchGEYLGSsra0hHo9DURREo1H4/X6YpskGODU1hZ/97Gd4//334ff72S7XdR2Kouyon8DWJBJvI1AFg0GMj4/j6NGjOHjwIEZGRiDLMgurhmGgXq+j0Wi0eCACEy0stSfLMur1OpaWljA7O4vp6WnMzc1hY2MDmqZB13W4XC5IkvQQ/eD72Y5z859RXyjcut1u+Hw+HDlyBF/+8pdx8uRJRKNRRmfoGvo3fUbjLJVKWFhYwJUrV3D16lXMz8+jVquh0WiwZ/Pf7Rl0N0B8IqGZFubMmTP47ne/i+effx6yLAPYAujCwgL++c9/QtM0eDwe6LqOYrGISqWCcDgMXdcRjUaRzWaRTCbx7LPPol6vIx6PIxKJ4OTJk/jBD36AX/7yl5ienoYoirAsCx6P55E9Ii2kqqoIh8NIJpM4fvw4Tpw4gUOHDmFoaAjNZhOFQgGbm5sol8sol8sMjM1mE6Zpsg1I3lJVVUSjUQQCgRaPaVkW8+SZTAa6rnfsu5Pn48Fn/5m8oMvlQiKRwLlz5/DFL34Rhw4dgsvlQr1eb/myLIupAjyQicsODw/jxRdfxFNPPYV33nkH169fx/r6OtuI7bLvT1y+se/I5557Dj/5yU8wOTkJYGtnGIaBhYUF3Lx5E+vr6wCAYrHIQrBpmjAMA7lcDm63m/HDfD6PDz74APv27cP3vvc9xONxfO5zn4OmafjpT3+KXC7HPGa3/MRpgtxuN0KhEMbHx3HkyBGcPHkS4+PjiMfjEEURuq6j0WiwBKpWq6FaraJerzOKQByNFrVWq7H2g8EgRFFEMplkfRUEAZVKBcVi0dGzkHVKTniPzId5URSRSqXwta99DZ/+9KcRDAaRz+dRKBRQLBah6zp0XYdhGAxQvERFagElkuRVh4eHcfjwYfz1r3/FzMwM87w8n6T+dQvExw7NTil+KpXCb37zG7b7ACCdTmN6ehrpdBrVapVJGYZhwOPxwDAMxitLpRIsy0IoFMLa2hq8Xi9CoRDu3r2LyclJvPzyy4jFYtA0Db/97W/x85//HM1mE5qmtZTDOhkv07jdbjQaDSiKgsOHD+Ps2bN47rnncPz4cXg8HkbeS6USCoUC8vk8qtUqdF1nhJ4AwHtFACwp8Hg88Pv96O/vRyKRgCzLuHPnDqampnD58mXMzs6iXq9DluWHgMbPL/1MnM9p4YkOnDp1ChcuXMDExAQqlQru3buHdDoNwzAAPMiaaQMRoPhnUVt+vx9+vx+BQADBYBB+vx+Li4v43e9+h/fffx/lcrll7J9oaOYfJooik2S+853v4KmnnoLL5YKu67h58yamp6dRLBahqiqq1SpKpRKbhHK5zBIb2oUErGazic3NTfaMdDqN9957D+fPn4eqqjh//jz+/ve/4/Llyy2JTjdGAKIdHwwGMTg4iNHRUYyNjSEWi6FcLmNtbQ3Ly8soFous7+RFeNmDT3qIt9L8uFwueL1eNJtNxONxxONxWJaFTCaD2dlZpNNpVCoVxlft3r0bAZ3Ww+1244UXXsA3vvENpFIp3L9/H3Nzc1hfX2dej+ehfLWIfy6FZ6INjUYDlUoFhUIBiUQCqVQK3/zmNxEIBPD222+jUqm0zMlOsufHAiIRcyKtpmni3LlzOH/+PARBgKZpmJ6exrVr1xi5J1mAkhNRFBnXIhmGiDuwxbVImonFYpBlGTMzM0ilUjh27BiGh4fx0ksv4d13331ID+tkJFUAWzpmJBLBwMAAkskkwuEw3G43yuUy1tfXsbq6iuXlZZTLZeYFKQTbvVE770gAK5fLMAwDLpcLPp8PsVgMg4ODyGQyyGazqFQq0HUdANomLdQefec9uyAIOH36NF555RUkk0ksLCzgxo0bKBQKTBHgQykBjE9seNpAyRN5TFEUWR8rlQri8TguXLgAURTxl7/8BdVqtaUE2i0Yd4UjEvkNhUL4whe+gEgkgmq1iosXL+Lq1asYHh5Go9FgA6LB12o1SJLESDEABlgCIN2naRpUVQUAVKtV3L59G6lUCqqq4uzZs9i/fz/m5+d31G+afEmSEIvFkEqlMDAwAABYWVnB6uoq8vk80uk0crkcAyB5bqdJdvKIFAJpfJubm1hbW4NpmlAUBfv27UM2m4VpmiwKAA+HYzKnSgh5ucnJSbzyyisYGxvD/Pw8bt68iXw+zxQHPrmw99WeddOakLOp1WqMQxqGgVqtBk3TkEql8NJLL2FhYQH//ve/IQiCY3LVyR47NJP212w2EY1GMTw8DEmScP/+fdy8eRO6ruP69etQVRWGYcA0Taiqir6+PjYBFFJJUyOeRxUTQRBYWBAEAaqqYmNjA5lMBqOjo4jFYnjmmWcwOzvb9bEjfiFEUUQ0GkUymUQikYAkSSgUCqhUKtjc3MTm5iYqlUpLaQ5oPVlDbZKRR+G9iyAIMAwDGxsbTNIRRREDAwPI5XIolUrIZDIs8eLbsmfL9DlvgUAAn//853Hw4EGUy2UsLS2hWCxCkiQWAQhc/BwAYLIarQU/JnIeAFq8J22UQCCAwcFBPPvss5ibm0OtVtuxqP3YJ7RpkqgSMTY2BmDLy3i9XsRiMRw5cgSCIDA9MJ/PMwmEqi1UyyWyblkW/H4/ZFlmXKXZbDKPmclksLy8DMMwIMsyDh06tOO+8xvB7/cjFoshHo8jGAyyxbOfuHEyOzApBPJhjhZG0zTmEUulEgRBQDQaRTweRyAQYPyYT3icxGq+TfK4Y2NjOHHiBNxuN5aXl5HP51uyYABsQ/AngigEy7LMtE8S5Wme6vU6DMOAYRjQdR21Wo2pBvl8Ho1GAxMTE9i3b59jH7ezx+aIpmlCkiRUq1WMjY0hHA7DsiwkEgkcO3YMs7OzAID9+/ej2WzC7/ejWq1ClmXous7CEQFQFEV4PB5GkhuNBvx+P5tw8o6RSAT/+te/MDExAUVRMDEx4RiyOkkIPLmn7Nbv98Pj8TB+RuGMrzvbM2MyvirCexv+WfV6HaVSiS1SJBKB1+tFIBCA1+t1rO/y4+F/5sOfKIr41Kc+haGhIWQyGczMzKBcLjvqjsQnqb+89MMfwKBx05h5Hkn9oHU0DAPj4+M4fvw4VldXWWbeLRAf2yMSH5AkCT6fD4qiMMD09/cDAAtBJK94PB6oqspkDT5j5isHVMulDJUSAMuykMvlkE6nsbi4CAAYGRlBX1/fY42FFtTn86Gvrw99fX2IRCIMnOQxeK9CZq+s8OIwD1C+lksaIwnf5P358G83p4qKaZoIhUJsMy4uLqJQKDAg0Rd5WvtGoj4DeOh6vi/8pqQwT4K4pmkIBALYv38/PB6PY1872WMnKxQWBEFgRXzacceOHcPc3Bzy+Twsy4KiKCgUCqjVajBNE/V6nfEhXlbgBVmaPFpUOoHj9XrRaDSQz+dhmiYCgUDHLNPJ+EXnKwxUN3a5tg4t0Oayi8r2A7J8WOY9Mn8PjZt0U7fb7Xhqx95H3pyqGFQNMgyDVTwoyeCTFJ7mEBjJUdhDKX+4hB+n/TMCJ/FFngp8Ylkzv7OoMO73+9FoNODz+ZBIJLC+vs4kGQIaJS5kBALgwckdmkQK1/ziuVwuxONxJBIJdi5wp0YTT/SgVCoxLY9OpHi9Xub1eY9Cnpnnju34Ib94ND77+cVOfIpvww5SajMQCMDj8TAKQUaemcbASze8BEXRgPfcfDjmky+etvCgtiyrpUqzE3vs0Mw/MJvN4vr166xC4nK5WMj0+/0t/IMmnogw7SzykryeyMs+RKbpXjpMUCwWHXlbO6OJpY1RLBaRTqeRTqdRLpeZAO31eqEoykOh2D4HtCD2hbHLLrSpvF4vPB4Pk77oVIud4/JAcfKMwFbGGwgE2JjIg/OAIC9P1AJAyzwTz6PnUOi1C912Xsl7WJ7XOykJnWxXOCJ1plAo4M0332RexTRNJBIJNjH80Sk6WMofved3YaPRaKmDEkBN02QKviiKiMfjEASB1VB3Ynzyk8vlsLi4iMXFRWxsbMCyLPh8Pvj9fqiqyg4r8ETd7slo0Xgw8tKHy+ViXJpPTigLJSB0k23ydECSJCiKAsMwIAgCE/55D8h7P3topr7bOSXNtx2MTnqjnQJQH7tei66v3GZSKHxcunQJ165dYzzR6/UikUigWq2iWq0yqYTCAHkI+u52u1nlgT91bZomO57V398Pv9+P06dPs2s+/PBDthPtvMzJeG2vXq9jfX0dd+/exfz8PDKZDBNvyXtRMsEnILz8wYPULmjzpT5FURAIBBCNRuHz+QAA5XIZhUKBVSXanUnkgcCHa/J++XwehmFgYGAA/f39kGWZeSmel9I95CX5RMyegPEZNS+w0+9pQ9KYSQlx4sid7LE5InELKsNtbGzg9ddfRyqVwuDgIARBwMDAAGKxGANjo9FgXBIAdF1nR7ioqkKcj0i3z+djcgGdmB4ZGWG8ZGpqase8hEIUeV9d1xkYE4kEADBxlpIjnkPxsgYfxvi54SURRVHg8/kQjUbR19cHQRBQKpWQzWaRTqdRLBbZeO0LyKsJ1DZvtVoN5XKZHZUbHR1lmh/xcaIhdq5qz/hpM9g3Er/R6DvdI0kS00jtvLkb25USH7lmShreeecd/OhHP8IPf/hDHD9+HH19faxGCaCllkq8hS+f8ZzQNE14vV72DgtN0tjYGKLRKBRFwdTUFN59990dyQX27JAowOrqKqanpyEIAjY3N+Hz+dBoNBAOh5l3LJVK7DArjYXmgW+TwhqBkM4mJhIJDAwMoFqtIp1OY2lpCXfv3mXZbqeXqXgwEjhpY+dyOYyMjDDKQoIzDw57OY9/Xjvw80kZzT/9nsbm8XiwurqK+/fvt4Tybm1XgEjJCWW4APD2229jaWkJr732Gj7zmc/g5MmT+OMf/4iPP/4Ysizj4MGDzI0LgoBIJAJgC6T8biTNTVVVxONxaJqG/fv349SpUxAEAYVCAX/605+Qy+VaJqkb4yeK+l0qlXDr1i1sbm4inU5jdHQU+/btY4db6b2UUqnEKkPUFt8ecVqiKJIkwe/3IxKJoL+/H319fSzJunfvHu7evcsqFPYyG29OXt80TWSzWdy+fRtHjx6FqqoQBAHxeByVSgVut5sdW7PriZRNE1Xh54YPu/Z5Iz2Yjra5XC5MTU1hYWGhZUN2a7sSmokjkRZH7n52dhY//vGP8eqrr+IrX/kKXn31VVy9ehW5XA6KouDevXusxFcqlSDLcktGJ8syVFVFs9lErVZDNpvF4cOHcebMGXZs/7333sOlS5dQKBTg8/m6Pn3DG78ItVoNS0tLyGazKBQK7JwdcVhgS4DmF89O1MlLESejik0oFGIUpFwuI5vNsvdB0uk0dF1vKa216yt955OkYrGIf/zjHxgaGsLp06dZIkgC88rKCtLpNDsdbj++ZtdCeQ/IV2IonNMbjNFoFOFwGB9//DGuXLnCdF2eW3Zju3Loga+akGchaeXevXv41a9+hbfeegvf//738dnPfpbVi+/cuYMPP/wQ+XwesiwzzY5CmqqqjFQXCgWMjY2x0z3NZhM3b97Ea6+9hsXFRfh8PpY1PorRohqGAU3TUCwWtybofxeuVqshEAiwGjq9tcdXJOi0EK8z8mGZ5BpKhm7duoVbt24hk8kwpcEucdE8twudNNeWZWFhYQF/+MMfUC6X8fzzzyMUCsHr9SKVSiEWi0GSJKyurqJWq7F7+D/u5JTtEvgoKSQBPhAIoL+/H0NDQyiXy7hy5Qru3r0LXddbEq5uJbVde3nKzgn4TJo+9/l8ePnll3HhwgUcPnwYbvfWO8nz8/NYX19nhwEoeaAXoxKJBEZHR3HgwAH4fD6sr6/j4sWL+PWvf42lpSUG3m5P3rTrP4AWz6aqKgKBAHvlNRwOIxKJIJVKYXh4mCVhlHlTckC8kWQVWmzDMJDJZLCysoKFhQXMzMxgeXmZeV+6B9ie5NuTFvJawJa4/fTTT+PMmTOYnJzE4OAgfD4fisUiZmZmsLq6yigQzzN5OsGL2QCgqioLx8FgkBUTNE3DG2+8gUuXLmFtbY0pHrQWpmni/v37287/E3uLz8kIlPv378e3v/1tvPjiiyzhsCwLxWIRS0tLzKvQO9CJRIIN7saNG3j99dfx5z//GdVqtUWH3Img3amP/M8kRdAR+YGBARw5cgSHDh3CgQMHMDw8zGQSPvt2uVxMFqGFzufzmJmZwfT0ND766CMsLCywo2/2g6nt+sX/zh767LJONBrFiRMncPbsWZY06rqObDbL3h6kd3D4ujGf9RKPJO0zHA4jFoshGAyiUCjg8uXL+Nvf/ob79+8zb2gvVe5JINKEezweHD9+HF//+tdx9uxZJJPJlneb6Vra5Wtra7h48SJ+//vf4/r160wcp3dsFUV5JJLcqa/8IvHSy4EDBzA6Oorh4WEkk0l4vd6WChB/8hsAOxSQy+Vw+/ZtzM3N4c6dO8hkMmwu2tWnqS9OxgOP94i0KSmJHBgYwKlTp/DCCy9gbGyMHXEDHhzC0DSNvZdDG4nOTKqqyt5TIQXj1q1beOutt/DBBx8wUPNcmz9YkU6nt53vTxyIxEsoG6tWq0gkEnjmmWfYO8TxeBymaSKXy2FpaQlTU1P46KOPkMvlWMjkDyjwdd+dHnzo1FcK07Q5KDTRS+mhUAjhcJhl0hS++HBHC0w63/r6OqsCVatV5um3E4Cdlon3Ovaf7aFbURSMjIxgcnISR48exfj4OIaGhhAKhVo8GIVnGi9/KJky8ytXruDatWssFNP882dHybMCYBuuk33iQATABmdZW7Vj8mqUMdJE0kFM4EE5jq9vkoxAvIROiu+WOckW/O94DY3ecPN6vRBFseUgRblcZrojfwqmW53NHn6dfucUpu0CNIE+HA5jdHQUTz/9NI4ePYpkMvkQIJvNrZfXCoUCMpkMbt++jZmZGdy5c4fpnXxCZq/A0DpYlrU3PSLQ/u+mUMLBk2S+Hkz38p6PQpIgCNB1/ZGz5m76TnSBEhOiDpQV07lF/i3ESqWCarXKrqfNQ7Xrbp9N5lRZafeZE9B5/qcoCoLBIKLRKAMi8IBKUCVM0zRGUfj77eK6kxNoNpt7zyNuZztV43f7/u3aBh68XsBXTUhHJfmKdDgCrZP34P9wZ7fPBnZ22NTpPrskxMs27dpvV1Z0ep49anQLxD31hzofF0RPCoTUNi/sOnkzp/ecgQd/dm83+sC33S67tmfW/O9oDOQxO81ZJ4/K04t2AO1WzAb2GBD3urVbeKcvfqH5BX+UzeK02O1AuV1ZkE/qnNp0CrvtEiJ72zuppNitB8RdMB4ETov7OCC0PwNoPYtov8Yeatv1xeka+72dQNaunW6lJ7v1gPiY1q7stt1nOzUn4DlxMqf7tmvPSSzvpj/btbuT9npA3CV7kvyUrFvAdNOXTvyyE/+ke7fry04Txx4Q/wusXXlvN20nbW8X/nmFoRea/w+b3XPtlmzVLvx3I6g73cOX/LazHhD/C8wpCXpUbdFu3fBMp2c43bOTapHdekD8f2xOCYuTbZd82D+3v/fcjfWA+F9g20kkTtd340XbyUDtvCQfptvVuPnP6ExAN9YD4n+B2bW97aohnaom7bygHWz2PxJAn/HfncqE9herugVi17XmnvXsSVrvf7Dv2Z6wHhB7tiesB8Se7QnrAbFne8J6QOzZnrAeEHu2J6wHxJ7tCesBsWd7wnpA7NmesP8Bgyg5xqRMOy0AAAAASUVORK5CYII=", "text/plain": ["<Figure size 200x200 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "", "version_major": 2, "version_minor": 0}, "text/plain": ["Validation: 0it [00:00, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "02c9a151f246464da9eebd015e6beb9d", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|                                                                                | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "233d0384d2c54aeca65d790b19565232", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|                                                                                | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 200x200 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "", "version_major": 2, "version_minor": 0}, "text/plain": ["Validation: 0it [00:00, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "cbaf0368cb304e6bbfa8ceac20f5a247", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|                                                                                | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "5bbc10ac52ca4835a55e13b73e1fb263", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|                                                                                | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 200x200 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "", "version_major": 2, "version_minor": 0}, "text/plain": ["Validation: 0it [00:00, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "2a95535f104048b2ac1756ff1db454f8", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|                                                                                | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "319034a2b62d49cbb3e4ca25a30bc76a", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|                                                                                | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 200x200 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "", "version_major": 2, "version_minor": 0}, "text/plain": ["Validation: 0it [00:00, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "8959e5f59fb14792abea3737d90363ee", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|                                                                                | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "e4c2571d928941b4850869d0d0a86ea9", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|                                                                                | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 200x200 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "", "version_major": 2, "version_minor": 0}, "text/plain": ["Validation: 0it [00:00, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "40b8269d8e05404da709e1f212edc47c", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|                                                                                | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "371f42e9994942a489b04ef0d2b2e022", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|                                                                                | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 200x200 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["2023-05-12 18:22:06,851 - `Trainer.fit` stopped: `max_epochs=200` reached.\n"]}], "source": ["n_epochs = 200\n", "val_interval = 20\n", "\n", "                          \n", "# initialise the LightningModule\n", "d_net = DiffusionUNET()\n", "\n", "# set up checkpoints\n", "\n", "checkpoint_callback = ModelCheckpoint(dirpath=root_dir, filename=\"best_metric_model_dunet\")\n", "\n", "                         \n", "# initialise Lightning's trainer.\n", "trainer = pl.Trainer(devices=1,\n", "                     max_epochs=n_epochs,\n", "                     check_val_every_n_epoch=val_interval,\n", "                     num_sanity_val_steps=0,\n", "                     callbacks=checkpoint_callback,\n", "                     default_root_dir=root_dir)\n", "\n", "# train\n", "trainer.fit(d_net)"]}, {"cell_type": "markdown", "id": "30f24595", "metadata": {}, "source": ["### Plotting sampling example"]}, {"cell_type": "code", "execution_count": 13, "id": "155be091", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "631635b665454a2884dc88d7d466c50b", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|                                                                                | 0/1000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["num_samples = 3\n", "def get_images_to_plot():\n", "    d_net.eval()\n", "    device = torch.device(\"cuda:0\")\n", "    d_net.to(device)\n", "\n", "    \n", "    val_loader = d_net.val_dataloader()\n", "    check_data = first(val_loader)\n", "    images = check_data[\"image\"].to(d_net.device)\n", "\n", "    sampling_image = check_data[\"low_res_image\"][:num_samples].to(d_net.device)\n", "    latents = torch.randn((num_samples, 3, 16, 16)).to(d_net.device)\n", "    low_res_noise = torch.randn((num_samples, 1, 16, 16)).to(d_net.device)\n", "    noise_level = 10\n", "    noise_level = torch.Tensor((noise_level,)).long().to(d_net.device)\n", "    scheduler = d_net.scheduler\n", "    noisy_low_res_image = scheduler.add_noise(original_samples=sampling_image, \n", "                                              noise=low_res_noise,\n", "                                              timesteps=torch.Tensor((noise_level,)).long())\n", "\n", "    scheduler.set_timesteps(num_inference_steps=1000)\n", "    for t in tqdm(scheduler.timesteps, ncols=110):\n", "        with autocast(enabled=True):\n", "            with torch.no_grad():\n", "                latent_model_input = torch.cat([latents, noisy_low_res_image], dim=1)\n", "                noise_pred = d_net.forward(x=latent_model_input,\n", "                                           timesteps=torch.Tensor((t,)).to(d_net.device),\n", "                                           low_res_timesteps=noise_level)\n", "            # 2. compute previous image: x_t -> x_t-1\n", "            latents, _ = scheduler.step(noise_pred, t, latents)\n", "\n", "    with torch.no_grad():\n", "        decoded = ae_net.autoencoderkl.decode_stage_2_outputs(latents / scale_factor)\n", "    return sampling_image, images, decoded\n", "\n", "sampling_image, images, decoded = get_images_to_plot()"]}, {"cell_type": "code", "execution_count": 14, "id": "32e16e69", "metadata": {}, "outputs": [{"data": {"image/png": "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******************************/f7nnHNOUjZv3rx294VXDlEu/lxMg2qZfbzRkCFDkrJx48Yl9vjx4xN74sSJ1fbOO++clGkMg2qZ/RjRsaZ6ZI0h8trlnGY4un/VW+fiq7z+OqfVjuK69NhcTEPfvn3brENbtl7Xt4/6x1xuc/9cXmnrNNSJS4jaqk7+/NLrlqzREf0e5n6jtf96W/2A7quxT97WMaVjfc2aNYnt1zCJ3gXMYv18tN5TW8f6OAbdV9dR0bWz6qwnUWdMRms8lBxb2l9bVee6MNMAAAAAAAAhfDQAAAAAAEAIHw0AAAAAABBCTEM7qL7Xa2W9TtbM7J/+6Z8SW7X2Xrenmj/VLUZ5jVVrqBprPdZr/lQPqPo41Qd6va9qDXN5fiP9sl5XdcV1dIonnnhite3X1TAz++xnP5vYnZUPHLoW2lejHOO58aTxOD6OYcyYMUnZ5MmTE3u33XZrt3ynnXZKynr37h3W2ccl+G2zNO95W8d6bbP6BPUvOkaifOyqoVbbxyXo2jPa7trO3oeoL1Kdd7SmRc6PaVuuXbu22ta2UD9VN/f5K5VWxiloP/LPpJX68VyMg69Hrl9EcQq61ovG4Wlspe/7GtujPkXHuo9x0DgoXT9h6dKlie3XO1GfksPXQ8dntIaTWerLcs+klURxcHXWXsgdWxJLEcWd5NZ0yMFMAwAAAAAAhPDRAAAAAAAAIciT2kGn91atWlVtf/GLX0zKVI6k+KlBnULNTat5GYBKAubOnZvYc+bMSez58+dX2z6lmllzOjOdJvVTlJquTac6VV4xYcKEalunWFWOtMceeyR2NOWosjCVYvh6vfOd70zKfve73yX2LbfcYrDto/IAlQdG40un/wcPHpzYXpKk8qNIjmSWpmDVfp3D31NOYqRj10twdPzkUrB636VSApU06Dj3586llVYf6M+l51UZlEo4vE/Q82qdNQ3lsmXLqm2935LU2JsztWJXp1TOUQf9rY3qEaUXzknTVG7n/Yj2G/UpkyZNSmwveYxSLbdVZ18vHcu5ceN/p/V+V65cmdiaYr4kvXCU1jknD9V78Oh4zMnCovJcSt3O6rO5PllybGfWmZkGAAAAAAAI4aMBAAAAAABC+GgAAAAAAIAQYhraQeMFvvOd71TbqpdXIi2wolo01S16DfK9996blN16662J7VOfmaUaQNU6a0yDpjd76qmnqm3VTWsaVa2zP1bjLObNm5fYXtttZrbPPvtU24ceemhSphrkqF31/r7yla8k9pFHHtnusbDtkItp8OMvF9OgaVV32WWXNrfNmvv1iBEjErskjkHr7MeXTwuqZWbN48DbOq5VBx2lP9axpz5A29LHT2gshdZRNeLeN2mZxjBEaXFViz18+PDE1nStPs5LNeDaNjm9uaduysOuzuaMW4jw143Sopo1P78opkF//6LYA42D2muvvRJb/YbvV9pf1Q9EaY71frS/qp/wvkD9gsb66LG+faJ0yWbxe0gupiGKR9OynKbft0dpf41Sn5b09TqxTjkfEpXXiZ0wY6YBAAAAAAAy8NEAAAAAAAAhfDQAAAAAAEBIl49p0Ny8Xm+nOrZBgwYl9urVqxPb619VH6+av9e85jWJffbZZ1fbPs7ArFmnpzo+X0/VGqrmesGCBYnt9YR33313UqZts99++yV2tKS76iUjHbFqGDW3tN6DX9Ni8eLFSZm2lcaOePuee+5JyjQOYd99901sr8nWdSd23nnnxNbn+6tf/ara1n6Ui//wz1vbXHWa25p+uauTi2nwz0c1tNoPdD2SiRMntrlt1hzDoOPL91XtE7n1A/z40jKNF1Bf5a+r19GxGcU05DS1kT5Z/Zb6S7X9eNM4k5EjRyZ2tC6Mxj/oudT2azzo84v6kVmsP1ai8q3RX5RotfX+ojz+pefydmnefn8P+put/VP9xrBhw6rtww8/PClTW3+nfAyg/u7k4h99H9T+qH5Az/3000+3W6bH6rn9+NT1rbStNG7Ko89A/VPuGXpy619FcQl6f3VicqL+G8Vklp43N05K/FEOZhoAAAAAACCEjwYAAAAAAAjhowEAAAAAAEK6fEyD6ulUD+uJ9HJmqQZQNamqvYvWYtD8yIrq2v3+qj3z+mQzs+uvvz6xve4tp3HUGI4olsLrdXVfs1SXqWiMg+oH/TNSvafmr9fn6fWRqs++4447ElvjFFSD7lH94NFHH53Yf/nLX6rtXA52RbXgHr2/aF9oPaqDjXSxOq41b//YsWMT28f2aF9UbW+UJ137ua69oHEL3taxqP4yun/ti+oDomMjDXhbth9/ufGk5d4nqG/V+CKNW/C26sejfc3StTSimJS28PebyyG/NcYtlFAnLqHOvpF2O9d/Pdofc+s2vOpVr6q23/jGNyZlEyZMSGwd697Wca9jOxon6sv0WMX7gigOyqz5/v1vXG6dBn0f8H5Rx0kubsg/Qz2vov7Xn7s0PqBODFKdNR7qxCW0cq0UZhoAAAAAACCEjwYAAAAAAAjp8vIknRrzshqdYtR9o+ksnfpS6cEb3vCGds+tkoAhQ4Yktk5l++m++++/Pym79957E9tPiZul02qjRo1KyrQeev9+ylElECqfWLFiRWIPHz682lZJgE5fRqng9Lz6TFQm5c+l6ROXLVuW2JrO1cuTchKjI444IrG9dGH58uVJmU656v36dq6zrDy0Tcm0bSm+P+qUvsrdNL2nT8GqfVXxKQ3NUhnCypUrkzKVGaqEwadLVsmiSo6iqXcdIzkpl7dLj/XkUg1qufen6lv1fvVY/0xL5Ul+f/VTmg5Sf0882n+1bba1tMwlPrAzx3ad63g7J30ZN25cYh977LHVtqZpzqU19nIe7evaT6J+o+fV32ztv759cv1RJbfe1vcX9ak6xvyx6iM1/fz8+fMT20u79djSMefJpQGOaOXvfdT3c2OsTvrhHMw0AAAAAABACB8NAAAAAAAQwkcDAAAAAACEdPmYhhJNo+q6VMcepdnaZ599ElvjFDpaZtasxfOpQjWmQVOwqQZw6dKl1XYu7Z9q1bwWX7XPqsHV63rdtOoFBw8enNjazt5W7aDGVowePTqxH3rooWr7wAMPTMrGjBmT2JHWVOukcRn6DKN4Fz1XpI/U+1XtaE4fC3ldaIluVJ+Htn+ULlA18AMGDEhs1edG6Bjy43rJkiVJmcYw6JjxfVn7dU676+2ctjXSBediGFRT7dE0jLmUh3786fOLfI+ibaH1UB/o+0Ok424Lf/+5dLTbetxTq+6vTqra0lgz359zKVZ9/J9Zmn5Z+6vGFui5Iq29/i7p73+UvjQX0+BjLXQs536zvJ/UttB3BY0L87GV6n/9O4hZc/ynf6/SeAf1P/r8ffvk/GDUz1o5dnN+oVXxPXXjLplpAAAAAACAED4aAAAAAAAghI8GAAAAAAAI6fICa9WOeu2d6tZyeYy9BlDLNKZBtcIejS1QzfGNN96Y2Lfffnu1rfEOqsGdN29eYvsc7qrp17bRHMl+7QnNOa96bdVWeo2gagtVp6j379tHrxvFTpiZDRs2rNo+6qijkjLNha91jsg9M98foqXuzZpzbft6aJ20HxHTUE6JBjzSw5s1Px8/hrRvqq3HRvp51RD7nOJmcUxDbv2VKP5G2ybyiaXa1jqxJNH4iuJMzOL1EvQZRZrwXP2jmAetk9ZZn0MU0/FKpjP7XOQn6qzToGXaT/S31P/WaD/Remi/8bFP+huVi0vw9dR9c+uq+GO1r+diHPx7h/7ea4yDrjXlx7P+Rvt3gVy91Ecq6lOj98hc7EDUr7RvRH0yF8ul+PI646ZuHAYzDQAAAAAAEMJHAwAAAAAAhPDRAAAAAAAAIV1eYK36Oa9dy+nJStB4ANXXqX7Qo1o01bH72AKfl9jMbNmyZYmtml1/btUL6nXXrFmT2F4fmdPSa855v66D5k+O9jVLdZo5Xbg+s5122qnafuKJJ5KyRx55JLH333//duulGsZcDId//qtXr07KNIZB7UjbXhJ3AfUpyflvlo4LLcvpr1WP7FE9svZHvxZDbl2GSH+c0863UlMb7Zvzvb68ZH0Es9QnalluzQd/v7m4gygveq5v1FlP6JVEZ+a1b2V/jo5Vbb2+K3i0z+lvR6Snj9ZJaetc/ne49N1Ix1V0Xf0tHThwYLWta9more83fvxqHXXM+euYpb//GsNx6623Jrauk+MpXf8gimmos9ZCK9chicrrrhPDTAMAAAAAAITw0QAAAAAAACFdXp6kUyd+Oi+XGkzx04iaAnHRokXhsdH0XW7K0U+z+SXmzZrrrPXy59JpQZ3W13r4KTtNV6oSCK2zT42m19EpRq2Xlwnp/enzVNnXkCFDrD30/qK0jjotqvtGcjOtsx6rfcHfk06Tqiws10dhy5GbttVn5+3SKV8/Xaz9qyTlX64/RfdQet0oDWWOOseWUJJ2s8RuZWrQbZ06aVJbKckoQaUwkaxNbU336eXJ+luZk+D439ro97yt60bkZLJRSmRt5yhtdS7lqBKlj478rVma+n2//fZLylTa7FNcm6Vtl3tHiSSuuXeFulKg9o7Nyek60x8x0wAAAAAAACF8NAAAAAAAQAgfDQAAAAAAENLlYxpU5+a1Wqq9i5ZVN0u16Hpe1cBFaIpE1c+/8Y1vTOz777+/2l64cGFSlosX8GnUNLVpLu2hL9c0qaqPVJ2iT2+2cuXKpGzOnDmJrSlm/T3pdVQDqPc7d+7calv1nho7oWksfcyKxrtoHbWveI2jakU1Ta6m2Fu3bl2719H7g/qUpK3T/hbpkfW5q6Y48kVaJ+0j6iO8Hlevm/Nrfn+NRYpSOKpdEndhlvqXXArHSGObeyZRXJC2Rc6/dLROuXrl0rWWxCp1ZkxHVyBq51beeytjJ/T5+d/D3LP1/t8sjRf0sYFmzWNdf/997KH6kFzqb99HozTNZs0+xt+v7ptLG+vfS9Qv6O+wxlb6+9e20DGmsYT+fUB/o/UdZcaMGYldxw9G+2+ueJ7cuaJz10lNbMZMAwAAAAAAZOCjAQAAAAAAQvhoAAAAAACAkC4f0xDFJeRy5KomzsciqNbw7rvvTuzVq1cn9qBBg6pt1Serjl3P7fV2v/rVr5KyBQsWJLbGFvhYBF0KXddaGDNmTLv1WrFiRVK2Zs2adq9jZvb4449X26rL79u3b2LrM/LxIbr0+4gRIxJbNcr+GUZLv5s134Pev0f1obouh9elaiyF2hpL4fWDqq3UvqFaUmimlbpn9QFR7nPVx5fEwSja33wMg5nZ6NGjq23tX6rd1f7m+6reX0le8NJc3t7flupiIw1xbp0bf6z6PH1mqr+O0HpE8S767LXOUa73XFttazEOdXLER22R05ZH162T1z6Xx19jGvx7Ru7Z6u+9X6dIfYbGUmp/jWKOcvXw51J/pGh7+LGg41PbRn/T/XuHtoXeQ7Q+VNSOZs1+wo/nOuOvzno1rVqzoSP1KFlzJgczDQAAAAAAEMJHAwAAAAAAhPDRAAAAAAAAIV1eYF2iW1QNXHSsatxURztr1qzE3m+//apt1dIPHz683TqZpRplXcPhhz/8YWIvXrw4sX3u4mHDhiVlqg9U3a3XHmrb6LlUi+jjNlTjqG2n+LgFjZXI5Y+OclyrjljXj/Aa5Jw+UvM4R22Vy30f5aDP6UMhT4luVNu/ZJ0GHQMaS6BxTt5W/bHmHNdx4H2G9lWNh9B4JN8/1cdFPkD3136tRLronGY6skvXS/D1zK2XENlapjEp2nY+pqV0fYgSbf62Rkm8gFInd330+5+rQ3Td3Jokq1atSmwfpzh16tSkTNct0D7pdfsaO6m/f9GaQNpuuXHiycX0RevV6DhYunRpYms8pI8BVL+n7a4+1beP+u5cDKcfz/+/9u6lV4qqC+P4Nt5QLoJH4MhFEPECKGiCUZyYmOjEmSMTv4OJEwdMTZw7UCcGdSiJfgFj0MREGYASGEgQgYMoAqIoF+++s37Xfrp7rbPY1X1azv836p3qS1V1VXVX6lmr9Piry5u5B0tm+23Z1ufyGMKVBgAAAAAuThoAAAAAuK6peJJestHLTPaSXHRp+o033qjGNkYUxZGUvc26XnJ87rnnqrHe7lwjOJbOs9fO88yZM9VYLxPqJUd7qTO6XKfr3V6CjeI5Gus4derUwHkopf/ypH4Pdrq2p9P1vnfv3mpsL/3qPOtlYb2cadedXvZtudSJ0bPfh+5PGkfSNr0nTpzoPdY2qXfccUc11liCbUWs27WOdb/2WivqZXrdr+04ahuqWi6f2/3AW55BYxtb0AiD7ov6uXZ59fvVdWWP06XU8U+Ngmg8ST/XLmOmHe21qGX5umoZnJ0HL9am24L+tn755Ze9x9u3b6+maVtw7zd7enq6GmvEUdu1W7oPRfuc5bVULaX/WGefr/uU/s/SFqz2O9R51O/Xi59pnHzfvn3VWKPcXlxSef8jo21wrmKKXjy0JRZVClcaAAAAAAQ4aQAAAADg4qQBAAAAgGviaxoybdSiNoB2utfWr5RSPvnkk6HjJ598spqmWVnNL9s8vWbr1qxZU411um3BqnlBzVhrJteuH21B9uCDD1ZjbW9qW6VpSzmtNdDspc0Za6szbWO5cePGoe+tueG1a9dW4/vvv78Mo1lu9dlnnw19vn5/+v1q5tzS7UpzmtQ0jJceEzSfaqfrNqM1DTMzM9XY1gV520Qp/S2OFy5cOPB9SunP7ev2aLcpnRa1Ybb7lGazNbusx0SvXWtUe2bH+h1oRlqPLzbLrbUitjakFL9to36/enzR9WHHUU2D7ud2Ged7TcOoRG1+PV4rYp0e5dY1p29rGvbs2VNNe+qpp6rxsmXLhn6ubV1eSilbtmypxlq3Z/ffqE7IEy2v1hHZedZjiL6X18baHhNL6T+26fdr6x8/+uijatqhQ4eqsdZW2OOE9z9xkK5aCEfba+ZzsvPRgisNAAAAAFycNAAAAABwcdIAAAAAwPWfq2mwWdooHx7dDt2jecFXXnml91iz9Rs2bKjGmn+1+bkoW/bAAw9UY1uLcPLkyWqa3sNBs9G21sDm/3SeSunP6No8peaINRus35HND+rn6P0StE7jvvvu6z3WHLjWMOjn2vmampqqpmkvbe3rbL8Xb5sbxHttl1lC5Ok+72XPdbvWY4DWENk6Bs3lax5Xp2vPdUv3Y+X1Ntd8tXcvAn2tHrd0u7frR9dVlGW2+V3NROvyas7bjvVYpM/V9ezdm8erYSilrgfR2hBdXs0nZ+4nlJ1+LfPWY9RfvuVY69U46udE9+I5ffp077Fm7bVeZ8eOHdXYHhf0fdevX1+Nz549W41tzZVur0rXlT2W6bqIjqF2fUS/f7rubM2m/gfR44ROP3r0aO+x3pdB92U9Lnj3uom2K++eB5HMfUda7qfgPbf1PwlXGgAAAAC4OGkAAAAA4OKkAQAAAIBr4msaMr2YM5kwzbF5vYdLKWX//v29x2+99VY17aWXXqrGmsW3on7u6uGHH+49tnn/Uvpz01rzYPOTej8Izfdqf/dVq1YNfe7x48ersX4nK1as6D3WmgZdr9qL+p577hn4PqX0Z581t6m5RWvXrl3VWO8fYbeHbKbY60Gv2e/5nFe+Wi0ZzGg/t9+Xbpta06DT7Vj3Rd3PNctse5JrnY/SfcjmnnWedPl0vux03Vb1uZrbt9uyHhOiezzYHLSuC11+rUeydQxLlixx51k/1/aF1/tu6Fh7yNtctLcuSunPeXt1Tvi/luNhS+a7y/oI3RbsfnX48OGh00rp3/a3bdvWe6z7idbv3HvvvUPnQ7dtXR5v34/+R3k1AN79Lga91tZg6TzrPOq+fezYsd5jre/Q/VH/G9j5iu5pocvgLW9mu5mr/wL6udl7R3E0AwAAAODipAEAAACAa+LjSV67y+ztvr1bw+tYL5Hby2jvvPNONU3bGu7cubMa25iNXjaL2EiO3mb9kUceqcYaQbKxBo0TaJRJb7tuYwDa6kwvueplxdWrV/ce27hRKXXsqZRSHn300WpsLyNG68qLeh04cKAav/vuu9VYtx2vHW+mjarGQzRCpXEKdEsvver3qtPtd6vHGo3s6Xdp9yndVnU78LZlbeGscSSvvam+r75Wl9fOl+4/up9rJMceP3VaJr6jxzGNXeix12tDqa0Vz58/X41tq2XborGU/mOgbW+t7x21XcZw9niZjWR0FV/KxJz1tVE8R9ltRfeDL774ohrrvmCPMZs3b66m3X777dVY47t239b9QI9dOl/2uBG1mPVakuv/DP1vpFEY+3w9/uh86L5u20dH86i/u3Y+dJ6iNrHZOM8wUZQpu81m3vtq37cUrjQAAAAACHDSAAAAAMDFSQMAAAAA18TXNHjtrrI1DTabFuXYtP2ezdtpXnn37t1Dn1tKKS+//HLv8d13311Ny+TjdHk3bNjgjm2eUNu3bdmypRo/8cQT1dhmLTX7Oz09XY01H2mzllrTsG7dumqsWWhdt5auV81k//jjj73H77//fjXt9OnT1VizpJ5M/jVqE4fx8moYdLrui/rd6bZqff/999XYa/9bSp3P1fzx0qVLq7HmcW19gDetlP793s6Xfk50TPRqwnRdZep+tJWyju0+pblmbYurrRdnZmZ6j48cOVJN09bR+j3YvHVUA0Vb1fFracWcyXHrtp3Jmnv/X0rpr3Gwv2Fa77d169ZqrPWBdl/Xegf9jdb5sjUNWhel27pX46E1DdraXOsfbR2R1l3oeveWwatJHMS+d9QmNtNWteUWAS1GVe8wCEc6AAAAAC5OGgAAAAC4OGkAAAAA4Jr4mgYvK5rNZtn3ijKomrv1svaa43vvvfeq8eeff957/MILL1TTnn/++Wq8cuXKWX9ulPG3GUfNB2puUcfWpk2b3HGLTOZPl0/zzK+99lrv8ZtvvllN08yjV0ui0zQ3rtl2O137UnvrFbOj20iXPahbsqA2Y6s1UFoHpHldm5/Xepvly5dXY609sGPt3a71AHr8sDUNWnehz/XqP7xpg3gZ4uheGnbd6T5/6tSpanzixIlqbO/NoPdp+O6776qx5q/tPEfrJlqG+WxceWvvONFlfjzKuNttI3quHhfsNvrDDz9U0/bv31+Nt2/fXo0feuih3mM9hmhtk3c/G92W9TdMfx/t8upvdFQnZmuU9H+U3idH65nsvbOi+1B49RDjrC3o6rO6vO9IFlcaAAAAALg4aQAAAADg4qQBAAAAgOu6f2cZcNKewNc6zdPZnJ/mAzXzrvk6m3PXfODU1FQ1fvHFF6vx008/3Xus92FQXq90/Zo1o6uZPyubz83k53Q+bB/nZcuWVdO0j/qrr75ajd9+++3eY6070Oy35ievdZrf7sIkHhNGlYkeNLbHhOh+CVo/YGsP7rzzzmqa1jVpz3W73levXl1Ni2oc7P1JtFYiOq5le6EPo+tR90Wv/uPYsWPVtK+//nrWY6130BoGPWbY3LPOo5dj1/Gk1jeM4phQSilr1qypxnY9RvtYy7ry3iv7Od79daI8uVfTEC2/fW20v+l/h40bN/Yeb9u2rZp21113VWP9H2J/h3W/1zoFPbZ5tU76P0rH9vf+zJkz1TS954pur7aeyd7voZT++gf9Pr2aRv2OWu6lpbz/Rpltv6V2IrrviN5zSHGlAQAAAICLkwYAAAAALuJJQ3iRI72MrZcRvTapGgPSS0XaRvWxxx7rPd6xY0c1TW8z//jjj1djjTV4n6OXID1elKmU/suXV+uDDz6oxjt37qzG2n7RRkD0dvW6fF1FLf4r5ks8qUUUR8pcptZ9RN/LHl80UqT77fT0dDW2kSSNHWhkYcmSJUPH+rmLFy+uxhqpsvuQ7k96zNNL3jbec+nSpWqatinW9rW29aTGj44cOVKNv/nmm2p8/Pjx3mNtbasxKD1uecfxDOJJs99vvPjOONejF6nKzIfXnrSUXEQleq4da8vVdevWVWM9dtt9XX8bdewdF/W4p/8z9L+TnW5bqJbSHx/Usf391/dVXiSnJeqT+U6i98rEoqIYlLKfG22T0XGBKw0AAAAAXJw0AAAAAHBx0gAAAADA1U34/BqkGTmbQdZ6h6iFlc3Kak42eu3Bgwd7jw8dOlRN27VrVzXWlolr167tPX7mmWeqac8++2w1Xr9+fTW2rRq9W86X4uf0tFWh5pU//vjjavz666/3Hmt7RV3v2mbNrlvNcOrnZnK2wCB2u9f9WI8fmvW106N8qm6r9rWay4/aqNqWq9rSWGsatC2jV9Og+6ayxwGtYdAaB8022/18ZmammqZjbRdoc9BRG0Yvb57NEM9nmbbHUZ68qzqGTLa8y8/S7SbTclX3+2hs31v3A63xO3r0aDW2v/da26O/995+EtU76vLafdBrVV9K/38JWw8R7Z9erUFLS/lx1tl0Nc+tOBICAAAAcHHSAAAAAMDFSQMAAAAAF/dpGEJztjYLrPldzS9HPYMtzdVqztjme/V9tY+6fpWaDfY+x96CvpRStm7d2nus94OwtRKDPvfEiRO9x3v37q2mHThwoBofPny4GtvcomYrNZ+t071NWfOfo8y0TiLu0xCL7tPgPV+3L92vvdol3Y+1lsDmjXWs92HQ99IaKnvs0ve1x7hB82HfW4+B0b1ZbNZZawuiGgdbl3D+/Plqmma19Zh38eLFoZ+j369mt+0yTeq9FlqM6j4N9j4ipfgZ8Mx9ClSXtRMq0yPfW4bod8eraVC6fWZ+03T59bhgx9Hvqr7W+36jebZjrVmI7nVjp0ffSZe/9y33bcg816tZydbBeZ+r73Xq1Cn3vbnSAAAAAMDFSQMAAAAAFycNAAAAAFzUNAzhZeSinuz6WtvTXPOASnN8Xt9mnQ/NItrP1ffVfK/3XrqJaPYwyjxaOh+ayba5veznau94y2abo+dei6hpyGvJwWYytZr7jcZ239Rpmb73Wv8Qjb3jmM6Hsvu912+9lP5jk+3frr3c9djrZab1GBe5FusYrEmoaZgrUZ68q3mOjiHe/QOU/q/wcuvRPR28ezHo50T3mhj2PoOeq/ugPS5k6w5b6k4ydTbKTtfn6jx58xF995n6iOi97HxF80hNAwAAAIAmnDQAAAAAcPl98uYx7/KOXs7RVqDKXkLXdoIa1/HaHurn6qV6fW87Xxon0EtUugze5SyNLSjvduf6Wo0N2eiCtoXVFpFnz56txjbWoLGn+RZHQruW9o/Ra+3z9RigkRvvEni21aulUQI9Rug+Y9usarwhagFol1HnSeNKXuvF6NK6zpddpkzMAldvlHGeq/3ccbXcjGTiSEr3m0zkJrPt6+d4rT/1c6MIoBeb8uJGgz532DwMeq/MdxbNhycTdYrWa0tkLrO82e2ZKw0AAAAAXJw0AAAAAHBx0gAAAADARU3DEFFGzopu/23HWjugGX+tU7hw4ULvsWaQbb3DoPf28oL6Xl4rNF0+zQ3rdC+DrM/VGg5bi6CtF7X+wWvXqm0cNa+dbb8IjEp0rPHqFrz9dtB0+1mac9ZaCh3bfShqrejlraN51rF37I1qK7xaNEyeTCY8Uw+Q2T6z76Uymfguay1atm87H9lMv32+18p1ELsuM21SI5lWt5n2u4Omz3aayrbj9aZ1uX1HOIoCAAAAcHHSAAAAAMDFSQMAAAAAFzUNQ0T9wK0o82ZzfvrcK1euuPPh3RNBs78ZUX7QyyJm+iln6j1KqZdJ50HH3vLr8lHDgLnk5VOjHupez3XdJzRT7PUrj3q563vb6S395qN6CO/Y1DLPmHzjyq2Pap68eRg07vKzMvUfXq1h9h4H3j1nontLZO69MK56l5aalMw8ZufLvldUs0BNAwAAAIA5w0kDAAAAABcnDQAAAABc1DQAQKNsltWr+1FefVEmb+zNw2ymZ/qGt2TTu8wBz3YaujOqHPco52NU20b2fTMZ/1HJ3gOgpS7Be26mXiCqXWpZr976GOcxpeUeEF1uV1xpAAAAAODipAEAAACAi3gSAHRsXJetWz4nG23ynp+9xH+1z53vEaNxRVS61FVErstlb2l92mUUzzPOdsEtUa7Muupy3bXEJb32pZGWY1lX0b3s+3htq7PHVK40AAAAAHBx0gAAAADAxUkDAAAAABc1DQCAUEsOuMvWr119bpefMyotLXXnOy/HncnHe+87SFfbUct3n2lXWkpdPzHKjL9XW9ByHBhVm+ZS/NqSf/75J/XemdoC77VdLm+0DIorDQAAAABcnDQAAAAAcHHSAAAAAMBFTQMAzEMtOe8u+49njPJzWnLu45LJuU9ivcM416mXRe+yLiFzP5PsfHjPzeTlVcv3MK7XdlmH4snWf7TMQ+ZeC13eK8TuC63rjSsNAAAAAFycNAAAAABwcdIAAAAAwEVNAwBgznLOk+K/sAzjynmPylzNb7a+w/auj+ZxXFn7cb02y7vHQ0u9RyRTc+VNb7nXQvZzM/fW8KZ79TrZ985ur1xpAAAAAODipAEAAACAi3gSAKBPl7GLlhaWozLK6ERXovWmMQX7/EmPKpXSbctR7/nZ13bVGnSuvoOWlsjZFqR2HEV9Wj5X37uryE20blo+11uGbMTIm4foO/LidsSTAAAAAHSKkwYAAAAALk4aAAAAALioaQAApFr+RbnYTG42m2X2XuvJ1iyM8r2919rlv/7666tpOr7hhhuGTm/JTI9KS0vKLj+ry5qGlnnO1ENE+0lXNUfZddNVHU22Xav3udF82GNKSw2HytSS6HGtZZscVb3HIJN3VAEAAAAwUThpAAAAAODipAEAAACAi5oGlFL6c26///577/GNN95YTdMc7Z9//jn0vTRXe+nSpWq8aNGi/MxeBV2+v//+eyyfq7ycseYSo5xiJvsMjJLuX7qde9u911Nc37ulLiFTsxFN67KGQcd2XUXrUY/F9lit08ZV4zCqWpBsFjvzfXZ5r4VR3c8kk5fPbp9dfW7Luopeq9uz99po+e2+ENUWZNbzKO+94P2mt9Q0ZP8LcaUBAAAAgIuTBgAAAAAuThoAAAAAuKhpQCmllL/++qsa33TTTb3HmiXM9IvWeojly5dX419++WXW89hlVjSTsc6MoyylZg/tutV1Zb+DQa+135l+f3/88Yc7H0CLbA1DJo/rHU9GuW+Oqh97llcTputRjxk333zz0Gnj0pKB73I9ep+TnT4uLfdpmO20LO8+KdnPjeqXvGneMSZzbxedjy7rGzP3XsiuK1s7mql3KKVeV14N6mxwpQEAAACAi5MGAAAAAC7iSSillLJgwYJqbGMzly9frqbZdqyl9F8Gt5e/NH5kL58PGltRXMBrMxZd5r711lvd957ttOxrb7nllmps19WVK1eqaRox0vey605b1+r3Cajsdp251K6Xy+3l8ehSusYFvP06E53IRpu6em12vuy6iiKLGh21Y13PcxXBaWmZ29VrI6NqX9qynbT87kRaWsyOal1FMWgvUqzbuh4XdHpXrX71WBW1Oc4cQ72oU/Qdee3bo/Uc4UoDAAAAABcnDQAAAABcnDQAAAAAcFHTgFJKKefOnavGixcv7j3WzNvKlSur8ZYtW6qxzd2eP3/e/dyff/65Gnut0HSsbUZtbi+6rfpPP/00dJ5a2jpG76V1CjabqPUd09PT1Xj9+vXVePPmzb3HmzZtqqatWrVq6DwBrby2qKX0Z3kzWftM5ralpqHl+ZnPzbLrzluPpfhZ7S5bSWZk2196uqwVGZVM7URLvUBLTUr0m9VVW+OI1uh4bVOj+iUv46/P1X3BTo+Wx2v1Gv0n8eowlD5X58Pu+9G2oG1V7XtFdRfhfKaeDQAAAGDe4aQBAAAAgIuTBgAAAAAuahpQSillxYoV1djm3C5cuFBN01y+5hRt9n7r1q3VtOXLl1djWzuhvF7Dg8Y2txflaL0ssOYSdaz3qbC5Rq/OopT+POHSpUt7j5ctW+bO49mzZ6vxV1991Xu8e/fuatrJkyer8cGDBwswKlHG1rtPQ0vuNzLK2gOryyy6t4zR8thj0VzVNCgvL9+S8Y+e21ID4L22y3t0ZOZhrpYv815RrZOXp1+yZEk1bWpqyh1793jS/yj6m/3bb78NfFxK//2S9Dfd7lfR/uhNj2q7Mvda0PtO6f2gbJ3ppUuXqml6L60IVxoAAAAAuDhpAAAAAODipAEAAACAi5oGlFL6+/raOgWtWdB7K+zZs6caf/jhh73Hmi3UHKLN2qlsL3gvW6m83suaF9R51vVhp+s0nQ9dzzZPefHixWqaji9fvlyNbbbSWx7MX13moDPv422P0b0HMq+NeJniUe4j9nO949Sg8bD3KaU/X63HV6++aly8zH92+/PqIeZKV/vQOD8389pov/BqCaPXao7f1jhq/aPee2jRokXV2P52er+Ng+bL/k7rNK/+Qafr5+jy6f+BBQsWDJ2mr/XuAaHfp/5H0X3/22+/7T3et29fNU1rOCL8swAAAADg4qQBAAAAgIt4Ekop/Zf3bGTAu8RWSn/kxl46W7hwYTVNL8HppTEvYhRd5vduSR+1frPLoPOkLWe9W9JHbWG9S5LR8ul6t5eFRxVDAa6Gt/3pvpeJJ0VtCjMRlmgfadmHvEhOFCX02j8rne4dE8YV7+myzW3mO8i0Ec20eu1yG5uUiJXy1ke0/XrT9LdS/2fMzMz0Hp87d66a9umnn1Zj3a40mmfpPOvvrj3maEQ6Oj550UMvBqXjqMVqpk2srguv9fuvv/5aTfNa1w7ClQYAAAAALk4aAAAAALg4aQAAAADgoqYBpZRSbrvttmpsc/2aw4xyxTa3p3lAzeJpTr9FJs+r8+HR5dPlt+MoK9uSQfZyuTpPtFzFJMlsjy21Spn3zdD5j3L73mdlWrB2VVdRSre1Bp6o3aUVLV9mnjN1C1FtQaZN7FzVKXRZt5apj2upldHv024b2so9+lz7Wn3fqA7D266i5etqm8y2YrZjbz2W0v+/y2vFrM+N8M8CAAAAgIuTBgAAAAAuThoAAAAAuK77d1IbBwMAAACYCFxpAAAAAODipAEAAACAi5MGAAAAAC5OGgAAAAC4OGkAAAAA4OKkAQAAAICLkwYAAAAALk4aAAAAALg4aQAAAADg+h9ugyEdmwg4NwAAAABJRU5ErkJggg==", "text/plain": ["<Figure size 800x800 with 9 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["low_res_bicubic = nn.functional.interpolate(sampling_image, (64, 64), mode=\"bicubic\")\n", "fig, axs = plt.subplots(num_samples, 3, figsize=(8, 8))\n", "axs[0, 0].set_title(\"Original image\")\n", "axs[0, 1].set_title(\"Low-resolution Image\")\n", "axs[0, 2].set_title(\"Outputted image\")\n", "for i in range(0, num_samples):\n", "    axs[i, 0].imshow(images[i, 0].cpu(), vmin=0, vmax=1, cmap=\"gray\")\n", "    axs[i, 0].axis(\"off\")\n", "    axs[i, 1].imshow(low_res_bicubic[i, 0].cpu(), vmin=0, vmax=1, cmap=\"gray\")\n", "    axs[i, 1].axis(\"off\")\n", "    axs[i, 2].imshow(decoded[i, 0].cpu().detach().numpy(), vmin=0, vmax=1, cmap=\"gray\")\n", "    axs[i, 2].axis(\"off\")\n", "plt.tight_layout()"]}, {"cell_type": "markdown", "id": "7fa52acc", "metadata": {}, "source": ["### Clean-up data directory"]}, {"cell_type": "code", "execution_count": 15, "id": "3a6f6d5a", "metadata": {}, "outputs": [], "source": ["if directory is None:\n", "    shutil.rmtree(root_dir)"]}], "metadata": {"jupytext": {"cell_metadata_filter": "-all", "formats": "ipynb,py:percent"}, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.16"}}, "nbformat": 4, "nbformat_minor": 5}