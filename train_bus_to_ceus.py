"""
BUS到CEUS图像转换的DDIM模型训练脚本
支持continue_train、模型保存、指标计算和可视化
"""

import os
import argparse
import json
import time
from datetime import datetime
from typing import Dict, Any
os.environ["KMP_DUPLICATE_LIB_OK"] = "TRUE"
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from torch.cuda.amp import GradScaler, autocast
from tqdm import tqdm
import numpy as np

from bus_ceus_dataset import create_data_loaders
from conditional_ddim_model import create_conditional_ddim_model
from metrics import MetricsCalculator, normalize_for_metrics
from visualization import TrainingVisualizer


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='Train BUS to CEUS DDIM model')
    
    # 数据参数
    parser.add_argument('--bus_dir', type=str, default='train_mini/bus',
                       help='BUS images directory')
    parser.add_argument('--ceus_dir', type=str, default='train_mini/ceus',
                       help='CEUS images directory')
    parser.add_argument('--val_bus_dir', type=str, default=None,
                       help='Validation BUS images directory')
    parser.add_argument('--val_ceus_dir', type=str, default=None,
                       help='Validation CEUS images directory')
    
    # 训练参数
    parser.add_argument('--epochs', type=int, default=100,
                       help='Number of training epochs')
    parser.add_argument('--batch_size', type=int, default=4,
                       help='Batch size')
    parser.add_argument('--learning_rate', type=float, default=1e-4,
                       help='Learning rate')
    parser.add_argument('--image_size', type=int, nargs=2, default=[256, 256],
                       help='Image size (height width)')
    parser.add_argument('--num_timesteps', type=int, default=1000,
                       help='Number of diffusion timesteps')
    
    # 模型参数
    parser.add_argument('--in_channels', type=int, default=1,
                       help='Input channels')
    parser.add_argument('--out_channels', type=int, default=1,
                       help='Output channels')
    
    # 保存和加载
    parser.add_argument('--output_dir', type=str, default='outputs',
                       help='Output directory')
    parser.add_argument('--save_every', type=int, default=10,
                       help='Save model every N epochs')
    parser.add_argument('--eval_every', type=int, default=5,
                       help='Evaluate every N epochs')
    parser.add_argument('--vis_every', type=int, default=10,
                       help='Save visualization every N epochs')
    
    # Continue training
    parser.add_argument('--continue_train', type=str, default=None,
                       help='Path to checkpoint to continue training')
    parser.add_argument('--start_epoch', type=int, default=0,
                       help='Starting epoch (for continue training)')
    
    # 其他参数
    parser.add_argument('--num_workers', type=int, default=4,
                       help='Number of data loading workers')
    parser.add_argument('--device', type=str, default='cuda',
                       help='Device to use (cuda/cpu)')
    parser.add_argument('--mixed_precision', action='store_true',
                       help='Use mixed precision training')
    parser.add_argument('--seed', type=int, default=42,
                       help='Random seed')
    
    return parser.parse_args()


def set_seed(seed: int):
    """设置随机种子"""
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    np.random.seed(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False


def save_checkpoint(
    model: nn.Module,
    optimizer: optim.Optimizer,
    scheduler: Any,
    epoch: int,
    train_loss: float,
    val_loss: float,
    metrics: Dict[str, float],
    save_path: str,
    is_best: bool = False
):
    """保存检查点"""
    checkpoint = {
        'epoch': epoch,
        'model_state_dict': model.state_dict(),
        'optimizer_state_dict': optimizer.state_dict(),
        'scheduler_state_dict': scheduler.state_dict() if scheduler else None,
        'train_loss': train_loss,
        'val_loss': val_loss,
        'metrics': metrics,
        'timestamp': datetime.now().isoformat()
    }
    
    torch.save(checkpoint, save_path)
    
    if is_best:
        best_path = save_path.replace('.pth', '_best.pth')
        torch.save(checkpoint, best_path)
        print(f"Best model saved to {best_path}")


def load_checkpoint(
    model: nn.Module,
    optimizer: optim.Optimizer,
    scheduler: Any,
    checkpoint_path: str,
    device: str
) -> Dict[str, Any]:
    """加载检查点"""
    print(f"Loading checkpoint from {checkpoint_path}")
    checkpoint = torch.load(checkpoint_path, map_location=device)
    
    model.load_state_dict(checkpoint['model_state_dict'])
    optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
    
    if scheduler and checkpoint.get('scheduler_state_dict'):
        scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
    
    print(f"Loaded checkpoint from epoch {checkpoint['epoch']}")
    print(f"Train loss: {checkpoint['train_loss']:.6f}")
    print(f"Val loss: {checkpoint['val_loss']:.6f}")
    
    return checkpoint


def train_one_epoch(
    model: nn.Module,
    inferer: Any,
    train_loader: DataLoader,
    optimizer: optim.Optimizer,
    device: str,
    scaler: GradScaler = None,
    metrics_calc: MetricsCalculator = None
) -> Dict[str, float]:
    """训练一个epoch"""
    model.train()
    total_loss = 0.0
    num_batches = len(train_loader)
    
    if metrics_calc:
        metrics_calc.reset()
    
    progress_bar = tqdm(train_loader, desc="Training")
    
    for batch_idx, batch in enumerate(progress_bar):
        bus_images = batch['bus'].to(device)
        ceus_images = batch['ceus'].to(device)
        
        optimizer.zero_grad()
        
        if scaler:
            with autocast():
                # 前向传播
                noise_pred = inferer(
                    inputs=ceus_images,
                    diffusion_model=model,
                    condition=bus_images
                )
                
                # 计算损失（MSE损失）
                noise_target = torch.randn_like(ceus_images)
                loss = nn.functional.mse_loss(noise_pred, noise_target)
            
            # 反向传播
            scaler.scale(loss).backward()
            scaler.step(optimizer)
            scaler.update()
        else:
            # 前向传播
            noise_pred = inferer(
                inputs=ceus_images,
                diffusion_model=model,
                condition=bus_images
            )
            
            # 计算损失
            noise_target = torch.randn_like(ceus_images)
            loss = nn.functional.mse_loss(noise_pred, noise_target)
            
            # 反向传播
            loss.backward()
            optimizer.step()
        
        total_loss += loss.item()
        
        # 更新进度条
        progress_bar.set_postfix({
            'Loss': f'{loss.item():.6f}',
            'Avg Loss': f'{total_loss / (batch_idx + 1):.6f}'
        })
    
    avg_loss = total_loss / num_batches
    
    result = {'loss': avg_loss}
    
    if metrics_calc:
        avg_metrics = metrics_calc.compute()
        result.update(avg_metrics)
    
    return result


def validate_one_epoch(
    model: nn.Module,
    inferer: Any,
    val_loader: DataLoader,
    device: str,
    metrics_calc: MetricsCalculator = None,
    num_inference_steps: int = 50
) -> Dict[str, float]:
    """验证一个epoch"""
    model.eval()
    total_loss = 0.0
    num_batches = len(val_loader)

    if metrics_calc:
        metrics_calc.reset()

    generated_images_list = []
    target_images_list = []
    bus_images_list = []

    progress_bar = tqdm(val_loader, desc="Validation")

    with torch.no_grad():
        for batch_idx, batch in enumerate(progress_bar):
            bus_images = batch['bus'].to(device)
            ceus_images = batch['ceus'].to(device)

            # 计算训练损失（用于监控）
            noise_pred = inferer(
                inputs=ceus_images,
                diffusion_model=model,
                condition=bus_images
            )
            noise_target = torch.randn_like(ceus_images)
            loss = nn.functional.mse_loss(noise_pred, noise_target)
            total_loss += loss.item()

            # 生成图像用于评估
            if batch_idx < 5:  # 只对前几个batch生成图像以节省时间
                # 设置推理步数
                inferer.scheduler.set_timesteps(num_inference_steps)

                # 生成图像
                noise = torch.randn_like(ceus_images)
                generated_images, _ = inferer.sample(
                    input_noise=noise,
                    diffusion_model=model,
                    condition=bus_images,
                    verbose=False
                )

                # 收集图像用于指标计算
                generated_images_list.append(generated_images.cpu())
                target_images_list.append(ceus_images.cpu())
                bus_images_list.append(bus_images.cpu())

            # 更新进度条
            progress_bar.set_postfix({
                'Loss': f'{loss.item():.6f}',
                'Avg Loss': f'{total_loss / (batch_idx + 1):.6f}'
            })

    avg_loss = total_loss / num_batches
    result = {'loss': avg_loss}

    # 计算图像质量指标
    if generated_images_list and metrics_calc:
        all_generated = torch.cat(generated_images_list, dim=0)
        all_targets = torch.cat(target_images_list, dim=0)

        # 归一化图像用于指标计算
        gen_norm, target_norm = normalize_for_metrics(all_generated, all_targets)

        # 计算指标
        batch_metrics = metrics_calc.update(gen_norm, target_norm)
        avg_metrics = metrics_calc.compute()
        result.update(avg_metrics)

        # 返回一些图像用于可视化
        result['sample_images'] = {
            'bus': torch.cat(bus_images_list, dim=0)[:8],
            'target': all_targets[:8],
            'generated': all_generated[:8]
        }

    return result


def main():
    """主训练函数"""
    args = parse_args()

    # 设置随机种子
    set_seed(args.seed)

    # 设置设备
    device = torch.device(args.device if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")

    # 创建输出目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = os.path.join(args.output_dir, f"bus_to_ceus_{timestamp}")
    os.makedirs(output_dir, exist_ok=True)

    # 保存配置
    config_path = os.path.join(output_dir, 'config.json')
    with open(config_path, 'w') as f:
        json.dump(vars(args), f, indent=2)

    # 创建数据加载器
    print("Creating data loaders...")
    train_loader, val_loader = create_data_loaders(
        train_bus_dir=args.bus_dir,
        train_ceus_dir=args.ceus_dir,
        val_bus_dir=args.val_bus_dir,
        val_ceus_dir=args.val_ceus_dir,
        batch_size=args.batch_size,
        image_size=tuple(args.image_size),
        num_workers=args.num_workers
    )

    print(f"Train batches: {len(train_loader)}")
    print(f"Val batches: {len(val_loader)}")

    # 创建模型
    print("Creating model...")
    model, scheduler, inferer = create_conditional_ddim_model(
        image_size=tuple(args.image_size),
        in_channels=args.in_channels,
        out_channels=args.out_channels,
        num_train_timesteps=args.num_timesteps,
        device=device
    )

    print(f"Model parameters: {sum(p.numel() for p in model.parameters()):,}")

    # 创建优化器和学习率调度器
    optimizer = optim.AdamW(model.parameters(), lr=args.learning_rate, weight_decay=1e-4)
    lr_scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=args.epochs)

    # 混合精度训练
    scaler = GradScaler() if args.mixed_precision else None

    # 指标计算器
    train_metrics_calc = MetricsCalculator(data_range=1.0)
    val_metrics_calc = MetricsCalculator(data_range=1.0)

    # 可视化器
    visualizer = TrainingVisualizer(output_dir)

    # 加载检查点（如果指定）
    start_epoch = args.start_epoch
    best_val_loss = float('inf')

    if args.continue_train:
        checkpoint = load_checkpoint(
            model, optimizer, lr_scheduler, args.continue_train, device
        )
        start_epoch = checkpoint['epoch'] + 1
        best_val_loss = checkpoint.get('val_loss', float('inf'))
        print(f"Continuing training from epoch {start_epoch}")

    # 训练循环
    print("Starting training...")
    for epoch in range(start_epoch, args.epochs):
        epoch_start_time = time.time()

        print(f"\nEpoch {epoch + 1}/{args.epochs}")
        print("-" * 50)

        # 训练
        train_results = train_one_epoch(
            model, inferer, train_loader, optimizer, device, scaler, train_metrics_calc
        )

        # 验证
        val_results = validate_one_epoch(
            model, inferer, val_loader, device, val_metrics_calc
        )

        # 更新学习率
        lr_scheduler.step()

        # 记录指标
        train_loss = train_results['loss']
        val_loss = val_results['loss']

        # 提取指标（排除非数值项）
        train_metrics = {k: v for k, v in train_results.items() if k != 'loss' and isinstance(v, (int, float))}
        val_metrics = {k: v for k, v in val_results.items() if k not in ['loss', 'sample_images'] and isinstance(v, (int, float))}

        # 更新可视化器
        visualizer.update_metrics(train_loss, val_loss, train_metrics, val_metrics)

        # 打印结果
        epoch_time = time.time() - epoch_start_time
        print(f"Epoch {epoch + 1} completed in {epoch_time:.2f}s")
        print(f"Train Loss: {train_loss:.6f}")
        print(f"Val Loss: {val_loss:.6f}")

        if val_metrics:
            print("Validation Metrics:")
            for key, value in val_metrics.items():
                print(f"  {key.upper()}: {value:.4f}")

        print(f"Learning Rate: {optimizer.param_groups[0]['lr']:.2e}")

        # 保存可视化
        if (epoch + 1) % args.vis_every == 0 and 'sample_images' in val_results:
            sample_images = val_results['sample_images']
            visualizer.save_comparison(
                sample_images['bus'],
                sample_images['target'],
                sample_images['generated'],
                epoch + 1
            )

        # 保存模型
        is_best = val_loss < best_val_loss
        if is_best:
            best_val_loss = val_loss

        if (epoch + 1) % args.save_every == 0 or is_best:
            checkpoint_path = os.path.join(output_dir, f'checkpoint_epoch_{epoch + 1:04d}.pth')
            save_checkpoint(
                model, optimizer, lr_scheduler, epoch, train_loss, val_loss,
                val_metrics, checkpoint_path, is_best
            )

        # 保存训练进度图
        if (epoch + 1) % args.eval_every == 0:
            visualizer.save_progress_plot()

    # 保存最终模型
    final_checkpoint_path = os.path.join(output_dir, 'final_model.pth')
    save_checkpoint(
        model, optimizer, lr_scheduler, args.epochs - 1, train_loss, val_loss,
        val_metrics, final_checkpoint_path
    )

    # 保存最终的训练进度图
    visualizer.save_progress_plot()

    print(f"\nTraining completed! Results saved to {output_dir}")
    print(f"Best validation loss: {best_val_loss:.6f}")


if __name__ == "__main__":
    main()
