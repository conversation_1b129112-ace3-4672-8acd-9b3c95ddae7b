{"cells": [{"cell_type": "code", "execution_count": null, "id": "6f9b7ad6", "metadata": {}, "outputs": [], "source": ["# Copyright (c) MONAI Consortium\n", "# Licensed under the Apache License, Version 2.0 (the \"License\");\n", "# you may not use this file except in compliance with the License.\n", "# You may obtain a copy of the License at\n", "#     http://www.apache.org/licenses/LICENSE-2.0\n", "# Unless required by applicable law or agreed to in writing, software\n", "# distributed under the License is distributed on an \"AS IS\" BASIS,\n", "# WITHOUT WAR<PERSON><PERSON>IES OR CONDITIONS OF ANY KIND, either express or implied.\n", "# See the License for the specific language governing permissions and\n", "# limitations under the License."]}, {"cell_type": "markdown", "id": "aa69c25f", "metadata": {}, "source": ["# Vector Quantized Variational Autoencoders with MedNIST Dataset\n", "\n", "This tutorial illustrates how to use MONAI for training a Vector Quantized Variational Autoencoder (VQVAE)[1] on 2D images.\n", "\n", "Here, we will train our VQVAE model to be able to reconstruct the input images.  We will work with the MedNIST dataset available on MONAI\n", "(https://docs.monai.io/en/stable/apps.html#monai.apps.MedNISTDataset). In order to train faster, we will select just one of the available classes (\"HeadCT\"), resulting in a training set with 7999 2D images.\n", "\n", "The VQVAE can also be used as a generative model if an autoregressor model (e.g., PixelCNN, Decoder Transformer) is trained on the discrete latent representations of the VQVAE bottleneck. This falls outside of the scope of this tutorial.\n", "\n", "[1] - <PERSON><PERSON> et al. \"Neural Discrete Representation Learning\" https://arxiv.org/abs/1711.00937\n", "\n", "\n", "### Setup environment"]}, {"cell_type": "code", "execution_count": 1, "id": "46909773", "metadata": {"lines_to_next_cell": 2}, "outputs": [], "source": ["!python -c \"import monai\" || pip install -q \"monai-weekly[tqdm]\"\n", "!python -c \"import matplotlib\" || pip install -q matplotlib\n", "%matplotlib inline"]}, {"cell_type": "markdown", "id": "6b8ae5e8", "metadata": {}, "source": ["### Setup imports"]}, {"cell_type": "code", "execution_count": 2, "id": "c1d85fe6", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/media/walter/Storage/Projects/GenerativeModels/venv/lib/python3.10/site-packages/tqdm/auto.py:22: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}, {"name": "stdout", "output_type": "stream", "text": ["2023-02-09 20:43:28,036 - A matching Triton is not available, some optimizations will not be enabled.\n", "Error caught was: No module named 'triton'\n", "MONAI version: 1.2.dev2304\n", "Numpy version: 1.23.5\n", "Pytorch version: 1.13.1+cu117\n", "MONAI flags: HAS_EXT = False, USE_COMPILED = False, USE_META_DICT = False\n", "MONAI rev id: 9a57be5aab9f2c2a134768c0c146399150e247a0\n", "MONAI __file__: /media/walter/Storage/Projects/GenerativeModels/venv/lib/python3.10/site-packages/monai/__init__.py\n", "\n", "Optional dependencies:\n", "Pytorch Ignite version: 0.4.10\n", "ITK version: 5.3.0\n", "Nibabel version: 4.0.2\n", "scikit-image version: 0.19.3\n", "Pillow version: 9.3.0\n", "Tensorboard version: 2.11.0\n", "gdown version: 4.6.0\n", "TorchVision version: 0.14.1+cu117\n", "tqdm version: 4.64.1\n", "lmdb version: 1.4.0\n", "psutil version: 5.9.4\n", "pandas version: 1.5.3\n", "einops version: 0.6.0\n", "transformers version: 4.21.3\n", "mlflow version: 2.1.1\n", "pynrrd version: 1.0.0\n", "\n", "For details about installing the optional dependencies, please visit:\n", "    https://docs.monai.io/en/latest/installation.html#installing-the-recommended-dependencies\n", "\n"]}], "source": ["import os\n", "import shutil\n", "import tempfile\n", "import time\n", "\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "import torch\n", "from monai import transforms\n", "from monai.apps import MedNISTDataset\n", "from monai.config import print_config\n", "from monai.data import DataLoader, Dataset\n", "from monai.utils import first, set_determinism\n", "from torch.nn import L1Loss\n", "from tqdm import tqdm\n", "\n", "from generative.networks.nets import VQVAE\n", "\n", "print_config()"]}, {"cell_type": "code", "execution_count": 3, "id": "f7f7056e", "metadata": {}, "outputs": [], "source": ["# for reproducibility purposes set a seed\n", "set_determinism(42)"]}, {"cell_type": "markdown", "id": "51a9a628", "metadata": {}, "source": ["### Setup a data directory and download dataset"]}, {"cell_type": "markdown", "id": "9b9b6e14", "metadata": {}, "source": ["Specify a `MONAI_DATA_DIRECTORY` variable, where the data will be downloaded. If not\n", "specified a temporary directory will be used."]}, {"cell_type": "code", "execution_count": 4, "id": "4dbb12d8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/tmp/tmphz3r78k8\n"]}], "source": ["directory = os.environ.get(\"MONAI_DATA_DIRECTORY\")\n", "root_dir = tempfile.mkdtemp() if directory is None else directory\n", "print(root_dir)"]}, {"cell_type": "markdown", "id": "049661aa", "metadata": {}, "source": ["### Download the training set"]}, {"cell_type": "code", "execution_count": 5, "id": "1c8522d6", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["MedNIST.tar.gz: 59.0MB [00:03, 16.9MB/s]                                                                                                                                                                                       "]}, {"name": "stdout", "output_type": "stream", "text": ["2023-02-09 20:43:31,731 - INFO - Downloaded: /tmp/tmphz3r78k8/MedNIST.tar.gz\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["2023-02-09 20:43:31,802 - INFO - Verified 'MedNIST.tar.gz', md5: 0bc7306e7427e00ad1c5526a6677552d.\n", "2023-02-09 20:43:31,803 - INFO - Writing into directory: /tmp/tmphz3r78k8.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Loading dataset: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 47164/47164 [00:13<00:00, 3384.84it/s]\n"]}], "source": ["train_data = MedNISTDataset(root_dir=root_dir, section=\"training\", download=True, seed=0)\n", "train_datalist = [{\"image\": item[\"image\"]} for item in train_data.data if item[\"class_name\"] == \"HeadCT\"]\n", "image_size = 64\n", "train_transforms = transforms.Compose(\n", "    [\n", "        transforms.LoadImaged(keys=[\"image\"]),\n", "        transforms.EnsureChannelFirstd(keys=[\"image\"]),\n", "        transforms.ScaleIntensityRanged(keys=[\"image\"], a_min=0.0, a_max=255.0, b_min=0.0, b_max=1.0, clip=True),\n", "        transforms.RandA<PERSON>ed(\n", "            keys=[\"image\"],\n", "            rotate_range=[(-np.pi / 36, np.pi / 36), (-np.pi / 36, np.pi / 36)],\n", "            translate_range=[(-1, 1), (-1, 1)],\n", "            scale_range=[(-0.05, 0.05), (-0.05, 0.05)],\n", "            spatial_size=[image_size, image_size],\n", "            padding_mode=\"zeros\",\n", "            prob=0.5,\n", "        ),\n", "    ]\n", ")\n", "train_ds = Dataset(data=train_datalist, transform=train_transforms)\n", "train_loader = DataLoader(train_ds, batch_size=64, shuffle=True, num_workers=4, persistent_workers=True)"]}, {"cell_type": "markdown", "id": "d437adbd", "metadata": {}, "source": ["### Visualise examples from the training set"]}, {"cell_type": "code", "execution_count": 6, "id": "6c05ca5d", "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 640x480 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Plot 3 examples from the training set\n", "check_data = first(train_loader)\n", "fig, ax = plt.subplots(nrows=1, ncols=3)\n", "for image_n in range(3):\n", "    ax[image_n].imshow(check_data[\"image\"][image_n, 0, :, :], cmap=\"gray\")\n", "    ax[image_n].axis(\"off\")"]}, {"cell_type": "markdown", "id": "8c6ca19a", "metadata": {}, "source": ["### Download the validation set"]}, {"cell_type": "code", "execution_count": 7, "id": "8fef4587", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2023-02-09 20:43:50,332 - INFO - Verified 'MedNIST.tar.gz', md5: 0bc7306e7427e00ad1c5526a6677552d.\n", "2023-02-09 20:43:50,333 - INFO - File exists: /tmp/tmphz3r78k8/MedNIST.tar.gz, skipped downloading.\n", "2023-02-09 20:43:50,333 - INFO - Non-empty folder exists in /tmp/tmphz3r78k8/MedNIST, skipped extracting.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Loading dataset: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 5895/5895 [00:01<00:00, 3360.05it/s]\n"]}], "source": ["val_data = MedNISTDataset(root_dir=root_dir, section=\"validation\", download=True, seed=0)\n", "val_datalist = [{\"image\": item[\"image\"]} for item in val_data.data if item[\"class_name\"] == \"HeadCT\"]\n", "val_transforms = transforms.Compose(\n", "    [\n", "        transforms.LoadImaged(keys=[\"image\"]),\n", "        transforms.EnsureChannelFirstd(keys=[\"image\"]),\n", "        transforms.ScaleIntensityRanged(keys=[\"image\"], a_min=0.0, a_max=255.0, b_min=0.0, b_max=1.0, clip=True),\n", "    ]\n", ")\n", "val_ds = Dataset(data=val_datalist, transform=val_transforms)\n", "val_loader = DataLoader(val_ds, batch_size=64, shuffle=True, num_workers=4, persistent_workers=True)"]}, {"cell_type": "markdown", "id": "1cfa9906", "metadata": {}, "source": ["### Define network, optimizer and losses"]}, {"cell_type": "code", "execution_count": 8, "id": "f9708f30", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Using cuda\n"]}, {"data": {"text/plain": ["VQVAE(\n", "  (encoder): Sequential(\n", "    (0): Convolution(\n", "      (conv): Conv2d(1, 256, kernel_size=(4, 4), stride=(2, 2), padding=(1, 1))\n", "      (adn): ADN(\n", "        (A): ReLU()\n", "      )\n", "    )\n", "    (1): VQVAEResidualUnit(\n", "      (conv1): Convolution(\n", "        (conv): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "        (adn): ADN(\n", "          (D): Dropout(p=0.1, inplace=False)\n", "          (A): ReLU()\n", "        )\n", "      )\n", "      (conv2): Convolution(\n", "        (conv): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "      )\n", "    )\n", "    (2): VQVAEResidualUnit(\n", "      (conv1): Convolution(\n", "        (conv): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "        (adn): ADN(\n", "          (D): Dropout(p=0.1, inplace=False)\n", "          (A): ReLU()\n", "        )\n", "      )\n", "      (conv2): Convolution(\n", "        (conv): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "      )\n", "    )\n", "    (3): Convolution(\n", "      (conv): Conv2d(256, 256, kernel_size=(4, 4), stride=(2, 2), padding=(1, 1))\n", "      (adn): ADN(\n", "        (D): Dropout(p=0.1, inplace=False)\n", "        (A): ReLU()\n", "      )\n", "    )\n", "    (4): VQVAEResidualUnit(\n", "      (conv1): Convolution(\n", "        (conv): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "        (adn): ADN(\n", "          (D): Dropout(p=0.1, inplace=False)\n", "          (A): ReLU()\n", "        )\n", "      )\n", "      (conv2): Convolution(\n", "        (conv): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "      )\n", "    )\n", "    (5): VQVAEResidualUnit(\n", "      (conv1): Convolution(\n", "        (conv): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "        (adn): ADN(\n", "          (D): Dropout(p=0.1, inplace=False)\n", "          (A): ReLU()\n", "        )\n", "      )\n", "      (conv2): Convolution(\n", "        (conv): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "      )\n", "    )\n", "    (6): Convolution(\n", "      (conv): Conv2d(256, 32, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "    )\n", "  )\n", "  (quantizer): VectorQuantizer(\n", "    (quantizer): EMAQuantizer(\n", "      (embedding): Embedding(256, 32)\n", "    )\n", "  )\n", "  (decoder): Sequential(\n", "    (0): Convolution(\n", "      (conv): Conv2d(32, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "    )\n", "    (1): VQVAEResidualUnit(\n", "      (conv1): Convolution(\n", "        (conv): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "        (adn): ADN(\n", "          (D): Dropout(p=0.1, inplace=False)\n", "          (A): ReLU()\n", "        )\n", "      )\n", "      (conv2): Convolution(\n", "        (conv): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "      )\n", "    )\n", "    (2): VQVAEResidualUnit(\n", "      (conv1): Convolution(\n", "        (conv): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "        (adn): ADN(\n", "          (D): Dropout(p=0.1, inplace=False)\n", "          (A): ReLU()\n", "        )\n", "      )\n", "      (conv2): Convolution(\n", "        (conv): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "      )\n", "    )\n", "    (3): Convolution(\n", "      (conv): ConvTranspose2d(256, 256, kernel_size=(4, 4), stride=(2, 2), padding=(1, 1))\n", "      (adn): ADN(\n", "        (D): Dropout(p=0.1, inplace=False)\n", "        (A): ReLU()\n", "      )\n", "    )\n", "    (4): VQVAEResidualUnit(\n", "      (conv1): Convolution(\n", "        (conv): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "        (adn): ADN(\n", "          (D): Dropout(p=0.1, inplace=False)\n", "          (A): ReLU()\n", "        )\n", "      )\n", "      (conv2): Convolution(\n", "        (conv): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "      )\n", "    )\n", "    (5): VQVAEResidualUnit(\n", "      (conv1): Convolution(\n", "        (conv): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "        (adn): ADN(\n", "          (D): Dropout(p=0.1, inplace=False)\n", "          (A): ReLU()\n", "        )\n", "      )\n", "      (conv2): Convolution(\n", "        (conv): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "      )\n", "    )\n", "    (6): Convolution(\n", "      (conv): ConvTranspose2d(256, 1, kernel_size=(4, 4), stride=(2, 2), padding=(1, 1))\n", "    )\n", "  )\n", ")"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "print(f\"Using {device}\")\n", "model = VQVAE(\n", "    spatial_dims=2,\n", "    in_channels=1,\n", "    out_channels=1,\n", "    num_channels=(256, 256),\n", "    num_res_channels=256,\n", "    num_res_layers=2,\n", "    downsample_parameters=((2, 4, 1, 1), (2, 4, 1, 1)),\n", "    upsample_parameters=((2, 4, 1, 1, 0), (2, 4, 1, 1, 0)),\n", "    num_embeddings=256,\n", "    embedding_dim=32,\n", ")\n", "model.to(device)"]}, {"cell_type": "code", "execution_count": 9, "id": "4d74562c", "metadata": {}, "outputs": [], "source": ["optimizer = torch.optim.Adam(params=model.parameters(), lr=1e-4)\n", "l1_loss = L1Loss()"]}, {"cell_type": "markdown", "id": "331aa4fc", "metadata": {}, "source": ["### Model training\n", "Here, we are training our model for 100 epochs (training time: ~60 minutes)."]}, {"cell_type": "code", "execution_count": 10, "id": "0b06b603", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Epoch 0: 100%|█████████████████| 125/125 [00:30<00:00,  4.07it/s, recons_loss=0.088, quantization_loss=1.2e-5]\n", "Epoch 1: 100%|███████████████| 125/125 [00:30<00:00,  4.10it/s, recons_loss=0.0402, quantization_loss=1.08e-5]\n", "Epoch 2: 100%|███████████████| 125/125 [00:30<00:00,  4.06it/s, recons_loss=0.0333, quantization_loss=1.02e-5]\n", "Epoch 3: 100%|███████████████| 125/125 [00:31<00:00,  4.01it/s, recons_loss=0.0292, quantization_loss=9.15e-6]\n", "Epoch 4: 100%|███████████████| 125/125 [00:31<00:00,  3.99it/s, recons_loss=0.0274, quantization_loss=8.31e-6]\n", "Epoch 5: 100%|███████████████| 125/125 [00:31<00:00,  3.95it/s, recons_loss=0.0264, quantization_loss=9.04e-6]\n", "Epoch 6: 100%|█████████████████| 125/125 [00:31<00:00,  3.97it/s, recons_loss=0.025, quantization_loss=9.8e-6]\n", "Epoch 7: 100%|███████████████| 125/125 [00:31<00:00,  3.95it/s, recons_loss=0.0242, quantization_loss=9.38e-6]\n", "Epoch 8: 100%|███████████████| 125/125 [00:31<00:00,  3.92it/s, recons_loss=0.0238, quantization_loss=1.17e-5]\n", "Epoch 9: 100%|███████████████| 125/125 [00:32<00:00,  3.88it/s, recons_loss=0.0232, quantization_loss=1.28e-5]\n", "Epoch 10: 100%|██████████████| 125/125 [00:32<00:00,  3.88it/s, recons_loss=0.0227, quantization_loss=1.15e-5]\n", "Epoch 11: 100%|██████████████| 125/125 [00:32<00:00,  3.87it/s, recons_loss=0.0227, quantization_loss=1.38e-5]\n", "Epoch 12: 100%|██████████████| 125/125 [00:32<00:00,  3.84it/s, recons_loss=0.0219, quantization_loss=1.46e-5]\n", "Epoch 13: 100%|██████████████| 125/125 [00:32<00:00,  3.84it/s, recons_loss=0.0212, quantization_loss=1.62e-5]\n", "Epoch 14: 100%|███████████████| 125/125 [00:32<00:00,  3.83it/s, recons_loss=0.021, quantization_loss=1.52e-5]\n", "Epoch 15: 100%|██████████████| 125/125 [00:32<00:00,  3.84it/s, recons_loss=0.0206, quantization_loss=1.57e-5]\n", "Epoch 16: 100%|██████████████| 125/125 [00:32<00:00,  3.82it/s, recons_loss=0.0209, quantization_loss=1.85e-5]\n", "Epoch 17: 100%|██████████████| 125/125 [00:32<00:00,  3.85it/s, recons_loss=0.0201, quantization_loss=1.83e-5]\n", "Epoch 18: 100%|██████████████| 125/125 [00:32<00:00,  3.87it/s, recons_loss=0.0204, quantization_loss=1.83e-5]\n", "Epoch 19: 100%|██████████████| 125/125 [00:32<00:00,  3.88it/s, recons_loss=0.0203, quantization_loss=1.89e-5]\n", "Epoch 20: 100%|██████████████| 125/125 [00:32<00:00,  3.86it/s, recons_loss=0.0201, quantization_loss=1.87e-5]\n", "Epoch 21: 100%|██████████████| 125/125 [00:32<00:00,  3.89it/s, recons_loss=0.0197, quantization_loss=1.85e-5]\n", "Epoch 22: 100%|██████████████| 125/125 [00:32<00:00,  3.87it/s, recons_loss=0.0197, quantization_loss=1.76e-5]\n", "Epoch 23: 100%|███████████████| 125/125 [00:32<00:00,  3.88it/s, recons_loss=0.0193, quantization_loss=1.9e-5]\n", "Epoch 24: 100%|███████████████| 125/125 [00:32<00:00,  3.88it/s, recons_loss=0.019, quantization_loss=1.99e-5]\n", "Epoch 25: 100%|██████████████| 125/125 [00:32<00:00,  3.87it/s, recons_loss=0.0189, quantization_loss=1.99e-5]\n", "Epoch 26: 100%|█████████████████| 125/125 [00:32<00:00,  3.89it/s, recons_loss=0.0193, quantization_loss=2e-5]\n", "Epoch 27: 100%|██████████████| 125/125 [00:32<00:00,  3.88it/s, recons_loss=0.0191, quantization_loss=1.97e-5]\n", "Epoch 28: 100%|██████████████| 125/125 [00:32<00:00,  3.90it/s, recons_loss=0.0191, quantization_loss=2.03e-5]\n", "Epoch 29: 100%|██████████████| 125/125 [00:32<00:00,  3.89it/s, recons_loss=0.0192, quantization_loss=1.76e-5]\n", "Epoch 30: 100%|██████████████| 125/125 [00:32<00:00,  3.88it/s, recons_loss=0.0183, quantization_loss=2.37e-5]\n", "Epoch 31: 100%|█████████████████| 125/125 [00:32<00:00,  3.89it/s, recons_loss=0.0182, quantization_loss=2e-5]\n", "Epoch 32: 100%|██████████████| 125/125 [00:32<00:00,  3.88it/s, recons_loss=0.0182, quantization_loss=2.02e-5]\n", "Epoch 33: 100%|██████████████| 125/125 [00:32<00:00,  3.89it/s, recons_loss=0.0186, quantization_loss=2.06e-5]\n", "Epoch 34: 100%|██████████████| 125/125 [00:32<00:00,  3.89it/s, recons_loss=0.0187, quantization_loss=1.92e-5]\n", "Epoch 35: 100%|██████████████| 125/125 [00:32<00:00,  3.86it/s, recons_loss=0.0182, quantization_loss=2.33e-5]\n", "Epoch 36: 100%|██████████████| 125/125 [00:32<00:00,  3.89it/s, recons_loss=0.0177, quantization_loss=2.37e-5]\n", "Epoch 37: 100%|██████████████| 125/125 [00:32<00:00,  3.89it/s, recons_loss=0.0178, quantization_loss=2.46e-5]\n", "Epoch 38: 100%|██████████████| 125/125 [00:32<00:00,  3.88it/s, recons_loss=0.0177, quantization_loss=2.37e-5]\n", "Epoch 39: 100%|██████████████| 125/125 [00:32<00:00,  3.89it/s, recons_loss=0.0181, quantization_loss=2.09e-5]\n", "Epoch 40: 100%|██████████████| 125/125 [00:32<00:00,  3.89it/s, recons_loss=0.0187, quantization_loss=1.99e-5]\n", "Epoch 41: 100%|███████████████| 125/125 [00:32<00:00,  3.88it/s, recons_loss=0.018, quantization_loss=2.24e-5]\n", "Epoch 42: 100%|██████████████| 125/125 [00:32<00:00,  3.88it/s, recons_loss=0.0174, quantization_loss=2.13e-5]\n", "Epoch 43: 100%|██████████████| 125/125 [00:32<00:00,  3.90it/s, recons_loss=0.0174, quantization_loss=2.02e-5]\n", "Epoch 44: 100%|██████████████| 125/125 [00:32<00:00,  3.88it/s, recons_loss=0.0174, quantization_loss=2.29e-5]\n", "Epoch 45: 100%|██████████████| 125/125 [00:32<00:00,  3.87it/s, recons_loss=0.0175, quantization_loss=2.22e-5]\n", "Epoch 46: 100%|████████████████| 125/125 [00:32<00:00,  3.89it/s, recons_loss=0.018, quantization_loss=1.8e-5]\n", "Epoch 47: 100%|███████████████| 125/125 [00:32<00:00,  3.88it/s, recons_loss=0.0177, quantization_loss=2.4e-5]\n", "Epoch 48: 100%|███████████████| 125/125 [00:32<00:00,  3.88it/s, recons_loss=0.0172, quantization_loss=2.3e-5]\n", "Epoch 49: 100%|███████████████| 125/125 [00:32<00:00,  3.89it/s, recons_loss=0.017, quantization_loss=2.13e-5]\n", "Epoch 50: 100%|███████████████| 125/125 [00:32<00:00,  3.87it/s, recons_loss=0.017, quantization_loss=1.89e-5]\n", "Epoch 51: 100%|██████████████| 125/125 [00:32<00:00,  3.89it/s, recons_loss=0.0171, quantization_loss=2.11e-5]\n", "Epoch 52: 100%|███████████████| 125/125 [00:32<00:00,  3.88it/s, recons_loss=0.018, quantization_loss=1.88e-5]\n", "Epoch 53: 100%|██████████████| 125/125 [00:32<00:00,  3.88it/s, recons_loss=0.0174, quantization_loss=1.82e-5]\n", "Epoch 54: 100%|███████████████| 125/125 [00:32<00:00,  3.88it/s, recons_loss=0.017, quantization_loss=2.51e-5]\n", "Epoch 55: 100%|██████████████| 125/125 [00:32<00:00,  3.81it/s, recons_loss=0.0168, quantization_loss=1.95e-5]\n", "Epoch 56: 100%|██████████████| 125/125 [00:32<00:00,  3.88it/s, recons_loss=0.0168, quantization_loss=1.96e-5]\n", "Epoch 57: 100%|███████████████| 125/125 [00:32<00:00,  3.89it/s, recons_loss=0.0172, quantization_loss=1.6e-5]\n", "Epoch 58: 100%|██████████████| 125/125 [00:32<00:00,  3.89it/s, recons_loss=0.0173, quantization_loss=2.46e-5]\n", "Epoch 59: 100%|███████████████| 125/125 [00:32<00:00,  3.88it/s, recons_loss=0.0166, quantization_loss=2.5e-5]\n", "Epoch 60: 100%|██████████████| 125/125 [00:32<00:00,  3.88it/s, recons_loss=0.0166, quantization_loss=2.11e-5]\n", "Epoch 61: 100%|██████████████| 125/125 [00:32<00:00,  3.89it/s, recons_loss=0.0165, quantization_loss=2.52e-5]\n", "Epoch 62: 100%|██████████████| 125/125 [00:32<00:00,  3.89it/s, recons_loss=0.0166, quantization_loss=2.23e-5]\n", "Epoch 63: 100%|██████████████| 125/125 [00:32<00:00,  3.87it/s, recons_loss=0.0172, quantization_loss=1.92e-5]\n", "Epoch 64: 100%|██████████████| 125/125 [00:32<00:00,  3.89it/s, recons_loss=0.0172, quantization_loss=2.12e-5]\n", "Epoch 65: 100%|██████████████| 125/125 [00:32<00:00,  3.86it/s, recons_loss=0.0168, quantization_loss=2.25e-5]\n", "Epoch 66: 100%|██████████████| 125/125 [00:32<00:00,  3.88it/s, recons_loss=0.0168, quantization_loss=2.27e-5]\n", "Epoch 67: 100%|██████████████| 125/125 [00:32<00:00,  3.88it/s, recons_loss=0.0164, quantization_loss=2.07e-5]\n", "Epoch 68: 100%|██████████████| 125/125 [00:32<00:00,  3.89it/s, recons_loss=0.0162, quantization_loss=2.12e-5]\n", "Epoch 69: 100%|██████████████| 125/125 [00:32<00:00,  3.89it/s, recons_loss=0.0162, quantization_loss=2.33e-5]\n", "Epoch 70: 100%|███████████████| 125/125 [00:32<00:00,  3.88it/s, recons_loss=0.0162, quantization_loss=2.5e-5]\n", "Epoch 71: 100%|██████████████| 125/125 [00:32<00:00,  3.89it/s, recons_loss=0.0168, quantization_loss=2.34e-5]\n", "Epoch 72: 100%|██████████████| 125/125 [00:32<00:00,  3.89it/s, recons_loss=0.0171, quantization_loss=2.01e-5]\n", "Epoch 73: 100%|██████████████| 125/125 [00:32<00:00,  3.89it/s, recons_loss=0.0166, quantization_loss=2.05e-5]\n", "Epoch 74: 100%|██████████████| 125/125 [00:32<00:00,  3.89it/s, recons_loss=0.0165, quantization_loss=2.36e-5]\n", "Epoch 75: 100%|██████████████| 125/125 [00:32<00:00,  3.89it/s, recons_loss=0.0161, quantization_loss=1.96e-5]\n", "Epoch 76: 100%|██████████████| 125/125 [00:32<00:00,  3.89it/s, recons_loss=0.0161, quantization_loss=2.22e-5]\n", "Epoch 77: 100%|██████████████| 125/125 [00:32<00:00,  3.88it/s, recons_loss=0.0166, quantization_loss=2.06e-5]\n", "Epoch 78: 100%|██████████████| 125/125 [00:32<00:00,  3.89it/s, recons_loss=0.0161, quantization_loss=2.05e-5]\n", "Epoch 79: 100%|██████████████| 125/125 [00:32<00:00,  3.89it/s, recons_loss=0.0161, quantization_loss=1.79e-5]\n", "Epoch 80: 100%|██████████████| 125/125 [00:32<00:00,  3.87it/s, recons_loss=0.0162, quantization_loss=2.33e-5]\n", "Epoch 81: 100%|██████████████| 125/125 [00:32<00:00,  3.89it/s, recons_loss=0.0163, quantization_loss=1.92e-5]\n", "Epoch 82: 100%|██████████████| 125/125 [00:32<00:00,  3.89it/s, recons_loss=0.0162, quantization_loss=2.08e-5]\n", "Epoch 83: 100%|██████████████| 125/125 [00:32<00:00,  3.89it/s, recons_loss=0.0162, quantization_loss=2.08e-5]\n", "Epoch 84: 100%|███████████████| 125/125 [00:32<00:00,  3.88it/s, recons_loss=0.0162, quantization_loss=1.9e-5]\n", "Epoch 85: 100%|██████████████| 125/125 [00:32<00:00,  3.89it/s, recons_loss=0.0158, quantization_loss=1.92e-5]\n", "Epoch 86: 100%|██████████████| 125/125 [00:32<00:00,  3.89it/s, recons_loss=0.0157, quantization_loss=1.78e-5]\n", "Epoch 87: 100%|███████████████| 125/125 [00:32<00:00,  3.89it/s, recons_loss=0.0157, quantization_loss=2.1e-5]\n", "Epoch 88: 100%|██████████████| 125/125 [00:32<00:00,  3.89it/s, recons_loss=0.0157, quantization_loss=1.92e-5]\n", "Epoch 89: 100%|██████████████| 125/125 [00:32<00:00,  3.89it/s, recons_loss=0.0158, quantization_loss=1.94e-5]\n", "Epoch 90: 100%|██████████████| 125/125 [00:32<00:00,  3.90it/s, recons_loss=0.0164, quantization_loss=1.93e-5]\n", "Epoch 91: 100%|██████████████| 125/125 [00:32<00:00,  3.89it/s, recons_loss=0.0164, quantization_loss=1.94e-5]\n", "Epoch 92: 100%|██████████████| 125/125 [00:32<00:00,  3.90it/s, recons_loss=0.0162, quantization_loss=2.09e-5]\n", "Epoch 93: 100%|██████████████| 125/125 [00:32<00:00,  3.90it/s, recons_loss=0.0156, quantization_loss=1.86e-5]\n", "Epoch 94: 100%|██████████████| 125/125 [00:32<00:00,  3.90it/s, recons_loss=0.0155, quantization_loss=2.16e-5]\n", "Epoch 95: 100%|██████████████| 125/125 [00:32<00:00,  3.86it/s, recons_loss=0.0158, quantization_loss=2.12e-5]\n", "Epoch 96: 100%|███████████████| 125/125 [00:32<00:00,  3.89it/s, recons_loss=0.016, quantization_loss=1.94e-5]\n", "Epoch 97: 100%|██████████████| 125/125 [00:32<00:00,  3.90it/s, recons_loss=0.0162, quantization_loss=2.08e-5]\n", "Epoch 98: 100%|██████████████| 125/125 [00:32<00:00,  3.90it/s, recons_loss=0.0157, quantization_loss=2.28e-5]\n", "Epoch 99: 100%|██████████████| 125/125 [00:32<00:00,  3.89it/s, recons_loss=0.0153, quantization_loss=2.18e-5]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["train completed, total time: 3323.38680434227.\n"]}], "source": ["n_epochs = 100\n", "val_interval = 10\n", "epoch_recon_loss_list = []\n", "epoch_quant_loss_list = []\n", "val_recon_epoch_loss_list = []\n", "intermediary_images = []\n", "n_example_images = 4\n", "\n", "total_start = time.time()\n", "for epoch in range(n_epochs):\n", "    model.train()\n", "    epoch_loss = 0\n", "    progress_bar = tqdm(enumerate(train_loader), total=len(train_loader), ncols=110)\n", "    progress_bar.set_description(f\"Epoch {epoch}\")\n", "    for step, batch in progress_bar:\n", "        images = batch[\"image\"].to(device)\n", "        optimizer.zero_grad(set_to_none=True)\n", "\n", "        # model outputs reconstruction and the quantization error\n", "        reconstruction, quantization_loss = model(images=images)\n", "\n", "        recons_loss = l1_loss(reconstruction.float(), images.float())\n", "\n", "        loss = recons_loss + quantization_loss\n", "\n", "        loss.backward()\n", "        optimizer.step()\n", "\n", "        epoch_loss += recons_loss.item()\n", "\n", "        progress_bar.set_postfix(\n", "            {\"recons_loss\": epoch_loss / (step + 1), \"quantization_loss\": quantization_loss.item() / (step + 1)}\n", "        )\n", "    epoch_recon_loss_list.append(epoch_loss / (step + 1))\n", "    epoch_quant_loss_list.append(quantization_loss.item() / (step + 1))\n", "\n", "    if (epoch + 1) % val_interval == 0:\n", "        model.eval()\n", "        val_loss = 0\n", "        with torch.no_grad():\n", "            for val_step, batch in enumerate(val_loader, start=1):\n", "                images = batch[\"image\"].to(device)\n", "\n", "                reconstruction, quantization_loss = model(images=images)\n", "\n", "                # get the first sample from the first validation batch for\n", "                # visualizing how the training evolves\n", "                if val_step == 1:\n", "                    intermediary_images.append(reconstruction[:n_example_images, 0])\n", "\n", "                recons_loss = l1_loss(reconstruction.float(), images.float())\n", "\n", "                val_loss += recons_loss.item()\n", "\n", "        val_loss /= val_step\n", "        val_recon_epoch_loss_list.append(val_loss)\n", "\n", "total_time = time.time() - total_start\n", "print(f\"train completed, total time: {total_time}.\")"]}, {"cell_type": "markdown", "id": "ab3f5e08", "metadata": {}, "source": ["### Learning curves"]}, {"cell_type": "code", "execution_count": 11, "id": "8eb14535", "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.style.use(\"ggplot\")\n", "plt.title(\"Learning Curves\", fontsize=20)\n", "plt.plot(np.linspace(1, n_epochs, n_epochs), epoch_recon_loss_list, color=\"C0\", linewidth=2.0, label=\"Train\")\n", "plt.plot(\n", "    np.linspace(val_interval, n_epochs, int(n_epochs / val_interval)),\n", "    val_recon_epoch_loss_list,\n", "    color=\"C1\",\n", "    linewidth=2.0,\n", "    label=\"Validation\",\n", ")\n", "plt.yticks(fontsize=12)\n", "plt.xticks(fontsize=12)\n", "plt.xlabel(\"Epochs\", fontsize=16)\n", "plt.ylabel(\"Loss\", fontsize=16)\n", "plt.legend(prop={\"size\": 14})\n", "plt.show()"]}, {"cell_type": "markdown", "id": "e7c7b3b4", "metadata": {}, "source": ["###  Plotting  evolution of reconstructed images"]}, {"cell_type": "code", "execution_count": 12, "id": "383495dc", "metadata": {"lines_to_next_cell": 2}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 1850x3050 with 10 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Plot every evaluation as a new line and example as columns\n", "val_samples = np.linspace(val_interval, n_epochs, int(n_epochs / val_interval))\n", "fig, ax = plt.subplots(nrows=len(val_samples), ncols=1, sharey=True)\n", "fig.set_size_inches(18.5, 30.5)\n", "for image_n in range(len(val_samples)):\n", "    reconstructions = torch.reshape(intermediary_images[image_n], (64 * n_example_images, 64)).T\n", "    ax[image_n].imshow(reconstructions.cpu(), cmap=\"gray\")\n", "    ax[image_n].set_xticks([])\n", "    ax[image_n].set_yticks([])\n", "    ax[image_n].set_ylabel(f\"Epoch {val_samples[image_n]:.0f}\")"]}, {"cell_type": "markdown", "id": "517f51ea", "metadata": {}, "source": ["### Plotting the reconstructions from final trained model"]}, {"cell_type": "code", "execution_count": 13, "id": "c58a2f5e", "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 640x480 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, ax = plt.subplots(nrows=1, ncols=2)\n", "ax[0].imshow(images[0, 0].detach().cpu(), vmin=0, vmax=1, cmap=\"gray\")\n", "ax[0].axis(\"off\")\n", "ax[0].title.set_text(\"Inputted Image\")\n", "ax[1].imshow(reconstruction[0, 0].detach().cpu(), vmin=0, vmax=1, cmap=\"gray\")\n", "ax[1].axis(\"off\")\n", "ax[1].title.set_text(\"Reconstruction\")\n", "plt.show()"]}, {"cell_type": "markdown", "id": "222c56d3", "metadata": {}, "source": ["### Cleanup data directory\n", "\n", "Remove directory if a temporary was used."]}, {"cell_type": "code", "execution_count": 14, "id": "4737aed9", "metadata": {}, "outputs": [], "source": ["if directory is None:\n", "    shutil.rmtree(root_dir)"]}], "metadata": {"jupytext": {"cell_metadata_filter": "-all", "formats": "auto:light,ipynb", "main_language": "python", "notebook_metadata_filter": "-all"}, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.6"}}, "nbformat": 4, "nbformat_minor": 5}