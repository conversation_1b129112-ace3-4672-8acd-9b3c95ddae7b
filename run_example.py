"""
BUS到CEUS图像转换的简单运行示例
"""

import os
import subprocess
import sys


def check_dependencies():
    """检查依赖项"""
    required_packages = [
        'torch', 'torchvision', 'numpy', 'matplotlib', 
        'tqdm', 'pillow', 'monai'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("Missing required packages:")
        for package in missing_packages:
            print(f"  - {package}")
        print("\nPlease install missing packages using:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True


def check_data():
    """检查数据目录"""
    bus_dir = "train_mini/bus"
    ceus_dir = "train_mini/ceus"
    
    if not os.path.exists(bus_dir):
        print(f"BUS data directory not found: {bus_dir}")
        return False
    
    if not os.path.exists(ceus_dir):
        print(f"CEUS data directory not found: {ceus_dir}")
        return False
    
    bus_files = len([f for f in os.listdir(bus_dir) if f.endswith('.png')])
    ceus_files = len([f for f in os.listdir(ceus_dir) if f.endswith('.png')])
    
    print(f"Found {bus_files} BUS images and {ceus_files} CEUS images")
    
    if bus_files == 0 or ceus_files == 0:
        print("No image files found in data directories")
        return False
    
    return True


def run_training():
    """运行训练"""
    print("Starting training...")
    
    cmd = [
        sys.executable, "train_bus_to_ceus.py",
        "--bus_dir", "train_mini/bus",
        "--ceus_dir", "train_mini/ceus",
        "--epochs", "50",
        "--batch_size", "4",
        "--learning_rate", "1e-4",
        "--image_size", "256", "256",
        "--save_every", "10",
        "--eval_every", "5",
        "--vis_every", "10",
        "--output_dir", "outputs",
        "--mixed_precision"
    ]
    
    try:
        subprocess.run(cmd, check=True)
        print("Training completed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"Training failed with error: {e}")
        return False


def run_testing():
    """运行测试"""
    print("Starting testing...")
    
    # 查找最新的检查点
    output_dirs = [d for d in os.listdir("outputs") if d.startswith("bus_to_ceus_")]
    if not output_dirs:
        print("No training output found. Please run training first.")
        return False
    
    latest_dir = os.path.join("outputs", sorted(output_dirs)[-1])
    
    # 查找最佳模型
    best_checkpoint = None
    for file in os.listdir(latest_dir):
        if file.endswith("_best.pth"):
            best_checkpoint = os.path.join(latest_dir, file)
            break
    
    if not best_checkpoint:
        # 查找最新的检查点
        checkpoints = [f for f in os.listdir(latest_dir) if f.startswith("checkpoint_")]
        if checkpoints:
            best_checkpoint = os.path.join(latest_dir, sorted(checkpoints)[-1])
    
    if not best_checkpoint:
        print("No checkpoint found for testing")
        return False
    
    print(f"Using checkpoint: {best_checkpoint}")
    
    cmd = [
        sys.executable, "test_bus_to_ceus.py",
        "--checkpoint", best_checkpoint,
        "--bus_dir", "train_mini/bus",
        "--ceus_dir", "train_mini/ceus",
        "--batch_size", "4",
        "--num_inference_steps", "50",
        "--output_dir", "test_results",
        "--save_images",
        "--save_comparisons",
        "--num_samples", "16"
    ]
    
    try:
        subprocess.run(cmd, check=True)
        print("Testing completed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"Testing failed with error: {e}")
        return False


def main():
    """主函数"""
    print("BUS to CEUS Image Translation with DDIM")
    print("=" * 50)
    
    # 检查依赖项
    print("1. Checking dependencies...")
    if not check_dependencies():
        return
    print("✓ All dependencies are available")
    
    # 检查数据
    print("\n2. Checking data...")
    if not check_data():
        return
    print("✓ Data directories are ready")
    
    # 询问用户要执行的操作
    print("\n3. What would you like to do?")
    print("   1) Train the model")
    print("   2) Test the model (requires trained model)")
    print("   3) Train and then test")
    print("   4) Exit")
    
    while True:
        choice = input("\nEnter your choice (1-4): ").strip()
        
        if choice == "1":
            run_training()
            break
        elif choice == "2":
            run_testing()
            break
        elif choice == "3":
            if run_training():
                run_testing()
            break
        elif choice == "4":
            print("Exiting...")
            break
        else:
            print("Invalid choice. Please enter 1, 2, 3, or 4.")


if __name__ == "__main__":
    main()
