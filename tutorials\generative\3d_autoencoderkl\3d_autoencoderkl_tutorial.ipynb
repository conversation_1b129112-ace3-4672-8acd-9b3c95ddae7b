{"cells": [{"cell_type": "code", "execution_count": null, "id": "8a7e6369", "metadata": {}, "outputs": [], "source": ["# Copyright (c) MONAI Consortium\n", "# Licensed under the Apache License, Version 2.0 (the \"License\");\n", "# you may not use this file except in compliance with the License.\n", "# You may obtain a copy of the License at\n", "#     http://www.apache.org/licenses/LICENSE-2.0\n", "# Unless required by applicable law or agreed to in writing, software\n", "# distributed under the License is distributed on an \"AS IS\" BASIS,\n", "# WITHOUT WAR<PERSON><PERSON>IES OR CONDITIONS OF ANY KIND, either express or implied.\n", "# See the License for the specific language governing permissions and\n", "# limitations under the License."]}, {"cell_type": "markdown", "id": "eca65c39", "metadata": {"lines_to_next_cell": 2}, "source": ["# 3D AutoencoderKL\n", "\n", "This demo is a toy example of how to use MONAI's AutoencoderKL. In particular, it uses the Autoencoder with a Kullback-Leibler regularisation as implemented by R<PERSON><PERSON> et. al [1].\n", "\n", "[1] <PERSON><PERSON><PERSON> et. al \"High-Resolution Image Synthesis with Latent Diffusion Models\" https://arxiv.org/pdf/2112.10752.pdf\n", "\n", "This tutorial was based on:\n", "\n", "[Brain tumor 3D segmentation with MONAI](https://github.com/Project-MONAI/tutorials/blob/main/3d_segmentation/brats_segmentation_3d.ipynb)"]}, {"cell_type": "code", "execution_count": null, "id": "0f82c364", "metadata": {}, "outputs": [], "source": ["!python -c \"import monai\" || pip install -q \"monai-weekly[tqdm, nibabel]\"\n", "!python -c \"import matplotlib\" || pip install -q matplotlib\n", "%matplotlib inline"]}, {"cell_type": "markdown", "id": "7325d9ae", "metadata": {}, "source": ["## Setup imports"]}, {"cell_type": "code", "execution_count": 2, "id": "a44e7a6e", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/miniconda3/envs/genmodels/lib/python3.9/site-packages/tqdm/auto.py:22: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}, {"name": "stdout", "output_type": "stream", "text": ["MONAI version: 1.1.dev2239\n", "Numpy version: 1.23.4\n", "Pytorch version: 1.13.0\n", "MONAI flags: HAS_EXT = False, USE_COMPILED = False, USE_META_DICT = False\n", "MONAI rev id: 13b24fa92b9d98bd0dc6d5cdcb52504fd09e297b\n", "MONAI __file__: /home/<USER>/miniconda3/envs/genmodels/lib/python3.9/site-packages/monai/__init__.py\n", "\n", "Optional dependencies:\n", "Pytorch Ignite version: NOT INSTALLED or UNKNOWN VERSION.\n", "Nibabel version: 4.0.2\n", "scikit-image version: NOT INSTALLED or UNKNOWN VERSION.\n", "Pillow version: 9.2.0\n", "Tensorboard version: NOT INSTALLED or UNKNOWN VERSION.\n", "gdown version: NOT INSTALLED or UNKNOWN VERSION.\n", "TorchVision version: 0.14.0\n", "tqdm version: 4.64.1\n", "lmdb version: NOT INSTALLED or UNKNOWN VERSION.\n", "psutil version: 5.9.4\n", "pandas version: NOT INSTALLED or UNKNOWN VERSION.\n", "einops version: 0.6.0\n", "transformers version: NOT INSTALLED or UNKNOWN VERSION.\n", "mlflow version: NOT INSTALLED or UNKNOWN VERSION.\n", "pynrrd version: NOT INSTALLED or UNKNOWN VERSION.\n", "\n", "For details about installing the optional dependencies, please visit:\n", "    https://docs.monai.io/en/latest/installation.html#installing-the-recommended-dependencies\n", "\n"]}], "source": ["import os\n", "import shutil\n", "import tempfile\n", "\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "import torch\n", "import torch.nn.functional as F\n", "from monai import transforms\n", "from monai.apps import DecathlonDataset\n", "from monai.config import print_config\n", "from monai.data import DataLoader\n", "from monai.networks.layers import Act\n", "from monai.utils import first, set_determinism\n", "from torch.cuda.amp import autocast\n", "from tqdm import tqdm\n", "\n", "from generative.losses import PatchAdversarialLoss, PerceptualLoss\n", "from generative.networks.nets import AutoencoderK<PERSON>, PatchDiscriminator\n", "\n", "print_config()"]}, {"cell_type": "code", "execution_count": 3, "id": "1aaa77a6", "metadata": {}, "outputs": [], "source": ["# for reproducibility purposes set a seed\n", "set_determinism(42)"]}, {"cell_type": "markdown", "id": "72bae2d5", "metadata": {}, "source": ["## Setup a data directory and download dataset\n", "\n", "Specify a `MONAI_DATA_DIRECTORY` variable, where the data will be downloaded. If not specified a temporary directory will be used."]}, {"cell_type": "code", "execution_count": 4, "id": "48155dfa", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/tmp/tmpyxyg6wxs\n"]}], "source": ["directory = os.environ.get(\"MONAI_DATA_DIRECTORY\")\n", "root_dir = tempfile.mkdtemp() if directory is None else directory\n", "print(root_dir)"]}, {"cell_type": "markdown", "id": "319bff04", "metadata": {}, "source": ["## Download the training set"]}, {"cell_type": "markdown", "id": "053fdee1", "metadata": {}, "source": ["Note: The DecatholonDataset has 7GB. So make sure that you have enought space when running the next line"]}, {"cell_type": "code", "execution_count": 5, "id": "1dbaf6af", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/miniconda3/envs/genmodels/lib/python3.9/site-packages/monai/utils/deprecate_utils.py:107: FutureWarning: <class 'monai.transforms.utility.array.AddChannel'>: Class `AddChannel` has been deprecated since version 0.8. please use MetaTensor data type and monai.transforms.EnsureChannelFirst instead.\n", "  warn_deprecated(obj, msg, warning_category)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["2022-12-08 20:46:51,956 - INFO - Verified 'Task01_BrainTumour.tar', md5: 240a19d752f0d9e9101544901065d872.\n", "2022-12-08 20:46:51,958 - INFO - File exists: /tmp/tmpyxyg6wxs/Task01_BrainTumour.tar, skipped downloading.\n", "2022-12-08 20:46:51,959 - INFO - Non-empty folder exists in /tmp/tmpyxyg6wxs/Task01_BrainTumour, skipped extracting.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Loading dataset: 100%|██████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 388/388 [02:39<00:00,  2.43it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Image shape (1, 96, 96, 64)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["channel = 0  # 0 = Flair\n", "assert channel in [0, 1, 2, 3], \"Choose a valid channel\"\n", "\n", "train_transforms = transforms.Compose(\n", "    [\n", "        transforms.LoadImaged(keys=[\"image\"]),\n", "        transforms.EnsureChannelFirstd(keys=[\"image\"]),\n", "        transforms.Lambdad(keys=\"image\", func=lambda x: x[channel, :, :, :]),\n", "        transforms.EnsureChannelFirstd(keys=[\"image\"], channel_dim=\"no_channel\"),\n", "        transforms.EnsureTyped(keys=[\"image\"]),\n", "        transforms.Orientationd(keys=[\"image\"], axcodes=\"RAS\"),\n", "        transforms.Spacingd(keys=[\"image\"], pixdim=(2.4, 2.4, 2.2), mode=(\"bilinear\")),\n", "        transforms.CenterSpatialCropd(keys=[\"image\"], roi_size=(96, 96, 64)),\n", "        transforms.ScaleIntensityRangePercentilesd(keys=\"image\", lower=0, upper=99.5, b_min=0, b_max=1),\n", "    ]\n", ")\n", "train_ds = DecathlonDataset(\n", "    root_dir=root_dir,\n", "    task=\"Task01_BrainTumour\",\n", "    section=\"training\",\n", "    cache_rate=1.0,  # you may need a few Gb of RAM... Set to 0 otherwise\n", "    num_workers=4,\n", "    download=True,\n", "    seed=0,\n", "    transform=train_transforms,\n", ")\n", "train_loader = DataLoader(train_ds, batch_size=2, shuffle=True, num_workers=4, persistent_workers=True)\n", "print(f'Image shape {train_ds[0][\"image\"].shape}')"]}, {"cell_type": "markdown", "id": "617a46a9", "metadata": {}, "source": ["## Visualise examples from the training set"]}, {"cell_type": "code", "execution_count": 6, "id": "8902c0a4", "metadata": {}, "outputs": [{"data": {"text/plain": ["<matplotlib.image.AxesImage at 0x7f555f9f39d0>"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAgMAAAClCAYAAADBAf6NAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjYuMiwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8o6BhiAAAACXBIWXMAAA9hAAAPYQGoP6dpAABqeklEQVR4nO29WY+k6XEdfHLf96y1q6u7p1eSI49FDkcSTEOCAAG6MWyAEHzjH2JAf0PwX/CFYQi6kmTTpgFbJmVSnBnOkOzpZXqpPfd9X76L+k7ked+uIUcz0921PAdodHdV5ptZlfHGcyLiRERguVwu4eDg4ODg4HBlEXzbb8DBwcHBwcHh7cKRAQcHBwcHhysORwYcHBwcHByuOBwZcHBwcHBwuOJwZMDBwcHBweGKw5EBBwcHBweHKw5HBhwcHBwcHK44HBlwcHBwcHC44gh/2QcGAoHX+T4crgjcjCsHBweH8weXGXBwcHBwcLjicGTAwcHBwcHhisORAQcHBwcHhysORwYcHBwcHByuOBwZcHBwcHBwuOJwZMDBwcHBweGKw5EBBwcHBweHKw5HBhwcHBwcHK44HBlwcHBwcHC44nBkwMHBwcHB4YrDkQEHBwcHB4crDkcGHBwcHBwcrjgcGXBwcHBwcLjicGTAwcHBwcHhisORAQcHBwcHhysORwYcHBwcHByuOBwZcHBwcHBwuOJwZMDBwcHBweGKw5EBBwcHBweHKw5HBhwcHBwcHK44HBlwcHBwcHC44nBkwMHBwcHB4YrDkQEHBwcHB4crjvDbfgMODq8bgUDgbb8Fh0uA5XL5xl/T2a7DN4EvY7suM+Dg4ODg4HDF4ciAg4ODg4PDFYcjAw4ODg4ODlccTjPwGhEIBKzmx39rDTAYDHq+HggE7Gv8e7lcYj6fo9frvZWapYODg4PD5YcjA68J0WgU4XAYkUgEkUjE/h2LxRAKhRAKhZBIJBCPx5FKpRCNRhGJRJBOp5FOpxGPx5HJZDAajXB8fIz/+l//K0ajEebz+dv+0RwuGUg6HRxeN5ytnV9cOTLw4MED5HI5JJNJpNNpBAIBTKdTzGYzhEIhZLNZM9ZQKITZbIb5fI7FYoH5fI7lconFYoFgMIhgMIhwOIxGo4F2u41bt25huVzaczS652PD4bBF+7FYDLFYDOl02r4Xj8cRj8cRi8WQSCQwHA4RCoXwgx/8AAcHB2g0Gjg5OcFisXA3lcNXAm0tHA6jUCggm82i3++j1+uh2+1iOp1iuVw6+3L4SgiHwxbc0AemUinEYjGkUik8efIE/X7/d14nFAqhWCwiGo0CAA4ODiyDGovFzFcGg0F0u110Op3X/aNdalwZMhAKhRCNRnHr1i1sbm4in8+jXC4jGAxiNBrZobu9vY3FYgHg1Kgnkwlms5kRBhKCYDBo0f2zZ89weHiIP/zDP8RyucR4PMZoNDKHOplMjDyEQiHM53OMRiO7aUgG+B55I8XjcUQiEYRCIbz77rt2A/T7fYzHY0ynU5cpcDgTgUAA0WgUoVDIvkYim0qlkEgkEIlE7N/xeNyyWalUCuPxGIPBAOPxGMvlEoFAAIPBwAiClracDV5taGmTfox/syzKr0UiERSLRUQiEbTb7VcIZzAYRCKRMB+YTCYBAIvFAvF43F4nmUwiHo+bHUciEUSjUQwGAwQCAYRCIUQiEUwmEwwGA0/w5Gz3bFwJMhCNRrG5uYnvfOc7ePDgAfL5PLLZLDKZDACg3++jUqkAODVGgkbOf9O4F4uFRVbqcPn1RCKBYDBoGQAlAsFgEPP53JMxCIVCiMViVkbgTZNOp+1rk8kE+Xweg8EAd+/exbNnz3B0dIQXL1684d+mw3kHnWE+n0cikTByu1wuMRqNsL6+jkKhgEgkYiS3UCggk8kgn8/jwYMHaLfbODg4QK1Ws+seHh5iNpsBgNlyMBjEeDy213C4emDAEg6HzafO53PLqNIPzmYzLBYLXLt2zbJQ/gxnOBzG2tqaZQRGo5Flq3K5nNlcKpWyzEAikUCpVMJ8Psf+/j4CgQDi8Tjy+TxarRZevnyJyWSCxWKBxWJhZdpQKITRaORs9//HpSYD0WgUyWQS77zzDjY2NnD37l1sbGwgmUzaobtcLhGNRpFKpQAA8Xgc0+nUSgGhUAjL5dITYc1mM8RiMUSjUSQSCcRiMQDAcDhENptFOp1GIpGwksF0OvW8r8VigWg0iul0aoSCbJjsNxqNIpPJmMHPZjNEIhEsl0usra1hfX0dL168wN7enjPmK45AIGAR13e+8x3cvn0bf/qnf4qtrS3E43HUajU8f/4cx8fH2N/fR71eR7vdxnA4tGu8ePECt2/fxvvvv49IJIJvf/vbyOfzAIBisYidnR0Mh0O0Wi0cHBzgww8/RKvVQr/fRzAYRK1Ww89//vO39BtweFsIBAIoFouezCajbmZVeRBPp1N0Oh17zIMHD7C3t4d+v28ROn1mq9XylE4TiQSm06llQweDAYbDoSfICgaDRkzot/P5PIrFIgBgMBig2WxiNptZ1pa+3uEKkIF8Po9vf/vb2NzcxNbWFvL5PMLhsBnscrm0dCkPYY3olQTwedPp1NJUWrcajUbI5XJ2rcVi4bkZyIL57/F4jGAwaCSAOgF9T4vFwm6qTCaDaDSKGzduoFAoIJ1O4+/+7u/s+g5XAzz8E4kENjc3LTIPBoPY3t7G3bt38f3vfx/ZbBahUAilUskyTdFo1LQqtOFAIIAf//jHCAaDJmAtl8vY3t5GIpFAsVjE9vY2JpMJWq0WCoUCRqMRqtUqKpWKvf7169fR7XYxn88RCoUwHo8xm83M+TpcHgQCActg8iBmJpVZAtVHETzsmepPp9MIBoOYTCYAYIc4swgsPfBvYJWx1QOd/jwSiQA4zUxMp1PP+6N/VV+cSqUwmUzMZpkJvopi7UtNBuLxODY3N/Enf/In2N7etsN1Op2i3+9jNBpZlE+VPw9jGifFKzSmcDiM8XiMTCZjXQBM57fbbUvBxuNxuxFGoxGm06nV+VmDHQ6HltJKp9PmoBeLBWKxGIrFohl7OBzG5uYmstksCoUCcrkcQqEQvve97+Hx48dW5nC43KCzzeVyeOedd/DDH/4QBwcHOD4+xkcffYT19XVcv34dN27cQLPZRK/XQyQSQblcNh0Ky01/+Id/iK2tLUQiEfzsZz/DbDbDcDjEvXv3UC6XLQPFSI4ZrbW1Nbz77rs4Pj5GOBzGkydPkEql8Od//uf45JNP0Ov1kE6ncXR0hG63i16vZ87d4XKAwUomk8F4PLbDVCP1bDaLRCKBVCqF2WyG0WiEVquFVqtlgdL6+jqWyyV6vR6A1SFOKHmgD+a/GWjx4F4sFkYSAoGAXRNY6QSoReDjwuGw+eZEImGEgxmLq4RLSwbW1tZQKBQQDodx8+ZN7OzsIBwOm3Maj8cePQDTSzRmMlTt/6fAiqUF6gUY2StoVOxI0C4DYOXUeW2CrNXfvaAZCr7fRCKBa9euodVqYTgcotvtvu5fq8NbRD6fx87ODn7wgx/g93//97GxsYH19XXU63WcnJwgl8shGo2i0WigWq1iOBxiMplY5M7OlVwu5xFyLZdL3Lp1y0gts1ShUMiiJtZuKbZliYH3WDQaxe7uLt5//32EQiHT4fR6PfT7ffy///f/8PLlS7Rarbf7S3T4WqBvZEZpOBxahoB+kG3R29vbHqE2CcFkMjFhHwMkBl9aWp3NZuj3+xgOhxiPx+Z/tZzK55BcDAYDuz71LQTfg3aCMdvK+4DXGQ6HiEajVq4A8Eq597LhUpKBQCCAjY0Ni7avXbuG69evW2Qyn8+tDYUpJ84CIAGgAJDf13YspvHJMFnz11SoX2FL0SAPfh0sRJZKAqDlBQDGePl9Xisej+PatWuoVCpot9tuMNElBe3z29/+Nu7evYv33nsPv/d7v2eCKpLY69evYzabYTweo9PpmK2Q+IbDYUuVxuNxI5jz+dy0L1Rh007plBeLBfr9vjlaRmmZTAaZTAaRSASbm5u4ffs2IpEIKpUK0um0lQr6/T6i0Sg+/vhjVza4wGDwo/NS4vG4hxAw05rJZJDNZq1MxSwnxYS0J/V5JJ69Xs86smiDauuqE2BXAEu1zMjST/pJA8sODPDoyzV7RVJM/VcgEECr1brUGoNLSwZu3LiBYrGIVCrl+aC1RZAHPr/unyeg0OidBkajYOqVGQNei0bIuqk6QSUGbEfkawOnBKDb7XrqZdQZMLrKZrO4f/8+RqMRAoEAjo+PX2HDDhcfsVgMhUIBf/mXf4nd3V20Wi1EIhGzmeVyiUQige3tbVQqFUwmExwcHFjLKqMqtm2xrtrtdj1tg8wI0LnP53MMh0NL3TK9GwwGkc/nkU6nkUqlMBgMkEgk8O677yKZTFq0x0xFoVDAt771LTx//hz/8T/+R3S7XSO6DhcLFDYnk0krASSTSYvo6QOZudQhavSZzIr6B641m010Oh2cnJygWq2i1Wp5Zr5o66J2KVBXNZ1O0ev1PNkwYNUJRqKr+iwlJartms1mSCaT2NrawsbGBgDgk08+QaPRwGAweGu//9eJS0cGyPauX7+OUqlkRkimSCKg0b+K+vTfOjpYGaYSAwBm2EzfayuXpvxVkOJnlyQkfK1gMGj/pyCH2QKdcVAqlbC+vo5ms2lDihwhuPhgJ0w4HMadO3fw53/+51hbWzONCjMA0+nUorRgMIiNjQ0Eg0FUq1UrDUwmEzv0p9OppfhpLzzsG40GgsEg/vRP/xTxeBzz+Rz1et2yACylUQ+j12cKlildHgqJRML+LJdL/Lt/9+/w8ccfY29vDycnJ2/5t+zwZcHBQayrM2JnmQCARfXD4RD9fh/5fN5Eqcyy0h9qyh5YtSfysE4kEhiPx3Zwa9rfL0qk32b2i6/BKJ6EgWdDqVRCOp22YVv9fh/NZhODwcDeE7PAACyLnM1mMRwOravhsuHSkYFIJIJsNosbN26gXC5bZK3RuTJGf3oegCczoLoBEgdVs5LlkmmyG4HPJQngH80G8Pl8Tb4+dQYaPfFG4tcY5ZXLZWxsbKDdbptQx5GBiw2ti+bzedy6dQsffPCBdanoMKzJZOIpV8ViMQQCAau1kgDQGaqAFThthx0MBubYte+bGStNxyrh5XVyuZzZOg8JirO0Bz2fz+Pdd9+16Epru+Px+G3+yh2+BJg1opCaBJS2pAco8Kr+iZ8zM5nUWTF1z8yCHsYs3zIbwMdraVWjeWCVCSBhoF1ycmEul0M+n0epVEKn07EygN5XvAfm87kJCfUec2TgnCMQCOAP/uAP8Bd/8RfY3t72tP1RpDIej61OxQNZ+03Vqel1lZXSGdLYmG3I5XKmduU1lAjw+rw2jdo/14Cvo2RAswTq/DkkJpPJ4Kc//an1kWsPucPFwvr6umWz3n//fXzve9/DrVu3zGFShEXnSodcLpfNafL5VPIT0+kUxWLRUvh7e3t4+vSpdR6EQiHs7e1hbW3N5nGwP5xOczKZWN83ywuAV/wFwJz4ZDKx+uvt27dxcHAAAPje976Hvb09HB0d4dGjR05HcI5Bf5PL5VAqlazsRHLoT8UnEglsbW0hl8shl8tZOv7o6AgvX760VumdnR0UCgVsbm6aTWt2UzUCSmRJLmhvqjsAVsREhx0lk0kUi0Xs7u5ifX0dOzs7ll2bTqcYjUYYDAY4PDzE8+fP0Ww20Ww2PZkG3hPaqXBZcGnIAJ3htWvXcPPmTSMBejCzrqSiKC0N0KA0OlLmy9cB4CESfC5rrf4xl3q9s/QImvbS8oT2z85mM/s6uwvIdKnWLRQK6HQ6GA6HJqRxuFhgS1Y+n7da++bmpsdmNXJnFomgfTFC4wENwKIxRk3j8dgi9kwmY3qC//t//y9u376N+/fvW0p4OBxa1MaMg1+cyOwVe8h1XgejwXK5jO9973u4fv06Xr58iWKxiG9961t499138dlnn+HZs2eesccO5wPL5RKDwcDsjTMqWD5iep+TAROJhLUWRiIR863D4dDEgcwGtVot1Go1TymX5Ybj42MbbjWdTj1ZA51nwPeomVx+jddkaYB6B+5KINkYj8c2FXE0GlkGhOSbkxCB05IB78nLIoi9VGSAXQPXrl2zTAAJAMemamZAW/eUaSqY1v8inYFG/yQgvB5TWVoC0DIFHS0JCACPo2etd7lc2rX5XkhSKNBKp9MolUoW4TUajTfye3f45kCdwPXr17GxsYGtrS3s7Owgl8sZMfTbHktgADztp/pYEk2SY7VZ7iJgCSKVSuHo6MiiNk4hpC4gEAhgNBpZHVgzbHx9klQA9tp8/6lUCjdu3EA+n0ev17NJnjdv3gRwOuxlb2/vUqu2Lxro96gJoG0BsBo9CSE7VZLJpGdUO3Up1BOwPXU2m1kGV22JWa96vY5er2dklF0JvDZJqB/8mpYQWMbV96MDuDjPpdFooFgs2jUGg4GJBnkt6gd4vlyG8uylIANsmYpGoyiXy3jw4AEODw+t/77T6Vh6kzMG/EuHWH/VdhRN5wMrZ6p1Kj3Ymb5XoR9LFDQWRlKcY8AShb/VkK/PjAAJDUkChS5c5sGZ3gcHBxfeKK8iQqEQ7t27h3/1r/4V/sN/+A9Ip9M2pCUYDFq0TNuhrQKwSJ09/9SW+JdZLZenuwmAVZ02FAohnU5ja2sLmUwGa2trJkisVqsW4bNEQfT7fZtqyFStamr8dq8CLz7vwYMHaDabGI/H+Pa3v4133nkHf/Znf4a/+qu/wuHhoSO05wQa6WtrNABPdogtsOovx+Ox+eBGo4Hj42NUq1V0u12zW15LpwUStF92zOiETEbuHBrEw1l9uUbsg8EA1WrVMhsvXrzA+vo6stks1tbWbChdMpnE2tqaBVj8eXkfDgYDHB0doVarWZns+PgYtVrtQhPYS0EGCEY4dGZMZbJ+TsekkTrwquqfaSdtM2Tkw9GZKiJk6mo6nRoT5iHOg1znavPxmsJSgQxvCGW3PPwp6uJ7YbvhYrFAOp22FLCSGYfzDw6Q+v3f/31T6zMjRAKgAj1Cy1w6xY1T4bT8xP8zQmOqNxKJIJfL2fRCbn7z11z1nmHWSmdz+P8wuqd9BwIBy8zNZjNEo1Fr2+J2ulQqhd/7vd8DAGsTc3h7YCthJpMxeyDZpA5ANxKq4E/FozygB4MBIpGI7Qzggc6BQjo3gEOEGLwxc0a75XOYichms/ZYVfxrSRcAms0mGo0Glssljo+PkclkUCqVkM1mjUyz7LG2tuYJ/Pr9vpVLSqUSJpMJksmkTfz8yU9+cmHHGF8KMkBnVCwWkU6nPUrT2WzmcZIaUQErhqsfOK+n3QWshfpBxsj1mcpW+R7oAOkc+Rw6aGYG9Ho6rIgHAFN1wEpYQ6cNwPrKz8puOJxfhEIhZDIZG9rD7JISUy0f0bb066xz6p4KjdSV7GrtVddl5/N5bGxs2Ghr1oe1RQtYTXIjSFKB1f1EMLvF96CkmM49Go3a/RGLxXDnzh1Uq1W8ePHC87M6vFlo6j+dTttnoV1ZqmFRv6s+SMkC9xFQX8DgjbV71UtNp1Nr+RuNRggGg57pmCpY1IwZ7ctPBji6mNfs9Xpot9tIJBK2tTOZTKJQKGBjY8OGJqnAUOcj8OeLx+M26Ovx48fodrtGxi8SLgUZ2NzcxK1bt3Dv3j1kMhk0Gg0T0TGCAbyOlKIRdXKM1DmURTMDTHWSUPgjt2AwiE6nYwauugS2UY3HY2POFKbQyGjMwKvrkpkN4I0yGo2MRfM1QqEQksmk3Rhra2vo9XqXUvV6mcC5Ad///vdtep862V6v5zmEAdjnzX/TDnSWOg9f2j7JhQoFaYeLxemAK93TQbvsdrtmm1q60hHbKuTi6zIbx/Icd8ozeozFYp56LocXTadTj4Cy3+87MvCWQH85HA7NJ9KOeNjrch8VYWuKXoknW/pICmiLOs6d9qxDtTTDy/ITbYnX4HU4EZO+kHbL7pdarWazBdrtNlqtFg4PD7FcLpHNZvGd73zHViin02m0Wi1MJhO0223LInATbiaTMUHkcDjEH/3RH+G//bf/hh/96Ed4+fLlhSobXAoyEI/HUS6XUSqVEIvFzKmoSFAZKwAPaz1Lje3vFmAJgFEbU1SaOuNhT2eqsw1Un6BCLr2BtFzhz0L4v88WRmYVqIYFYEybabaLZJBXBezPf/DgAd577z3cvn0bOzs7iEaj1i6ohI+p2bNKWcAq+tHWVBJfHejCuuhgMMDJyYkJUx8/foxisegpK7BrRVP9hM4FYHbhrJ9R/62dPfxeJpNBLBZDvV5HpVJBs9lEJpPB7du3MZ/PUalUTOfg8PoRDAZRLBbt849EIhYxM8OjwZASAf2aivs0lZ9Kpazuz+ia3SbMDOiEQU3588AFVoRYgyCeAywlaJeLCg3z+byJFpPJpK02ZgcBiUomk0E+n7csyHA4RCKRsO+zOyGRSNhZUyqV8OzZM3z66ac4ODi4UL730pABpnYSiYS1oWgEox0DgLdl0N+Gog6Rf1MnwGvRuEgmmDGg4XGTluoTtBeXNWD/HAIVY6m4hoSEDJmiQYpn5vPTaXHUDpRKJWuVcQNdzh+CwSDW19fxwQcf4N/+23/rIZeajmTk7tcB6KFPhTVthg6bToxCU04E5LKuZ8+emSN98eIFbt++/YoegeO1VSTGaN/fUaNDYxjpaYeBPo5f4/3SaDQsUrtz5w7u3buHYrGIf/iHf7D7z+Gbh/pAakBKpZJ9TizlxGIxj/iUn73+308G2HbIpW48fHWcsR70tC0ObtPZMCQC+njg9D7iSGPasrZn65ZCkgOWppiZ6Pf7CAaD6Ha7Rlaok8hkMubLOXabHRP6c9Gnr62t4datW9jd3b1wezguBRlgy8uTJ08QCoWwublpCzWYsqdjJLtVMsAPkl/T/QWpVArL5RL1et0ew8EYvJ6KXPh+dAEHSQC1BXTeSjyAlVCQ16IQh9ekU26321aiYEai3+/j5cuXGI/HZszcy6ClEoe3j1AohFQqhR/+8If4oz/6I7z77rtotVrodDr2N/dp6G4NHp4kkrQVqrBV8EdbZBaAqVk+Zz6f44MPPkA2mzW9S7lcxsHBAXK5nPVhB4NBI9dn2RDvF7bxktRoh4yKY2m3dMbNZhOHh4dIJpP41re+hXg8bina5XKJZDKJv//7v8ff/u3fXrga7EUA25Kz2SwqlYq1+/EQn0wm6HQ6ntkmJJeaOQVW2SjaoPpAEg3aSSBwOiWTnQXcDshyAidpdjod23zJ4VXJZBLb29t2MK+trSGbzSKdTmMwGKDf76PRaJif9AdhFCBmMhmk02lrU+TQuFQqZUEX5w5wnguJwGQyQaPRsEFdOu9gd3cX/+bf/Bv88pe/tDkJFwGXggwwaj45OcHm5qY5LbJTPZT96S2yRY161Onwe5yTrWI+XktbFVnnUvasW700XaUKV4KpMeBV7QCzBnw9FThOp1P0+31LIbOWls1m3RCXcwb29t+9exeFQsGzpErFT8PhENls1myUpR/N9Kgz9gu4/OJBPo691qy1hkIhlMtlIwZ+ESDvC81waRaC7wOA597Rg4P/14lyzLCx/5sEhPdrKBTCzs4Orl+/jp2dHRwdHXns3uGrg22ATHtHo1Gsra1hPp+bqj4QCKDdbtvMfgDmjzQyTiQSAE5tgC2D1Hr4WxB5QPP6JAbZbNauSXvg13VEMJ+n2S4upGO2lO+LuhT+vJp5oN3zWlpOm8/nphPQMmsoFDItWrVaNVJdKpVQKpWQz+dtH0MymcS7776LYDD4yhTQ84pLQQbY4tLpdLC1tWVpItaq6Gz97Xs0Ds0UsPbOQ5VjN0ejkdVb6ZA4xEgjMaYztb5Kp8v2KTpWJQ1nlTJUnMObSUfR6s1BNbnW0orFIsLhMCqVinOg5wjMWN2+fRu5XM7Ee9pTzUlt4/HY2qYYkfhrtv7Pllkngg5uMpnYTA5GXnwspxCm02kA8ER0/rKZqse1DKCiRe2a0ftL28goWEwkEqZpYC83uw2KxSJu3LiBu3fvotPpWITo8PUQDAYtsqYtrK+v24FKu6LPoa9itjKVSpnQc2Njw7RUjIRZg1cSOh6P0Wg00Ol07PPO5/MWtHB6YSqV8uhlSB5Vi6Ur6Lmqnoc835t2jvF+Uc2KBlokF/yZj4+PEYlEcHx8bHMWisUiRqMRRqMRPv/8cxweHqLX6+HevXu4e/cubty4YVmFUCiE9957D/1+Hy9evHhl5sF5xKUhA6PRCJVKBdVq1ZgYIywaEA2AhqiHMJ2YRlKqhtX+WV3GoZGKir3IloFVGyDgdbJ8PX85gE6RfzPzQQebyWSMRXOTGCPN58+f4+TkBP1+H91uF81m89wb4VUDU+S0o8lkYort7e1tDAYDS1EyU3R0dGROkmuCWdNXlb+/3RQ4dYS0ByXBzAjkcjkAq+2Yh4eHnm4Btmvx0KZz1usDsDKFgveX9o9zOEwkErEDg6DTB1YliO3tbfzxH/8xMpkMHj9+jE8//fT1fDBXCKFQyMRx7XbbPs/FYmECQmZAWbJZW1tDoVBAsVjE+vq6R+fCKFqn+VEgSLuKRqPIZrPI5XJ2wCqxpI/UyaysyeuCIE4K1IOdgZlf/O23V+pPtCuMPysPepbrmAW4c+cOtre38Z3vfMdIBwd08e9cLmc/L8+jTqeDZDKJu3fv4rPPPvOsVT6PuBRkgB/waDRCu91GtVr1pNapQKXxkhCc1St71rWXy6VnUxeJBADP81Qt7Z/VTqhY0M9QNcrzt+2oOItpMI3yeJDUajUcHR2ZIfMGcTgfIPnT+ulyufQIVIGzU/MayfsjDc0U+G0HWNmxLrCiWNEfDapG4CzxqwpbNdryg+/H76BVNOv/3WgWgiURHly3b99Gq9WyTIfDV0codLoCnT5QNUlMjfsn+WlXANX6y+XSspXMPPF7aov0W9Qz8ZDX16etaUqdAtqzgjO/HZC88g9LELzf9H7QM0Czcgy4WKrI5XLWqkhtGM8bllp43zCrq0Eosyj5fN6Eu+cVl4IMkImxFekXv/iFrVWdTCbGVpmSJEvV5S/ASlmrNVEaJpWnLBtomp+Po8OmToBslCRCjVUjfTVW7SmnoVEgCaxWNLNeRkcOnJKdo6MjPH36FL1ez4zX4fxAtSMqPtKeah3Cw/JWJpMx56edKqpdIVSlr90ojMr1dThOlc51NptZBwojK4qqdEIcsCLC2v2iZMFPAIDVgc8/qvYGVk6a2pxer2cdMvfv30elUkE0GnWLuL4GSOxSqRRGo9Erh6cSPM5C8ZNCCqlHo5FlYllGoI/S7BEA65DRhW6At1MK8A5eY3aCX6c96TAu1SZQN9Xr9WxGBt8TbZll21gs5ulW4M+YSqVsa6fuKOh0Op5ldwz0NLPADIXeu6lUCltbW6YfOK+48GSAyuVWq2W1no8++gi7u7sWCXEGQaFQMDKgfdqMzvxRFY0ROO1N1fGadGaM2LXEMJ1OLXpnqlM1AWTgwOrQ1zICoVmBXq9nylcSEooXaeQUshQKBfuZ3Crj84NQKITd3V0bqEOBU7fbtcONDg84HdHLz1E7VHTKoI6p5tdJBHQGBaO6SCRiaXoe/qoYJwEATg95Os10Ou2pv5KYkDT4IzdgpY3pdDqWvdK2SGBl7zxgSAL4/FqtBgC2kKtUKmF9fd3t4PgaoDZEO1J0ac/a2pr5smaziX6/b6Sh3W7b3/SjPOC1y8Uv3KPvpCaLuixN02t7Nf1vJpMB4N0LQ1tnMMfXIUFhuyG7rTRoU00L7YzahVwu5xFWxmIxC7x4T1HL8/jxY+zt7dm+hdu3b1vLN8nr7du3sbm5aUHZL37xC1vffR5xYckAnRyVr3RKo9EItVrNtq0Nh0OkUik7kBmlnzX0h9C0Pg3T/zyNvlTMxwNa61R05jR0jer8rwd4+3Y1XQd4Iye+FqcgUjyTy+U8e7oZaZ7netVVQj6fx/Xr101XouI7YJUm1VKUpiDP0rjQHtiSqgeyvkY8Hrd7gaNgg8HT6Zk8mFOpFEKhkGUN2HqlRIM2z1Sovn9/+UJJM6NH7dxRIaT+LNrpw5ox+9P9HQ8OXw78nfLgpB/i8qhCoWCt2QCsXY8lAM0KAKtJl1pyJXiAqxAbgCczpIcz/63lWJZn+Tq0JRJaf9u0khEtQag+KxAImN3zoNfWQ7alswxLIsPgbj6fW5A3Go1sXDLJBTMgOlSJXRvnGReWDDBauX//PtrtNur1urUw8YPhwc20/Gw2MxGUGq1uXNNWE43GdJiF1vtjsRiGwyHG4zHW19fRaDRQq9XM6GgwNFj//AEAnmsC3pkCTO32ej3b1AWs0r79ft/qbxzleuPGDQyHQxSLRXS7XRPEnOcU1VXAcnm6E35jYwMffPAB8vm8pRP5x18G0JS5DjBRAkqoTkYJAR/LVq1g8HQo0fb2tg0HOjk5QSKRwGQywe3btzEej9Hr9Yx00wFSPFWv1+0w4WtpCYH3Cu8rklnVTCQSCfuZ+XX+nvh+i8WiPY9T69Lp9CsE2uHLQbUnWqZi7/7Gxgbu3r1rhzDbCmezmUdUx6xTIBDw2CsPfS2HMTXPg5SBm05m1ZKp7izgoQ2sysHMZFHwpwEWCYLOgtHyBZcPkfBSjM0yMO83JSkK/r5IVtT2lUTwbNDttefdZi8kGYjH47hx4wZ2d3dtU990OrXxkWtra1hfX7c0P0dG0in6xUcajaiAi06MRkzHSaJBBsgaFbMRzWbTSg00fo2MNEKikajD1mwAbwDu02b2gYSBr8ubM51OW2qq0+kgGo3ivffew+PHj/Ho0aM39yE5nAm2fO7u7tpsCNYiNRpSFT9txh9RAd46qmaSTk5OsLa2hkQiYWRU662BQMBmqgeDQWxsbNhBcXJy4skyMFqiY6VTY4lDO2L0HuJ7JxHQLgc/+VYnqyrvTCZjj+GCGqcV+GpIJpPY2dl5ZTdFOBw2VbyWQ/WzITFT/8i1wWwjZPBCkjCbzaw0BXh3HbBOz3Q8BwH5dSl8LJ9HhEIhEyOqX+dWQWqmeL9oQMazgrbMJUmasVKywp+JvpyDvrLZLJbLpZVvSfa1DVyXfJ130euFIwOBQACpVAqFQgHr6+umiObmqXK5jO3tbWuX4hQ3HtoafRNnpTcJVfGrUai+gHW3ZDJpfdJ6bVXlflF6U50jnS4ZuaZZ9fGLxWrWgZYmuLmRh8j169dRr9ctvevwdhAKhXD9+nVks1nPga4lK0ZIKtLTrAFJoD/y1giG5SOmSdUWtEavNsmBWsCpA/aTVH5PD3fCT6b95Q0ArxwstG/tetCfW5+nREFLCw5fHvwdqliT9X5mWRmFs6TIA52/a5YI+FkCsM+Q19dgRzOy9DvMeHK8sK49VptTUaO/tMoIXMsQeo8QtFW+noq32QrJbCmzqwAsC6zXITngtTSbpeXjs8g6ACu1nGdcODIQCoVw48YNi8I5XGI0GmF3dxfXrl3DrVu3PB8eRSi6KlgjLnVeWvfijaEpMR7M6sRYCqCClhP/NEWqYzj9BqyvDXgJhJ90cCQnv87tWwBsKA2jKDr5nZ0dtFotfP7556jVak478JYQi8Xwwx/+ENFoFM+ePUO5XDYHpAe96gD4OatgipEznZCmH/XfJKU6650bL0migVXEriSU16YNUhXN6IeHCkkEiYdG+/waNQdMnQKw+4Kvw/ek2/FoyzywtOzn8M8DsyqdTsdq5fl83qYPMgPDeS089DXdf1YXCTOmzGpxXTt9r5+0ctXvZDJBv983u9Aout1ue7Jl1Ikwa+HvaFHhIEtJXLrFbod6vW7ZC07g7PV6WC6XVk5gu2UikbD7iLMwmJngH4p+Vfio5Q4SFmbXDg8Pz/1Y4gtJBkqlktV8mKoBgPX1dZRKJWQyGU/tlaptOlNG+SrCUvZJqNHxelobDYVCqFarmM9PV8MOh0Msl6ez14fDoafepXVhPt8vnlEywPoXb2LWevn+yF6Hw6Hd4Eo8OBwDOFUPb25uYn193QbcuJbDNw/W3BeL030Bh4eH1sfM+qfWQKnm52erOwpU2a+kga/DdDwjIdocBxj1+33cuXPHY8ssQYXDYbRaLdTrdQwGA2xubuLu3buechfvFaaUCSWabEdU8RUPeGA1YTMYDNpyJl6Lh4uKvBjR+bNkDr8bqvgHYF0eg8EAwWAQz54982QJWPtWYR/FdGxr1m4E2hkPSKbSqehncMLSWLfbtVHH8XgcjUbDOr1IPDmYi++F71tLXbR5EmgdWATA3kM6nTZBNf0fbZwLk3Qpl5Ym+D673a6dG5wdsL29bWdOKpVCv9/3aH9oryyBnGec73fngw7Zobo4lUrZ9/P5vKW7mBKiY6TzVGGhkgE1ID3A6Sx1IIWmtFqtFhaL04mDZJixWMxa/rQsoFEXU510rH5ioOnQcDhsoim9GYLBoNXtaPRkqqqSzeVyNjeb79eRgTcLpmQBmEPs9Xqe/n3Au6+Czk4jZzo7f/qe0HQ8sFrJrdcZjUYmBmNkCKzU3yTNFHox06TjZXmv6AGuRJrtrpoGBk7Jiaqy9f7ivaC2q0RH23kdvjzos1SVTx/AAGaxWBhhGI/H9tnRNwLwaEi0i4DlJiVrnBK4XJ4um6LQVHUxJCcMjCiY1WyE6heYjfJnBvRnVAEr/aR/rgy/D8DIKXVZy+XSdDy8T5jiZ+cM3xs7t3SnB0ssvH8YnPE9nmdcKDJAYWCtVkOhUMD169ct2mUPMtkk06iEptWDwaClcJgeUnIArJimP+VJ46ZjPT4+Ri6XQ7FYRLVaxWw2Qz6ft2idJQpC6728KXUmvb4WGWU0GsXW1pZdQ9XiXP25XC49Q0Q4jIgaglKphO3tbUsNc7a3w5tBqVTC9evXMZlM0G630Wg0TKmtDlqdJqMvFToBsB0Zk8nkldSjP0pfLBaeFa6lUgm1Wg3NZhOff/457t69i62tLVQqFSPZT58+RSAQwPb2NjY3Nz0LX+j8z3LMzFxRpU1HnE6n7ZBgpM9+cx46qn1gCxZTxtT89Pt9S9O6zMCXAw9J3Sap5VH9N30SI/h4PG5RbzKZtINyMBjYXBcdwKPXoB3zgARWOzny+bzZjj/7StC/kWySkKr4VbVSDA454lhbcJllZdRP/80yGG2y3W7bGm2m9Y+Pj21vx82bN7G+vo5cLod0Om12rASAP/90OrWJg4PBwDNU6bziQpEBMkqq9Tc3N1EsFo2F8eDU9BGwcoqajtQUmP7Rdi4AHkPV2QDAatwqHeRwOEQ4HEahUPBE+Ezvst+f16RzJPumEWv7D50wCQ6/zhQf03c6a4D6Ar3ZM5kMbty4gW63azVf9sU6vH4kEgmk02k8e/bM6qAPHjyw1lSmODnXXW2BGTFG8MBqx4V+jn6lPW2q3+/bDgtmKLjOuNVqYX9/H/v7+8jlcigUClbKYCQEwCOcArxT4vhavH9oo7zPgNMOm8lkgmvXrtl7T6fT9h75OryfeIAVCgUj8f/rf/0vPH36FOl0GtVq9fV/aJcIqnPSnSeqVeGsAa4E5thhEj/aA+2RGVdmYUlQtWWQ3VCdTseCFEbW1LKon1NtAbu06Ou44Ij+UQkuB3mxU0LLHFoaAE5tjPtceICrODWdTmNra8tE6iwB8LxhiVpFrSQtHBSmItmLQl4vFBnQNGcoFLKyAEUsjKwBb7pVywGanvczY/2atkmpKpqkgl+ns2N9iG06+h5UMKjXJRk4q+aqokb+7Prz8Cbhe9AhGiro4g2fTCaxubmJw8NDVKtVD2FweP3g/vRqtYp8Po9sNmsLYbrdLoDVNEqdPaBiJLb36UHMkpOql/UzpYPW+4PvpdVqYTQaodls2qQ/OmttqaL40D8fg+8FeHXQkKb36ZB1rju1PEzdarlPbZ4DxdrtNh49eoR6vW6tYP6f1eFsaGDB8o2Kn2lvjK7T6TSy2aynNAmsSk6M0v2Dp3RREP0lAzWKWXmYq8/z66ZoK5PJBLVaDfV6HZ1OB/V6HQA8WQISZBJp2h0Dx+VyaaRDuyg4E0F1YnwMRa4kDOl0GslkEoVCwZYs8X2o5oYlBP6uNHDUctp5xYUiA9PpFIPBAOl0Gul02jIBNEqma/g4nQrFD03VymS1Ov5UD2Pt9WbGQBnnbDZDoVBAp9NBrVZDJpMxNSqNXscFM6WkNxHfGw9xdeyAt/bF9zWfz224EYUvnF1PpqpzCyaTCbLZLO7cuYOTkxPUajXrPHDO9M1gc3MTd+7cwYcffohkMolMJoPBYOCpkbLm6t+ZwR5s9jCT4PmzRkzNAt59AVRE8wDe2NhALpfDr371K8znc1Nvc0Qye7dpn9oupYQFgIccMF3L+4NkYrFY2PrXDz/80N5TsVi0dPPdu3fNuZIYTCYTdLtd7O/v4+nTpyZyu379Op4+fers90uA0SrByXj0g6xpc2GVpuJVFEiRIA/gXC5nGQV+ht1uF/1+***************************************************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\n", "text/plain": ["<Figure size 640x480 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["check_data = first(train_loader)\n", "\n", "# Select the first image from the batch\n", "img = check_data[\"image\"][0]\n", "fig, axs = plt.subplots(nrows=1, ncols=3)\n", "for ax in axs:\n", "    ax.axis(\"off\")\n", "ax = axs[0]\n", "ax.imshow(img[0, ..., img.shape[3] // 2].rot90(), cmap=\"gray\")\n", "ax = axs[1]\n", "ax.imshow(img[0, :, img.shape[2] // 2, ...].rot90(), cmap=\"gray\")\n", "ax = axs[2]\n", "ax.imshow(img[0, img.shape[1] // 2, ...].rot90(), cmap=\"gray\")"]}, {"cell_type": "markdown", "id": "902e37b5", "metadata": {}, "source": ["## Download the validation set"]}, {"cell_type": "code", "execution_count": 7, "id": "0550cac3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2022-12-08 20:49:45,062 - INFO - Verified 'Task01_BrainTumour.tar', md5: 240a19d752f0d9e9101544901065d872.\n", "2022-12-08 20:49:45,064 - INFO - File exists: /tmp/tmpyxyg6wxs/Task01_BrainTumour.tar, skipped downloading.\n", "2022-12-08 20:49:45,065 - INFO - Non-empty folder exists in /tmp/tmpyxyg6wxs/Task01_BrainTumour, skipped extracting.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Loading dataset: 100%|████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 96/96 [00:36<00:00,  2.60it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Image shape (1, 96, 96, 64)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["val_transforms = transforms.Compose(\n", "    [\n", "        transforms.LoadImaged(keys=[\"image\"]),\n", "        transforms.EnsureChannelFirstd(keys=[\"image\"]),\n", "        transforms.Lambdad(keys=\"image\", func=lambda x: x[channel, :, :, :]),\n", "        transforms.EnsureChannelFirstd(keys=[\"image\"], channel_dim=\"no_channel\"),\n", "        transforms.EnsureTyped(keys=[\"image\"]),\n", "        transforms.Orientationd(keys=[\"image\"], axcodes=\"RAS\"),\n", "        transforms.Spacingd(keys=[\"image\"], pixdim=(2.4, 2.4, 2.2), mode=(\"bilinear\")),\n", "        transforms.CenterSpatialCropd(keys=[\"image\"], roi_size=(96, 96, 64)),\n", "        transforms.ScaleIntensityRangePercentilesd(keys=\"image\", lower=0, upper=99.5, b_min=0, b_max=1),\n", "    ]\n", ")\n", "val_ds = DecathlonDataset(\n", "    root_dir=root_dir,\n", "    task=\"Task01_BrainTumour\",\n", "    section=\"validation\",\n", "    cache_rate=1.0,  # you may need a few Gb of RAM... Set to 0 otherwise\n", "    num_workers=4,\n", "    download=True,\n", "    seed=0,\n", "    transform=val_transforms,\n", ")\n", "val_loader = DataLoader(val_ds, batch_size=2, shuffle=False, num_workers=4, persistent_workers=True)\n", "print(f'Image shape {val_ds[0][\"image\"].shape}')"]}, {"cell_type": "code", "execution_count": 8, "id": "8e21e0ce", "metadata": {}, "outputs": [{"data": {"text/plain": ["<matplotlib.image.AxesImage at 0x7f555ff98190>"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 640x480 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["check_data = first(val_loader)\n", "\n", "img = check_data[\"image\"][0]\n", "fig, axs = plt.subplots(nrows=1, ncols=3)\n", "for ax in axs:\n", "    ax.axis(\"off\")\n", "ax = axs[0]\n", "ax.imshow(img[0, ..., img.shape[3] // 2].rot90(), cmap=\"gray\")\n", "ax = axs[1]\n", "ax.imshow(img[0, :, img.shape[2] // 2, ...].rot90(), cmap=\"gray\")\n", "ax = axs[2]\n", "ax.imshow(img[0, img.shape[1] // 2, ...].rot90(), cmap=\"gray\")"]}, {"cell_type": "markdown", "id": "19532ecb", "metadata": {}, "source": ["## Define the network"]}, {"cell_type": "code", "execution_count": 10, "id": "5e0514e5", "metadata": {"lines_to_next_cell": 2}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Using cuda\n"]}, {"data": {"text/plain": ["PatchDiscriminator(\n", "  (initial_conv): Convolution(\n", "    (conv): Conv3d(1, 32, kernel_size=(4, 4, 4), stride=(2, 2, 2), padding=(1, 1, 1))\n", "    (adn): ADN(\n", "      (D): Dropout(p=0.0, inplace=False)\n", "      (A): LeakyReLU(negative_slope=0.2)\n", "    )\n", "  )\n", "  (0): Convolution(\n", "    (conv): Conv3d(32, 64, kernel_size=(4, 4, 4), stride=(2, 2, 2), padding=(1, 1, 1), bias=False)\n", "    (adn): ADN(\n", "      (N): BatchNorm3d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (D): Dropout(p=0.0, inplace=False)\n", "      (A): LeakyReLU(negative_slope=0.2)\n", "    )\n", "  )\n", "  (1): Convolution(\n", "    (conv): Conv3d(64, 128, kernel_size=(4, 4, 4), stride=(2, 2, 2), padding=(1, 1, 1), bias=False)\n", "    (adn): ADN(\n", "      (N): BatchNorm3d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (D): Dropout(p=0.0, inplace=False)\n", "      (A): LeakyReLU(negative_slope=0.2)\n", "    )\n", "  )\n", "  (2): Convolution(\n", "    (conv): Conv3d(128, 256, kernel_size=(4, 4, 4), stride=(1, 1, 1), padding=(1, 1, 1), bias=False)\n", "    (adn): ADN(\n", "      (N): BatchNorm3d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (D): Dropout(p=0.0, inplace=False)\n", "      (A): LeakyReLU(negative_slope=0.2)\n", "    )\n", "  )\n", "  (final_conv): Convolution(\n", "    (conv): Conv3d(256, 1, kernel_size=(4, 4, 4), stride=(1, 1, 1), padding=(1, 1, 1))\n", "  )\n", ")"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "print(f\"Using {device}\")\n", "\n", "model = AutoencoderKL(\n", "    spatial_dims=3,\n", "    in_channels=1,\n", "    out_channels=1,\n", "    num_channels=(32, 64, 64),\n", "    latent_channels=3,\n", "    num_res_blocks=1,\n", "    norm_num_groups=32,\n", "    attention_levels=(False, False, True),\n", ")\n", "model.to(device)\n", "\n", "discriminator = PatchDiscriminator(\n", "    spatial_dims=3,\n", "    num_layers_d=3,\n", "    num_channels=32,\n", "    in_channels=1,\n", "    out_channels=1,\n", "    kernel_size=4,\n", "    activation=(Act.LEAKYRELU, {\"negative_slope\": 0.2}),\n", "    norm=\"BATCH\",\n", "    bias=False,\n", "    padding=1,\n", ")\n", "discriminator.to(device)"]}, {"cell_type": "code", "execution_count": 11, "id": "da14911d", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/miniconda3/envs/genmodels/lib/python3.9/site-packages/torchvision/models/_utils.py:208: UserWarning: The parameter 'pretrained' is deprecated since 0.13 and may be removed in the future, please use 'weights' instead.\n", "  warnings.warn(\n", "/home/<USER>/miniconda3/envs/genmodels/lib/python3.9/site-packages/torchvision/models/_utils.py:223: UserWarning: Arguments other than a weight enum or `None` for 'weights' are deprecated since 0.13 and may be removed in the future. The current behavior is equivalent to passing `weights=SqueezeNet1_1_Weights.IMAGENET1K_V1`. You can also use `weights=SqueezeNet1_1_Weights.DEFAULT` to get the most up-to-date weights.\n", "  warnings.warn(msg)\n"]}], "source": ["perceptual_loss = PerceptualLoss(spatial_dims=3, network_type=\"squeeze\", fake_3d_ratio=0.25)\n", "perceptual_loss.to(device)\n", "\n", "adv_loss = PatchAdversarialLoss(criterion=\"least_squares\")\n", "adv_weight = 0.01\n", "perceptual_weight = 0.001\n", "\n", "optimizer_g = torch.optim.Adam(model.parameters(), 1e-4)\n", "optimizer_d = torch.optim.Adam(discriminator.parameters(), lr=5e-4)"]}, {"cell_type": "code", "execution_count": 12, "id": "5c0b87e9", "metadata": {}, "outputs": [], "source": ["scaler_g = torch.cuda.amp.GradScaler()\n", "scaler_d = torch.cuda.amp.GradScaler()"]}, {"cell_type": "markdown", "id": "7d19616e", "metadata": {}, "source": ["## Model training"]}, {"cell_type": "code", "execution_count": 13, "id": "aa98bfa9", "metadata": {"lines_to_next_cell": 0}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Epoch 0: 100%|█| 194/194 [04:33<00:00,  1.41s/it, recons_loss=0.078, g\n", "Epoch 1: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.0408, \n", "Epoch 2: 100%|█| 194/194 [04:33<00:00,  1.41s/it, recons_loss=0.0368, \n", "Epoch 3: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.0347, \n", "Epoch 4: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.0333, \n", "Epoch 5: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.0314, \n", "Epoch 6: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.0312, \n", "Epoch 7: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.0301, \n", "Epoch 8: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.0289, \n", "Epoch 9: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.0307, \n", "Epoch 10: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.0293,\n", "Epoch 11: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.0294,\n", "Epoch 12: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.0298,\n", "Epoch 13: 100%|█| 194/194 [04:33<00:00,  1.41s/it, recons_loss=0.0284,\n", "Epoch 14: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.0283,\n", "Epoch 15: 100%|█| 194/194 [04:33<00:00,  1.41s/it, recons_loss=0.029, \n", "Epoch 16: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.0293,\n", "Epoch 17: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.0286,\n", "Epoch 18: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.0276,\n", "Epoch 19: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.0285,\n", "Epoch 20: 100%|█| 194/194 [04:32<00:00,  1.40s/it, recons_loss=0.0275,\n", "Epoch 21: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.0279,\n", "Epoch 22: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.0271,\n", "Epoch 23: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.0278,\n", "Epoch 24: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.028, \n", "Epoch 25: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.0261,\n", "Epoch 26: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.0266,\n", "Epoch 27: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.0264,\n", "Epoch 28: 100%|█| 194/194 [04:33<00:00,  1.41s/it, recons_loss=0.0277,\n", "Epoch 29: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.0279,\n", "Epoch 30: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.0266,\n", "Epoch 31: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.0273,\n", "Epoch 32: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.027, \n", "Epoch 33: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.027, \n", "Epoch 34: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.029, \n", "Epoch 35: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.0285,\n", "Epoch 36: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.0271,\n", "Epoch 37: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.0267,\n", "Epoch 38: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.0248,\n", "Epoch 39: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.0246,\n", "Epoch 40: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.0248,\n", "Epoch 41: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.0258,\n", "Epoch 42: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.0254,\n", "Epoch 43: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.0245,\n", "Epoch 44: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.0248,\n", "Epoch 45: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.0247,\n", "Epoch 46: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.0247,\n", "Epoch 47: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.0248,\n", "Epoch 48: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.0256,\n", "Epoch 49: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.0238,\n", "Epoch 50: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.0239,\n", "Epoch 51: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.025, \n", "Epoch 52: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.0239,\n", "Epoch 53: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.0234,\n", "Epoch 54: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.0242,\n", "Epoch 55: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.0236,\n", "Epoch 56: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.0242,\n", "Epoch 57: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.0227,\n", "Epoch 58: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.0216,\n", "Epoch 59: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.022, \n", "Epoch 60: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.0234,\n", "Epoch 61: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.024, \n", "Epoch 62: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.0229,\n", "Epoch 63: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.0238,\n", "Epoch 64: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.023, \n", "Epoch 65: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.0241,\n", "Epoch 66: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.0223,\n", "Epoch 67: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.0218,\n", "Epoch 68: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.022, \n", "Epoch 69: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.0221,\n", "Epoch 70: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.0238,\n", "Epoch 71: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.0229,\n", "Epoch 72: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.022, \n", "Epoch 73: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.0222,\n", "Epoch 74: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.0238,\n", "Epoch 75: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.0217,\n", "Epoch 76: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.0228,\n", "Epoch 77: 100%|█| 194/194 [04:32<00:00,  1.40s/it, recons_loss=0.0218,\n", "Epoch 78: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.0231,\n", "Epoch 79: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.0224,\n", "Epoch 80: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.0217,\n", "Epoch 81: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.0222,\n", "Epoch 82: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.0232,\n", "Epoch 83: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.021, \n", "Epoch 84: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.0208,\n", "Epoch 85: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.0212,\n", "Epoch 86: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.0219,\n", "Epoch 87: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.0214,\n", "Epoch 88: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.0224,\n", "Epoch 89: 100%|█| 194/194 [04:32<00:00,  1.40s/it, recons_loss=0.0219,\n", "Epoch 90: 100%|█| 194/194 [04:32<00:00,  1.40s/it, recons_loss=0.0223,\n", "Epoch 91: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.0229,\n", "Epoch 92: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.0218,\n", "Epoch 93: 100%|█| 194/194 [04:32<00:00,  1.40s/it, recons_loss=0.022, \n", "Epoch 94: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.0215,\n", "Epoch 95: 100%|█| 194/194 [04:32<00:00,  1.40s/it, recons_loss=0.0207,\n", "Epoch 96: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.0207,\n", "Epoch 97: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.0214,\n", "Epoch 98: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.0215,\n", "Epoch 99: 100%|█| 194/194 [04:32<00:00,  1.41s/it, recons_loss=0.0223,\n"]}], "source": ["kl_weight = 1e-6\n", "n_epochs = 100\n", "val_interval = 6\n", "epoch_recon_loss_list = []\n", "epoch_gen_loss_list = []\n", "epoch_disc_loss_list = []\n", "val_recon_epoch_loss_list = []\n", "intermediary_images = []\n", "n_example_images = 4\n", "\n", "for epoch in range(n_epochs):\n", "    model.train()\n", "    discriminator.train()\n", "    epoch_loss = 0\n", "    gen_epoch_loss = 0\n", "    disc_epoch_loss = 0\n", "    progress_bar = tqdm(enumerate(train_loader), total=len(train_loader), ncols=70)\n", "    progress_bar.set_description(f\"Epoch {epoch}\")\n", "    for step, batch in progress_bar:\n", "        images = batch[\"image\"].to(device)\n", "        optimizer_g.zero_grad(set_to_none=True)\n", "\n", "        # Generator part\n", "        with autocast(enabled=True):\n", "            reconstruction, z_mu, z_sigma = model(images)\n", "            logits_fake = discriminator(reconstruction.contiguous().float())[-1]\n", "\n", "            recons_loss = F.l1_loss(reconstruction.float(), images.float())\n", "            p_loss = perceptual_loss(reconstruction.float(), images.float())\n", "            generator_loss = adv_loss(logits_fake, target_is_real=True, for_discriminator=False)\n", "\n", "            kl_loss = 0.5 * torch.sum(z_mu.pow(2) + z_sigma.pow(2) - torch.log(z_sigma.pow(2)) - 1, dim=[1, 2, 3, 4])\n", "            kl_loss = torch.sum(kl_loss) / kl_loss.shape[0]\n", "\n", "            loss_g = recons_loss + (kl_weight * kl_loss) + (perceptual_weight * p_loss) + (adv_weight * generator_loss)\n", "\n", "        scaler_g.scale(loss_g).backward()\n", "        scaler_g.step(optimizer_g)\n", "        scaler_g.update()\n", "\n", "        # Discriminator part\n", "        optimizer_d.zero_grad(set_to_none=True)\n", "\n", "        with autocast(enabled=True):\n", "            logits_fake = discriminator(reconstruction.contiguous().detach())[-1]\n", "            loss_d_fake = adv_loss(logits_fake, target_is_real=False, for_discriminator=True)\n", "            logits_real = discriminator(images.contiguous().detach())[-1]\n", "            loss_d_real = adv_loss(logits_real, target_is_real=True, for_discriminator=True)\n", "            discriminator_loss = (loss_d_fake + loss_d_real) * 0.5\n", "\n", "            loss_d = adv_weight * discriminator_loss\n", "\n", "        scaler_d.scale(loss_d).backward()\n", "        scaler_d.step(optimizer_d)\n", "        scaler_d.update()\n", "\n", "        epoch_loss += recons_loss.item()\n", "        gen_epoch_loss += generator_loss.item()\n", "        disc_epoch_loss += discriminator_loss.item()\n", "\n", "        progress_bar.set_postfix(\n", "            {\n", "                \"recons_loss\": epoch_loss / (step + 1),\n", "                \"gen_loss\": gen_epoch_loss / (step + 1),\n", "                \"disc_loss\": disc_epoch_loss / (step + 1),\n", "            }\n", "        )\n", "    epoch_recon_loss_list.append(epoch_loss / (step + 1))\n", "    epoch_gen_loss_list.append(gen_epoch_loss / (step + 1))\n", "    epoch_disc_loss_list.append(disc_epoch_loss / (step + 1))\n", "\n", "    if (epoch + 1) % val_interval == 0:\n", "        model.eval()\n", "        val_loss = 0\n", "        with torch.no_grad():\n", "            for val_step, batch in enumerate(val_loader, start=1):\n", "                images = batch[\"image\"].to(device)\n", "                optimizer_g.zero_grad(set_to_none=True)\n", "\n", "                reconstruction, z_mu, z_sigma = model(images)\n", "                # get the first sammple from the first validation batch for visualisation\n", "                # purposes\n", "                if val_step == 1:\n", "                    intermediary_images.append(reconstruction[:n_example_images, 0])\n", "\n", "                recons_loss = F.l1_loss(reconstruction.float(), images.float())\n", "\n", "                val_loss += recons_loss.item()\n", "\n", "        val_loss /= val_step\n", "        val_recon_epoch_loss_list.append(val_loss)\n", "\n", "progress_bar.close()"]}, {"cell_type": "markdown", "id": "a28c94e3", "metadata": {}, "source": ["## Evaluate the trainig"]}, {"cell_type": "code", "execution_count": 14, "id": "066417fe", "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure()\n", "val_samples = np.linspace(val_interval, n_epochs, int(n_epochs / val_interval))\n", "plt.plot(np.linspace(1, n_epochs, n_epochs), epoch_recon_loss_list, label=\"Train\")\n", "plt.plot(val_samples, val_recon_epoch_loss_list, label=\"Validation\")\n", "plt.xlabel(\"Epochs\")\n", "plt.ylabel(\"Loss\")\n", "plt.legend()\n", "plt.tight_layout()\n", "plt.show()\n", "plt.close()"]}, {"cell_type": "code", "execution_count": 15, "id": "bb1b6dd8", "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAkEAAAHZCAYAAACB2e8eAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjYuMiwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8o6BhiAAAACXBIWXMAAA9hAAAPYQGoP6dpAADCY0lEQVR4nOzdd3hTZfsH8G+SpnvTTUsHpcxCWS17qUwBkamgCCg4UByIoCLgePEVRVB++oqyVZCpDEX2HmWUvaED2tK9R5om5/fH6Tk5pxlN26RJ2/tzXVykyTnJk47kzv3cz/1IGIZhQAghhBDSyEgtPQBCCCGEEEugIIgQQgghjRIFQYQQQghplCgIIoQQQkijREEQIYQQQholCoIIIYQQ0ihREEQIIYSQRomCIEIIIYQ0ShQEEUIIIaRRoiCIWFxISAgkEgleeuklSw+l0Vm7di0kEgkkEgkSEhLM8hgvvfQSJBIJQkJCzHL/1qJfv36QSCTo16+f2R6jLn5ehDQmFASRKh09epR/4ZVIJDh16pSlh0QaAS6oqM2/tWvXWvppkGooKirCqlWrMG7cOLRo0QLu7u6wtbWFj48PunfvjnfffRdnzpyx9DBJA0JBEKnSunXrRF+vX7/eQiMhhDRUP/30E8LCwvDyyy9jy5YtuHfvHvLy8qBUKpGRkYEzZ87g22+/Rffu3REdHU0fxohJSGgDVWJISUkJfH19UVBQAGdnZxQWFsLDwwOpqamws7MzyWOEhIQgMTERkydPpk/uDdBLL72EdevWITg4uFpTOPHx8SgqKtJ528cff4y//voLAPDvv/8iICBA53GBgYFwd3ev7pBJHVKr1XjttdewcuVKAIBUKsXIkSMxdOhQhIeHw9XVFRkZGbh69Sr++usvnDhxAgAwcuRI/PnnnxYcOWkIbCw9AGLdduzYgYKCAgDAd999h6lTpyInJwe7du3CmDFjLDw60pCFhobqvU0Y2ERERDT4eqOGbNGiRXwA1KJFC2zduhXt27fXOm7QoEGYPXs2Tp48ibfeequuh0kaKJoOIwZxU1/t27fHlClT0LJlS9H1hBBSUxcuXMDnn38OAAgICMCJEyd0BkBCPXv2xKlTp/D888/XxRBJA0dBENErNTUVBw4cAABMmjRJ9P/evXuRkZFh1P38888/GDp0KLy9veHo6IiIiAi8++67SE5ONnhe8+bNIZFI0LNnzyofIzk5GTKZDBKJBHPmzNF5TF5eHhYvXoyePXvC29sbtra28Pf3x/Dhw7F161YYmhnmCm0XLlwIADh06BDGjh2LoKAgyOVyrUxESkoK5s6di06dOsHNzQ1yuRy+vr6IjIzEc889h7Vr1yI/P1/rcYqKivDHH3/g5ZdfRlRUFH+ut7c3+vbti6+//hqFhYUGvxfVGWtVq43UajUOHTqE2bNno2fPnvDy8oJcLoe7uzuioqIwe/ZsJCUlGRyPJVR+XgqFAsuWLUO3bt3g5eUl+v4AQFlZGXbt2oWZM2eia9eu8PDwgFwuR5MmTRATE4OFCxciMzPT4GMaWh2WkJCgVay9f/9+DB8+HH5+frCzs0NoaChee+01PHr0yOjnVdUYkpOT8e677yI8PBwODg5o0qQJBg0ahH/++aeqbyEA9sNO37594eHhAWdnZ0RGRuLTTz/lf3cr/65V1+LFi6FWqwEA33//PXx8fIw6z87ODuPGjRNdZ+zKOV0/C6HKqxlTU1PxwQcfoG3btnBxcYFEIsGRI0cwdepUSCQSODg48NlyQ1q2bAmJRILo6Gidt6tUKqxbtw5PP/00AgICYGdnhyZNmqBXr15YunQpSkpKDN7/hQsXMG3aNERERMDJyQn29vYICgpC586d8cYbb2Dnzp0GX+MaLYYQPZYsWcIAYKRSKfPo0SOGYRjmwYMHjEQiYQAwy5cvr/I+3nnnHQaAzn/e3t7MuXPnmODgYAYAM3nyZNG5H3/8MQOAkUgkTHx8vFFjBcBcunRJ6/YDBw4wTZo00TsWAMzQoUOZgoICnffPHbNgwQLmww8/1Do3ODiYP/bYsWOMq6urwccCwOzatUvrcfr27VvleaGhoczNmzf1fi+qM9Y1a9bw1+v6Hi9YsKDK8Tg6OjLbt2/XO57JkydrPW5tcfepb9zC53Xu3DkmKipKa9wLFizQeX/6/jVp0oQ5ceKE3jFxP7u+fftq3RYfH8/fz5o1a5i5c+ca/Lu4ceOGzseo6uclHMOJEycYLy8vvY+zZMkSvc+lrKyMGTlypN5zW7RowSQkJOj8XhorJyeHkclkDAAmJCSEUalU1b4Poaq+N5zKP4vKhL+vp0+f1vk9PHz4MHPgwAH+67Vr1xoc27lz5/hjly1bpnV7YmIi06FDB4O/f+Hh4czt27d13v/SpUsZqVRa5e+wvte3xoxqgoheGzZsAMB+umzatCkAtk6jR48eOHnyJNavX29wbn7ZsmX49ttvAbCp7nnz5iE6OhqlpaXYs2cPli1bhrFjx6K4uFjn+RMnTsTnn38OhmHw+++/48MPP9T7WL///jsAoG3btujQoYPotpMnT2LIkCFQKpXw9fXFm2++iQ4dOiAgIAApKSn4448/8Ouvv+Lvv//G5MmTsW3bNr2Ps337dly9ehWRkZF455130K5dO5SUlODSpUsAAIVCgQkTJiA/Px8uLi547bXX0L9/f/j4+KCsrAzx8fE4deoUduzYofP+y8vLERkZiREjRqBLly4ICAgAwzBITEzEjh07sHnzZsTHx+OZZ57BpUuXYG9vX+OxGqO8vBz+/v4YNWoUunfvjrCwMNjb2+Phw4c4deoUfvjhBxQWFuL555/HxYsX0bp1a6Pvu65MmzYNV69exYsvvojx48fDz88PSUlJosL+8vJyhIWFYdSoUYiOjkazZs1gY2ODxMREHDhwAKtXr0ZWVhZGjRqFa9euGZ2x0OXnn3/GqVOn0LdvX8yYMQMRERHIzc3F+vXrsX79emRkZGDq1Kk4ffp0jR8jNTUVzzzzDKRSKb788kv06tULtra2OHHiBD799FPk5uZi3rx5GDJkCNq2bat1/qxZs/jC87Zt22L27Nlo164d8vPzsWPHDvz4448YP358jccHACdOnIBKpQIADB48GFKpdU1MFBYWYvTo0SgtLcVHH32Ep556Co6Ojrh69Sr8/f3RokUL/jXkt99+w+TJk/XeF/f6JJPJMGHCBNFtWVlZ6NWrFx4+fAg7Ozu88sor6Nu3L0JCQlBYWIh9+/Zh+fLluHfvHoYMGYKLFy/Czc2NP//KlSuYPXs21Go1QkNDMXPmTERFRcHT0xMFBQW4ffs2Dh8+zP88SSWWjsKIdYqLi+M/PaxevVp0248//sjfdv36dZ3np6WlMY6OjvwnqtTUVK1jDh48yNjY2PD3VTkTxDAM06lTJwYA07ZtW71jvXnzJn8f//nPf0S3lZWVMSEhIQwAZvDgwUxRUZHO+1i5ciV/H/v27dO6HYJPU0888QRTWlqq834OHjxoMNPDUSqVTF5entb1d+7c0XsOwzDM/v37+U98v/zyi85jjB0rw1T96Tk+Pp4pKyvTe/7Dhw+Zpk2bMgCYSZMm6TzG0pkgQ98rzr179xi1Wq339itXrjDOzs4MAObjjz/WeYyxmSAAzCuvvKLz8V5++WX+mIsXLxp8XoYyQdz3m8vgCh0/fpzP5r711ltat1+8eJG/vXv37kxxcbHWMVu2bNGbVTPW559/zp//888/V/v8ykydCQLAODs768wsc959910GACOTyZjHjx/rPEalUjEBAQEMAGbgwIFatz///PP8z+vBgwc67+PixYuMk5MTA4D58MMPRbfNnz+fAcA4OTnpHQPDMExubm6ts20NkXWF3sRqcIXPDg4OGD16tOi2cePGwdbWVnRcZevWreMzPN988w38/Py0jhkwYABeeeUVg+OYOHEiAOD69eu4fPmyzmN+++03AGx9QuViyU2bNiEhIQH29vZYv349HB0ddd7HK6+8ws/VG1qmL5VK8csvv+htD/D48WP+cp8+ffTej42NDVxdXbWub9Gihd5zAODJJ5/EiBEjAKDK5cFVjdUYISEhkMvlem8PDAzE+++/DwBWW3MwYMAATJs2zeAxXP2ZPpGRkXj55ZcBVP19r4q/vz++//57nY83e/Zs/vLx48dr9Tjff/89n8EV6tWrF2JiYvQ+xsqVK/mf488//wwHBwetY8aMGYNRo0bVanxZWVn85dpk1sxpzpw5WpllIe71SaVSYdOmTTqPOXz4MFJSUkTHcxISEvDHH38AAFasWKF3RWTHjh3xxhtvANB+feJecyIiIuDr66t3rG5ublaXbbMG9B0hWsrLy/n07fDhw7XerD09PTF06FAAbADCFTYKcQXVHh4eGDlypN7Hmjp1qsGxTJgwgf/D5cZU2caNGwGwq0aCg4NFt+3cuRMA0LdvX3h7ext8LC5oMTQN0bNnT4PLsf39/fnLa9asMfh4xsjIyMDdu3dx7do1/h/3PPQFhcaOtSby8/MRHx+P69ev8+PhAkvuNmtT+Y3HGDk5Obh//77oeXLL8m/cuAGlUlnj8YwZM0ZvYNqyZUs4OzsDAB48eFDjx3B3d8ewYcP03t65c2e9j8H97Xbs2FHnVBnnxRdfrPH4AIiKiZ2cnGp1X+ZS1e9Op06d0KpVKwD6X5+46x0cHLQCxz179kClUsHR0RFDhgwx+Fjc61NKSopoMQL3mnPjxg3ExsYavA+ijWqCiJZ///0XaWlpADSrwSqbNGkS/vzzTzx69AiHDx/GE088Ibr96tWrANgXUhsb/b9mUVFRsLW1RVlZmc7bAwIC0L9/fxw8eBAbN27El19+KfoEffbsWdy/fx+A7hes8+fP88/J0Cd9IWE2p7Kqlu/26tULYWFhePDgAd5++2389ttvGDVqFPr06YOuXbvyGTRDTp48ie+++w4HDhxAdna23uOqWq1U1ViNlZiYiK+//hq7du1CYmKiwWMzMzMRFhZmksc1FWO/D1evXsW3336Lf/75x+DvgFqtRk5OTo2zF9ybpj4eHh4oLCw0asWRPi1atDD4qd/T0xMAtB6jtLQU9+7dA6AJlPTp0qVLjccHAC4uLvxlfU0xLcnZ2dmo3+WJEydi/vz5iI2Nxb179xAeHs7fplAosH37dgDAiBEjRM8Z0Lw+FRcXG3ydrOzx48do1qwZAOC5557D4sWLoVAo0LNnTwwePBjDhg1Dr1690LZtW6Nf9xorygQRLdwUV5MmTTB48GCdxzz99NP8J2NdU2Lcm3dVbxQ2Njb8C7I+XHDz8OFDHDt2THQbNxUml8sxduxYrXPT09MN3rcuhpaienh4GDxXLpdj165dfIHwuXPn8OGHH6JXr15wd3fH4MGD8fvvv/MFoZUtXLgQvXr1wubNmw0GQFWN05ixGuOff/5BmzZtsGLFiioDIGPGZAnGfB9WrVqFTp06Yc2aNQYDIE5tnqe+KVkOF7zo+x0x5WNUzuLm5ubyl6vKnFZ1e1WaNGnCX+Y+dFkTYzuNC6fgudcjzp49e/jvqa4PaTV5fQIgWkzSqlUrbNy4ER4eHigvL8fu3bvx2muvITIyEj4+PnjhhRdqPbXakFEQRETy8vL4KaSsrCzY2trq3JjS3t6e/+Pevn273k9ypvgUMnr0aH4VlDDlrFKpsHnzZgDs6hLhi6rwGAAYMmQIrl69avQ/fWQyWZXjbdOmDa5evYodO3Zg6tSp/CfDkpIS/Pvvv5g4cSJiYmK0XgAPHjyIRYsWAQDCwsLwww8/4MqVK8jNzYVSqQTDMGAYBvPnz69yDMaO1ZDMzEw8//zzKC4uhrOzMxYuXIjTp08jPT0dCoWCH8/Bgwf5c6yxJqiq78OtW7fw6quvory8HD4+PliyZAkuXLiArKwslJWV8c9z1apV/DnW+DzrG2GtzcWLFy04Et2M/fsJCwtD9+7dAWhPiXFf6/tAyb0+eXl5Vev1qWvXrqL7GT16NOLj4/HTTz/h2Wef5QPUzMxM/Prrr+jTpw9eeuklnaULjR1NhxGRzZs3o7S0tFrnFBYWYvv27XjhhRf46zw8PPD48eMqP+GVl5dXmfFwdXXF008/ja1bt2Lr1q1YsWIF5HI5Dh48yN+/vrn7Jk2aICUlBWVlZWjXrl21nldtyGQyPPPMM3jmmWcAsEuW9+7di//7v//DhQsXcOHCBcyYMUO0VP7nn38GwH7vzpw5o/eTdlXfL1PZunUrH+ju2LEDTz75pEXHYy5r165FeXk5ZDIZjh49qne6qr4/T2MIsx9VNUM1tlmqPr169YJMJoNKpcI///wDtVpdq8Jd4bmG3uzNMfU2ceJEnD59Gnfu3MH58+fRpUsX5OfnY8+ePQCAsWPH6lxgwH1wKygoQOvWrWv1wcXNzQ3Tp0/H9OnTAQA3b97EX3/9he+//x4pKSlYt24dOnbsiFmzZtX4MRoiygQREW5qy9/fHxs3bqzyX2BgoOg8TmRkJADg0qVLKC8v1/t4ly9f1lsPJMQFOdnZ2Xy3Wy717OLiwq+Yqqxjx44A2Ll3Yx7HXPz9/TFlyhScPn0anTp1AgDs3r1bNK1y/fp1AED//v0NTjVwdQTmxo3H09NTbwBUl+MxF+55dujQwWC9Tn1/nsawt7dH8+bNAbAdiA2p7ffD3d2d/5CQmJhY61V3wnqbnJwcvcfduXOnVo+jy7hx4/iaHi77s23bNv4Dpb4Padzrk0KhMPnvV+vWrTF37lycOXOGLzznMudEg4IgwouPj8fJkycBsOnVCRMmVPmPWz5/6NAh0TYY3JtmdnY2du3apfcxV69ebdTYhg4dytd2/PbbbygtLeWzKKNGjdK5jBcAHxzl5eWZZLVWbcnlcvTt2xcAmwUT1mBwwaKhT6pxcXE4e/asWcdYeTylpaV6P1kXFxfzTTXrK2O+76mpqfw0cUPHLXKIi4vjA0RdTLF/4Lx58/gMzptvvml0jYxCodB6QxcuLzcUUHCrSU3J29sbAwcOBMC25VCr1XwwFBwcrHfrn+HDh/MlA8uWLTP5uAAgKCgIERERAKpeTNEYURBEeOvXr+drHYzdIZ47Tq1W49dff+Wvnzx5Mh+YvPvuuzqnxY4ePcrvHl0VW1tb/rF27dqF33//nV/ZYmgZ6+TJkxEUFASA7cFSubC6shMnTuDo0aNGjUmX48eP86trdCkrK+Pv39nZWZTx4XoEnThxQud9ZGRkiKYczY0bT3Fxsc5PkCqVCi+//DLfA6W+4p7n3bt3cerUKa3bi4uL8fzzz1tl0bc5TJ8+nX9jfuWVV3Q+723btuntel4dnTt3xscffwyAXfrdq1cvgzV5ANvComfPnlr1N+3ateMXWaxYsQIKhULr3M2bN2PLli21Hrcu3OtQamoqfv/9dxw+fBgAWzitrzayZcuW/IKOTZs2YenSpQYfIz4+XiuI+/PPP0Ufpip7+PAhbt26BQB6+xA1ZhQEER73id7Hxwe9e/c26pwePXrwfSqEGQFfX1989tlnANiGYJ07d8b//d//4dy5czh+/DjmzZuHQYMGoWnTpkavMuFeZEpKSvDee+/xj1N5eb6QnZ0dNm/eDDs7OxQWFmLAgAGYNGkStm7digsXLuDcuXPYuXMnFixYgPbt26N3795VvggbcvDgQbRs2RL9+vXDkiVL8O+//+LixYs4efIk1qxZg969e/NFoNOmTRMti+X6rhQVFaFv3774/vvvcerUKZw6dQpff/01OnTogBs3bvBFmOY2btw4vp/NlClTMHfuXBw8eBDnz5/HunXrEBMTg40bNxq1wa014wJLtVqNYcOG4T//+Q+OHTuG2NhY/Pjjj4iKisKRI0fq/fM0VufOnfkmpqdPn0bXrl2xbt06XLhwAYcPH8abb76J8ePHizYCrc0CiAULFvB1LHfv3kVUVBRGjx6N1atX49ixY4iLi8O+ffuwdOlS9OvXDz169NA5VWdjY4MZM2YAAK5du4YBAwbgr7/+QlxcHPbu3Ytp06bhueeeQ48ePWo8VkNGjhzJTzu9+eabfNFzVb2GfvzxR34p/nvvvYe+ffti1apVOHPmDOLi4nDgwAF88803eOqppxAeHq61rc+yZcvQtGlTjBs3Dv/73/9w9OhRXLp0CYcPH8aSJUvQs2dPPpB99dVXTf206z8LdaomVubEiRN8q/gZM2ZU69zXX3+dP/f8+fOi29566y29m/l5eXkxsbGxejdQrUytVjNBQUGi+5g1a5ZRYzx9+rTWufr+rVu3Tut87raqtgcwZsNRAMzIkSN1bkcwZcoUvefIZDJm2bJlosfQxdixMkzVWw2sXr3a4MaM48ePF20kefjwYa37sPS2GVVtvsswDLNo0SKDP6/33nuvWpuXVlbVVg1Chv4eajMGoap+hxQKBfP000/r/X6EhoYy9+7d47/+8ssvDT6eMX788UfG29vbqL+fHj16MLGxsVr3UVRUxHTr1k3vef369WOuXbtm1LYZNfl9nThxoujxOnToYNR5qampTO/evY167lOmTBGda8ymy1KplPnss8+q/XwaA8oEEQDi+f3K22RURXh85TqB5cuXY8+ePRg0aBA8PT1hb2+P8PBwvPXWW4iLi9Na6mmIRCLBc889J7rO2G7A3bp1w927d/G///0Pw4YNQ0BAAGxtbWFvb4+goCAMHDgQX3zxBW7dulWrTrizZ8/Gtm3b8Nprr6Fbt25o1qwZ7O3tYW9vj5CQEIwbNw67d+/Gn3/+qbOOafXq1diwYQN69+4NFxcX2NnZITg4GC+88AJOnTpV5ys7pkyZguPHj+OZZ56Bt7c35HI5/P39MXjwYPzxxx/YtGlTrZfiW4NPPvkEe/bswcCBA+Hh4QFbW1sEBgbi2Wefxb59+/D1119beoh1ytbWFjt37sSaNWvQq1cvuLm5wdHREa1bt8aHH36ICxcuiFpSCDf0rKlXX30VDx48wM8//4wxY8agefPmcHV1hVwuh7e3N7p164Z3330XsbGxOHnypM7XDkdHRxw6dAhffPEFIiMj4eDgAFdXV3Tt2hUrVqzAgQMHzNqduvLrkbGvT35+fjh27Bh2796NiRMnIiwsDI6Ojvxz79GjB9577z0cPXpUq45y48aNWLlyJZ5//nlERUXBz88PNjY2cHZ2Rtu2bfHaa68hLi6On3YkYhKGoYYXhBBCqufEiRP8tPmBAwcMTksTYq0oE0QIIaTauAJduVxe5RYbhFgrCoIIIYSIZGZmGlxx9O+//+Knn34CwLahMHaLCUKsDU2HEUIIETly5AhGjhyJsWPH4sknn0Tz5s0hlUqRmJiInTt34tdff4VKpYKDgwMuXbrE96EhpL6hIIgQQojIkSNH0L9/f4PHuLq6YsuWLXyTQELqIwqCCCGEiBQWFmLbtm3Yu3cvLl++jIyMDOTm5sLV1RXh4eEYPHgwZs6cWeud5AmxNAqCCCGEENIo0S7yBqjVaqSkpMDFxaVWHVEJIYQQUncYhkFBQQECAgL4/el0oSDIgJSUFH7fKUIIIYTULw8fPkRgYKDe2ykIMsDFxQUA+010dXW18GgIIYQQYoz8/HwEBQXx7+P6UBBkADcF5urqSkEQIYQQUs9UVcpCzRIJIYQQ0ihREEQIIYSQRomCIEIIIYQ0ShQEEUIIIaRRoiCIEEIIIY0SBUGEEEIIaZQoCCKEEEJIo0RBECGEEEIaJQqCCCGEENIoURBECCGEkEaJgiBCCCGENEoUBBFCCCGkUaIgiBBCCDGz4rJy/H01FbcfF1h6KESAdpEnhBBCzOjkvUzM3X4FD7NLYGsjxa6ZvdDSz8XSw6qx3OIy2MtlsJfLLD2UWqMgiBBCCDGDvGIlvvj7Bjaff8RfV1auxvy/ruGP6d0gkUgsODrjqNUMbqTm42JSDi4k5uBiUg4eZpfAz9Ue/77dB26OcksPsVYoCCKEEEJM7PCtdMzZdgUZBQr+OplUApWaQWx8Nv66lIJnOja14AirdvROBj7ffQN30wu1bnucX4qT9zMxNNLfAiMzHQqCCCGEEBNKyirGjA0XUKZSAwCc7Wwwd0grBLjbY+ra8wCAL/6+iQGtfeBqb32ZlAcZhfh8z00cupWudZtEAjAMezk+s6iOR2Z6FAQRQghpcLacf4h/r6fhnadaoG2AW50+9m9nE/kAqHcLL3w1pj383RwAAAPb+GLfjTRkFCiwbP9dfDK8TZ2OzZBSpQrf7LuNNScTUK5m+OujgtwxokMAOgd7QM0wGPXDKQBAYhYFQYQQQohVScktwQfbrkDNAIpyFTZMi6mzxy5VqvDH+YcAAFuZFN+Oj4KXsx1/+/yn2+DY3QyUKtVYdzoBY7sEorW/a52Nz5Afj9zHz8fj+a/9XO0xd0grjIwK4OuXCkqV/O0JmcV1PkZToyXyhBBCqkWpUoveDK3NlvOPwCUyLj3MBcMwhk8woT1XUpFbzH5vhkb6iQIgAAjydMTM/uEAAJWawfw/r9Xp+Ay5m65Zvv9q3+Y4NLsvnunYVFTA7WIvh5ezLQAgnjJBhBBCdCkpU2H3lRR0CHJHhG/9XQ5dqlRhy/mHuJqch4fZJUjKLkZqXgnUDPBG/+aYPbClVa1yUqsZbK7IxABAQWk5krKLEdzEqU4ef/2ZRP7yC92DdR7zSp8wbLuYjPjMIpxPzMH2i8kY3TmwTsZnSGZhGX951hMt4GCrewl8SBMnZBaWIaNAgSJFOZzs6m8oQZkgQggxg6/33cb7W69g9A+nkF5Qaunh1NiakwmY/9d1bD7/CKcfZCE5t4TPsvzf4fv4Ys9Nq8lkAMDJ+5lIzi0RXXctOb9OHvvKo1xcfpgLAGjt74pOzTx0HmdnI8PCEW35r5cdvIPyihoiS8oqZFeyOdnK9AZAAEQBZUItskFKK3jOFAQRQogZnLqfBQAoUJTjh8P3LTyamouNzxJ97eYgF9Ww/HIiHot23bCaQGjTuYda111LyauTx/5VmAXqFmwwQ9Y3whu9W3gBAB5ml+Dva4/NPr6qZBWxmaAmlabwKgv1cuQvJ2bVrC6oXKXGoG+P4Z0/LvGBoyVQEEQIISamVKlxT1Bf8fvZJDzKqZ9FpPcy2B4xTrYyXF4wEJcXDMQ/s3rjv6Mjwb3Hrz2VgAU7r1cZCJk7UMouKsO+64/58XKuJZs/CMorVuKvSykAABc7G4yMCqjynNf6Nucv/3T0vkUDSaVKzdcyNamo+dEnxEuTCarpMvkDN9PwILMIO+KS8e2BOzW6D1OgIIgQQkzsQUYRlCrNG1qZSo3vDt614IhqplSpwqMcdmop3McZbg6anjbjuzbDkjEd+EBo/elEfPTnNajVut/I4zOLMOy7Exj07TFkF5XpPKa2dsQl89/356Kb8QW815LzzB5gbLnwEIpydnpndOdAo+pkujdvgsim7PL96yn5OHEv06xjNCRH8DNp4mQ4ExQimA6r6TL5NScT+MtTeobW6D5MgYIgQggxsVuPtWtQtl1Mxv0M7c67lZWr1EjILLKK6aX7GYV8Y7zmPs5at4/pHIil4zpAWhEI/X42CZ/t0Z4ayypU4KU1sbiRmo/baQXYczXV5GNlGAZ/nEviv54QHcT3B8opViIlz3x1WWo1I5oKm9RNd0F0ZRKJBK8KskH/O2q6adMiRTnS841/zsKiaK9qZIJqskz+ekoezsZnAwCaezuhT8W0oCVQEEQIISZ2S7BTeJdgtjhWpWbw7X7Daf9HOcUYvPw4+n19pMpj68I9wXYJLXx0r3Ab1TEQ346P4gOhNScT8O0BTdarVKnCK+vPi2pHMgVbSZhK3MNc3Eljx9sl2APhPi58lgUw75TYiXuZSKh4fj2aN0G4joBRn8Ht/BDShK2xOXkvC1cf1X6cOUVl6PnfQ+i2+CBiK4KNqmQVaX4mVU2HOdvZ8Ev/a7JMfq0gC/RSjxCLri6kIIgQQkzstiAIWvxsJP/JeveVVNxI0b1S6dbjfIz+8RQfePx07AEyC00fLFSHMAgy9MY+Mqop/ju6Pf/1dwfv4pfjD6BWM3h38yVcTMoVHZ9bbPrpsM2CgujxXYMAAO2aagq4jQmC0vJL8cKqs5j4y5lq9UHaflGzQeoLRmaBODKpBK/0CeO/NkU26OT9TOQWK6FmgP03jCu4zio0fjoMAB+4ccvkjZVVqMBflytqp+xt8Gwny7YGoCCIEEJM7FYqG+i42Nsg3McZr/cL52/7Zt9trePPJWRj3P9OIy1fE/QoytWiT8yWYGwQBABjuwRhgWALiM/33MSkVWfx91X2TdhGqvm0n11s2kaLhYpy7Kx4Y3W2s8Gw9uymnsLtMqoKghTlKszYcAHH72bi5L0sbBHs/F4VrnhcJpXgida+1R0+RncK5APlf66lIqGWe3IJs27C3ylDhAF3VZkgoNKUWDWyQRtjk1BWUTs1oWuQxXsMWWUQVFhYiAULFmDw4MHw9PSERCLB2rVrjT4/NzcX06dPh7e3N5ycnNC/f39cvHjRfAMmhJAKeYL6k1Z+LpBIJHg+phn83ewBAAdvpePDHVex5mQ8Dt9Ox59xyZj0y1nkl7KfptsGuEIuYwOG9acTUFiNT9mmxgVBtjIpgjwcqjx+Ss9QvPtUBP811yZAJpVg6fgo/npTZ4L+vpKK4jIVAGBEVAAcbdk31kAPB76Y+5qeDBzA1hPN//MaLgmWal95lKv3+MpSctmft5+rPWxtqv+2ai+X8cXBagb4+fgDvccqVWos3HkdC/66prfPTpIgCHpsZF1QVpGwJsj4TBBg/DJ5pUqNDRW1U1IJ8GL3EKPOMyerDIIyMzPx6aef4ubNm+jQoUO1zlWr1Rg2bBh+//13zJw5E1999RXS09PRr18/3L1b/1ZnEELql9tpmqmwVn7sdIy9XIZZT7Tgr//9bBIW7bqBKWvO4e0/LvGrinq38MLmGd0xMqopACC/tBybYpNgCUqVml/+HObtBBuZcW8Xbw4Ixyu9xat9Ph3ZFsPb+/PZoBwTB0En72tWVY0WTK9IJBK+LiijQKG3UPjXM4nYXCnzYyhoEiouK+dXuwW421dr3EKTYoL5Zf1bLjziGxdWtvfaY6w9lYB1pxOx54ruAvPEbE1mxtji6KxaZIKMXSb/z7XHfGbqyda+CPJ0rOIM87PKIMjf3x+pqalITEzEkiVLqnXu1q1bcerUKaxduxYLFizAG2+8gSNHjkAmk2HBggVmGjEhhLCEK8Na+mmKicd0DkSvcP2rYEZGBWDV5K5wsrPBq301NSK/HI/npw/qUmJWMb+TuK6VYfpIJBJ8OLQ1ZvQJg4u9Dd4f1BITY9jGge6O7JtrTpFpp8Pupmmmo4R1QADQVvD1VR1TYmcfZGHRrhv81y4V0zP3MwqNqnXhskAA0NS96myZPm6OcoyrqGUqK1frLWi+Iwiyb6TqDtQeZms6Zj/OLzVqpWH1a4Kqv0x+zUnN5qyWXBYvZJUbftjZ2cHPz69G527duhW+vr549tln+eu8vb0xbtw4/Prrr1AoFLCzq/oHTAghNXEzVfMm1dpfEwTZyKTYMC0aGYUKJGQWIyGrCAmZRXiYU4LIpq54uVcYpBWZknAfFzzZ2hcHbqbhcX4p/rqUjLFdgur0eYjqgbyND4IANhCaN7Q15g1tLbrew1GOzEKFSTNBajWDB5nsWIM9HWFnI97uoZ2oLihfVLOTkluC13+7yAd70/uEoaBUiY2xD8EwbJDRNcTT4OMLt+hoasSUoSFRQe785Yd6mms+zNZc/yBDO/hQlKuQkqcZU6lSjfzSclGPJ10yK7JZEgn7c6pKdZfJX3qYi7iKAvlWfi7oFmb4+1pXrDITVBtxcXHo1KkTpFLxU4uOjkZxcTHu3LH8slNCiPklZBZh3P9O4+M/r+pt4GcOtwWZoMobp0okEvi42CM61BPjugRhzuBW+P65jpjepzkfAHFe66fJBv107EGdPgcAoo7XLXyrFwTp41GRCSouU6FUqTLJfSbnlqBUyWbKdGWs2gmXyQu2z1CrGcz8/SJfC9O7hRfmDGopKqY2Zrl6co4m4AioRSYIgGh6SJjNEXokeDwu+Kt8e+XET5oRU2LcdJiHo61RU5/VXSa/VpQFsuyyeKEGFwSlpqbC399f63ruupSUFL3nKhQK5Ofni/4RQsyrXKU2S2PANSfjEZuQjV/PJIlqRsxJrWb45fGBHg5wsa/6E7U+nYM90TWE7TF0L70QB2+lm2SMxqrOyjBjeThpvh+5JlohVtU4gz0d4VwxxXVdMB229cIjful+kKcDvn+uI2xkUnFvISP2HEsRZoJqGwR5aIKgpGw9mSBBhigpq1irOFrXecYFQRX7hjlVXQ/E4fYQq2qZfF6Jkt8bzcNRzte8WYMGFwSVlJTonO6yt7fnb9dn8eLFcHNz4/8FBdVt+pkQa6RWM/h89w2MWHHC5A3nEjKLMHDZMXRbfBCpefr/NmtC2LDwwI00k963Po9ySlBUsUqJK4quDXN1EzYGt+xbKgFCBVMftcFlggDTFUdXNW0nlUrQNoD9WaTklSKrUIG8EiX+u/cWf8x/n23P1yu19HPhC7iN+X1PNmEQ5OVsCwc5O533UEcwU6pUiZa8l6sZreOSdKzUelxFt+zisnKUVGTmjCmK5hi7m/yuyyl8XdszHZvCXq5/h/q61uCCIAcHBygU2lX1paWl/O36zJs3D3l5efy/hw+1dyMmpLH55cQD/HIiHlce5Zn0jVhRrsLMjRfxIKMIafkK/BmnP0tbE8ItKg7cTK+TbSiERdHCeqCa6t/SBxEVU1EXEnPqZCNQgA1876ezb2rBTZy06mxqyt3cQZCejJV4Siwf3+6/w0+DDYv0Rw9Bwbq9XMZPY95LL0RxmeHiaGEQVNvpMIlEgmYVU2KPckq0pkCFj8WpvDJL13L19Co6dIuKoo1YHs8RBseGlslvvaBZeTe2s3UlFxpcEMStLKuMuy4gQP/OvnZ2dnB1dRX9I6Qxu5achyX/apr76fp0WlNf7b2Na8maoMGUmaCcojLRXkjJuSV6V9KYkjD7JFwZVlNSqQTjBAXRwpVB5pScW8JnBppXsyjaEE/BdJipVojdEwS7+laxCVeMbbvwiO9V4yCX4cNhrfUer2bEhe66cDVB7o5ykzT+C/JkA6kylRppBeIMjq6/v8rF0UnZ2hmZqjJBwkaJXtWYDgsW9ArSt0z+XnoB33+pjb8r2gRY1/tqgwuCoqKicPHiRajV4nnSs2fPwtHREREREXrOJIQIlZSp8PYfl0S7oSfnmmYTysO30rHqRLzoulQTbnB5T8dGpQdumL+mRpgJMsV0GAD4u2myCxlm2HNLF+H3z1T1QIDpM0EMw/CZoAA3e772pzLhCrGdl1OgqsiwvNG/uc4pLGP3HFOpGb4ZYW2nwjiGiqMf5mh/UKhcHF2TmqCaZoKMWSa/9UIyf3lMZ8tukaFLvQ6CUlNTcevWLSiVmk8UY8aMQVpaGrZv385fl5mZiS1btmD48OG0PJ4QIy3+56ZoqgFgPzEqymu3qictvxTvbbmsdb0pM0GVxw0AB26avy6IywTZ2UhFHXVrw9tF85pVV0HQfTMURQPimiBTdI3OqKjvAQz3MgrzduZrbTjBTRzxcu8wnccLp8909RbipOWX8gFVbafCOIaKox/pCHDuCzJBDMPw5wQ3cQS3AKvKIKgam6cKVbVMXqVmsCOOnQqzkUowMkr/TIylWGWfIABYsWIFcnNz+dVcu3btwqNH7DfzzTffhJubG+bNm4d169YhPj4eISEhANggqFu3bpgyZQpu3LgBLy8v/PDDD1CpVFi0aJGlng4h9crhW+lYf5qdMrCzkSLcxxnXKzropuUp0MyIN/giRTkW7ryOnGIl2jV1RWRTN7Rr6oa3N13iO+w+2doX15Lz8Di/tMqUfXUIgyBbGynKytW4mpyH1LwSUWbFlEqVKn7Ppxa+zkZ3WK6KKAgysKGqWs2gTKU2SdEp13wQAFqYMAgSTodlm2A6zNgVbDKpBG0CXHEhMYe/7pOn2+j9XrX2d4VMKoFKzRjMBJlyZRinmSgTJA4shCvDuPEJp6HSCxR8u4AwLycUl6mQUaCocuuMzGo2SuRwy+QzCxU6l8kfv5vBF3IPaOVTrSxTXbHaIOjrr79GYmIi//X27dv57M6kSZPg5uam8zyZTIa///4b77//Pr777juUlJSga9euWLt2LVq2bFknYyekPsssVOD9rZpMzcfDWiMhq5gPglLySowKgr4/dA9bKgoidWVh/N3ssWRMe0xddw6P80uRWVgGRbnKJEW4dwVvjmM6B+L3s0kV40iv9i7fRj9mWiG4OlZTTYUBxmWCsovKMGjZMQDAqsld0D7QvVaPaUydTU24mzgTVJ2MVTtBEDSglY/BjU7t5TK08HHGrccFuJteiFKlSmfAZMqVYRzh35ZWEFQxPSaVAO0D3RCXlIuMAgUKSpVwsZeLipODmzgho1CBjAL2n0rNQCbV3ZtHOB3mVY1MEMAuk8+seJwiRbmoLmqLoCDaGqfCACueDktISADDMDr/cVmftWvXir7meHh44JdffkFmZiaKiopw5MgRdOnSpe6fBCH10Bd7bvKfDAe08sGkbsH85p+AcdNWpUoVNp3Tv+eVVAIsGx8FDydb0X2n5Zlmuod7c3Sxs8Hz0c346825VP6mqB6o9kXRHCdbGT+Voy8I2nf9Mf9m984fl2rViFBYZ+NvoM6mJoTTYdkmCIKq09V6bJcg2Egl8HO1F+12rw83JaZSM7ipp6jelN2iOYGC+6k8HcZlgvzdHNBS0IiTK44WHt/M0xF+ruzflpoRFz9XJp4Oq162JkTPMvm8YiX2X2f/3po42aJ/K59q3W9dsdogiBBS9249zsefl9hCRndHOf47uj0kEomo3iHFiOLovy4l883wBrX1xYrnO2JGnzB0D2uC4CaO+OyZdogJawJAXPhrirqgIkU5/+YU7uuMtgGuCKgItE7fzzLbruy3UrU3TjUFiUTCZ4P0TYcJi8rvZxTh2/0174yfWVjG19mYsh4IANwc5HydSo4JmiVWp4C7XVM3XF4wEPvf7SPqb6OPMcXRpuwWzXG01XRiFk5/FZQq+b+pQA8HhHlrb2CaJAhCgps4wtdV8AHDwJSYuDC6epmgED3L5HdeSUFZRSPHkVFNITfR9LCpWe10GCGk7n39722+5f7M/uH8m291MkEMw2DNyQT+69f7haNDkDuebq+7KFJ837WvCxIuGQ73doZEIsGTbXyx/nQiylRqHLuTgaGR2l3lqyOnqAy7r6SguY8zYkKbQCaV4HaaIBNkgh5BQt4udkjKLkZusVLnlGHlN7iVxx9gYFtfdA6u/v5MdwXbZZg6CJJJJXBzkCO3WGmS6TAuE+ThKDcqg1GdJezGFEeboyYIYJfJZxYqkJav4KfihCvFgjwdEeql+dk8qAgGEytlgoRB0OO8UrTXMyPFZYlsZVJ+A1ljCTNBwvqkrfVgKgygIIgQUuF8QjYO3GSXkfu72WOSoHZG+Ck3tYpM0Nn4bH6VVFSQOzoINoXUxc/EQZCuN/EnW/vyhd4HbqTVOgiau/0K/q1I9Xu72GFYpD9fM+XlbMd/kjcVb8H9ZRWWaWUdKhe+Mgwwe8sV/P1WbzjY6q+xupdegK//vYMuIR6Y0jMUMqnEbCvDOB6OtsgtVvLF8TWVX6rki27NMc42/q6QStipJGE/KyEu42hrI612LY0hzTwd+c1GH+WUINzHWZQVCvJwFGWC7lcEH8JMTJBgOgwA0gysLOQaRzZxtq32nl7CXkH/XEvFvfRC3Esv5ANHa+wNJGSd+SlCSJ1iGAZfCZoiznqihagQ1MvZjt9KIKWKQGXdqQT+8pSeIVU+tnA67LEJpsN0rRjqFtaEr205dDsd5ZX2W6oOtZrBqXtZ/NcZBQqsPZXAT1WYsh6I4+WieYPVVdvBrayTyyT8TuTxmUX4et9trWM5inIVZmy4gL3XH+PzPTfx0ppYZBUqRN+/Fj6mfy7cDuUFpeW1+jmIgzXTj9PBVsb//txJK9Cqs2IYhp8Oa+ruYNINQYXL5LniaGGRdJCnA5p5OvJ/k1z2kzvGz9Ue9nIZfFw1wXOanr9btZrhA9LqToUB4umwa8n52BGXLMqcje1ivVkggIIgQgiAo3cyEBufDYBdWls5fS2TSvjUuqHpsOTcEvx7nd0o0dvFDkPaVZ1xEU6HVRVgGUPXm7itjRR9W3oDYDfuPC9YKl1dSdnFKKioK/JwlMPWRvwy2tYMn3q9nTXfI13F0dy2CD4u9vh6bAd+TKtPxvM/18r+d+SBqMfM8buZGPbdCRy/q9ls1lyZIE5uSc3rgu6aOWMFaKbEygUb43LyS8r5feJMORUGiJfJc8XOwt3jgzwdIZdJ+eMSMotQUKrkMzrc9cIsq76aoPxSJd/rqDrL4znOdjZoHyherS2VsBmiCV2D8HxMMz1nWgeaDiOkkVOrGdHWGO8OjNDZ4ybA3R7JuSXILVaipEylc5plw+lEfpn4xJhmWgGCLj4udvy0gyl6BXHFsnY2UtGKnada+2LPFXb7nAM30tCtojC7uoQ7i0/uEYKpvUKx/3oa/rmWCkW5Gi/2CKn54PUwtExeUa7iP8n7udkj3McZ7w9siS/+vgmGAd7eFIetr/UQTaHdzyjE/x2+B4ANcD0c5cgsLBNNq3k62cKzGlsoGEvUNbqorMZTh+aetgPY4ujtF9mFAtdS8kRTu49yNZmZAHf7yqfWSpCOXkGiTFBFpijUywkPMotQolSJgl1umb2vi6AmSE8QlFmLomjOj5M649CtdLg7yBHu44xQLyer2iTVEMoEEdLI/X0tla9naRvgiqF6sjfCaasUHdkg4bJ4uUxi9CdAG5kUPi5clql2QVBZuZqviwjzdhb1RenX0pv/WpjtqC5hqr9dgBtc7eUY3TkQv0zuig3TYkyeFQAMB0Hpgl3FfSumP6b2CkWXYA8AbHZt0i9n+Wk0hmHw0Y6r/Mqdl3uH4u+3eiM6VFxEXdWS85oS7R9WixVixjZKrI12BlaICVdJNnU3TXdwDrd/GKDJBHE1QbY2UvhU/D4I64IO39ZsCxNcEUS5CzKVwt8ToSzhvmE1DEibujvghW7BGN4hAK39XetNAARQEERIo1auUmPpPs1y6vcHtYRUT0M1f8GnXV3F0cJl8cMi/fnAxhhc2r6223IkZBXxqf3Kb4zujrb8zu530gv4ZeDVdV1QJBsZqLtpq6kZ6hot/ITPTVnKpBL8MKkTv3XHg8wivLgqFnklSmy58AhnHrBZgyBPB7z9RAR8XO3x+8sxeLVvc/6+YsKqv7LMGKbaP4zL+DnayvgWCKbWxt+VX9JfeYVYco75MkH+bg58vc/DnBIwDMNPhwW6O/B/o2GCQPXwrQz+MpcJkkgkfGCsLxOUJShQb2KGzJ+1oyCIkEbsYlIuHlSsLIkO9UTfCG+9xwZUkQlad0rT4f2lnqHVGofwTUTfJ1ZjiOuBtLMDnZux2RGGAb+zdXUwDMO/GXo52/GfyM3NUCZIOIUoXA3k42KPX1+O4WuubqTm46U1sfjP3zf5Yz5/JpKf1rSRSTF3SCtse60HFj8bidf6aQIiUxJOseXUcIVYqVLFTw81r2iDYA5OdjZ8RuxmagHyBJkrYf2aqRolcmRSCX+fD7OLkV1UhuKK+qNAwVRZmKAoWdi4UVhTxP1O5JUodTbRFGaCrHFbC3OjIIiQRkzYCXdEhwCDbyaGlslnFSpwo+K+2jV15VcoGcvPVdiMseYrxKqaIulUMUUEQLSPlLEe5ZTwGaTIpq5me/OtTLj8unIQJCx49auUEQn0cMSvL8fwn/DjknL5bN2IDgE6g97OwR54LroZHG3NUzLKrQ4Daj4dFp9ZxNeemWsqjNOn4nukUjOiKSdho0RzTIFygUyhohxXBFkoYUfpUG/dTR+FzSB9qmiYaIqaoPqMgiBCGrFbj4Vdjg0vMzbUMPGmoFty15DqT6MIM0FVbfZoSFUrhroIxnYhUfeqKUOEdSHCehFzs7ORwc2BDR4qT4el6ZgOE2ru7Yx1U6PhYq8JalztbTD/6aq3jjAHU+wfVhf1QJyBbTR7jO278Zi/zGVeJBLt4NMUAgXL5E/d09SwCZfPezvbaTU3dLGzEQWaol5BOrKswi0zvGqwOqy+oyCIkEbstmC/q4gqgiDR1hmVCphvpGqCgzb+1V8ibqqGidybo0wqEXWy5QS42fNvCpeScqvdp0a4MqwugyBAMyWmNR0mKozW/Wbcrqkb1rzUle+VtGhkW9EUW10STofVtGFiXQZBnYM9+KDiyO0MfkqJC4K8ne1MsulvZcIprVP3NX2phEXTEolEVBzN3u4oylD6CnoF6fqAUZstMxoCCoIIaaQYhsGdNPbNpKm7A1zt5QaP93CUw65ipUlqpSmrGymaYKom3WFFWaYaToep1Ay/fUBwE0edy/MlEgk6V0yJFZWpcDutQOsYQ64KiqLrPAiqqNcoLlOhSLD/WZqemqDKuoR44sj7/XDovb4Y1dFyDezcTTAdVpdBkI1Myu84X1ymwqn7mVCUq/hg1NT1QBxhEHRDMG0tzAQB7DJ5IWEHZ0AcGOtqmCgMgszREsHaURBESCP1KKeE30y0pRFdjoUbqabksitWONyLtFwmqVGXYfEmqjXLBCXnlEBRzmZ2DC3v7iyoC7pYjboghmFwvWI6zNPJ1mwrkvTxEmRuhF2juU/3rvY2BrfIANhi7jAzLX03lrtD7VeHcUGQXCbhl4Obk2hK7HqaqCbOVBunVibM+Aj+1EQ9hABo/TybGQqCdNUEVUyHudjZ1Kul7aZCQRAhjZSwA64xQRCgydgUlamQX8oGUKVKFd95ONzHxagGiZVxDROBmtcEGbvxpzAIqk7n6NS8Un45cduAuiuK5gj3D+OyEAzD8G9s5qhLMQdbG80mnTUJgspVan6jzpAmTjobe5pa7xbesJezj3PgZppoH69AMwVBzXQEd062MlG9DwCt6bBgT/HXwuygoekwz0Y4FQZQEERIoyWcCjJ2vytxxoadtrqTVsD35qlJPRAgbpiYUsUGrfqIlsf76g+C2gS48m9o1VkhJiyKjqzjqTBA9zL5vBIln/3SVw9kjdwrGibm1mA6LCm7mG/0aO6pMI6DrQx9WrCrxDILy7D7cip/m7kyQW4Ocq2i58r1PgAQ5lUpE+SpPxNUuf1EWbmaX+3YGHsEARQEEdJoiVeGGRe8BOhomFjbeiCOsGFiWXn1N9YU1Yl46w/q5DIpOgS6A2CnBPXtqVSZpVaGcXQ1TNTVKLE+4PYPyy0ug1rNVHG02HXB71vrGgbdNTGwrR9/+c9LyfxlcyyPB9jp58pTX4Ee2tmhEC/xdZVrghxsZXCtWBlYORMkzMQ1xh5BAAVBhDRa3MowuUx7hYk+urbOEBZt1jQTxN531Zs9GiJcHt/cx/Dz6VyDfkHXBG++1pIJ0tco0dpxQZCaYTfwrA7xCr26C4KeaOXDT9kqBEG6uQqjAe2sjrBOiONoa8PXp9lIJaK/Iw4XIKfll4pq+TJFW2ZQJogQ0kiUlavxoKKOp7m3M+RG1lXo2jpDlAmqVRBU8+JohmH4DTWbujtU2eivJkEQ1ynazUEualhXV3TVBIl6BNWTmiCgdg0ThduWtAuou2DUw8lWa381wHzTYYB20FN5ZRjnxR4hkErYTYt11UhxWVaFYPoLqLQ8vhH2CAJoF3lCGqX7GYUor5iGMLYoGtDeOkOtZviu003dHeDmaHiZvSGGmjFWJTWvFAUVK92MqRPp1Kx6QVB6fikfeLSrw07RQrozQZpP8vUpEyRsmJhdVKa1zFsfhmH4TJCXs52oG3JdGNjGj993DWBXVHFNLM1BOxOkOwh6tW9zTO4eond1oG+lhonc91/YKLEx9ggCKBNESKNUk5VhgHYmKCm7GEUVexrVph5I676rmQkSrvIyZorEw8kWzSumAK+n5OncU0mo8s7xluDpZMtPx3A1QWkFwpqg+vNJXtiPpjpdo5NzS/hi6rqcCuM8JVgqD5g3CwSI9wkDdE+HcQy1R9DXMFHcKLH+/P6YEgVBhDRC1dkuQ8jVXrNiJTWvxGT1QIA4E/S4mkHQ2QeajroxoU2MOoebElOqGFx5lGfw2GsWbJLIkUkl/BsVPx1Wb2uCajYdds1CU2GcIE9H0e+5OeuBAB2ZID3TYVXx09MrSLhvmBetDiOENDR30wpwVccbvHC7jJZGrgzjcBmb1LxSXBcUqdY2E+SnY/m9sWLj2SkKmVQi2iTVkC7Bwn3EDE+JXbXwyjCOV0UQlFmoAMMw/Kd6YYBUHwinw6qzk/wNCxVFCw1sq8kGCVdLmoNw5Zmnky2c7GpWweKjp2t0Y99BHqAgiJAG635GIQYtO4bhK07g0K000W3cdJiLvU21Ox9zBcyKcjVO3NNkYGqbCRI2TKzOdFhWoYJfGdauqRu/P1ZVxDvKZ/P3teZkPN7dfAk/HLmHm6n5bKfoijdfFzubOulQrA9XF6RUMcgrUfKf6n1c7CCT1n2dUk0Jp8Oq0zBRuEKvrYWmJUd0COAbgkYbmXWsKXu5DF0qfk+7hVV/Y2KOKBMkmELNKhJOhzXOTBAVRhPSQF1+mAuuBcv3h+5hQCv2E2xeiZLfALWlr0u1i3yFn34vP8wFwAZTtV0xJZdJ4e1ih7R8RbWCoHMJmkLVGB2rd/QJ83KCu6McucVKxMZnY/r68zh0K50vGAeAr/behq+rHb/7dpsAV0gtGGwIV4gl55bw0xl1XSBcW+L9w6oRBFl4hR7AblPx1xs9kVmoQK9wL7M/3soXu+Dsgyz0alHzxxIWRguL6blMkESiaVvQ2FAmiJAGSrjJZlxSLi4msVM+d9JqVhTNES5l57TxN82KKe6+q9Mw8Wx8zYIgqVSCzhWrxPJLy7HvRpooAOKkCbrsWqI/kJBwhZiwNYFfPSqKBsRvuDlFxtUEpeeXIr2iFsoS25YItfZ3Re8W3nUyBk8nWwyJ9IdLFRscG+LlrCmqTy/QrgnydLStV5lEU6IgiJAGqlAhXvG0+kQ8gJoXRXN0NWOrbT1Q5ftmGOMbJp6tWLIskYjrfIxRue+Lj4sdZvQNw5ZXu2PRiLbo19IbdoK90Pq29K7W/ZuaMAgSdrCuT0XRQKUgyMhMkLBTtCXrsuojG5mUrydLzSuFWs2AYRh+iXxjnQoDaDqMkAZLmAkCgH+uPUZKbkmtiqIB3cuCTbV9gXAT0Mf5pXr7onDySpS4WfF8Wvm5VrtP0XMxzfj6qOFRAegd7sU3m+sa4onJPUJQqlThXEI2HOQydAmpeV2GKQiDIGGxdn1qlAiwy7nt5VKUKtVGB0HCoK+tiYLuxsTPzR7pBQpkFCjQbuG/aOHrglIlm21trI0SAQqCCGmwCisFQSo1g/WnE2vcI4ijMxNkoiBI1Iwxt+oVYhcSs8HtAlCdqTCOq70cS8dHGTzGXi5D7xaWzQBxhDVBwvYE9S0TBLDZoNS8UqOXyFMmqHbaBrjxrSCKy1R8PR9AmSBCSANUORMEABtjk/gNKwPc7GvU7bZyTZCNVGJw1/bq8Ktmr6CzD2pWD1RfCTNB3Kd4oH5tnsrhgqDc4jIwDFNlfQ3XKdrRVobQJsZ1mCYac4e0QqCHAy49zMXtxwVIyi7mb9O1HUhjQUEQIQ1UUZkmCOoe1gSnH2SJ9g2qSRYIYKcyPBzl/Cf4cB9n2Nno71ZbHQHV7BotLIpuDC/kwiBIqF4GQU5sAK5UMShUlBss/M0tLsOjHDYz2Mbfsiv06is3Bzne6B/Of12oKMftxwVQMwy/DL8xosJoQhooYWH0m0+Ea91ek3ogjjAbZKqiaKB6DROLFOV8XUy4j3OjaPbmam/D96gR8qtnNUGAuGFibhVTYjQVZnrOdjboHOyBriGeFl1pZ2kUBBHSQAmnw6JDPLWmi2qyMowjzNiYqh4IYFdnca/HVU2HXUzKgapiaq8xTIUBgEQiEdUFAeybmbENIq2JZzVWiFFRNDEXCoIIaaC4IMjORgobmRTTeoWKbq/pdBgABAr2MDJl5165TAofF81SXkNiG9lUGMer0pRYfdo4VUi4f1h2FVtnXKNMEDETCoIIaaC41WFcluCJ1r78hoz2cimae9e8mHlSt2Zo5eeC4R0CTJ6F4fZL4pbz6iMuijbv9gXWpHImqD7WAwHVnQ5jM0G2NlKE+5imCJ8QgIIgQhosLhPEbbook0rw7fgo9G7hhf+MitRZW2KscB8X7H27D75/rqPJi1S7hWkCmiO303UeU6pU4VLFEt/gJo71siampioXR9fH5fGA8fuHFSrKEZ9ZBICdwpXL6G2LmA79NhHSQBVVFEYLd57uHOyBDdNi8GynQEsNq0oDWvnwlw/d0h0EXXqYizIVu0Q82sINDOta5SCovjVK5Ij2DzMwHcZuYstettSmqaThoiCIkAaorFzNBwnOdqZZvl5XOjbz4N8gj9/N1LmHmLAeKCas8UyFAQ01E6R/OkxYFN2uKRVFE9OiIIiQBki4Msypnq0ckkkl6BfBdmguVJTjvGCXeABgGAb/Xn/Mf91YVoZxtGuC6mthtCYIyjYwHSbcHqQdZYKIiVEQREgDVFiPgyAA6G9gSuxqch7fN6ZDoFuV+4s1NFrTYfU0EyScDsvVEwSp1QyO380EwK5yrM2KRkJ0oSCIkAZI2C3a2bb+BUF9I7zB1VsfqlQcvTE2ib/8XHSzuhyWVfCpPB1WT2uCnO1sIJexP+ScIt3TYVeT8/gVgr1beMFeXr+mdon1oyCojj3IKMS87Vfx+m8XsO3CI0sPhzRQ9Xk6DGCXT3euaOX/IKMIiVns6qBCRTl2XkoBADjZyjC8Q4DFxmgpXoLpMKlEe3qsvpBIJPwyeX2rww7cTOMvP9Hat07GRRoXCoLqWF6JEhtjk/D31ceiuW5CTEm4ZUZ9K4zm6JoS23U5BUVl7HMbEdW0XgZ4teVgK4NLxfP2craDTT1eMt6kojg6o0CBPB3F0QduarKATwh+Hwgxlfr711NPiRuEGe6SSkhN1fdMEKB7qbxwKuz5RjgVxhnfNQgSCTChnn8PujdnV/aVqxnsiBNnxh/lFONmqqb2y6ee1j4R60ZBUB1zc9AUAwp39CbElOp7YTQAtPR1QUBFvcvZB9k4l5CNK4/Y7Gm7pq6IDGy8K4U+froNri4chHefirD0UGpFWNO1MfYhGK4hEMQF8U/SVBgxEwqC6pirveYNiYIgYi7CTFB93FwTYGtGuCmxMpUa72+5zN82oWv9zoCYQn39uQpF+LrwtV+30wpwMSmXv23/DU090JNtKAgi5kFBUB2zkUn5+fxcCoKImTSE6TAAeKK1ZkosIasYAOAgl2FkVOMriG6ohNmgTRXTnQWlSpx5kAWA3UuuFS2NJ2ZCQZAFuFZMieVTEETMRFgY7VRPC6MBoHuYF+wq7XE2okMAXOzles4g9c2wSH+4VGTId11JQX6pEsfvZkKpYqfGnmztA4nEtPvTEcKhIMgCuCZhucVK0Rw4IabSEKbDAHYlVI/m4m0xJkQHWWg0xBwcbGUY1bEpAKBUqcZfcck4cIOWxpO6QUGQBXDF0eVqBsVlqiqOJqT6Gsp0GCBeJdbKzwVRQe6WGwwxC2GN129nk3C4okGms50NYsIa17YopG5ZbRCkUCjwwQcfICAgAA4ODoiJicH+/fuNOvfAgQPo378/vLy84O7ujujoaGzYsMHMIzYerRAj5lbYQDJBADC4nWa65LV+zWlqpAFqE+CKDhXB7a3HBfyGqn0jvGFnU3+nc4n1s9og6KWXXsLSpUsxceJELF++HDKZDEOHDsWJEycMnrdz504MHDgQZWVlWLhwIb744gs4ODjgxRdfxLfffltHozdMvGcOBUHE9ITbZtT3TJC3ix0OvtsXe97qhZFRTS09HGImz+uY5hQWxhNiDlYZBMXGxmLTpk1YvHgxlixZgunTp+PQoUMIDg7GnDlzDJ67YsUK+Pv749ChQ5g5cybeeOMNHDx4EM2bN8fatWvr5glUwZUyQcTMhIXRjg1gvyUfV3u0pR3EG7Sn2wfAyVbzuyqVAP1bUhBEzMsqg6CtW7dCJpNh+vTp/HX29vaYNm0aTp8+jYcPH+o9Nz8/Hx4eHrCz0+ynY2NjAy8vLzg4OJh13Mai6TBiblxNkJOtDFIpTR8R6+dkZ4ORHTWZvi4hnvBwsjVwBiG1Z5VBUFxcHCIiIuDq6iq6Pjo6GgBw6dIlvef269cP169fx/z583Hv3j3cv38fn332Gc6fP19lFkmhUCA/P1/0zxzcHTR/2HkltHUGMT0+CKrnU2GkcXmhWzBkFUE79YIidcEqXyFTU1Ph7++vdT13XUpKit5z58+fj/j4eHzxxRf4/PPPAQCOjo7Ytm0bRo4cafBxFy9ejEWLFtVi5MahTBAxN64wur4XRZPGpbW/KzbP6IaU3FIMi9R+DyDE1KwyE1RSUiKazuLY29vzt+tjZ2eHiIgIjBkzBhs3bsSvv/6KLl26YNKkSThz5ozBx503bx7y8vL4f4am3WqDCqOJOTEMQ5kgUm91DvbE8A4BNI1L6oRVvkI6ODhAoVBoXV9aWsrfrs/MmTNx5swZXLx4EVIpG+ONGzcObdu2xaxZs3D27Fm959rZ2ekMvkyNMkHEnEqVaqgrenDW527RhBBiblaZCfL390dqaqrW9dx1AQG654rLysqwatUqDBs2jA+AAEAul2PIkCE4f/48ysosX4NDQRAxp4bUI4gQQszJKoOgqKgo3LlzR6swmcviREVF6TwvKysL5eXlUKm0uzArlUqo1Wqdt9U1N0cKgoj5NKRu0YQQYk5WGQSNGTMGKpUKK1eu5K9TKBRYs2YNYmJiEBTENtVKSkrCrVu3+GN8fHzg7u6OHTt2iDI+hYWF2LVrF1q1amUVy+SdbW3ATXdTEERMrZCCIEIIMYpVvkLGxMRg7NixmDdvHtLT0xEeHo5169YhISEBq1at4o978cUXcfToUX4TUplMhtmzZ+Pjjz9Gt27d8OKLL0KlUmHVqlV49OgRfv31V0s9JRGpVAI3BzlyipVUGE1MrqFsnkoIIeZmta+Q69evx/z587Fhwwbk5OSgffv22L17N/r06WPwvI8++gihoaFYvnw5Fi1aBIVCgfbt22Pr1q0YPXp0HY2+alwQRJkgYmqiLTNsrfZPnBBCLM5qXyHt7e2xZMkSLFmyRO8xR44c0Xn9888/j+eff95MIzMNrjg6v1QJtZqh5aDEZIRbZtDqMEII0c8qa4IaAzdHtms0wwAFpeVVHE2I8Wg6jBBCjENBkIXQMnliLrQ6jBBCjENBkIW4OWjenCgIIqZEfYIIIcQ4FARZiHAT1VzaRJWYEGWCCCHEOBQEWQhNhxFzocJoQggxDgVBFuJGm6gSM6HCaEIIMQ4FQRZCmSBiLjQdRgghxqEgyEKEQVA+BUHEhETbZlCzREII0YuCIAtxp+kwYiZcx2ipBLCX0584IYToQ6+QFkLTYcRciioKo53sbCCRUCdyQgjRh4IgCxEGQbREnpgSVxNERdGEEGIYBUEW4iCXwVbGfvvzSmjbDGI6XBBERdGEEGIYBUEWIpFI4MptokrTYcRE1GoGRWWa6TBCCCH6URBkQVxxdG4xTYcR0yhWaholOlOjREIIMYiCIAvi6oKKylRQqtQWHg1pCIpoeTwhhBiNgiALol5BxNRo81RCCDEeBUEW5C5aIUZBEKk96hZNCCHGoyDIglypVxAxsUIKggghxGgUBFmQqGEidY0mJlCkoMJoQggxFgVBFiTcOoMyQcQUaDqMEEKMR0GQBdHWGcTUaDqMEEKMR0GQBdEmqsTUimh1GCGEGI2CIAuiTBAxNZoOI4QQ41EQZEG0iSoxtUIqjCaEEKNREGRBbg62/GVqlkhMgTJBhBBiPAqCLIimw4ipFZbRthmEEGIsCoIsyNZGCkdbdsqCCqOJKVBhNCGEGI+CIAvjskGUCSKmQNNhhBBiPAqCLIyCIGJKXGG0rUwKWxv68yaEEEPoVdLCuCBIUa5GqVJVxdGEGMZlgpxoZRghhFSJgiALo+JoYkqaIIimwgghpCoUBFmYqFcQFUeTWuK2zaCiaEIIqRoFQRZGm6gSUylXqaEoVwOgTBAhhBiDgiALo+kwYipFgm7RFAQRQkjVKAiyMDdHTdfo3GLaOoPUnLBRIm2ZQQghVaMgyMIoE0RMRdQjiLpFE0JIlSgIsjAKgoipFFKjREIIqRYKgizMnYIgYiK0ZQYhhFQPBUEWRpkgYiq0ZQYhhFQPBUEWRn2CiKkUClaHUWE0IYRUjYIgC3O1gkzQw+xi/OfvmzjzIMsij09MgzJBhBBSPRQEWZhMKoGLPfuGlW+hIOiLPTex8tgDvPrrBZRVNNsj9Q8VRhNCSPXQK6UVcHeUo6C0HLkWCoIuPcwFwE7HJWUXI9zH2SLjILVDhdENj1KphEpFGysTIpPJIJfLqz6wmuiV0gq4OcjxECXIK1GCYRhIJJI6e+xCRTke55fyXz/IKKQgqJ6i6bCGIz8/H5mZmVAoFJYeCiFWw87ODl5eXnB1dTXZfdIrpRXgiqNVagaFinK42Js+2tUnPqNI9PWDzCI9RxJrR4XRDUN+fj6Sk5Ph7OwMLy8vyOXyOv1gRIi1YRgGSqUSeXl5SE5OBgCTBUIUBFkBdwfh1hlKo4OgUqUKtx8XILKpG6TSmr1IPsgsFH19P71Qz5HE2lEmqGHIzMyEs7MzAgMDKfghpIKDgwNcXFzw6NEjZGZmmiwIosJoKxDo4cBfvmdkEHI+IRv9vz6Ckf93Ep/uvlHjx64c9FAmqP4qKqMgqL5TKpVQKBRwc3OjAIiQSiQSCdzc3KBQKKBUmqaGloIgK9AmQBPRXk/JM3isWs3gf0fvY/zKM0jNY2t5Tt7LrPFj368U9DzIoExQfVVIe4fVe1wRtDkKQAlpCLi/DVMtGKBXSivQVhAE3UjN13tcTlEZ3ttyGYdupYuuF775VVflTFBOsRLZRWXwdLLVcwaxVtx0mINcBlkNp0eJdaAsECG6mfpvw2ozQQqFAh988AECAgLg4OCAmJgY7N+/3+jz//jjD3Tv3h1OTk5wd3dHjx49cOjQITOOuOZCvZxhL2d/FNdTdAdBafmlGPbdcT4AkkgAWxv2nMLSmgVBajWDhCzt6S/KBtU/Zx5kITGrGADgbE+fbQghxBhWGwS99NJLWLp0KSZOnIjly5dDJpNh6NChOHHiRJXnLly4EM899xyCgoKwdOlSfP7552jfvj1fVW5tZFIJWvmx2aDErGIUlGrPda489gApFdNfnk62WDclGq39XAAAhWXlUKuZaj9uSl4JSpXazRHvUxBUrxy5nY7Jq2OhqGh0+VQbXwuPiBBC6ger/MgYGxuLTZs2YcmSJZg9ezYA4MUXX0S7du0wZ84cnDp1Su+5Z86cwaeffopvvvkG77zzTl0NudbaBLjyTQtvphYgOtRTdHtsfDYANgO0c2ZPBHo44qdj9wEADAMUK1XVbpB3X7A8vo2/Kz8V9yCDiqPri3+vP8bM3y9CqWKD4P4tvfHJ020sPCpCCKkfrDITtHXrVshkMkyfPp2/zt7eHtOmTcPp06fx8OFDvecuW7YMfn5+mDVrFhiGQWFh/chqiOqCKhVHFyrK+YLplr4uCPRwBCDuClyTKTHhtJcwe3CfgqB64a9LyXj9N00ANKSdH356oQvs5dQjiNR/ly5dwquvvoo2bdrA1dUVtra28PPzw1NPPYVvvvkGGRkZlh5inQoJCUFISIilh9HgWGUQFBcXh4iICK0+ANHR0QDYPw59Dh48iK5du+K7776Dt7c3XFxc4O/vjxUrVphzyLXWxl9/cfSlpFxws11dQjz4653tNCtIChXVXy4onPbq1cKLr0uimiDrd+ZBFt7+4xJUFb8Yz3Zsiu+f68jXiRFSX6nVasyePRsdO3bEL7/8Aj8/P0ydOhXvv/8+hg8fjtTUVMyePRuhoaFWW+JA6g+rnA5LTU2Fv7+/1vXcdSkpKTrPy8nJQWZmJk6ePIlDhw5hwYIFaNasGdasWYM333wTcrkcM2bM0Pu4CoVC1KY+P1//Si1Ta+XnCqkEUDPaxdGxCdn85a4hmmkyF0EBbEGNMkGajE+4tzNCvZxxMzUfSdnFUKrUkMvoDdVaLT9wF0xFYPxcdDN88Uy7GjfMJMSafPTRR/jmm2/QqVMn/PHHHwgPD9c65uLFi/jggw9QUlJigRGShsQq3+VKSkpgZ2endb29vT1/uy7c1FdWVhZ++eUXzJ49G+PGjcOePXvQpk0bfP755wYfd/HixXBzc+P/BQUF1fKZGM/BVoYwb3bPrrtphaLd3M/rCYJE02E1WCbPZYI8nWzh4WSLMG8nAEC5mkFSdnG174/UjSuPcnH6QRYAINTLCZ9TAEQaiDt37mDJkiXw9vbG3r17dQZAANCpUyfs379fa3roypUrmDBhAvz9/WFra4vg4GC8+eabyMrKEh2XkJAAiUSCl156Cffu3cOoUaPg4eEBJycnPPnkk7h8+bLOx01PT8c777yD8PBwfh+r0aNH49q1a1rHctNXubm5mDlzJoKCgmBjY4O1a9cCAC5cuICZM2eiXbt2cHNzg4ODAyIjI/Hll1+KGgFyY01MTERiYiIkEgn/b+HChaLHXLNmDWJiYuDs7AxnZ2fExMTwjyd05MgR/vxTp05h4MCBcHd3b5StGWoVBKlUKuTn56O8XPwGXFJSgkWLFmHUqFF455139GZu9HFwcNC5cWBpaSl/u77zALaZ0pgxY/jrpVIpxo8fj0ePHiEpKUnv486bNw95eXn8P0O1R+bATYmVqdR852ilSo24pFwAQFN3BwS4a567cCl0dWuCChXlSMtnv8dhXmzw07zif4C2z7BmPx17wF9+pXcY9QQiDca6deugUqkwY8YMeHt7V3m8jY3mNXDnzp2Ijo7Gzp070a9fP7z99tuIjIzEihUr0L17d+Tk5Gidn5CQgG7duiE7OxtTp07FU089hYMHD6J///5IS0sTHXv//n107twZy5YtQ/PmzfHmm29i6NCh2Lt3L7p164azZ89q3b9CocCAAQOwb98+jBgxAm+88QZ8fdn6y59//hk7duxAZGQkZsyYgWnTpoFhGMybNw8TJkzg78Pd3R0LFizgP5wvWLCA/9evXz/+uLfeegtTp05FcnIypk2bhmnTpiE5ORlTpkzBrFmzdH7/Tp06hX79+kEikWD69OkYP358ld/zBoephU8++YSRSqXMsWPH+OvUajUTHR3NSKVSRiKRMFKplGnWrBmTnZ1t9P0++eSTTOvWrbWuP3DgAAOA2blzp87zVCoVY29vz/j5+Wnd9uOPPzIAmEuXLhk9jry8PAYAk5eXZ/Q5tfG/I/eY4A92M8Ef7Ga2nH/IMAzDXErK4a97a+NF0fG/nkngb/vjXFK1HuvyQ839ztlymWEYhvkz7hF/3Y9H7pnmSRGTSsgsZELnsj+jzp/tY0rKyi09JGJCJSUlzI0bN5iSkhJLD8Ui+vfvzwBgDh48WK3zMjMzGVdXV6Zp06ZMQkKC6LaNGzcyAJiZM2fy18XHxzMAGADMl19+KTr+448/ZgAwixcvFl3fo0cPRiaTMXv37hVdf/v2bcbFxYWJjIwUXR8cHMwAYAYNGsQUFxdrjTkxMZEpLxf//arVambq1KkMAObEiRNa9xccHKzz+R89epQBwLRu3ZrJzc3lr8/OzmYiIiIYAKL36cOHD/PPf/Xq1Trv01oZ+zdi7Pt3rWqCDh48CD8/P/Tu3Zu/bteuXTh37hwiIiLw+uuv459//sG+ffvw888/Y86cOUbdb1RUFA4fPoz8/HxRcTQXaUdFRek8TyqVIioqCufOnUNZWRlsbTVdj7lslDGfLiyl8vYZYzoH4pxgKqxLiHjZfG1WhwnrgbhpsDAvZ8HtlAmyRr8cj+eL5Kf0DKWVYI3M8O9PIKNAO0tuLbxd7LDrzV41Pv/x48cAgICAAK3bjhw5giNHjoiu69evH/r164f169cjPz8fK1asQHBwsOiYCRMmYMmSJdi0aRO+//570W2hoaF4//33RddNmzYNn3/+Oc6dO8dfFxcXh1OnTmHq1KkYNGiQ6PiIiAi88sorWLp0Ka5du4Z27dqJbv/qq690zl40a9ZM6zqJRII33ngDq1evxoEDB9CzZ0+tY3RZt24dALZHnpubG3+9h4cHFixYgIkTJ2Lt2rWi92qAnVacMmWKUY/RUNUqCIqPj0erVq1E1/3111+QSCT47bff0LlzZ7z++usIDAzE1q1bjQ6CxowZg6+//horV67k+wQpFAp+vpOr1UlKSkJxcbFoDOPHj8eZM2ewbt06vPLKKwDYabTffvsNbdq00fnHZS1EK8QqiqPPJ2hSuF0FK8OA2hVGC1eGNa+oReKCIfZ2WiZvbbIKFdh8np2idbSVYVJMcBVnkIYmo0CBx/mllh6GRRw5cgSLFi3Sur5fv344c+YMAPaD8v3797WOKS0tRWZmJjIzM+Hl5cVfHxUVBalUXBUSGBgIAMjNzeWv4+4/LS1Nqw4HAG7dusX/LwyC7O3tERkZqfP5lJWVYcWKFdi0aRNu3bqFwsJCMIym6W11ykji4uIAQDQ9xunfvz8A3auqu3btavRjNFS1CoKysrLg5+cnuu7kyZNo2rQpOnfuzD6AjQ26devG/xIZIyYmBmPHjsW8efOQnp6O8PBwrFu3DgkJCVi1ahV/3IsvvoijR4+KfnFmzJiBX375BW+88Qbu3LmDZs2aYcOGDUhMTMSuXbtq83TNromzHfxc7fE4vxQ3UvOhVjM4n8hmglzsbRDh4yI6vjZL5HVlgpzsbPjHp0yQ9Vl3OpHvCv1cdDO4OdImm42Nt4v2ghFrUtvx+fr64ubNm0hJSdH6gL1w4UI+ANm0aROee+45/rbsbPZ18v/+7/8M3n9RUZEoCKrchgXQ1BkJN+jk7n/Pnj3Ys2ePwfsX8vHx0VtsPGbMGOzatQsREREYP348fHx8IJfLkZubi+XLl+usi9UnPz8fUqlU50yHr68vJBKJztXOXH1SY1arIMjGxkb0Q8/JycHdu3cxbtw40XEuLi7IyzO8O3pl69evx/z587Fhwwbk5OSgffv22L17N/r06WPwPAcHBxw6dAhz5szB6tWrUVRUhKioKOzZs0crjWmN2gS44nF+KQpKy3HiXiYyC8sAAF2CPbRWANVmdRiXCZLLJAjydOSvD/N2wuP8UtpI1coUl5Vj/ekEAOw2K1N7hVp2QMQiajPVVB/06NEDR44cweHDhzFgwACjz+OCmatXr2pNR5kCd//ff/89Zs6cafR5+gKgc+fOYdeuXRg0aBD27NkDmUwzrX3mzBksX7682uNTq9XIyMiAj4+P6Lb09HQwDKMz4GuMq8Eqq9XqsLCwMJw5cwZqNfvpdPfu3WAYBr16if9Q09PTq12LY29vjyVLliA1NRWlpaWIjY3VCmKOHDkiygJxfHx8sHbtWmRlZaG0tBRnzpypFwEQIO4cve5UAn+5cj0QUPPpMJWaQXwmG7w283QU9QPipsYAqguyJlvOP0JuMZvtG9EhAE3dda+QJKQ+mzx5MqRSKVauXInMzEyjz4uJiQEAnD592izjMvX9c1N2w4YNEwVAAHD8+HGd58hkMlF2Sqhjx44AoFUzJbxOXy1tY1erIGjEiBFIT0/HyJEjsXz5cnzwwQeQyWQYPnw4fwzDMIiLi0NoKH1yNYawLujQ7XT+clcdQVBNM0EpuSX8tIow6AHEdUG0h5h1YBgGq07E819P7xNmwdEQYj4RERGYM2cO0tPTMWTIENy7d0/nccJ6HQCYMmUKXFxc8NFHH+H69etaxxcXF1erJKOy6OhoxMTEYOPGjfjjjz+0bler1Th69KjR98cVb1feEPz69etYvHixznM8PT2RmZnJt4oRmjx5MgBg0aJFommvvLw8vo6KO4aI1Wo6bM6cOfjrr79E86Rz584VVb2fOHECmZmZWtkholvbAE1lP5fkspVJ0T7QTetYpxquDhMWRYdpBUHOOo8jlvMgs4hvXtk9rAla+2untQlpKL744guUlZVh6dKlaNWqFfr06YMOHTrA0dER6enpuHLlCmJjY+Hs7MxnN7y9vbFx40aMHTsWHTp0wODBg9GqVSsoFAokJCTg6NGj6NGjB/bu3VvjcW3cuBH9+/fHhAkTsGzZMnTq1AkODg5ISkrC6dOnkZGRoTNA0SU6OhrR0dHYvHkzUlNT0a1bNyQlJWHnzp0YNmwYtm7dqnXOgAEDcP78eQwZMgS9e/eGra0t+vTpw/9788038f3336Ndu3YYPXo0GIbBtm3b8OjRI7z11ltVlpI0VrUKglxdXREbG4utW7ciLS0NXbt2Rd++fUXHZGVlYdasWY2zCVMNBHo4wMXOBgWCzE5koJvOpdC2NlLY2UihKFdXKxN0X0dRNKc5rRCzOmceaLrd9m1pvS0eCDEFqVSKb775BpMmTcL//vc/HDt2DOfOnYNCoYCnpyfatm2LJUuW4MUXXxTVvwwbNgxxcXFYsmQJDhw4gP3798PJyQmBgYGYMmUKJk2aVKtxhYaGIi4uDkuXLsWff/6JNWvWQCaTwd/fH3369BE16K2KTCbD7t27MXfuXOzduxfnzp1DixYt8PXXX2PIkCE6g6D58+cjJycHu3fvxvHjx6FSqbBgwQI+uPnuu+/QsWNH/Pjjj1i5ciUAoG3btvj0008b/TJ4QySMrqIaAoCtuHdzc0NeXp7OojJzGffTacTGa/oDvdq3OeYOaaXz2C6fH0BmoQJN3R1wcq5xhYQf7biK386ynbO3vdYDnYM1S+/VagZtFuxFqVKNMG8nHHqvX82fCDGJtzbGYedldrnsjtd7oGMzjyrOIPVVaWkp4uPjERoaym8TRAjRMPZvxNj3b7PuHZaXl6ezcJkY1qbSdEfl/kBCXHF09TJBwh5B4kyQVCpBSBP2uqQsdiNVYjkMw/CZICdbGdo11Z4WJYQQUjO1CoKuXbuG7777Dnfu3BFdf/jwYYSGhsLT05NfqUWMJ+wcDUCUqamMK44uVJQbHXByBc9NnGzh7qi9BL65D1sXRBupWl5CVjHSKzoEdw7xFK3kI4QQUju1ekX97rvv8O6774pagmdlZeGZZ55BYmIiGIZBVlYWXn75Zb6jJamacJl8hK+zzkCFwwVBKjWDUmXVWZuCUiX/plq5Hogj3Ej12J0MHLqVhg1nEvHt/juibTyI+QnrgbqFaa8QJIQQUnO1CoJOnjyJtm3b8ttYAMCGDRtQUFCAGTNmIDc3F+vXr4dardbas4XoF+HrwgcoI6OaGjzWWdQrqOqu0cJl75WXx3OEK8QW7bqBqWvPY/6f17D84F28sOos0hpp235LOCsIgmJCm1hwJIQQ0vDUKghKS0vT2gRu//79kMlk+Pzzz+Hq6opJkyahY8eOZmti1RDJZVLsnNkLu2b2wmt9mxs81kWwTL7AiLqghCxNEBTqpTsTVHk6TqhUqcauy8bvaUNqjq0HYjNvDnKZzjYJhBBCaq5WQRBXfS109uxZREVFoUkTzafWFi1aIDk5uTYP1eg429kgMtBNa6sMrePsq9crKClLU+MT3ER3EBTh64KPh7VGnwhvjOkciLeeaIEPh2pWp1EQVDeSsov5zTK7hHhQPRAhhJhYrfsECYObmzdvIjs7GxMnTtQ6lvYoMY/qdo0WFjo3E+wZVtnLvcPwcm9xZ+I/41JwIzUflx/lITGrSG8QRUxDXA9EU2GEEGJqtfpoGRUVhVOnTvGtzVetWgWJRKLVMDE+Ph7+/v61eSiih3M19w9LFARBQZ7V239qeIcA/jJlg8zv7ANNEXpMKBVFE0KIqdUqCJoxYwaUSiU6d+6Mjh074ttvv4WPjw+GDRvGH1NQUIBLly6ZZWdfArjYy/nLxmSCHlYEQZ5OtqJzjTG8gyaQ3UlBkFkJ+wPZy6VoH+hu2QERQkgDVKsgaOzYsVi4cCHKy8tx+fJlBAcHY8uWLbCzs+OP2bx5M5RKpVZ2iJiGi2j/MMOrw0qVKr7GJMjAVJg+gR6OfM+iO2mFuP24oNr3QYzzKKcEKXnsz6pzsAdsbageiBBCTK3Wr6yffPIJcnJykJ6ejgcPHmhtlPrUU08hLi6O9i4xk+rUBCXnlvCbsgbXIAgCgBGCKbGdl6nY3VxOC+uBaGk8IYSYhUk+Xtra2sLLy0vnbc2aNUOHDh3g7Ky7Jw2pHVFNUBVBkHBlmKGiaEOGRvqDW7C263IqbYtiJqJ6ICqKJoQQs6jV6jChsrIyXLhwgV8t1rRpU3Tu3Bm2tvq7HZPaE2WCqiiMNnZlmCHeLnbo0dwLJ+5lIim7GJcf5SEqyL1G90X04+qB7Gyk6BBE/YEIIcQcap0JKi8vx/z58+Hj44NevXph/PjxGD9+PHr16gUfHx988sknKC83fnNPUj0u9sZPhyWJVobVLAgCKk2JXaICaVN7mF2M5NwSAECnZh6ws5FZeESEWL8jR45AIpFg4cKFlh4K+vXrZ9a2MGvXroVEIqF9OU2gVkGQWq3GiBEj8J///Af5+flwd3dHx44d0bFjR7i7uyM/Px9ffPEFRo4cCbWadiM3B2EmqKol8qJMUJOaB0GD2vpBLmP/wHdfSYFKTVNipkT9gUhjlpCQAIlEIvrn6OiIgIAAPPHEE/jkk09w//59Sw+zUQoJCUFISIilh2FStQqCfvnlF+zduxfBwcHYunUrsrKycP78eZw/fx5ZWVnYtm0bgoODsXfvXqxatcpUYyYC1ekYzS2Pt5VJ4edqX+PHdHOUo2+EDwAgvUCB2Hj9m6reSMnH0v13kFKR2SCGlZSp8MMRzQs8bZpKGqvmzZtjwYIFWLBgAWbNmoUhQ4YgPT0dn332GVq2bIkPP/xQVJMYHR2NmzdvYubMmRYcNWv9+vW4efOm2e5/1KhRuHnzJkaNGmW2x2gsalUTtH79ejg4OODQoUM6o8NRo0YhKioKbdu2xbp16/DKK6/U5uGIDnY2MtjKpChTqQ0WRjMMw2eCAj0cIKtiO46qjIgKwIGbaQCAzecfontz7YxFRoEC4386jQJFOeKScrBhWkytHrMxWLr/NuIz2f3dOjZzR9cQCoJI4xQeHq5zauvEiRN44YUXsHjxYshkMnz22WcAAEdHR7Rq1UrreEuovKemqbm5uWltWUVqplaZoGvXrqFfv34G02OhoaEYMGAArl27VpuHIgZw2aBChf4+QZmFZSguUwGoXT0Q58nWPnCteNwdccm4/DBX65il++/wgdnp+1lGNXNszC4m5WDViXgAgK2NFEvGdKhy7zhCGptevXph7969sLOzw1dffYWHDx8C0F8TdPfuXUyZMgWhoaGws7ODp6cnOnTogLfffltrdWtBQQEWLVqE9u3bw9HREW5ubujYsSPmz58PpVLz+iqRSNCvXz8kJyfjxRdfhJ+fH6RSKY4cOQJAd02QsI5n165diImJgaOjI5o2bYr58+fzJSPr1q1Dhw4d4ODggGbNmmHJkiVa3wN9NUHcuNLS0jB58mR4eXnBwcEB3bp148cmdOHCBcycORPt2rWDm5sbHBwcEBkZiS+//FL0fLkpysTERCQmJoqmKit/v9esWYOYmBg4OzvD2dkZMTExOmuXhD+vU6dOYeDAgXB3d6/zLbZqFQQpFAqjolEXFxcoFIraPBQxgCuONjQdZoqVYUKOtjaY9WQE//WiXddFLyi3Hufjj3NJ/NflagbnEvRPmzV2pUoV5my9Aq686p0nIxDuQ20lCNGlZcuWGDduHMrKyvDnn3/qPS4lJQXR0dH47bffEBUVhXfeeQcTJ06Ev78/fvjhB6hUKv7Y9PR0REdHY+HChZDJZHjttdcwdepU+Pn54b///S+KiopE952VlYXu3bvjypUrmDBhAqZPnw5XV9cqx75jxw6MGzcOYWFhePXVV+Hs7IzPP/8cn3zyCZYsWYK33noLHTp0wPTp06FWqzFnzhysX7/e6O9Nbm4uevXqhevXr+OFF17As88+i/Pnz2PQoEFayYiff/4ZO3bsQGRkJGbMmIFp06aBYRjMmzcPEyZM4I9zd3fHggUL+AwUN025YMEC9OvXjz/urbfewtSpU5GcnIxp06Zh2rRpSE5OxpQpUzBr1iyd4z116hQfNE6fPh3jx483+rmaBFMLLVq0YEJCQpjy8nK9x5SXlzMhISFMeHh4bR7KIvLy8hgATF5enqWHYtDQ5ceY4A92M+Ef7mHUarXOY3ZcfMQEf7CbCf5gN7Py6H2TPG5ZuYoZ8PVh/n53XHzEMAzDqNVqZuLPZ/jruX9f7LlhksdtiP77z03++zT8++OMslxl6SERCygpKWFu3LjBlJSUWHooFhMfH88AYAYNGmTwuFWrVjEAmBdeeIFhGIY5fPgwA4BZsGABf8x3333HAGCWLVumdX5WVpbo69GjRzMAmA8//FDr2MePHzNKpZL/GgADgJkyZYrO97++ffsyld9e16xZwwBg5HI5Exsby1+fn5/P+Pj4MI6Ojoyfnx9z/77m9TkpKYmxtbVlIiMjdd7XmjVrRNdz43r99dcZlUrzGvLLL78wAJgZM2aIjk9MTNQav1qtZqZOncoAYE6cOCG6LTg4mAkODtZ6vgzDMEePHmUAMK1bt2Zyc3P567Ozs5mIiAgGAHPs2DH+eu7nBYBZvXq1zvvUxdi/EWPfv2tVEzRo0CD88MMPmDVrFr799lvI5eK9qMrKyvDOO+8gKSkJb7zxRm0eihjArRBTqhgoytWwl2svqU7MMs3KMCG5TIpPhrfF5NWxAIDF/9zEU218cTY+CyfuZQIA/FztkVZQCoYBTt3PNMnjWoOycjXe23IZRYpyfDO2Azycat4P6+qjPPx07AEAQC6TYMmYDrCR0TYZRIef+gKF6ZYehX7OPsCMo3XyUAEBbKuOzMyqX1ccHLQ3i/b01NTbPX78GNu3b0fz5s111iH5+vpqXWdra4uvvvoKMln1WlhMmjQJXbt25b92cXHB008/jdWrV+ODDz5AWFgYf1tQUBB69eqFo0ePory8HDY2Vb9lOzk54b///S+kUs1ryOTJk/Hqq6/i3LlzomN11S5JJBK88cYbWL16NQ4cOICePXsa9bzWrVsHAFi4cKFohsjDwwMLFizAxIkTsXbtWvTu3Vt0XqdOnSy6o0StgqC5c+fi999/x48//oi//voLEyZMQGhoKADgwYMH+OOPP5CSkgJPT0988MEHJhkw0Va5V5CuIMjU02GcvhHeeLK1Dw7cTEdavgLfH7qH/Tce87d/NKw1fjxyHzdS83E9JR+5xWVwd6z/DTQP3EzDropNZFccvof5T7ep0f2o1QzmbLvCtxl4c0ALtPRzMdk4SQNTmA4UUG8uYw0fPhzz5s3DG2+8gYMHD2Lw4MHo27evKNAAgPPnz4NhGPTv31/rw7w+oaGhendKMCQqKkrrOn9/f4O3qVQqpKWloWnTplXef0REhNYODTY2NvD19UVubq7o+rKyMqxYsQKbNm3CrVu3UFhYKCprSEkx/nctLi4OAETTY5z+/fsDAC5duqR1mzAgtIRaBUFNmzbF3r17MXbsWCQlJWHp0qWi2xmGQbNmzbBt2zajfnikZip3jfZyttM65qGJGiXq8tGwNjh6JwNKFYP/HdUs7+4c7IGn2/vj8sNc3EjNB8MAZx5kY3A7P5M+vikwDFOtgrwbKfn85S3nH2L2wJZwsK1+U8PYhGzcTGXvq7W/K17r17za90EaEWcfS4/AsDocH/cG7e3trfeYkJAQnDlzBgsXLsTff/+NzZs3AwBatWqFTz/9FGPHjgUA5OXlAUC13qd0ZYeMoatuiMvwGLpNWKhc3fvn7kdYAwUAY8aMwa5duxAREYHx48fDx8cHcrkcubm5WL58ebVqefPz8yGVSnX+PHx9fSGRSJCfn6/zNkuq9bYZXbt2xZ07d7BlyxYcOXJEtG1Gv379MHbsWNy4cQPHjh1Dnz59aj1gos3ZiK7RXCaoiZOtKGgyhVAvJ0ztGcpP6XA+HtYaEokEPcKb4JeKVU+n72daXRD037238Me5h/jk6TZ4pqNxL4J30gr4y/ml5dh5ORnju1Z/WSyXTQKAV/uGQU7TYMSQOppqqg+41U5VZRLatWuHrVu3QqlU4sKFC/jnn3/w3XffYfz48QgICEDPnj3h7u4OAPz7lzHqehWTqZ07dw67du3CoEGDsGfPHtG03pkzZ7B8+fJq3Z+rqyvUajUyMjLg4yMOhtPT08EwjM4AzdLfR5O8G9ra2mLixImYOHGizttfe+01nDt3jrbPMBNnO036VlfX6FKlCo/zSwGYPgvEmTkgHNsuJiOzkP3kMDIqAB2beQAAuoZ4QiaVQKVmcOp+lta5DMMgv7QcrvY2df4HkVmowI8VzQm/O3i3RkEQAKw/nYhxXYKqNX6lSo1/rrFTh/ZyKZ5sbdlPRITUF3fu3MHmzZthZ2dndMNAuVyObt26oVu3bggPD8eLL76I3bt3o2fPnujSpQukUikOHz4MpVJp9JRYfcZ13R42bJhWXdPx48d1niOTyVBWVqbzto4dOyIuLg5HjhzBuHHjRLdxAauu6T5Lq7OPnQztNm42wpqgglLtlOmjHM1UWLCJiqK1xyDHpyPbQiaVwNvFDnMGtxLd1j6QLZS7m16I9IJS0bmf7r6BDov24aM/676X1LE7GfzlB5lFyCnS/QcuVFKmQqJgehEArqfkI05HryRDTt3PQnbF4z3R2hdOJs7QEdIQnTx5EoMGDYJCocDcuXMNTmFduHBB5xRMWhrb6NXenu2c7+vri9GjR+P+/ftYtGiR1vHp6ekN7kN8cHAwALb5pND169exePFined4enoiMzMTpaWlWrdNnjwZALBo0SLR9zwvL4//nnLHWBN61W0ARDVBOqbDzFUUXdnQSH9EBbnDydYGbo7iT1I9mjdBXFIuALZx4sgo9oXrQmIO1pxMAAD8fjYJozs1RefguuuSfOR2hujruIc5GNDKcEbmXnohuJje3VGO3GI28Pz1dCI6VWS/jCGcChvePsDAkYQ0Pvfu3eNXapWVlSE9PR2xsbG4evUqZDIZPv74YyxYsMDgfWzYsAE//fQT+vTpg+bNm8PV1RU3btzA33//DU9PT9GqpB9++AHXrl3DF198gb///hsDBgwAwzC4c+cO9u3bh7S0NH7arCGIjo5GdHQ0Nm/ejNTUVHTr1g1JSUnYuXMnhg0bhq1bt2qdM2DAAJw/fx5DhgxB7969YWtriz59+vD/3nzzTXz//fdo164dRo8eDYZhsG3bNjx69AhvvfWWVZbEUBDUAFS1k3xSlvmKoisLcNdeigoAPZp74f8Os+lXLghiGAZf7LkhOu6rvbexaXq3OpkWU6kZHL9bKQhKyq0yCLotmAp7uVcofj4ej7wSJXZfScVHw1qjiY7C9MoU5Sr8WzEV5mxng34t9Rd3EtIYCbMyDg4OcHd3R6tWrTB//nxMnjwZzZtXvYjgueeeQ2lpKU6ePInY2FgoFAoEBgbitddew/vvvy9aIu7l5YUzZ87g66+/xpYtW7BixQrY29sjNDQUc+fOhZOTk9meqyXIZDLs3r0bc+fOxd69e3Hu3Dm0aNECX3/9NYYMGaIzCJo/fz5ycnKwe/duHD9+HCqVCgsWLOCDm++++w4dO3bEjz/+iJUrVwIA2rZti08//dSiy+ANkTB1ME/VvXt3xMbGalWmW7v8/Hy4ubkhLy/PqE6glrLv+mNM33ABAPD+oJZ4o3+46PZPd93A6pNsYfKm6d0ssjN5qVKF9gv3oUylRjNPRxyb0x9/X03F679d1Dp23dRo9I0wf1AQl5SDUT+cEl3XM7wJfnu5m8Hz/vP3TaysKAJfPzUax+9m4Ofj7Pf3g8GtjFrhJfyZPduxKZaOj6rBMyANTWlpKeLj4xEaGspP1RBCNIz9GzH2/ZuWojQAVa0OS8rWtHs353SYIfZyGToFu1eMpxgPMgrx5T+3+NvHdg7kLy/59xbUavPXkB29k6F13aWkXL5njz63H2syQS39XDCpWzD/9a9nEqs8HwB2XUnlLw/vQFNhhBBiCRQENQAugtVhuvYP42qCbGVS+Lla7tNlj+aaxmLv/HGJH1eP5k3w39Ht0cafjdavJefzq6bMSVgPFBXkDgAoKlPhbnqBnjNY3MowNwc5fFzsENzEic9cJeeW4Mhtwx19i8vKceAGW5jp7ihHz/DqN1wjhBBSe9WqCarOJm5CGRnan7iJ6RjKBDEMwwcbgZ4OFt2VvEfzJli6n718+RHbnEwiYbtKS6USvD+4JaasYdu6f7P/Nga19YWNTIoiRTnWnkrA7iupeCYqADP61r6hYE5RGS4/ygUARPg6Y1ikPy5VrO66mJiLVn6606d5JUqk5pXy53G1Sy92D+YzS+tOJ+IJA8vdD91KR4mSnRoe0s4Ptjb0WYQQQiyhWkHQSy+9VKOC1ep24yXVI1wdVrlPUEahAqVKNQDLTYVx2ge6w9FWhuIyTW3Ysx0D0TaAXT7fL8Ib0SGeiE3IxoOMImyMTUKZisGPR+4hs5BdSn7rcT5GRAXA3013Abaxjt3N4Fd49Wvpg47N3PnbLibl4PkY3Y0P7wqKoiN8Ndtb9Gvpg6buDkjOLcGxOxk4eidDb10TrQojhBDrUK0gqFmzZhTMWCHx6jBxn6CHdbQ83hi2NlJ0DfHkMyZ2NlLMHhTB3y6RSDBncEuM+d9pAMD8v65r3QfDsEHE9D61ywYdFUyF9YvwRrumbpDLJFCqGMQl5eg9T7gyTLjHl0wqwawnWmDOtisAgI92XMW+d/rA0Vb8J5ZfqsThisf2crZDjAWK1AkhhLCqFQQlJCSYaRikNuxspLCRSlCuZrSmw0S7x1s4CAKAXuFefBD0Su8wrYxOlxBPDGjlg0O3xHU1T7b2xYGbbB3NjrjaBUFqNYNjFUvjHW1l6BziATsbGdoEuOHyw1zczyjSu9Hrnce6M0EAMLZLILbHPcKZB9l4lFOCZQfu4sOhrUXH7L36GGXlbGZuWKQfZBacniSEkMaOihEaAIlEwtcFVS6MrqtGicZ6oXswnu3UFM9FN9Nays+ZO6QV3BzYYu8nWvlgz1u98MvkLnzx8s3UfNEKreq6npLPT6/1aO4FOxu2ZXzHivsHoLf78520Qv5y5SBIIpHgP6Mi+RqfX44/wLXkPP72gzfT8MlOTVdsWhVGCCGWRUFQA8HVBVWuCRIFQWbaMqM67OUyLB0XhcXPRurddT3C1wUH3u2LU3MHYNVLXfmaoVGCfb3+vGT8RoeVHb2jyTL1FTQp7BSs6fbMdbeujFsZ5u1iB08n7UxRmLcz3hrABndqBpi7/QrKVWpsjE3CK+vP8/VZ/Vp6V6u7NGlcaJshQnQz9d8GBUENhIs9mzkpqDQdJqwJCvKwfBBkLG8XO63u08Pa+/PTRzsvpdS4l9CRSvVAnE6C4mhddUGZhQpkVez11bJSFkhoep/m/O3XkvPx/M9nMW/7VXDDfbq9P356obNFV+oR68RtZKlUau8BSAjR/G1U3vS1pigIaiBcKjJBZeVqKMrZ1VcMw+BBBtso0cvZrt5v0OnlbIc+LdieOsm5JTiXkF3t+8grVuJiRYAT5u0k2kakqbsDvF3YLS8uJeVqBVmG6oGEbG2kWDw6EtwagljBOF/uFYrvJnTkp+AIEZLL5bCzs0NeXh5lgwiphGEY5OXlwc7ODnK5vOoTjFC/3xUJT9grqEihgp2NDOkFmsxFa3/9b9r1yTMdm/Krq/68lGL06iqGYXD5UR7Wn0rgMzL9InxEx0gkEnRq5o5/r6ehQFGOexmFomBHvDLM2eDjdWrmgRe7BWPd6UT+uo+HtcbLvcOMGi9pvLy8vJCcnIxHjx7Bzc0NcrmcVuWSRo1hGCiVSuTl5aGwsBBNmzat+iQjURBU19RqIP8RkHUPcGsGeOkuDq4u0U7ypeXwdLLFjZR8/jquG3N991QbX77X0J4rKVg4oo3BrEpqXgnWn07E7ispeJhdIrpN16alnZp54N/r7Cq0i4k5oiDojp4eQfrMHtQSlx/lISGrCJ+NbEeF0MQo3D5HmZmZSE6uee0bIQ2NnZ0dmjZtatK9PCkIqmu3/wb+mMhe7jcP6DfXJHcrzAQVVPQKupGqCYJaN5AgyNHWBoPa+mFHXDLyS8tx5HYGBrX103lsRoECw78/icxCheh6uUyCUR2bopeO7So6CoqVLyblYEK0pmmicEVaCyOCIBd7Of58oyfUaobqf0i1uLq6wtXVFUqlst5tPE2IOchkMpNNgQlREFTXmggyP5l3TXa3LpUyQYA4CGoT0DCCIAAYGRWAHXHsJ+Q/45L1BkGL/7nJB0AyqQQ9mjfB8PYBGNTWD26Ouv+Y2ge68T2XhCvEGIbhl8c3dXcQZd6qQgEQqSm5XG6WF35CCIuCoLrmGQpIpACjZqfETEQ0HVaxQuxmxXSYrY0UYV5OJnssS+sV7gUvZ1tkFpbh4K105JUo+b5CnDMPsrD9IhsouTnI8fes3mjqXvVWG/ZyGdoEuOLKozzcTS/k7zslr5T/vgo7RRNCCKm/aHVYXbOxA9wrpliy7gEmWgFSeRPV4rJyxGexK8Na+bnARtZwftQ2Mimerthzq6xcjXWnEkS3l5WrMf9PTVPCOYNbGhUAcYRNE9/fchlZhQqjV4YRQgipP6z2nVGhUOCDDz5AQEAAHBwcEBMTg/3791f7fp566ilIJBLMnDnTDKOsoSYt2P/LCoHCNJPcZeVNVG89LuDjq9Z6dkSvz0Z3CuQvL91/B98dvMsvKV59Mh5309mpqw5B7pjQVfdmqPoIC5j33UjDoGXH8NvZJP66qlaGEUIIqR+sNgh66aWXsHTpUkycOBHLly+HTCbD0KFDceLECaPvY/v27Th9+rQZR1lDZqgL4polAmwmSLQyrAHVA3EiA934zswAGwh9tvsmHuUUY/kB9nsqlQCfj2xX7f25uoR44seJneBRUTeUWVjG71sGUCaIEEIaCqsMgmJjY7Fp0yYsXrwYS5YswfTp03Ho0CEEBwdjzpw5Rt1HaWkp3nvvPXzwwQdmHm0NCJfFm6guSLiTfEGpssEWRQu9O7AlPhzaiv969cl4jFhxEiVKdjXNC92CERnoVqP7HhLpj3/f6YMnWol7CUklQHNvygQRQkhDYJVB0NatWyGTyTB9+nT+Ont7e0ybNg2nT5/Gw4cPq7yPr776Cmq1GrNnzzbnUGumiemDoMp9gm4KgqBWDbiQd3qf5vjv6EhwyZ7siuaQXs52eG9Qy1rdt4+LPX6Z3AVfjW4Pp4p9zroEe8JeTt2eCSGkIbDK1WFxcXGIiIjQaogUHR0NALh06RKCgoL0np+UlIQvv/wSq1evhoOD8QWxdYarCQJMFwQJMkF5JUrcSmULeZt5Ooqmyhqi8V2bwdVejlmbLqFMxW5QOv/p1nA1wfOWSCQY1zUIvSO8cPxups4Gi4QQQuonqwyCUlNT4e/vr3U9d11KSorB89977z107NgREyZMqNbjKhQKKBSaxnr5+fkGjq4FF39A7ggoi01XEyTIBF1LyeenhBpKp+iqDIn0h5uDHF/9extdgj0wwsTdmf3dHDCui/7AmxBCSP1jlUFQSUkJ7OzstK63t7fnb9fn8OHD2LZtG86ePVvtx128eDEWLVpU7fOqTSoFmjQHHl8FchIAlRKQ1S5rIcwE3atYGQU03HogXXqEe+FPHV2gCSGEEF2ssibIwcFBlJHhlJaW8rfrUl5ejrfeegsvvPACunbtWu3HnTdvHvLy8vh/xtQe1RhXF8So2EColhzkMuhaBNVYMkGEEEJIdVllJsjf31/nxoGpqakAgIAA3VMd69evx+3bt/HTTz8hISFBdFtBQQESEhLg4+MDR0dHnefb2dnpzECZReW6IK8W+o81gkQigbOdDfIrtszgtG5EmSBCCCGkOqwyExQVFYU7d+5o1eRwU1xRUVE6z0tKSoJSqUTPnj0RGhrK/wPYACk0NBT79u0z69iNZuZeQQC7XUSAm71J7psQQghpaKwyEzRmzBh8/fXXWLlyJb/EXaFQYM2aNYiJieFXhiUlJaG4uBitWrG9YiZMmKAzQBo1ahSGDh2KV155BTExMXX2PAwyQ6+gypt6tvF3hURCm3cSQgghulhlEBQTE4OxY8di3rx5SE9PR3h4ONatW4eEhASsWrWKP+7FF1/E0aNH+e0SWrVqxQdElYWGhuKZZ56pi+Ebxwy9goQNE4HGVRRNCCGEVJdVBkEAO301f/58bNiwATk5OWjfvj12796NPn36WHpopmHvBjj5AEXpZukVBACtqSiaEEII0ctqgyB7e3ssWbIES5Ys0XvMkSNHjLovxkQ7tZucVws2CCpMA0rzAfvaBS26psMIIYQQoptVFkY3Gk2aay6bIBsknA6TyyQI96E9rgghhBB9KAiyJBNvnyHMBLXwcYGtDf14CSGEEH3oXdKSTFwc7WynWSJPRdGEEEKIYRQEWZKwQaIJegUJp8OoKJoQQggxjIIgS3IPBiQy9rIJMkH9W/nA2c4GLvY2GBrpV+v7I4QQQhoyq10d1ijY2AIeIUD2fSDrPsAwQC2aG4Z6OeH0vAGQy6Swl8tMN05CCCGkAaJMkKVxdUHKIqAgtdZ352IvpwCIEEIIMQIFQZZm4rogQgghhBiHgiBLM3GvIEIIIYQYh4IgSzNxryBCCCGEGIeCIEszw0aqhBBCCKkaBUGW5uIH2FZsb0E1QYQQQkidoSDI0iQSTV1QbiJQXmbZ8RBCCCGNBAVB1sArgv2fUQOply07FkIIIaSRoCDIGoT20Vy+84/lxkEIIYQ0IhQEWYOIwQAqOkXf+tuiQyGEEEIaCwqCrIGzDxDYlb2ccRPIfmDZ8RBCCCGNAAVB1qLVUM3l2zQlRgghhJgbBUHWoiUFQYQQQkhdoiDIWnhFAJ4VS+UTTwHF2ZYdDyGEENLAURBkLSQSoOUQ9jKjAu7ut+x4CCGEkAaOgiBr0mqY5vLtPZYbByGEENIIUBBkTQKjAQdP9vK9g0C5wrLjIYQQQhowCoKsicymomcQgLJCIP543Txufirw70dAwom6eTxCCCHEClAQZG24uiAAuF1HjRO3TQNOrwB+G0sF2YQQQhoNCoKsTfMBgMyOvXz7H4BhzPt4GXeAxJPsZWUxcG2beR+PEEIIsRIUBFkbO2cgrC97uSAFSL1k3seL22D4a0IIIaSBoiDIGgmnxA5+CmTerfqc/FTg2BLg/2KAX54ESnKrPkelBC5vEl+Xehl4fLVawyWEEELqIwqCrFHEEEAqZy/fPwSs6ApsnQak39IcoyoHCjOAO/uATROBb9sChz4HMm4Bj84Bl36r+nHu7gOK0tnLdq6a6+OMOJcQQgip5yQMY+6ik/orPz8fbm5uyMvLg6ura9UnmNK1bcCe94CSHMGVEsAtCCjNBRT5hs8P7QtM3mn4mI3PaYqvR68C/nwdUCnYZfrv3QJs7GrzDAghhBCLMPb9mzJB1qrdaODtq8CTCwHHJhVXMkBeku4AyNkP6D2bDZIAduuNUgOBUsFj4M6/7GWXAKDtKKD1cPbrkmzav4wQQkiDZ2PpARAD7FyAXu8A0dOB86uBc6vYAMjBE3DwABw9AScvdvPVFoPYPkOKfCB2JaBWAg8OA21G6r7vy5vY7TkAIOp5QCoDOk4Erm1lr4v7FWj7TJ08TUIIIcQSKAiqD2ydgB5vsv+q0mIQGwQBbL2QriCIYdggh9NxIvt/aF82k5T3ELh/EMhPAVwD2NtK84E7ewH/DoB3y9o9H0IIIcQK0HRYQxPSC5A7spfv/guo1drHPDwLZFWsOAvpDXiGsZelMjYrBACMGri8kQ2Yrmxmi7O3vwL8rxfwMNb8z4MQQggxMwqCGhq5PRDWj71clAGkxmkfI+wF1HGS+DYuCAKAC2uBtcPY4KfwMXudqoxdjZaXbMpRE0IIIXWOgqCGKGKQ5jJX/MxRFADXdrCX7VyB1iPEt3uEAKF92Mu5SZpu0gDg6MX+X5QObHoeKCuueiy39gB/zzGu1xEhhBBShygIaohaDNRcrhwEnVgGKIvYy+1GA7aO2udHVcoOeYQCz28B3ogF3Jux16VeAnbO1L+tR0kusO1lNliK/YnNKBU8rsGTIYQQQsyDgqCGyDUA8ItkL6de0gQf6beAk8vZy1I50O113ee3GQkE9wTs3YD+HwOvnwEiBgJOTYDnNgFyJ/a4a9uAE0u1z48/DvzYE7i6RXNdYRqwZQrbpboyRSFw7yCgLKnR0yWEEEJqglaHNVQRgzXbX9zdx2Z3dr/DLp0HgJ6zAO8I3efK7YEpf7NF1dJKcbJvW+DZlcAfFSvKDn4GXP8TcPEDnH0AtapiK46KDJGdG3t/hWlA0ingwEJg0Bea+0u+CGyezPY/8ooApv7LLv0nhBBCzIwyQQ1Vi0p1QZd+Y4MQgJ3e6jO76vuoHABxWj/NZogAAAzw+AobaMX9yq4o4wKgkN7AayeBCb9rtgE5vQK4tp29fGEdsHoQGwABQOYdtou1srQ6z5QQQgipEQqCGqqmnTSFzPcPA/vna24b9g0gd6jd/feZDfT7kO02LZGJb5PKgac+A17cCbgHAYFdgMGLNbf/NRPYOhXY9Ra72kzo4RlgxwzdS/tNTa0C7u4H/pgEfN8ZuLrV/I9JCCHEatDeYQZYdO8wU9jxakVmRqDdGGDMKtM+jlrNbrVRmAYUZQI+rdmpMSGGYYObK39onx89HYgcC6x/RlO03X2meNrMlPIesVmruF/ZxpAciQyYuBkIf9K4+ykrZvsp2TmbZ5yEEEJqhPYOI+Kl8gBbnzPoP6Z/HKmU3b7Dty0Q1lc7AAIAiQR4ehng01ZznY0DMGolMHQJEBQNjF2rySqdXgGc+dG048xJBP56A1jWHjiyWBwAAew2IptfAtKuV31fiaeBZe2AryOAhJNVH08IIcTqUBDUkDUfAEgFte9PLQRcfC02HNg6AhN+BQKj2dVnLx8AOozX3B4xEHhasNps7zxg1yygML12j5ufAux+l53yivtVs2eaRMrWTk34HWj1NHtdWQHw+3igIE3//d0/BGwYBRRnsZmr7dPZlgCEEELqFZoOM6DeT4cBbOYj7lc2IJq4TX+xszU59DlwbInma1tnoPe77JL+qmqZSvPYxoyZd9h/GXeAewcAlUJzjJ0bEDMD6DwZcAtkrysrBtYOBVIqOmwHdARe+lu7j9Ktv4Etk7VrmSLHAqN/qdnzJYQQYlLGvn9TEGRAgwiCysvYfcK8WrK7zNcHDMNOhx35L5uZ4bgFAeFPsEGR3JHdWFZVBmQ/ALLus/8XZ+q/X1tnoNtrQPc3AAcP7dsLHgM/PwHkP2K/DusHdHge8G3DLt+/uYvN+nCZpPCn2H3UFHns16NXAZFjTPItIIQQUnMUBJlAgwiC6rPCDODIf9g9zJharBazdQG6TAF6vs02fDQk7TqwapA4+ALYaUW1Cvzy/8ixwDM/Ajf+ArZNY6+zcwNeP6XJLlWmLGH7IqVdY2ugAjrW/DkRQgjRi4IgE6AgyEqk3wT2fcxOaxni4g94hgFNwtnMjVcE2xDSLQiQygyfK3TvILDlJUCRr/v2TpOBp7/V3OfWacC1iuX1Ib3Z1gCMis1Mpd8EUi4CSWfYqTZuGk1mC0zdCzTtrH3/henA0a8ArxbstB0hhJBqoSDIBCgIsjL5KewSfGUxUFYIlBWxxc0eoYBnKDs9ZirF2UDyBTZrk3YDSL/BLq3vOg0YMJ9d7cYpyQF+7KWZRnMPZseq1rFFiJBbEDDjmLhDdmkesGYo+7gA8MKfQPP+pntehBDSCFAQZAIUBBGjxR8D1o0AP12mS5NwoFk34PE1dk83AGj+BDBxC5tVKlcAv44GEo5rzmkxiO1dRAghxGj1vk+QQqHABx98gICAADg4OCAmJgb79++v8rzt27dj/PjxCAsLg6OjI1q2bIn33nsPubm55h80abxC+wD9P2IvS+WAd2ug7Si2q/b434DZd4E3LwAj/49dks938z7ITn2p1cCfr4kDIAC4+y+Qea9unwshhDQSVpsJeu6557B161a8/fbbaNGiBdauXYtz587h8OHD6NWrl97zvLy8EBAQgGeeeQbNmjXD1atX8b///Q9hYWG4ePEiHByM3y6CMkGk2kpy2Wk5mdzwcQ+OAhueqSj4lgAtBrIBD8A2kWw9HLhakQGKns42lCSEEGKUej0dFhsbi5iYGCxZsgSzZ7MbfZaWlqJdu3bw8fHBqVOn9J575MgR9OvXT3Td+vXrMXnyZPz88894+eWXjR4HBUHErI4vBQ4uEl8nkbKZombdgKVt2PonW2fg3RuAvZtlxkkIIfVMvZ4O27p1K2QyGaZPn85fZ29vj2nTpuH06dN4+PCh3nMrB0AAMGrUKADAzZs3TT5WQmqs1ztAy2Hi655eBrQcwvYx6jCBva6skG14SQghxKSsMgiKi4tDRESEVvQWHR0NALh06VK17u/x48cA2KkyQqyGRAI88wPgGwlAAjzxCdvFmhPzquby2Z8q+hQRQggxFatsIZyamgp/f3+t67nrUlJSqnV///3vfyGTyTBmjOFuvgqFAgqFZnuF/Hw9fWIIMRUHd+CVQ4CiQLuRo3dLdvXY/YNAbiJwZy/QapjOuyGEEFJ9VpkJKikpgZ2dndb19vb2/O3G+v3337Fq1Sq89957aNGihcFjFy9eDDc3N/5fUFBQ9QZOSE3Y2OrvZN3tNc3lMz/WzXgIIaSRsMogyMHBQZSR4ZSWlvK3G+P48eOYNm0aBg0ahC+++KLK4+fNm4e8vDz+n6HaI0LqRPMn2P5CALt8/vE1y46HEEIaEKsMgvz9/ZGamqp1PXddQEBAlfdx+fJljBgxAu3atcPWrVthY1P1zJ+dnR1cXV1F/wixKKlUXBt06DOqDSKEEBOxyiAoKioKd+7c0arJOXv2LH+7Iffv38fgwYPh4+ODv//+G87OzuYaKiHm1+E5wN6dvXxnL7DzTba5IiGEkFqxyiBozJgxUKlUWLlyJX+dQqHAmjVrEBMTw9fqJCUl4datW6JzHz9+jIEDB0IqleLff/+Ft7d3nY6dEJOzcwZGr2I7UQPApd+AvR8A1tfiixBC6hWrbJYIAOPGjcOOHTvwzjvvIDw8HOvWrUNsbCwOHjyIPn36AGB7Ah09ehTCpxAVFYXLly9jzpw5iIyMFN2nr68vnnrqKaPHQM0SiVW58Re7uz1TkQXq9S7w5AKLDokQQqxRve4YDbBF0PPnz8evv/6KnJwctG/fHp999hkGDRrEH6MrCJIId/eupG/fvjhy5IjRY6AgiFidSxuBPwU1QgM+Bvq8b7nxEEKIFar3QZA1oCCIWKVzvwB73tN83fUVYPDiqvcrI4SQRqJeb5tBCDGg68vAU59qvj73M7B+JFCYYbkxEUJIPURBECH1Uc9ZwIgVgMyW/TrxJLCyL5ASZ9lxEUJIPUJBECH1VacXgJf+BlwqtpjJTwZWDQLu7LPsuAghpJ6gIIiQ+iyoKzD9CBAUw36tUgD/zrPokAghpL6gIIiQ+s7FD5i8G/BuxX6ddR8o1952hhBCiBgFQYQ0BDa2gE+bii8YIJf2vSOEkKpQEERIQ+ERormck2CpURBCSL1BQRAhDYUoCIq32DAIIaS+oCCIkIbCI1hzOTfRcuMghJB6goIgQhoKmg4jhJBqoSCIkIbCNRCQyNjLFAQRQkiVKAgipKGQ2QDuQezlnESAtgUkhBCDKAgipCFxr6gLUuQDJTmWHQshhFg5CoIIaUioLogQQoxGQRAhDQkFQYQQYjQKgghpSGiZPCGEGI2CIEIaEsoEEUKI0SgIIqQh8QjVXKYgiBBCDKIgiJCGxMEDsHNlL1MQRAghBlEQREhDIpFolsnnPQJU5ZYdDyGEWDEKgghpaLjiaHU5kJ9s2bEQQogVoyCIkIaGiqMJIcQoNpYeACHExLSCoL4WGgghVUg8BRz7GnBvBoT1BUJ6A05elh4VaUQoCCKkoREGQQ2hV5BaDZxYygZ0Ty6kN8mGZO88IPUSe/nCGvZ/33ZAq6eB3u8CNnYWGxppHCgIIqShaWjTYbE/AYc+Yy/nPQJe2MEWgJP6TaUE0q5rX592jf2X9xB45oe6HxdpVKgmiJCGxi0IQEWQUN+DoPRbwP4Fmq8fHAbiNlhuPNbi/Grgu07ApY2WHknNZd0D1Er2crPuQM9ZQEBH8L+7l34Dbv9jseGRxoGCIEIaGrk94BrAXs6px9Nh5WXA9lcAlUJ8/b8fAXmNeNWbWsUGhtn3gX0fAwxj6RHVTPoNzeXwJ4CnPgWmHwFGfK+5fudbQHF2nQ+NNB4UBBHSEHG9goozAUWBZcdSU0e/BB5fYS97twIix7KXFfnArln1982/tjJusd8DgP35Ztyy7HhqKk0QBPm00VzuOAloMYi9XJQO7HmvbsdFGhUKgghpiER1QfUwG5R0FjjxLXtZagM8uxIYugRw9mOvu7cfuPS75cZnSY/Oib9OOGGZcdRWup4gSCIBRnwH2LuzX1/fDlzbXqdDI40HBUGENET1uThaUQjsmA4wavbrfvMA/w7sliDDl2mO2zsPyE+xyBAt6tF58dcJxy0zjtriiqLlTprMJcfFDxj2jebrPe8BBWl1NzbSaFAQREhDZO5l8uUK4OxK4P4h09/3oc81gVtgNNDzbc1tLYcA7SewlxV57LSYWm36MVgzrSDoRP2bGlQUaH4vfVoDUh1vRe1G/397dx4XdbX+Afwz7IssooAggguhiQuiQeCa3hRzSXPXa5maSyrpTdMW7SZq95pXypsiWj/NJa00LTXJJSVzQVRwuypuCAEuibLIzpzfHw8z3xmGZQZm2OZ5v168/M53mTlzwO88c85zzgHav0rbuenAvtD69z5ZncdBEGMNUWOVb9aGaAk6/SVwcAGwbaR6bkd1FRcBl3bStpk1MHw9YFpqJo+QT4FGrrR98xDwy3zj+XDMy9DMAcp5DDy8VjvlqaqHKu/BtX3Z58hkwKDVgK0zPU6IAn7/zPBlY0aFgyDGGqLyusPkciDlAnU5Vce1ffSvKAYubKnec6n6MxbIfULbPv2BJm00z7FxovljZKb0+NzX1HpkDFIuACgJ+Mxtpf31LS/oocr8QC6+5Z9n2xQYFgHlsPljy4Fr+w1atDonLxO4dRQoyKntkjRIHAQx1hA1cgXMrGhbkRidlwFsfRXY+BKw+ZWqrzD/7C8gNV56fOk7Gs6uDwlR0rbPwPLP8/4btRIpnFgFnPpv+ec3FKpdYd3elLbrW16QastVeS1BCs+9DPRbIj3eM12/rY913bdjgG2vAXtn1HZJGiSeMZqxhkgmo9agR9cp9yIzFdg+GnhwmY6nXQQu7gD8J+r+3LePQdkaAVC+RsJBKX+jOhJ+LdmQ0YdfRTqNBnKfUrccQHPmWDnq/p6yHwFPk4DCZ0BhLlDwDCjIBp49omPPHgLZD2kSyoH/BqzsdXxTeqQ6MqzrJJo4Mi8DuHeSWvnKyq2pi1RninapJAgCgB7z6Joru+h3s3Mc8NYxahWsafnZwKk19Dfx8lLD/j08TQKSTtH2tX2UHG7narjXM0IcBDHWUDl6URBUlAds6ANklxpdE/1vCiR0XZ/p9lHNfXHbqh8EPUkEHpW0EHi8oN0aYYHTqPvs+Ap6vC+URhZVFkAp3IgCvp8IFGvZkmVuDQxerd25+iaEFARZOwFNvAGv7sCNXygv6NH1yltV6gIhpOHxti7a/Z5lMppE8fFNCuCfJAI/TAL+/qNmzpgh3T0B/DRLSuq2sqdAyFBuH5O2hRz430/0N19axp/A+c30f7BZx7KfSy6n5UggAPvmgE0TXn4G3B3GWMOlmhekCIAcPIEWgbSdkUw3Tl3I5ZSfAADmNoC9B23fOlL94eoJh6RtnwHaX9f7PSBwJm0LOXBosXYjxh5cBXZP0T4AAmiRz/uXtT9fn57cpVY3gIJEmQxo2UM6Xl/ygrIfUtAG6Ba0WdgAY7+lwAkA7kZLc0kZWsEz4Jf3gG8Gq4+2vPhd1buVtVF69OWV3ZrnCEGtvL9/Rv8WF5b9XL9/BkT2BCJ7AZ+1AZa5Al/4ATvGU4uTkeIgiLGGSjUIAgDXjsDUw8DAldK+31fRDV5bD65Q9xAAtOxJs/sCFHxcrOY6Vmr5QCHaXyeTAQNW0HB6gFqTbh2u+JpnfwE7xlLXCgB49aCh+C99CPRfBgwOB0ZvBSYfAkLjgJc+ovOEHDi4sHZGo6nmA3l0o3/VgqB6khekbVJ0WRw8gDFbaQJNADizzvAJw/evABHdaSFfBdOS1tPs+8Dd44Z5XXkxcKfUcyefAZ4mq++7/ZtUp1mpZf8dFBeqlx+g5Wie3AVuHFBfn8/IcBDEWEPV3F/abtULePMAdRW5+wHPD6X9zx4CZzdo/5yqXWHe/QC/8dLjuG1VDw7ys6Sbt70H4Krjh6OJCdBjrvT4j8/LP7eoAPhuovTt180PmPAD8PIn1KoUPAfoNhloPxTwDAScWgPdQ+lfgPJvru7RrXz6oJoPpAiCXDsAVg5SuVRbwDJSgOP/An5bTsHuqS+Bsxtp9uWs+zVX7tJUk5qr0n3n+SLg+xpt56YDFw04c7i8mLrdntylx2bWQMi/gRFfSecYahHb1Dgg7yltm6h0+ZX+24tZr/64rL/Nu9FS61tTHxpY4NIeMDGnfdcPUG6ZEeIgiLGGyvNFYHgk8MoqYMIu6cMSoBYPxbDjPz7X/gZ4SzUI+hvNR9SqNz1OvwMkna5aWe8cl7qlfAZULVfBZyDd4AFKJk0+q3mOEMCBf0jJpo2aAeN2UFdLRcwsgQGfSo8PLVZvgcj4E9gzE9gyjLYNQRkEyYDmXWnTxJTyggApLwig1oKv+wPHPwV+Xwn8FgYc+pDmVNr1JvCftsCXATQT89W91DWYmUaTYBpaectl6CJ4trR9ei0FK4Zw4yDlIQGA8/PAzJPAizOopdK6JCn7+n7DBBCqXWFBs6Rt1S6xv27RXFmqru3T7BK7rHJNvyXA33cDb5+WRhgW51O+kRHiIIixhqzzWCDgLc3kZ5d2dAygb5un11b+XPnZQNIZ2nb0klpGFF1iALUGVUVVu8JUmZgAwaHS45NfaJ5zZh2NqAJoCoGx3wL27to9v88ACvwAIPNP4OTn9OF7JgJYG0gtEneOUYCkb4W5Ui6Sc1v1gLZ0XlDWA2DLUCpjRf66AcR+BfzwBhARDKxuByxzAVY0B9YFl8xJZADKIEhGC+NWhVtn9eD7xkG9FE3DqTXSdv8wad4qMwug40jaLsqjQFLfVIOgF6ZSiyUApMUDj2/TtmoXl2LeqNwn1PKjUJhHgRoAWNoD3iqDBhT3AIDym4wQB0GMGaveC6Vm9tNrqSWgIol/APKSb5je/aTWmueHAJYlH8pX9+q+ar1cLiVFm1kDrXrqdr2qTqMBOzfavn4AeJQgHTu/Gfj1A+nxq2sBj67aP7dMRq1Bijo7+QWwsS8QtUjKLQKA/+2lD2Z9SrsIyEsScBVdYQqqQdD1/cDW4dLrO7UBxv8AjNsJjNoMDFsP9Hy3JLHatOzXKsimHJN9ofpfkkReLM0W7dSq8ha4igTPkbYNMUdUUgyQHEPbLu2lAFhBLYDYqd/XzsuUWjKbPAc4etIyIgpXdlPrk2IRYXMbYOC/pOOqXWK3jgD5mbTdbjBgbiUdc/en5weAe39UPUH62j6aPf6X9+gekP2oas9TCzgIYsxYObUC/F+n7YJsas2oKFH61hFpu00/advcGuhYcoMufAac+A/w7LH25UiLk5Kt27xEz1dVZpbAiyUjxSCA0yUfjue/oXXGFHq9J32T14WzDxBYMmldUR59K1dQDE0Wcsq/0Se1fKAX1I+p5gXdjZaSZB1aAK//RDNvtx0I+A4H/MZRd8jUI8CiexQg9XyXWvPaDgI8g2ihWoBanq7puYvkSSJQlEvbVe0KU/D+m9SSlHwGSI6t+HxdqbYCBc/R7KJ19weatqXtpFNA+l39vXbiCZqNHQDa9KV/fYdLxy/volZXRfDdeSzQcZTUGnRtvzSBqWr3mWogBdB76jxGenypCq1B2Y+A3VNpMMLZSGpZXOUNfPkCtYrmZer+nDWIgyDGjFmv96S1mfIzKHdkTRfqJimdV6BIijYxo0RrVapdYn+E0xDcr/4GRK+kCRBvHwMST9IH1f0r6s+tnCARug2NL0/XSdTsD9A39BP/oVYNheA5wEsflHmpVnqr1BlAH8STfwXe2AdYNKJ9cdtoKLi+VBQEqeYFKTRypQDIsUX5z2lpRwFSvyXUKjbuW2ByFPCaStLvb8v1OwRcdZJEXZPfS5PJ1FuDTlfSGpSZCuycQInOt45W3Mr11y1qSQSoZbFDGQGzTEZBpYI+W4NUc+8UQZBjC6DFiyXlu6G+jlrgDPry0LZklvW8pxQQFzyTupqtnYDWvTVfq+Noafvid7oPbjizjr4QlPZXAgWSkT01F/2tQzgIYsyY2bsB06KBLhMBWcntIPsBJcxGBFOXAEDfchVdLB4BmrPkuvtLN2sAQMnEfseWA9+OBrYOo6U6vv4bsL47sLIN8P0b1JyvuhbUc/2r/56sHGh0F0DJ1kdVJrMLmg28HFa9SeKsHIDx31HLyctLgeknKAndujEFYAAlmpYetVMdf56nfy0alZ1Ho9olZt0YmLi37HXXtOHdD/AMpu3HN6vWOlAefSRFq+o4SlpM99q+8rshC3JoSoTr+6mraNtrwNoXgDPry05qPv0llLOiB86gHKAyX380lAMMLu7Q39QJinwgE3P1361qS45ijb02fSlPDFBvLbq6h3KlCksS+Nu/Cpiaa75WYy8piH58E0jVIRcsL4O+MCnKOuJroPs7QPNuUnfrk0Tg/wbQlxFDJbBXAwdBjBk7h+bAq18Cb8dIQ+cB+ib3fwOon//az9J+736azyGT0Qi0SQfoJuj8fMWvmZ9BuTN7Z0rdN26dtU9SrkzgDMC01AdX0GyaA0gfs+Q270otJ93fUf+ADJolDTuO/Ur3/KiyZKZKSc7uXajlp7TO46hrpnFLGvlTnZmjZTKgn0pyd/S/9Lc2nD5bggDq/gwomUFZyClJvTQhqCUw7aL6/se3gKiFwH+eB6Lel3Lish9JuTYWduprtJXm0Bxo3Ye2n96r+uhIVel3pCH5LQIBy0bSsfavSl9WFBTdswB1EVrY0fa1/epzd1XU/dtJpUtMlwTp2K+lfCO/cfQaLy8F3joKhF6QWi3lRfRlZMurNHVDHcJBEGOMOPvQRHRTf5OGYENQP/9hlQUsywqCAPpwbtmDboKzzgBzLwNDvqDh+L0W0MitwJn0bdXKUfP6qo4KK4u9m3ri6ouz9BcAVfi67tIHSl6G+ozcOenArx8C3wwBDi6ilouc9IqfTwj1lpjSXWEKNk7A7LPAnDiV3101eAVLeV9Pk4AL3+h2vRA0kjAmkloTFS0kipYgMytpdGF1dZtMicEAcGELcGGreovMqTXA5R9o29yWpoxoqZJ8X/iMunS+6EwtoMc/pZY8AOj6hvpIvLKozpWlCJ6qQ3WpjDYvqR+zc1Uvu1Mb9dFe5lZAu1doOz9DyuOzc6N8r/L4DpMmgLyyq/xZp1UV5lK9ARSYdZ+rfrxxS+DNg0DP+VC2liWeAL7sBhz/t26TtBoQrx3GGFPn0RWYcpi+Vf+2TEpkBQCbpkCzzto9j6On1D1UWnERdZfdPETDyq0cgRffrm7J1Q1YQblBTbypHDW1TlL3UCB+Oygxey3wwlvA5e9pVl7Fshd3fwdiSlotXNpTLpT/G5SsrpD7BNg/T32kT8tSuT+l6XMB1b4fSXlgv68C/CZUPpqrKJ8ScWPWq7e8NHmOWgoU3VXObctu0aoKGyf6/SpyU36eTXU2dA2NRDvyT+nc4etpEsyAt2gl+7MbKHApyqPAJ1YlH8rETCXJvgLtBlE3ZUE2BayuvkDA9Kr/LlSHxqt1MZfoPFYaAv/iTM3X8R2u2YXpO7zi+rZyoODp6h6ab+rWESm/qDxx22iRYYBaqMrqfjU1p1bF1r2BH6fTjNaFObTW3/nNlI/WaUytLvwrE6I25n+vHzIzM+Hg4ICMjAzY29fiytGM1Zb0O8DPodJszn5/B4ZpMaeQsds5QZqbxaEFrdNWKRm1snWbQsHG3reBTJWug26TgUGra3bRS9X30Xsh5aTIi+inKJ9asnIe009mCgVAig/GinQeRwGJvhTmUsCo2v1jYUd1peiu6b0IeOl9zWuzH1IO0NmvqFVIodNY4LVIzfPLcnCRFNQCQOuXgGHrdO/eLS4CVraiMls7AQtuawYIQlB5haAu3tLHi/KBz7yl9w0AU49qTq1Q2o0oYEdJK2b7YcDoClr/iguBNf5ARsmQ+uknALdOFT9/TjrNYB77lTTyDaBu8AGfVh7g60jbz+86GwTl5+djyZIl2Lp1K548eYJOnTph2bJlePnlyleHTklJwbx583Do0CHI5XK89NJLCA8PR+vWujW/chDEGOhme3kXrRsWNBto5Fz5NcYuOZaSwEvrMJJGlz2+TctcJP4B3L9E+SzlsXKkbkXfYYYqbfkeXgPWBUGZJKwLNz8q862jmutZ9V+mPrJLXxJ+pakQskrNedVuMK0FV1GLQ046tSbFfkXdZpP2q7fMVaQon1qcFN1DAP3eBodTK4w2gWthLrWu/DKfHncYAYz8P+1ev7Q9M6SA0NELeOdi5WUoLgT+0w7I+YtawQJnAD3mAbZNNc+N3wHsLclF8n4Z+Psu7cv2KIG61xNUJrhs2ZPqW4/qfRA0btw47Nq1C3PnzsVzzz2HzZs3IzY2FseOHUOPHj3KvS47Oxv+/v7IyMjAu+++C3Nzc4SHh0MIgfj4eDRp0kTrMnAQxBirsk2DaAI6gJbzeGVV2UOUsx/RLNbnNknfrBVa9aIJDh2aG7685flxmvYjxGSm1N0UOBNoESB98KbfoW6nyz9QPtDrP9E6doaQ+5Ryr+JLZi93fp4WDra00/45hKhai9vtY9SCl5Uq7XN+nqaQ6DxWM6AozKNk6ss/UI6YauvNq2vVp57Qxc3DwPaSROie89UT3Sty5J80xYWCuS11uQXPpqCu4BmVcetwaYmWNw9SDpmubh8DDn1EyfLTf6+8JUlH9ToIOnv2LAIDA/HZZ59h/nyKivPy8tChQwe4uLjg1KlT5V67cuVKLFy4EGfPnsULL1AS4fXr19GhQwe89957WLFihdbl4CCIMVZl6XcoAdStMy17UN4wawV5MbWanPuaEogDplFCdy3mSwCgBO/olcCzkhYCExP619SCumxsnACbJvTj3I6Sd+uCxD9ofpouEwFb7b/8VltOOq1PV3ohUxMzSv5v5Aqk3wYe3ynpJi3jI7ipD01oWVlSdnmEoCHpmSk0JYTqCLOKFBcCRz+hhXZV5/4xMafWStVuLICSrSdHocrkxZRAr+euMKCeB0HvvfceVq9ejfT0dLXCf/rpp/jggw+QlJSEFi3KngQsICAAAAVSqgYMGIDbt2/j1q1bWpeDgyDGGGM6E4IWJD0TQbNZa8PCjlrROo6iFkB9JY5XRWYaBVHnN0tL5ZRlwm7guTK6fesAbT+/6+TosLi4OPj4+GgUXBHgxMfHlxkEyeVyXLp0CZMnT9Y4FhAQgEOHDiErKwt2djo0izLGGGO6kMkoH8p3GPDXTcr1ubiDJiJVsHKgIe5NfWgkls+A6i0Zo0/2bsCgVTTS8cRq6rIzs6JJUi1Lflr2qLMBkC7qZBCUlpYGNzc3jf2KfampqRrHACA9PR35+fmVXtu2bdsyr8/Pz0d+fr7ycWZm3V7zhDHGWB3X9Dng5U+AvotpWggTUwp+bJxqdqRfVTh6AkM+r+1SGFSdnCwxNzcXlpaWGvutrKyUx8u7DkCVrgWou83BwUH5U16XG2OMMaYTUzPAK4gSxm2b1P0AyEjUySDI2tparUVGIS8vT3m8vOsAVOlaAHj//feRkZGh/ElO1mZuD8YYY4zVR3WyO8zNzQ0pKZrri6Sl0dwP7u5lT0Dl5OQES0tL5Xm6XAtQC1JZrUiMMcYYa3jqZEuQn58fEhISNHJyYmJilMfLYmJigo4dO+LcuXMax2JiYtC6dWtOimaMMcYYgDoaBI0cORLFxcXYsGGDcl9+fj42bdqEwMBAZa5OUlISrl+/rnFtbGysWiB048YN/Pbbbxg1alTNvAHGGGOM1Xl1cp4gABg9ejT27NmDefPmwdvbG9988w3Onj2Lo0ePolevXgCAPn36IDo6GqpvISsrC126dEFWVhbmz58Pc3NzrF69GsXFxYiPj4ezs/ZT/vM8QYwxxlj9U6/nCQKALVu2YPHixWprh+3fv18ZAJXHzs4Ox48fx7x587Bs2TLI5XL06dMH4eHhOgVAjDHGGGvY6mxLUF3ALUGMMcZY/aPt53edzAlijDHGGDM0DoIYY4wxZpQ4CGKMMcaYUeIgiDHGGGNGiYMgxhhjjBklDoIYY4wxZpTq7DxBdYFi9oDSy3cwxhhjrO5SfG5XNgsQB0EVyMrKAgDlMh2MMcYYqz+ysrLg4OBQ7nGeLLECcrkcqampsLOzg0wmq/LzZGZmokWLFkhOTuZJFw2M67rmcF3XHK7rmsN1XXMMWddCCGRlZcHd3R0mJuVn/nBLUAVMTEzg4eGht+ezt7fn/1Q1hOu65nBd1xyu65rDdV1zDFXXFbUAKXBiNGOMMcaMEgdBjDHGGDNKHATVAEtLS3z88cewtLSs7aI0eFzXNYfruuZwXdccruuaUxfqmhOjGWOMMWaUuCWIMcYYY0aJgyDGGGOMGSUOghhjjDFmlDgIYowxxphR4iDIgPLz87Fw4UK4u7vD2toagYGBOHz4cG0Xq96KjY3F7Nmz4evrC1tbW3h6emL06NFISEjQOPfatWsICQlBo0aN4OTkhIkTJ+LRo0e1UOqGY/ny5ZDJZOjQoYPGsVOnTqFHjx6wsbFBs2bNEBoaiuzs7FooZf124cIFDB06FE5OTrCxsUGHDh2wZs0atXO4rqvv5s2bGDt2LDw8PGBjY4N27dph6dKlyMnJUTuP61p72dnZ+PjjjxESEgInJyfIZDJs3ry5zHO1vT/L5XKsXLkSrVq1gpWVFTp16oQdO3bot+CCGczYsWOFmZmZmD9/voiMjBRBQUHCzMxMnDhxoraLVi+NGDFCNGvWTMyZM0ds3LhRhIWFCVdXV2FraysuX76sPC85OVk0bdpUtGnTRnzxxRdi+fLlonHjxqJz584iPz+/Ft9B/ZWcnCxsbGyEra2t8PX1VTsWFxcnrKysRJcuXURERIT48MMPhaWlpQgJCaml0tZPv/76q7CwsBCBgYFi9erVYsOGDWLhwoViwYIFynO4rqsvKSlJODo6Ci8vL/Hpp5+KyMhIMWnSJAFADB06VHke17Vu7t69KwAIT09P0adPHwFAbNq0SeM8Xe7PixYtEgDEW2+9JTZs2CAGDRokAIgdO3bordwcBBlITEyMACA+++wz5b7c3FzRpk0bERQUVIslq79Onjyp8Z8kISFBWFpaigkTJij3zZw5U1hbW4t79+4p9x0+fFgAEJGRkTVW3oZkzJgxom/fvqJ3794aQdDAgQOFm5ubyMjIUO7buHGjACB+/fXXmi5qvZSRkSFcXV3F8OHDRXFxcbnncV1X3/LlywUAceXKFbX9r7/+ugAg0tPThRBc17rKy8sTaWlpQgghYmNjyw2CtL0///nnn8Lc3FzMmjVLuU8ul4uePXsKDw8PUVRUpJdycxBkIAsWLBCmpqZq/4GEEGLFihUCgEhKSqqlkjU8/v7+wt/fX/nYxcVFjBo1SuM8Hx8f0a9fv5osWoMQHR0tTE1NxaVLlzSCoIyMDGFmZqbWWiGEEPn5+aJRo0ZiypQpNV3ceikiIkIAEP/73/+EEEJkZ2drBENc1/qxcOFCAUA8evRIY7+JiYnIzs7muq6mioIgbe/Pa9euFQDE1atX1c779ttvBQC99ahwTpCBxMXFwcfHR2NRuICAAABAfHx8LZSq4RFC4MGDB2jatCkAICUlBQ8fPkS3bt00zg0ICEBcXFxNF7FeKy4uxpw5czB16lR07NhR4/jly5dRVFSkUd8WFhbw8/Pj+tbSkSNHYG9vj5SUFLRt2xaNGjWCvb09Zs6ciby8PABc1/rSp08fAMCUKVMQHx+P5ORkfPfdd4iIiEBoaChsbW25rg1El/tzXFwcbG1t8fzzz2ucpziuDxwEGUhaWhrc3Nw09iv2paam1nSRGqTt27cjJSUFY8aMAUD1DqDcuk9PT0d+fn6NlrE+W79+Pe7du4ewsLAyj1dW3/x3rp2bN2+iqKgIr776KgYMGIDdu3dj8uTJWL9+Pd58800AXNf6EhISgrCwMBw+fBhdunSBp6cnxo4dizlz5iA8PBwA17Wh6HJ/TktLg6urK2QymcZ5gP4+Q8308ixMQ25ubpnroVhZWSmPs+q5fv06Zs2ahaCgILzxxhsApHqtrO55XaDKPX78GEuWLMHixYvh7Oxc5jmV1Tf/nWsnOzsbOTk5mDFjhnI02GuvvYaCggJERkZi6dKlXNd61LJlS/Tq1QsjRoxAkyZNcODAAaxYsQLNmjXD7Nmzua4NRJf7c019hnIQZCDW1tZltjgomratra1rukgNyv379zFo0CA4ODhg165dMDU1BSDVK9d99X300UdwcnLCnDlzyj2nsvrmutaOop7GjRuntn/8+PGIjIzE6dOnYWNjA4Drurp27tyJadOmISEhAR4eHgAo4JTL5Vi4cCHGjRvHf9cGosv9uaY+Q7k7zEDc3NyUTX+qFPvc3d1rukgNRkZGBgYOHIinT58iKipKrS4VTaXl1b2TkxO3Amnh5s2b2LBhA0JDQ5GamorExEQkJiYiLy8PhYWFSExMRHp6eqX1zX/n2lHUk6urq9p+FxcXAMCTJ0+4rvVk3bp16NKlizIAUhg6dChycnIQFxfHdW0gutyf3dzccP/+fYhSa7zr+zOUgyAD8fPzQ0JCAjIzM9X2x8TEKI8z3eXl5WHIkCFISEjA/v370b59e7XjzZs3h7OzM86dO6dx7dmzZ7netZSSkgK5XI7Q0FC0atVK+RMTE4OEhAS0atUKS5cuRYcOHWBmZqZR3wUFBYiPj+f61lLXrl0BUL2rUuQ9ODs7c13ryYMHD1BcXKyxv7CwEABQVFTEdW0gutyf/fz8kJOTg2vXrqmdp/fPUL2MMWMazpw5ozFPUF5envD29haBgYG1WLL6q6ioSAwdOlSYmZmJAwcOlHvejBkzhLW1tdo0BEeOHBEARERERE0Utd579OiR2LNnj8aPr6+v8PT0FHv27BGXLl0SQggREhIi3NzcRGZmpvL6r776SgAQBw8erK23UK9cuHBBABDjx49X2z9u3DhhZmYmUlJShBBc1/owePBgYWFhIW7cuKG2f9iwYcLExITrWg8qGiKv7f05OTm53HmCmjdvzvME1QejRo1SzjURGRkpgoODhZmZmYiOjq7totVL77zzjgAghgwZIrZu3arxo5CUlCSaNGki2rRpI9asWSNWrFghGjduLDp27Cjy8vJq8R3Uf2VNlnj+/HlhaWmpNrOulZWV6N+/fy2Vsn6aPHmyACBGjx4t1q5dK0aNGiUAiPfff195Dtd19SnmvXJxcRFLly4Va9euFQMHDhQAxNSpU5XncV3r7r///a8ICwsTM2fOFADEa6+9JsLCwkRYWJh4+vSpEEK3+/OCBQsEADFt2jSxceNG5YzR27dv11uZOQgyoNzcXDF//nzRrFkzYWlpKV544QURFRVV28Wqt3r37i0AlPuj6sqVK6J///7CxsZGODo6igkTJoj79+/XUskbjrKCICGEOHHihAgODhZWVlbC2dlZzJo1S+0bNKtcQUGB+Oc//ym8vLyEubm58Pb2FuHh4RrncV1XX0xMjBg4cKBo1qyZMDc3Fz4+PmL58uWisLBQ7Tyua914eXmVe3++e/eu8jxt78/FxcVixYoVwsvLS1hYWAhfX1+xbds2vZZZJkSprCPGGGOMMSPAidGMMcYYM0ocBDHGGGPMKHEQxBhjjDGjxEEQY4wxxowSB0GMMcYYM0ocBDHGGGPMKHEQxBhjjDGjxEEQY4wxxowSB0GMMYNr2bIlZDJZpT+bN2+u7aJqTVFmxlj9ZVbbBWCMGY/u3bvD29u73OMVHWOMMX3jIIgxVmOmTp2KSZMm1XYxGGMMAHeHMcYYY8xIcRDEGKuTVHNuNm7ciK5du8LW1haOjo545ZVXcObMmXKvTU9PxwcffABfX1/Y2NjAzs4OXbt2xcqVK5Gbm1vudSkpKViwYAE6duwIOzs72NrawsfHB5MmTcKpU6fKvW737t3o0aMH7O3tYWtri+7du+OXX34p89y0tDS888478PHxgZWVFWxsbNCiRQv069cPq1at0rJ2GGP6wKvIM8YMrmXLlrh37x42bdqkdXeYIgCaN28ePv/8c3Tv3h0tWrTA5cuXceXKFZiZmeH777/H8OHD1a67c+cO+vbti3v37sHZ2Rm9evVCYWEhjh07hqysLPj7++PIkSNo3Lix2nVHjx7FyJEj8fTpU7i4uCAoKAgWFhZITExEfHw8xo8fr5a4rSjfkiVLEBYWhuDgYHh4eOD69eu4ePEiZDIZdu/erVa++/fvo2vXrkhNTYWnpye6dOkCKysrpKam4urVqyguLsbTp091r2DGWNUIxhgzMC8vLwFAbNq0SetrAAgAwtraWhw9elTt2MqVKwUA4eDgIB48eKB2LDAwUAAQQ4cOFdnZ2cr9Dx8+FP7+/gKAGD9+vNo1SUlJwsHBQQAQixYtEvn5+WrHHzx4IE6cOFFm+RwdHcWZM2fUjn388ccCgPDx8VHb/8knnwgAYtq0aUIul6sdKygoEEeOHNGiZhhj+sJBEGPM4BRBUGU/T548UV6j2Dd37twyn7Nbt24CgFi+fLly34kTJwQAYWNjI+7fv69xzblz5wQAYWJiIpKTk5X7586dKwCIIUOGaP2eFOVbs2aNxrG8vDxlUJWUlKTc//bbbwsA4scff9T6dRhjhsOjwxhjNaayIfIWFhYa+954440yz3399ddx7tw5HD9+HB988AEA4Pjx4wCAkJAQuLq6alzTtWtXdO7cGRcvXkR0dDQmTJgAAIiKigIATJs2Taf3AwBDhgzR2GdpaYnWrVsjLi4OKSkpaNGiBQAgICAA69atw6JFiyCEQP/+/dGoUSOdX5Mxph8cBDHGakxVhsi3atWqwv1//vmncl9KSkqF1wBAmzZtcPHiReW5AHDv3j0AQLt27XQqGwB4enqWud/e3h4AkJeXp9w3ceJEHD58GNu3b8eIESNgamqK9u3bo0ePHhg5ciT69u2r8+szxqqOR4cxxuo1UctjO0xMtL+NmpiYYNu2bbh69SpWrlyJwYMHIy0tDREREejXrx+GDh2K4uJiA5aWMaaKgyDGWJ129+7dMvcnJiYCADw8PJT7mjdvDoBGiJVHcUxxLiC15ly/fr1aZdVW+/btsWDBAuzduxcPHz7EkSNH4OLign379mHLli01UgbGGAdBjLE6buvWrRXu79Onj3KfYjsqKgoPHjzQuCYuLg7x8fEwMTFBr169lPtDQkIA0HxENU0mk6Ffv34YP348ACA+Pr7Gy8CYseIgiDFWp0VERCgTnhXCw8Nx9uxZ2NnZYcqUKcr9PXr0QGBgIHJzczF9+nTk5OQoj/3111+YPn06AGDs2LHKZGUA+Mc//gE7Ozv8/PPP+Oijj1BYWKj2eg8fPsQff/xR7feyZcsWnD9/XmN/VlaW8j16eXlV+3UYY9rhyRIZYwanmCyxstFh/fv3V7aIKCYjnDt3Lr744gv07NkTzZs3x5UrV3D58mWYmppi586dGDlypNpzqE6W6OLiojZZYmZmZrmTJR46dAgjR45EVlYWXF1dERQUBHNzc9y7dw9xcXHlTpZY3i20T58+iI6OxrFjx5QtVMOGDcNPP/0Ed3d3+Pn5oXHjxnjy5AlOnjyJjIwMdOjQAadOnYKdnZ1O9csYqxoeHcYYqzEnT57EyZMnyz3u6OioDIIUwsPD0bZtW0RGRiI2Nhbm5uYICQnB4sWLERwcrPEcrVu3xoULF7Bq1Srs3bsX+/fvh4mJCdq2bYsxY8YgNDQU1tbWGtf1798fV65cwerVqxEVFYWoqCiYmZnB3d0dEydOxFtvvVXt9//uu++iVatWOHXqFC5cuID09HQ4OTmhffv2GD9+PN58803Y2tpW+3UYY9rhliDGWJ1UWUsLY4xVF+cEMcYYY8wocRDEGGOMMaPEQRBjjDHGjBInRjPG6iTOBWKMGRq3BDHGGGPMKHEQxBhjjDGjxEEQY4wxxowSB0GMMcYYM0ocBDHGGGPMKHEQxBhjjDGjxEEQY4wxxowSB0GMMcYYM0ocBDHGGGPMKP0/DKCeVTLLLTQAAAAASUVORK5CYII=\n", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.title(\"Adversarial Training Curves\", fontsize=20)\n", "plt.plot(np.linspace(1, n_epochs, n_epochs), epoch_gen_loss_list, color=\"C0\", linewidth=2.0, label=\"Generator\")\n", "plt.plot(np.linspace(1, n_epochs, n_epochs), epoch_disc_loss_list, color=\"C1\", linewidth=2.0, label=\"Discriminator\")\n", "plt.yticks(fontsize=12)\n", "plt.xticks(fontsize=12)\n", "plt.xlabel(\"Epochs\", fontsize=16)\n", "plt.ylabel(\"Loss\", fontsize=16)\n", "plt.legend(prop={\"size\": 14})\n", "plt.show()"]}, {"cell_type": "markdown", "id": "21bbecae", "metadata": {}, "source": ["### Visualise some reconstruction images"]}, {"cell_type": "code", "execution_count": 16, "id": "caf2b1e1", "metadata": {}, "outputs": [{"data": {"image/png": "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*****************************+opHq9Hjqdjnhc6ClOJBJYW1sTzy7JbLValdZJTI/Q/VapqHiOVCol/ZhbrRa63a54xtj9ot/v4/nnn8fe3h6+/vWvyxgZAmbYWK8itkg5MovRvmhiZfFgEHY+hz0u54Q2jpmmkEwm8X3f932yOI6ev9wuEolIqoD2Jo/HYymio3FLGcHUKK5eSa6gUx6IXq8n2wCnHmien97iTqeDbrcrfdhJqnl9TKtgtIjH015v/m926PDyrGtPOQuPeR30EpP30XNMOarlBM/HawrzPN3eD69I/KJ4zEORVhEG5o3RN1V/rsONfGlzuRyefPJJvOtd70KpVMJwOMStW7eQy+WkYvXatWtYW1vD/v4+ms2mKKBGo4Hj4+N7BLYOo5gP18wv4m8vC01fT5CHb3qhw+xrYXGWuN+ERnuLADiE+pNPPolisYjDw0MAkHZnJIKEFvRugpkCX7dQcrtOt/lnGs1aITGcCZwqNXqNSKC1UiRR5986JNvpdKRanddHr1A0GkWxWES/30e5XJb+xyzO0/cyyP32ulYLCzecxbviFn3V4X4alaurq9IjnKBRTFJpEjrt3aXhTAJtLv3sZjjrNAvOU62nucAHjWBdiGd6Zb2Ob3qtzXtj3h+3aLeWd26tKHX0XfdmdzME9G83b7fb8zL/vl8ITI71ii9uWJTH2PSmaphkz+t7/q2tRm2tUPHQoltbW8Pm5iaeeOIJJJNJtFotVKtVaXMUjUaxsrLiSFK/evWqhDs7nQ6y2SwGgwH29/dRq9Wwv7+PXq+HXq8nPRH1S22SaDevsZel5HePvO6n1/9++**********************************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\n", "text/plain": ["<Figure size 800x600 with 15 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# get the first 5 examples to plot\n", "n_evaluations = 5\n", "\n", "fig, axs = plt.subplots(nrows=n_evaluations, ncols=3, constrained_layout=True, figsize=(8, 6))\n", "\n", "\n", "# Remove ticks\n", "for ax in axs.flatten():\n", "    ax.set_xticks([])\n", "    ax.set_yticks([])\n", "\n", "\n", "for image_n in range(n_evaluations):\n", "    axs[image_n, 0].imshow(\n", "        intermediary_images[image_n][0, ..., intermediary_images[image_n].shape[3] // 2].cpu(), cmap=\"gray\"\n", "    )\n", "    axs[image_n, 1].imshow(\n", "        intermediary_images[image_n][0, :, intermediary_images[image_n].shape[2] // 2, ...].cpu().rot90(), cmap=\"gray\"\n", "    )\n", "    axs[image_n, 2].imshow(\n", "        intermediary_images[image_n][0, intermediary_images[image_n].shape[1] // 2, ...].cpu().rot90(), cmap=\"gray\"\n", "    )\n", "    axs[image_n, 0].set_ylabel(f\"Epoch {val_samples[image_n]:.0f}\")"]}, {"cell_type": "code", "execution_count": 18, "id": "dd03417f", "metadata": {}, "outputs": [{"data": {"image/png": "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***************************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\n", "text/plain": ["<Figure size 640x480 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, ax = plt.subplots(nrows=1, ncols=2)\n", "ax[0].imshow(images[0, channel, ..., images.shape[2] // 2].cpu(), vmin=0, vmax=1, cmap=\"gray\")\n", "ax[0].axis(\"off\")\n", "ax[0].title.set_text(\"Inputted Image\")\n", "ax[1].imshow(reconstruction[0, channel, ..., reconstruction.shape[2] // 2].detach().cpu(), vmin=0, vmax=1, cmap=\"gray\")\n", "ax[1].axis(\"off\")\n", "ax[1].title.set_text(\"Reconstruction\")\n", "plt.show()"]}, {"cell_type": "markdown", "id": "292506bf", "metadata": {}, "source": ["## Clean up data directory\n", "\n", "Remove directory if a temporary storage was used"]}, {"cell_type": "code", "execution_count": null, "id": "25551b82", "metadata": {}, "outputs": [], "source": ["if directory is None:\n", "    shutil.rmtree(root_dir)"]}], "metadata": {"jupytext": {"formats": "ipynb,py"}, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.13"}}, "nbformat": 4, "nbformat_minor": 5}