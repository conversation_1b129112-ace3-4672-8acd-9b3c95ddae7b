#default_language_version:
#  python: python3.8

ci:
  autofix_prs: true
  autoupdate_commit_msg: '[pre-commit.ci] pre-commit suggestions'
  autoupdate_schedule: quarterly
  # submodules: true

repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: end-of-file-fixer
        exclude: ^tutorials/
      - id: trailing-whitespace
      - id: check-yaml
      - id: check-docstring-first
      - id: check-executables-have-shebangs
      - id: check-toml
      - id: check-case-conflict
      - id: check-added-large-files
        args: ['--maxkb=1024']
      - id: detect-private-key
      - id: forbid-new-submodules
      - id: pretty-format-json
        args: ['--autofix', '--no-sort-keys', '--indent=4']
      - id: mixed-line-ending

  - repo: https://github.com/asottile/pyupgrade
    rev: v3.3.1
    hooks:
      - id: pyupgrade
        args: [--py37-plus]
        name: Upgrade code
        exclude: |
          (?x)^(
              versioneer.py|
              monai/_version.py
          )$

  - repo: https://github.com/asottile/yesqa
    rev: v1.4.0
    hooks:
      - id: yesqa
        name: Unused noqa
        additional_dependencies:
          - flake8>=3.8.1
          - flake8-bugbear
          - flake8-comprehensions
          - flake8-executable
          - flake8-pyi
          - pep8-naming
        exclude: |
          (?x)^(
              generative/__init__.py|
              docs/source/conf.py
          )$

  - repo: https://github.com/hadialqattan/pycln
    rev: v2.1.2
    hooks:
      - id: pycln
        args: [--config=pyproject.toml]

#  - repo: https://github.com/psf/black
#    rev: 22.3.0
#    hooks:
#      - id: black
#
#  - repo: https://github.com/PyCQA/isort
#    rev: 5.9.3
#    hooks:
#      - id: isort
