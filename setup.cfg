[metadata]
name = monai-generative
author = MONAI Consortium
author_email = <EMAIL>
url = https://monai.io/
description = MONAI Generative Models makes it easy to train, evaluate, and deploy generative models and related applications
long_description = file:README.md
long_description_content_type = text/markdown; charset=UTF-8
platforms = OS Independent
license = Apache License 2.0
license_files =
    LICENSE
project_urls =
    Documentation=https://docs.monai.io/
    Bug Tracker=https://github.com/Project-MONAI/GenerativeModels/issues
    Source Code=https://github.com/Project-MONAI/GenerativeModels
classifiers =
    Intended Audience :: Developers
    Intended Audience :: Education
    Intended Audience :: Science/Research
    Intended Audience :: Healthcare Industry
    Programming Language :: C++
    Programming Language :: Python :: 3
    Programming Language :: Python :: 3.8
    Programming Language :: Python :: 3.9
    Programming Language :: Python :: 3.10
    Topic :: Scientific/Engineering
    Topic :: Scientific/Engineering :: Artificial Intelligence
    Topic :: Scientific/Engineering :: Medical Science Apps.
    Topic :: Scientific/Engineering :: Information Analysis
    Topic :: Software Development
    Topic :: Software Development :: Libraries
    Typing :: Typed

[options]
python_requires = >= 3.8
# for compiling and develop setup only
# no need to specify the versions so that we could
# compile for multiple targeted versions.
setup_requires =
    torch
    ninja
install_requires =
    monai>=1.2.0rc1
    torch>=1.9
    numpy>=1.20

[flake8]
select = B,C,E,F,N,P,T4,W,B9
max_line_length = 120
# C408 ignored because we like the dict keyword argument syntax
# E501 is not flexible enough, we're using B950 instead
# N812 lowercase 'torch.nn.functional' imported as non lowercase 'F'
# B023 https://github.com/Project-MONAI/MONAI/issues/4627
# B028 https://github.com/Project-MONAI/MONAI/issues/5855
# B907 https://github.com/Project-MONAI/MONAI/issues/5868
# B908 https://github.com/Project-MONAI/MONAI/issues/6503
ignore =
    E203
    E501
    E741
    W503
    W504
    C408
    N812
    B023
    B905
    B028
    B907
    B908
per_file_ignores = __init__.py: F401, __main__.py: F401
exclude = *.pyi,.git,.eggs,generative/_version.py,versioneer.py,venv,.venv,_version.py,tutorials/

[isort]
known_first_party = generative
profile = black
line_length = 120
# generative/networks/layers/ is excluded because it is raising JIT errors
skip = .git, .eggs, venv, .venv, versioneer.py, _version.py, conf.py, monai/__init__.py, tutorials/, generative/networks/layers/
skip_glob = *.pyi
add_imports = from __future__ import annotations
append_only = true

[mypy]
# Suppresses error messages about imports that cannot be resolved.
ignore_missing_imports = True
# Changes the treatment of arguments with a default value of None by not implicitly making their type Optional.
no_implicit_optional = True
# Warns about casting an expression to its inferred type.
warn_redundant_casts = True
# No error on unneeded # type: ignore comments.
warn_unused_ignores = False
# Shows a warning when returning a value with type Any from a function declared with a non-Any return type.
warn_return_any = True
# Prohibit equality checks, identity checks, and container checks between non-overlapping types.
strict_equality = True
# Shows column numbers in error messages.
show_column_numbers = True
# Shows error codes in error messages.
show_error_codes = True
# Use visually nicer output in error messages: use soft word wrap, show source code snippets, and show error location markers.
pretty = False
# Warns about per-module sections in the config file that do not match any files processed when invoking mypy.
warn_unused_configs = True
# Make arguments prepended via Concatenate be truly positional-only.
strict_concatenate = True

exclude = venv/

[coverage:run]
concurrency = multiprocessing
source = .
data_file = .coverage/.coverage
omit = setup.py

[coverage:report]
exclude_lines =
    pragma: no cover
    if TYPE_CHECKING:
    # Don't complain if tests don't hit code:
    raise NotImplementedError
    if __name__ == .__main__.:
show_missing = True
skip_covered = True

[coverage:xml]
output = coverage.xml
