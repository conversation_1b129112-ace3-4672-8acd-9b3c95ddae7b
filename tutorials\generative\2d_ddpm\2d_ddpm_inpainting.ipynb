{"cells": [{"cell_type": "code", "execution_count": null, "id": "4c8ec6e8", "metadata": {}, "outputs": [], "source": ["# Copyright (c) MONAI Consortium\n", "# Licensed under the Apache License, Version 2.0 (the \"License\");\n", "# you may not use this file except in compliance with the License.\n", "# You may obtain a copy of the License at\n", "#     http://www.apache.org/licenses/LICENSE-2.0\n", "# Unless required by applicable law or agreed to in writing, software\n", "# distributed under the License is distributed on an \"AS IS\" BASIS,\n", "# WITHOUT WAR<PERSON><PERSON>IES OR CONDITIONS OF ANY KIND, either express or implied.\n", "# See the License for the specific language governing permissions and\n", "# limitations under the License."]}, {"cell_type": "markdown", "id": "9d71306f", "metadata": {}, "source": ["# Inpainting with Denoising Diffusion Probabilistic Models\n", "\n", "This tutorial illustrates how to use MONAI for training a denoising diffusion probabilistic model (DDPM)[1] to inpaint 2D images.\n", "\n", "[1] - <PERSON> et al. \"Denoising Diffusion Probabilistic Models\" https://arxiv.org/abs/2006.11239\n", "\n", "[2] - <PERSON><PERSON><PERSON> et al. \"RePaint: Inpainting using Denoising Diffusion Probabilistic Models\" https://arxiv.org/abs/2201.09865\n", "\n", "\n", "## Setup environment"]}, {"cell_type": "code", "execution_count": 1, "id": "6aa3774e", "metadata": {}, "outputs": [], "source": ["!python -c \"import monai\" || pip install -q \"monai-weekly[tqdm]\"\n", "!python -c \"import matplotlib\" || pip install -q matplotlib\n", "%matplotlib inline"]}, {"cell_type": "markdown", "id": "f3154fee", "metadata": {}, "source": ["## Setup imports"]}, {"cell_type": "code", "execution_count": 2, "id": "dd62a552", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["MONAI version: 1.1.dev2248\n", "Numpy version: 1.23.4\n", "Pytorch version: 1.13.1+cu117\n", "MONAI flags: HAS_EXT = False, USE_COMPILED = False, USE_META_DICT = False\n", "MONAI rev id: 3400bd91422ccba9ccc3aa2ffe7fecd4eb5596bf\n", "MONAI __file__: /home/<USER>/Envs/gen2/lib/python3.8/site-packages/monai/__init__.py\n", "\n", "Optional dependencies:\n", "Pytorch Ignite version: 0.4.10\n", "Nibabel version: NOT INSTALLED or UNKNOWN VERSION.\n", "scikit-image version: NOT INSTALLED or UNKNOWN VERSION.\n", "Pillow version: 9.3.0\n", "Tensorboard version: NOT INSTALLED or UNKNOWN VERSION.\n", "gdown version: NOT INSTALLED or UNKNOWN VERSION.\n", "TorchVision version: 0.14.1+cu117\n", "tqdm version: 4.64.1\n", "lmdb version: NOT INSTALLED or UNKNOWN VERSION.\n", "psutil version: 5.9.4\n", "pandas version: NOT INSTALLED or UNKNOWN VERSION.\n", "einops version: 0.6.0\n", "transformers version: NOT INSTALLED or UNKNOWN VERSION.\n", "mlflow version: NOT INSTALLED or UNKNOWN VERSION.\n", "pynrrd version: NOT INSTALLED or UNKNOWN VERSION.\n", "\n", "For details about installing the optional dependencies, please visit:\n", "    https://docs.monai.io/en/latest/installation.html#installing-the-recommended-dependencies\n", "\n"]}], "source": ["import os\n", "import shutil\n", "import tempfile\n", "import time\n", "\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "import torch\n", "import torch.nn.functional as F\n", "from monai import transforms\n", "from monai.apps import MedNISTDataset\n", "from monai.config import print_config\n", "from monai.data import CacheDataset, DataLoader\n", "from monai.utils import first, set_determinism\n", "from torch.cuda.amp import GradScaler, autocast\n", "from tqdm import tqdm\n", "\n", "from generative.inferers import DiffusionInferer\n", "from generative.networks.nets import DiffusionModelUNet\n", "from generative.networks.schedulers import DDPMScheduler\n", "\n", "print_config()"]}, {"cell_type": "markdown", "id": "be99fa93", "metadata": {}, "source": ["## Setup data directory\n", "\n", "You can specify a directory with the MONAI_DATA_DIRECTORY environment variable.\n", "\n", "This allows you to save results and reuse downloads.\n", "\n", "If not specified a temporary directory will be used."]}, {"cell_type": "code", "execution_count": 3, "id": "8fc58c80", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/tmp/tmpd73sz8x4\n"]}], "source": ["directory = os.environ.get(\"MONAI_DATA_DIRECTORY\")\n", "root_dir = tempfile.mkdtemp() if directory is None else directory\n", "print(root_dir)"]}, {"cell_type": "markdown", "id": "a36b12f0", "metadata": {}, "source": ["## Set deterministic training for reproducibility"]}, {"cell_type": "code", "execution_count": 4, "id": "ad5a1948", "metadata": {"tags": []}, "outputs": [], "source": ["set_determinism(42)"]}, {"cell_type": "markdown", "id": "b41e37b3", "metadata": {}, "source": ["## Setup MedNIST Dataset and training and validation dataloaders\n", "In this tutorial, we will train our models on the MedNIST dataset available on MONAI\n", "(https://docs.monai.io/en/stable/apps.html#monai.apps.MedNISTDataset). In order to train faster, we will select just\n", "one of the available classes (\"Hand\"), resulting in a training set with 7999 2D images."]}, {"cell_type": "code", "execution_count": 5, "id": "65e1c200", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2023-01-05 16:00:04,171 - INFO - Downloaded: /tmp/tmpd73sz8x4/MedNIST.tar.gz\n", "2023-01-05 16:00:04,240 - INFO - Verified 'MedNIST.tar.gz', md5: 0bc7306e7427e00ad1c5526a6677552d.\n", "2023-01-05 16:00:04,240 - INFO - Writing into directory: /tmp/tmpd73sz8x4.\n"]}], "source": ["train_data = MedNISTDataset(root_dir=root_dir, section=\"training\", download=True, progress=False, seed=0)\n", "train_datalist = [{\"image\": item[\"image\"]} for item in train_data.data if item[\"class_name\"] == \"Hand\"]"]}, {"cell_type": "markdown", "id": "5d503ec9", "metadata": {}, "source": ["Here we use transforms to augment the training dataset:\n", "\n", "1. `LoadImaged` loads the hands images from files.\n", "1. `EnsureChannelFirstd` ensures the original data to construct \"channel first\" shape.\n", "1. `ScaleIntensityRanged` extracts intensity range [0, 255] and scales to [0, 1].\n", "1. `RandAffined` efficiently performs rotate, scale, shear, translate, etc. together based on PyTorch affine transform."]}, {"cell_type": "code", "execution_count": 6, "id": "e2f9bebd", "metadata": {"tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Loading dataset: 100%|██████████████████████████████████████████████████████| 7999/7999 [00:04<00:00, 1861.75it/s]\n"]}], "source": ["train_transforms = transforms.Compose(\n", "    [\n", "        transforms.LoadImaged(keys=[\"image\"]),\n", "        transforms.EnsureChannelFirstd(keys=[\"image\"]),\n", "        transforms.ScaleIntensityRanged(keys=[\"image\"], a_min=0.0, a_max=255.0, b_min=0.0, b_max=1.0, clip=True),\n", "        transforms.RandA<PERSON>ed(\n", "            keys=[\"image\"],\n", "            rotate_range=[(-np.pi / 36, np.pi / 36), (-np.pi / 36, np.pi / 36)],\n", "            translate_range=[(-1, 1), (-1, 1)],\n", "            scale_range=[(-0.05, 0.05), (-0.05, 0.05)],\n", "            spatial_size=[64, 64],\n", "            padding_mode=\"zeros\",\n", "            prob=0.5,\n", "        ),\n", "    ]\n", ")\n", "train_ds = CacheDataset(data=train_datalist, transform=train_transforms)\n", "train_loader = DataLoader(train_ds, batch_size=128, shuffle=True, num_workers=4, persistent_workers=True)"]}, {"cell_type": "code", "execution_count": 7, "id": "938318c2", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2023-01-05 16:00:26,522 - INFO - Verified 'MedNIST.tar.gz', md5: 0bc7306e7427e00ad1c5526a6677552d.\n", "2023-01-05 16:00:26,522 - INFO - File exists: /tmp/tmpd73sz8x4/MedNIST.tar.gz, skipped downloading.\n", "2023-01-05 16:00:26,522 - INFO - Non-empty folder exists in /tmp/tmpd73sz8x4/MedNIST, skipped extracting.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Loading dataset: 100%|██████████████████████████████████████████████████████| 1005/1005 [00:00<00:00, 1902.10it/s]\n"]}], "source": ["val_data = MedNISTDataset(root_dir=root_dir, section=\"validation\", download=True, progress=False, seed=0)\n", "val_datalist = [{\"image\": item[\"image\"]} for item in val_data.data if item[\"class_name\"] == \"Hand\"]\n", "val_transforms = transforms.Compose(\n", "    [\n", "        transforms.LoadImaged(keys=[\"image\"]),\n", "        transforms.EnsureChannelFirstd(keys=[\"image\"]),\n", "        transforms.ScaleIntensityRanged(keys=[\"image\"], a_min=0.0, a_max=255.0, b_min=0.0, b_max=1.0, clip=True),\n", "    ]\n", ")\n", "val_ds = CacheDataset(data=val_datalist, transform=val_transforms)\n", "val_loader = DataLoader(val_ds, batch_size=128, shuffle=False, num_workers=4, persistent_workers=True)"]}, {"cell_type": "markdown", "id": "a56a4e42", "metadata": {}, "source": ["### Visualisation of the training images"]}, {"cell_type": "code", "execution_count": 8, "id": "b698f4f8", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["batch shape: torch.Size([128, 1, 64, 64])\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 1200x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["check_data = first(train_loader)\n", "print(f\"batch shape: {check_data['image'].shape}\")\n", "image_visualisation = torch.cat(\n", "    [check_data[\"image\"][0, 0], check_data[\"image\"][1, 0], check_data[\"image\"][2, 0], check_data[\"image\"][3, 0]], dim=1\n", ")\n", "plt.figure(\"training images\", (12, 6))\n", "plt.imshow(image_visualisation, vmin=0, vmax=1, cmap=\"gray\")\n", "plt.axis(\"off\")\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "id": "d3090350", "metadata": {}, "source": ["### Define network, scheduler, optimizer, and inferer\n", "At this step, we instantiate the MONAI components to create a DDPM, the UNET, the noise scheduler, and the inferer used for training and sampling. We are using\n", "the original DDPM scheduler containing 1000 timesteps in its Markov chain, and a 2D UNET with attention mechanisms\n", "in the 2nd and 3rd levels, each with 1 attention head."]}, {"cell_type": "code", "execution_count": 9, "id": "2c52e4f4", "metadata": {"lines_to_next_cell": 0, "tags": []}, "outputs": [], "source": ["device = torch.device(\"cuda\")\n", "\n", "model = DiffusionModelUNet(\n", "    spatial_dims=2,\n", "    in_channels=1,\n", "    out_channels=1,\n", "    num_channels=(128, 256, 256),\n", "    attention_levels=(False, True, True),\n", "    num_res_blocks=1,\n", "    num_head_channels=256,\n", ")\n", "model.to(device)\n", "\n", "scheduler = DDPMScheduler(num_train_timesteps=1000)\n", "\n", "optimizer = torch.optim.Adam(params=model.parameters(), lr=2.5e-5)\n", "\n", "inferer = DiffusionInferer(scheduler)"]}, {"cell_type": "markdown", "id": "5a316067", "metadata": {}, "source": ["### Model training\n", "Here, we are training our model for 50 epochs (training time: ~33 minutes).\n", "\n", "If you would like to skip the training and use a pre-trained model instead, set `use_pretrained=True`. This model was trained using the code in `tutorials/generative/distributed_training/ddpm_training_ddp.py`"]}, {"cell_type": "code", "execution_count": 10, "id": "0f697a13", "metadata": {"lines_to_next_cell": 0, "tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Epoch 0: 100%|████████████| 63/63 [00:39<00:00,  1.60it/s, loss=0.739]\n", "Epoch 1: 100%|████████████| 63/63 [00:54<00:00,  1.15it/s, loss=0.287]\n", "Epoch 2: 100%|███████████| 63/63 [00:56<00:00,  1.11it/s, loss=0.0926]\n", "Epoch 3: 100%|███████████| 63/63 [00:53<00:00,  1.17it/s, loss=0.0349]\n", "Epoch 4: 100%|███████████| 63/63 [00:49<00:00,  1.27it/s, loss=0.0206]\n", "100%|████████████████████████████████████████████████████████████████████████| 1000/1000 [00:09<00:00, 103.49it/s]\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 200x200 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["Epoch 5: 100%|███████████| 63/63 [00:47<00:00,  1.34it/s, loss=0.0178]\n", "Epoch 6: 100%|███████████| 63/63 [00:52<00:00,  1.19it/s, loss=0.0155]\n", "Epoch 7: 100%|███████████| 63/63 [00:53<00:00,  1.19it/s, loss=0.0156]\n", "Epoch 8: 100%|███████████| 63/63 [00:52<00:00,  1.21it/s, loss=0.0145]\n", "Epoch 9: 100%|███████████| 63/63 [00:54<00:00,  1.16it/s, loss=0.0135]\n", "100%|████████████████████████████████████████████████████████████████████████| 1000/1000 [00:09<00:00, 107.32it/s]\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAKEAAAChCAYAAACvUd+2AAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjYuMiwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8o6BhiAAAACXBIWXMAAA9hAAAPYQGoP6dpAAA04ElEQVR4nO2d2W4rybG1g7NEzdq73W3AgAHDN+cR/P7vYMOAAaMN9540UpzESTwX/X+pxdiRVVmU9O9zoQAIkjVk5bAyYkXkUK3tdru1d3mXHyjtH52Bd3mXdxC+yw+XdxC+yw+XdxC+yw+XdxC+yw+XdxC+yw+XdxC+yw+XdxC+yw+XbumFf/vb38zMbLlc2mazse12a09PT+l8u922dvt3TK9WK9tsNtZut63Vatl6vbb1em2tVitdv1qtjDg5aZGeXsd/Pnr9drtNaWienp6e0n/NJ9/ki/OtVisd63Q635UN4Xr9cFzzqee0LFoG8q/H+N5sNt/lU9OIROuf8nS73e/ypWnoceptvV6nvGnZcuWmDJRJ21Hvr5JiEGqDRo309PSUKoFK8YVVUUBFDeUz76/T63Np+GcpmKPr/TFN96UDSznwvKaQXwWJ5r8KzD9y4KwYhKPRyLbbbdIwZs8F0V5oZtbtdr/TNAg9BY2DtNvtBGzf4xBfyRHIyYdPKxLVRojXZJEm8D1d7480YCTRPVoXvny5e6LytVqtZA38/dHH17fveCUA9dakiRSD8PHxMWVIK56H0jCdTidl6OnpKV3X7XZDk1vSI/VYrkJIhwpot9uhRtA01OzoOQW6gtk/K9KUOY0dlTXS+HpdifaMgJLrrIAkAqFPo4lEIFelVCfFIIRfmO2aZjNL/A8+QQ9EAF+kSVRyXCunlfQ+fZY/5sWbKA8wrxW2220CteZBgZ4rj3/mPtf4vCvQVetUURI9XwpyvS4y7ZECUZ5bSkGKQUhhSRjAIZ6Q+sypBsxpFX9PEzB64pxrXK2gEsDq/T7vPj85/hVd48/lzHMd31XtFuW3tHyRSfcWz5cDbRfV95toQrQbYCwxkXo+B6Sqys/dU/JM/3z97bkQkjOPKjmHypelqXgT6vNddZ/PTykv1TTq6jrSnr5zooia0Amzht6x2bPpjcxqrtLqTDDfOe0RXZs7VtKI+3CfKvMSHa/yUnOSqyOVHHXIabPovqp85q6PgBhpSW+SS6QRCElUOZ6/Jrovl16d1JHmKpA2BVoTJ6CJxmtiJUqkNJ8lWr0kf3X5iKxBU4tQDEJ6KQ4Kgc0cx9HfVT0y0hA5j7kKkD4gnbtO029irnJ5zV1bosX5X5XPiA/mOqSv57q2qSp/lFaOm+q3xodLpfGwXYn28+eqwPMa8lLOWJJ2k3N15fbAUzNWaiFeM78lz/OfKC8+VFMqjTWhhgRyvc1nvs4J8RI1YsnvUqlyjuruyWnpkjL476p7qxygujz6fJbWTU7TVwEtina0Wq0ULy6RYk0YccA6yXlUOSKfk5z5aaI9qqQq/bpjVfd5wDXhzG8tpZ59pN3qvqvSi6SRJvSxo5yUAKoqNuWvj8IpuXRzaVXdW0XiS7V4Ew5YkidfT1Vc0JelLm3ON9WWmg9/73a7OwlltVq9XYjGT1RQqSPC0fW+kUu9uqo0/XNLGkTvrbu2znGK7mnagXLPq8trE/NdkpfSe/SjscISaQxClRICWteT6zhWpNGiRi3RlnX5jPJRZbZyQH+pI+Y1TsT1omeUcLqqYzlNW2cB9bx6yG+mCfXBdaGOqEe/JQdSIFaFM6qkynxG2volQKyjAR6IuedWpdXE6arLQ4kTCgDfZNgu91B/7P+C7JOPJmDdR9PltFROq3MuAqJeU5rfSNM35faep0aOV4nm9NIIhCAclOsAdk69lzSU54alJN+fzx2rcniaSqTRcxqoij/m7tPjOU34knzn8tI0LbNnZ9VPBGnaSYt1pp+Lpur2/4oGbCIv0ZYv5Xovfe5rOG77SJVSeUmeijVhv9+3Vqtl3W7XOp1OmsgQZaBKC0XEvq4AngtFv+vSeSkfLeG0VeGROs1c9Uz/O5euz4fel+PwVW3n8xvlpcphLQV8sSas8np+hCYsqby3fmbp+Rwo98nzSzvSPuffum4bOyZ1lVei3Uo9O33eSyvCpxHFKKO8RfksLdtLzWdpaMUfr3JEXgtoOc1cWpcqxSBUDuiH8Jpwlty51winNLnO57/k+XXat4pqlDpoTc/p8boyRJ0ud01JnvbluF4ag1DNMlIVz/KSy3TUwC/RJLleWsLp6sQ3Zi6fTbhuVfp6fQmASnhkxOleSg/2CR+ZNQRhVCk50/pagenSNKpAV6ct6pyGfUxMVQeqe0Z0fdW9+syqMExTh6ROcmm/GQh5qMaF6q4tTdP/jxpQuWF0TSlX2iffr00Tcs+I4o++I9XV/Ws7ET5fObD53286YpLTLHWmpfRY03M58aZB02gCxpxmqSP4OU1TGsbRY6VWpc4y5fITmeQq2uTrNrKQbwLCJolWyb7m7CW80HOkJtquidl6bS0Upd/EqdB76kCXu7/quVX5aVIXjRe/r9frouubcMNcQ0faq8pc81yfjzouW6Ulc7ynpBz7ShNtWeK9Ria+Lt0q8Y4NdRzRpRJprN4ijbivV+Sl1FMr9RA9AKvIc0mDv7Wm0+eU1kVJWiXHXkO0zpuMmDTWhKvVKpuBnMqvur6peat6TkSOtZfyrVuS5Brca6Mq7RQ9/7Wlri6qnh9d8xIgRpyQtejUd5NNkfaeymW2u5OWZnCfApYS+ugaf57/vkf6TZKa8r0qcp/LTy7NEp5WYv59vWinKy3HS0TrV+u5ydLPF4EwypCXUsJflabvybljmgeOqTaESrASjDSWy6WtVitbr9e2XC7NzHb2ziG/EWg1HQUB4jdN8vmO0i0BcNXxfbVcxPWi81VARgsy4aVEXhWEPjNNK6POE45CQ/68B6ECEUoxGAxCfpgDVZRH/R9p6bq8vkTqwFJyv89j6TNzz4uoUGlEpVGIJgqU5o5HmfOVx/k6LhlpjDozHXE+FmkxHa3f71u327XHx0dbrVb2+Pho0+nUNpuNPT4+hmPkpQ0XdZDXAGJEO/bJX04ibd70vk6n811Hr5LGw3ZRiCYHxMhsVvG5Ko/QaygvOTOnIOS72+1ar9ezk5MT6/f7dnh4aKvVyubzeSrjZrOxzWZj6/U6S/5zZpRzXnNUcb8Sb/glgPOdvSnIIsuRS6fdbluv1yt2ThrvWe0rKLczV53nG6Wt4oFZZ6qj56sXrI3AHjqc73a7STt2u11brVbW7XZtvV7bdDpNfLF0P8MoTzkgRlJHAaJromf5a+tAXAVMf30diN/EMaFRN5tNdvP0CChNSbme43eu8aucHt/wfGNqN5uNHR0dWa/Xs4ODgx3zsV6vbTwe22KxsC9fvth8PrfHx0dbLpfhzvZNNZKWqfSe3LGcFYm0VNSxm4C5Dri+fV+dE/pdWetMYiQvcVb0f0kaUaNoR2KJgpklfgiZRut1u107Pj7emca2Xq/T6y+qwNeU9L+m1D27zsPd9zlRlKJE9jLHauqi6/gmUxE/ipwIf3+OAlTlse7/09NTcjroWIPBwE5PT63b7Vq/37fNZmMnJye2Wq2s3+/bfD63+/t7m0wmNp1ObTwe29PTUxi432634ftHmuTRS52ZLLlOry3hhPt0sKZeMdLYHNeZQH77zOl1TZ5Z9b9JmuQfUwit0Ncs4DkT22u323Z4eGitVsseHx/TS4Ew5/6FPjk6EjVak85VVeaXarN9n10FxOh3lRSDEBMVcaJcBr13tN1ud9605NOo03yRh1zisHjtvVgsbLPZ2Gw2s06nY7PZzA4ODuzg4CA5KIeHh4knrtdrOzg4sPl8bg8PDzYcDm2xWNjDw4Ot12tbLBY7gPZlANC+jHXcsqlpKzGxOQcpslBNNKuZpU5MHb46CD0njDIUAUGB6F8tUSWl3nBpOvp7s9ns8DtGTHjBz3a7tU6nY51Oxw4ODlKohve1rFYra7fbtlgsEoc0i19foVSE/x6ApeAp1fglWqpJmrnneBC+uTn2b2Cq8mxzmfYZzvG/nEb01/tj0bVR3tjqeDwe23K5TCZ6OBzaZrOxXq+XHBLCNoPBwLbbrZ2cnNjx8bHNZjMbDoe2XC5tPB7ber22+Xxuq9UqfUre3ZKrs7owTaQ9SzRXpPFyz40AnaMWnNsnBlkMwhJyHFVIjh9GlZwDYRRaiDRc9Dz/bDXLgIZxTrTdYDBIAW0+g8Fg5/9sNrNWq2WLxcK63W4CM2PPaprVVPt3/tXlOarfus5fpeG8uW0C3BwA9XwTM4zsPXZc5flpDFDfrIlo4dGwaCPPOX3vrQNcnQbR8/DT2WyW8od33O/3d0wwIZzDw0Nrt9t2cHBgnU7HVqtV0ojD4dAeHx9tNpvZbDaz5XJp8/ncNptNCutoGdR85zRaiSZ6iXPSBJSeWii94j4iDAcHB6/PCavMZF1mNTiroRottI+94cSU5Esl6rU54g/Xm06nyfNlxKTX61m/30/xxKOjI2u32zYcDu3o6MjW67WdnJzYer22yWRiq9XKbm9v7fHx0SaTiY3HY5tMJnZ/f78zFk3AO3oDludSuThcHZcr0Zw5idLP/Y5eNUy9UV8l8irmOBLVZjlAoL41GBy9RfSlzgkSmXWzZ464Wq1sNpslzdfv963X6yWPWj2/7XZrvV4vacVut2tnZ2dp9OXo6Mgmk4kNBoOUrgKe90ar5sdh2odXReXzv71EnK+Ka/sOzttcoSonJyd2dnZmh4eHdnZ29vog5OG+p1Q5EP43ogPch4eHOzNZCADTQJHJ8hVTZfZ93qPK1Rd2LxYL6/f79vj4aP1+38zMjo6OrNvtJjMNOIfDYQLj09OTnZ2dmdnzy4bQhI+Pj+n76urKZrOZTSaTndgjWtnMUqxS8x2VwZfRt1VOIidRz3nHj9lHugGmtuHFxYUdHx/b5eWl/fTTT3ZwcGDn5+dvA8ISiQBYBUpCIfoWUQqtpD4XFoqOlWiRSAMQfmm1Wsl8MrMGDqj5II5I3iNvn8kQABiQM1S4XC5tsVgkbzqXtyrwRea5VBvmRAGEteLDfy1Hv9+3wWBgg8HADg4OEncukRdxwhKOmPN62Vqu2+3awcFBMnuQeEIem80mfaMxNF3/O3q+/+2D6Bq64TnwQ0zzdDq14+NjOz4+tpOTkzSSQjgHE60gxZPebDZ2eXlpm83GfvnllxQeenx8tNFoZKPRyKbTqd3e3qYRGZ/viMbkyof4uGUOFJo+QANgvV4vUZBer5c6Hf/b7bYdHR3ZwcFBCl8dHR3ZxcXF60/lijJedy4Cq76Kgt+qTeBdmCZvqsyeJ1BE4KvzJqv4luYRzTSbzWyz2dh0Ok2miAYiLGP2PI1fQzDb7dYGg4E9PT0lLUpwu9PppBeZ07Fms1nqgMqNq+q7zknROoi+fV1ABZjwC/Xgtw5tKggHg4EdHh7a4eFh0oSvDkIqRYfdciYgR2rVEVBCjjZErTNKsVwud+b0waMWi0VqKDikvgBcvU/lsb7C/divxrnI73Q6tdlsZtvtNnE4M0sAQotrYylYeY5GCJihw3AgTky/37fVamX39/dpOJB6IHrgx+9Vi2s5eD6gibglsVDqP/rmPB/lgsfHx9bv9+309NSGw6GdnZ3ZxcWF9ft9Oz4+frsQTXTcf9f1Wo0jojl6vZ4dHh7acDi0k5OTxKc2m00KgRAkViAuFoudRkCDMXFVP1Xjt2aW+I7Kcrm07XabuCGcx8ySJ00e0Ob6rF6vt2PaySuhHzWvy+UyTY5Ay+qYvYas1BHT+lSg6TIGNcVa53i2p6enycPluDfHnguen5/bYDCwi4sLOzo6spOTEzs/P08B/1Jp/NJtJGpE73VFgKQC1AShGbrdrg2HwxToRPOiNQaDgS2Xy/TRyQNqSrfb7Q5/1GcDUNL2np+WxTshcDzSQBtqWEeJOqAmPqj5oxMxceL4+NgODw9tuVza+fl5Sn+9XtvDw4NNJhObz+c2Ho9ts9nYYrHYqVs1jcPhMHnv7XY7OVAakO92u3Z4eJiee3l5mSb4aigKpxFA6ojS8fFxek6/308xVDpMqUPUeFcu/7+J50VFaGPM5/M0botHRS/kWrxLCoiGZHGS0gSA5EHIMzVgDAjp7UoT1FNVKtFut3eebWYpD8zUpgw0qgKftOB/eJQEv1erlZ2enqa45Wazsevra7u/v0+B78VisQNCtBqxyo8fPyZHAfOpVAPQHB8fJy32888/J/OrHjBAVK04HA7T5A6AiYY8ODhIAfs3AWEOWNrQ+rvOa221WonTMa2KhsM8mz1PI6Oye72emf2unYfD4U48kfygaRCd3eHnEpo9Dx8qCJVieJBrPkmb2TU0DHkmX6qNuNfMUseiw3GMMh0cHNjFxYWNRiM7OTmx5XJpo9Eo0Rnqrd/v28XFhV1cXCQQwu/QYJhaNCGd//LyMqXlKUwESMqh4TSt6xKHCdkLhHUR/Sp+qJqFWc4M+s/nc1ssFrZcLu3w8NA+fPiQOBqxOnVi9Hmkx/Wz2SxxNcA7HA6/C7gDMDUfaC6zZ8DiHCCYKXgisUXKst1u0z0AVhsUykEaANjM7OTkxMwsgRmNOB6P7ebmJgW/AQB1A6cjhMRqwrOzM+v1enZ+fp4AqZsC9Ho9Ozo6Ss6TKhD1/smfTl2jI1TtUVQne80nRDST0bkqZ0aBSiObWerR6/U6NQIfM9tZC6JA8PlQ80djcy3feq2fbIuWRCMxpqwawqen5hZN62duEx/1ZswDAHBznnwwfKjrYNQ0EstEE7J0AfMb7Yygk1BVw6mQ12gEKxd1qJq+tvP8oqvsGf3qdPjMVsUHNdMehKQ/m83S7OV+v29XV1fW7XaT50Y86vT01E5OTtI4LaaFRkYj6kwOBQ/aAPAAFjSg9mzuR8spmLXBNISjXq2CEF7GbBxM48XFRRo5UTPMcCHHsASLxSJ5n1wDz9NYHWA8Pz9P+aauac/tdrszIuKnYvn6U6eDj5pnxcurg7AK+bnrmxzzjoCOqChhxlvWLT30fgARhWY0/agMXKtaToGmsTV/jucTiPYmyndMVvzxXJwv1SBaTrPfZxrR2RaLRXIy6KB42nROzqlFIb9+WI48q1bXekHgf+TT16eeexMQQpwh3frAqKJz8Tj97e+FpBM3o+d3Oh27vb21TqeTeM/R0ZFdXl4m3qM9kvTQNhoD1N0VdCYLmkpja2gGyqxBWw9UGkLT1pdP6yiDaha4q/diuY6QD5pusVikYcOzszPr9/v24cMHGw6HyfkA6NSpB40fliM9LUMEJG1jAKn8WpdMvJkm1ApESjyhqvMeqFQAZs3subeqSSAIDGCUZ2kP7ff7O89H4xBrVLNitvu6DNWOZs/8CXMecUONY3qNDAi1gTRWqSEV7Ug4YjwHcB8fH9tgMLCzszM7OjpKIzeEkJQSaEejTnU0RJ+p4Z8oZEU7+HveVBPSCFQ63Kn0YVVAVUBrARhRwPQAsMViYZPJxIbDoU0mk+T5AUg0GV7p4+PjDpA82D0x157tRwuUIqBhAQ3lYHa2akIf+lBqo5MRNDSkZpnYKb/pADgdZ2dnNhwOU36JMjw9/T4TSOkUZWFGuQaivTb2edVvXXdE+hprffUQjSfWDDs1Fe1hufPKr7QgVDCjCQzf9Xq9ZLoJheDEEH+LnklFEfLRYTTu8Y2k/3FwVHMBXN+R/HQvNfOYNcg8DgqdRrWu0oJ2u53CMMT71IvVdDUERafSEAvPoEyMquhoUuQTaFTALL87R5W8KFgd8Tzv+VaFasy+B4bXVma7vFFn1BAgNvs9xqgBY52iPxqNUjra8IQ11FR6go121fySR+J3nv+oh6nmHC2q1ygg6HTQBxrex0WxQq1WK+WZPPAcJs7q5Aflo+RN24dhUBxAHbJTK6GdI6JmJY6ryt7B6jrnI5ImQNTexb2eR8H3+N1ut9NEA3oy6evaYcj75eWlHR0d7Zg5BaHZ88Id73XTITwJb7Wep7t7x6bT6aQhL3VW0Jx+aYN2GAUAXJP8tdvtNPmBNHWcmU5LiMfHI82e46JobpxDdcR4vteQ/PZplkpjc+yBUQK86LoqLVqXJg3OnoKYaO2t2oN1ZgxjxZ54MyY7n893Kp31IHQCeBDP0vxq/fgQh29s1Uh6rQLXay7VqAAWh0LNoI4ARQ5GVMe+fTVuqB91wDjvy6LXlkjj3fvJsK88fywHvFyoJrrGcxD1vAgFzOfznTx4Qk2cTIf7mFSgphCTxTR8GpYFSjQeTpk+QzWDjytqLNDMkrmDY9KYdCwNprP+Rmf7wMW73e6OE8cUNj6aZ9Xe3vlRTsjzNX+DwSA5ezrxWOtaQWhmqQ5LfYZGU7l8cLZUc71UtHeaxeDnt2rCdrud4muMKhBsVi2jjoc6CtoZPEXIdbA6eqLAibxJjeVpufQDaNT8aYfV69RxUPPpvVqtC7UkkSb0IbCcxnt1TajbZRCn0qEpLYBmwJsDFV8ZXKfRfBXlR/rxjWBmKWTBtHM8PsrQbrdTqOP09DStDlMvlrISh8xRETqohlSoC+oGiabtK9dFw5k9j2n7ZysX9qMWGrfUYLWa96jOdRzbh2x0ppDWcQ6AqpFLpNEEhohX5PhFlEkFol6jGsb3Lm/2fMxOibEKC26YPo+JwXSZPYNagYaG4RrlRWrSqBOu8Z2sRAt4beQ/EQC1TtQ8R5xOAa3lVTOt5lidDg0JRTFDLWMdraqTxo4JvEm9Lq1ELYAOF/Fbo/f6zf1wNh06IwwBp/NANNv1lrvdrp2fn9vR0VGaM6cNi9eoIOIboEKwdS8anevn875Tqf+vs3hyjzbiGuqK6ykvGlsXPXGvcjcfS4Tfaj7MLD2TeiRd8q7Dd364UssWWSjfKcHFm4GQh/JA3/M1Fqa8SsMPpKMTDiggDQAh1+np7XY7zVyOph7RUACVYSxmlSBoF4a1iLlpGQCrBqcJ4XhSr3WTKlXyp/WCNvd1pY4T16CN6Vxmu/v2aH7RZr6NtFyADfHa13ecSPtWiSqbNxsxUfWtBVMNxkxeYm9cR+94enramfSphUMjsFyQmdWAEG9RV39xn5p0nss0JtZRUCn6qgga+/z8PC3QQZso5/Rl1riklkG1nR9f9g2rnWez2SQA1nFPgvEafuLbUwkdDuS4Oj+cg6LQRmqeKV/O5Oo35cKrfnVO6B9G5tSlZzAdMHJezTdDUog2HENheLHqUBC6UM3hPTazZw2N+WTxDQ2xXC7T6AN5Pz09tbOzswQyXU6gHCvHzbQjqWlDe3I/dITwEM9Sk6vA9592+3nNspplz4u1E5k9xyZzc/wAoXZmnTyssU1wEGFBO6PGYuuk0SaZiGbCe7VoLExoq9VKQ0csl1QPzvNIQEhvUo+NYTY+3KtxMs0Pz9D9bRhqY/Wedyo0P6qZiL3RoZSX+uCtOjAKCOXEOT7tiT+aWemL52bK68hn5M2iKbXdvFnWMikdyWlDTV9FLUidNA5WK6H3JhqNyGJoMj+fz9PYLtoMvqdOAaaXuW0+bMDsYV3sBABHo9FOwFanP+laZN1FVYHqtYfuM6MBbZ2yj7ZQMCMehACABe181DHxowxqTus4mU4R896yB5GPS+ozUAZqbVQDIlWcUalBiez9Rif9pjBqWqh8KgEz6kHoPVQNDWiFeI5FZfpnqkZRvqM7OiiYGK7TkAYftLiGSrgHc0OP92EMDfoCjKgetQEjsPnr/DBZrm20HqIQkn+ejzdq/XlRTe//o+3fxDHBoYgKjsOhi8FbrVZa/6BrcVnhf3JyYp1OZ2cNsNkzr1JOiGPCN2ZVPVzW4fpAMW9jYg2LOkdouMViYff39ztrddH8zMWjzKrB8OJ1Sle7/fv6EXap0mldZrYznGa2O1ZM46EB/YwhDd8gGvryCkA9cE0/msDqx7TV20XUamE9FGhcq2GaEmmkCc3i3RVUE6qn5ZdS+nCFftCKkSbQSlbtt1qt0vpiCg1IeZ4HqgIe7qRlQeuiJXxFUyacCtKhATqdTvJgdfKvD63wnfOEVdQ5QpQjek0Y1W8kngtrnny+lLJoXr2m1I766t6x9z69JgQUvK6V65bL5Q7BRfNxTMGDZsIEUvjpdLpTcCaz+jCD2XNAlmvn83naHdV3CMwqIO73+zYej9PCIQ1V6IQD9eS1UfQZTPHXBVvEOpVnaeP5ILhySRrXb8yuHjD0wAecvUTm25twFd8JPLg4rutL2FmjRPbaLjjqrdoDAB5AgXzrUkO0hYJNOSLXUGga+enpKc2qpqJV/LKD+XyezDBpK2/bbp83XuK8zhphgqkft1bv2IuaJV3U7mN7nkvlIg7KJ3XeH8ci776KW+Z4vdaNl0gLR+XWcNyrm2M/3EbGdEwZz+/6+jppFfVmB4OBjcfjHb5H4bjOx7sAiToY7MWiwFWtHJHjqLezGBywKX/lHsDGRFU6lF5Lw+nMHb9mQzuMdih1ttRJUOF6La8uG1Cao/Wmi8S8RJwP6+BDO7R/5PiYPS/DwC9AI5ZKY07oe4z2aOVGulhc90BhhRzaEkeC6fga72IyKVwO88YiHjPbaRS9Rh0dwjlcD7A4Tj50dENNooKO0E8US9Sgut8Jy4OEPCpH1rr2mks5oA/DRDxbtaZvL+2wyp+91+u9Xw9YXx7A92Zjx7kEI5deM4IZ7na7ict1u12bzWYJoBqPUv4D+HTKGOlrT9MxS62Ap6entOsVXqzZcyCV0RiG9nQAn3tYSKTrUTSMBF/UeKZOf9JQjTac55eqJT039CZbr/FaTrkpHTXnmPj6Ij9qerWDKOjRmmaWQl/QJNqoVPb2jpU7+DFK9Ro1RthqtWwymezECWl8RFU6O6SStg8B6DMxQX7URDfz5jnkF9CxMSedAkB1u920mRAgJH3lhzq86KdA6QwZs+fAOflTwFIe3U1Myx6VT2lHu93+bts8bTMNAeEU6ozyKA6pHYnftCUdnzcv0HZRGapk7/eY1KnaKpWvA+VoSQTnRSuIZynf00bx+UHzwOWYzMAG52yPwd585+fnCYSYaBrVmz8VP7Kgy0IjE8k9+q3jwBoTpLxaJi2jmmcAAi+DrrD0wXNF1WR+Z1ufZ09T1HmkHfAF1MF7E3MckeaclxxpJ7+3NN9KyqP4lIr2etL1wVyde8g4NC94+fDhQ9pjeTAY2M8//7yz4zzP32w2KcykvAoO550VP3NbvV/qTj1ybVA0IRTG36/gV2/Zb7iEkwMI2VgKrorm4lp1TDDdqmXJJ5ZEw0woDd3ASdPTYyXy6u8xQbTi+I5CAhrh57qc1lUPjWvhWsrLMPd8dFpXr9dLC8WZb8gcRM+3zHY3WHp6etox6drR0NCeT3lP03dQbVS9Rr+1s/Ec1WAIvHu5XKYdzsi31qlaFB261Hbx8ws1EqKx0Cgy4b3uOtnbMVEToNd48q38gkr3JNvMdky2pucbWsMmOgEWbsnEV51VzVAhgPvDH/6QNCPTxtrt5+1F0DKYGrPnLYEZjoNG+B2vKC/g0j1kKCeNuNls0kQPM9uZBUNdmVkaGeLcZrNJG6z7qXFowsVikV57oTPUlXsuFouddGg/LImP7XpRfqvt5TtynbzKdsHelY/MUJVEQI6eozyFUAnhEEDIZt5sA8J8QpwLjuk+fqTrOajmKTI5Onyn2gYgakNwrY9jaqhG44Bm34/FKu8iFKWjS2aW+KDGUrE0fFNGzDZrq309e83uwzcRCCOOXid7Te/3DaXnPIHWDOl9FNSbCq0EGoYerLvSo0HgVHixhFS4H7Pb7/cTMD9+/JjAyPpYJfh4tRFtQLs/PT3ZdDrdGR8GnJ4PDgYD+/Dhww7/VdBsNpvUkbhns9mk19Wye78Oh9FhiKVyH1suq2Ona7M1hAMI1Zslf/rOZyZh6Kx47tEOxTOVJpXIm3FClapeQc/05BuCzmTWTqeTdmfFmeBa1pRoSAWNg3Oi2+UyywVTGpkO1Rhm3091otL1uAbJdSSHzS11xg2iM1uUxnAOU4zp1CFI0lZNihZUsw9YdIKJgtB3nna7nQYT6HTKQyNtDkiXy2Vqk1cHYaTNonP6raTem2gdR1W+ohM84SXK6Yjr6fa4Gg7xG4PrBpOADjM2mUzSbwLgNIp3ghCN8Sn/BYTamAxPsuxUNSHpe48VM8kbrGazmU2n06Th4G4KQgDAK23ZNQLHARACvvl8nsCaCy6rxlZ+rOCLohdMcyMOXCJ7D9tFxyMQetOqWo4QAIDTiQPwun6/n3ZjBUS8cOfw8NDOz89TBamXjZesQ3SAgEodjUZpFEeH+6howKWNwu8oFqiePh2AmeJ0KA2pKH9SQo/2A1AKQMyvmkRCSovFwsbjcXoNm3YogsrQCEY39GXgmhfyR/qYWg9Y2lSdVBa9vbomVBOiYFNnJAKomho8WmJs6oXpBAYCpIRUaExGLYgB6nZpgINvgEQl6ZR+RhJGo9HOG0R92ahkFR2zVhPqIwB+koMHNQ2tHUU1h+fgqn1Wq1Wansb3aDRKgJ3P5wmE6kFjpnWSgY6YmH0fx/VxQKIT5DFyPjWcUyJ7bYiUC6eoeHUNT8As8e3fCqRBXMInOCS8EoGPPt+PoXp+htlVkI3H4534GGBQM0s5NUbGHtpocZ6hZdDOg/Oy3T7vEUijYrZ17NwDVeN5OCO8z+T29tYeHx/t+vraxuPxzkRfhtN4UynhGB/y8uKdTrVmyvc8zeL7zUCoFaQP9BmOPGDGZHVHeV1RpxxOp/erCQUIaC0fLKZHew3S7XZ3iLhShfF4vBOa0HFS/TazHU3lPXvlj+pBE6vDDGMJGK6EP5FfDb/gCbM8AR4HR+RN8nd3d7ZYLOzh4SGZYUDowzhVQ2o+1KbHcxpPr6GOdLTnTUBIJdP46gH6DJEpwiTstk+owhNXBaFuS6YaRpdr6kA9vTzSIjqhlms4N5lM0qwe7ygpbdAhOgSzCwDVPGP6ZrOZPTw87NAQ6IdyVdIZj8e2Xj+/kPvm5sbu7+8TEEejkd3e3tp0OrXffvstXbNardI9yt2U32rdaDvlvv0xHxngOGVXDvxmIyYaRPWZ0Ux7nqjmSQHm40hRJWiwlko1sx3Pz2s57lfPnEaGhHOelxx6M4zmQvtzXmff+HyqJ6njzXoeLan5UseBjqYTODREA7Cn0+mOc6GTFuiknkd6LaedNdJaOQB6CubbUJVVqTSOE1ZlVo+h1eB9eLNoFp9JepPuRqBTkdSEaWjDE3fywX+mGAFcNZu6vkX5qGouRmB++umnnbdIcR15x1NVShLNpOZaHKv1+vdXydJR6Sys1cH0TqdTu76+ts+fP9tsNrOrqytbLBZpvTWdUevE06JIedS1Z6Qo9BmU34/45EJckbxKsDpXMCW0fmaJv54G8lpExa8X1md7UGtsS8dHo2vUFKvp19EDXmBjZjtaU8tLufzH8zDVhABU5/j5j/JD3kSPBiQ+6Bs9slI5jhYBLmrHKO1cmjmnJ5LGL1j0vcw/DED5/8SWbm5udnqOn8lLxevQlI/Ma9q53qnrmf1KOwDs43rK6/hmFZ7uKcNYNHln+lhE/HWyhXJCTDoUBa1N+IWNz0ejkV1fX9vd3Z3d3NzY9fW1zedzu7293dHuPrSkv73DqBJRKL2/TnMqHiiP+gsl0nhDpKrelhPVSnAttKPyJMIP8CLlfapRAAyFjswzJpjna579aIOCEI0EbwWIs9nsu/eiKMBardaOQwClUHArJ/bA16AwM5RV+41GIxuPxzaZTL5bVmsWh8v8OQWL54QegFUYyAFR69DnqUoagdCb0ojkRr2JITJfCBpAr1dizbf3wKsK6M2g5pffkXnUa/C64ajM1jF7ftk2Y8G68AmNRqcjCM+4tWpYBeJ2u02xPiYszOdzu7+/t9FoZDc3N3Z1dWXj8ThNalAP1GszX1eeG0b1GNVnFT/09erbPGqznDQaMSntLb6n4bWpM8A5zztypiVKl+9IO+twmp7zdCI6jvYFhIvFIoEQR2u9XtvR0dFO3hRUXMv1LKYHOBohAIRXV1dJCz4+PtrDw4Pd398nEM7n8zTerVYhMptV7RJpxMix1ONRu0eOD/ImIKwSrYCcqtbhnwhoXpv6NMzy29VGkgMyzyrRGmbPXvbj42OaucxKQaZBHR4epmA0Wg7+xzxHRnjUi9cOiobDaszncxuNRnZ/f7/zZqYoBpdzGqrqpc5J0d8RyMziF1H6icwl8irzCX3mEVXL+m1m35lEChLxHP5HcUp/rWqjHElHA2lIw3/jueJQoYGYGrbdbm04HNpyuUzv+uDZOC7Hx8f2yy+/7HQe5bdMPJhMJjadTtOIyM3NjU0mE/vtt9/s6uoqBaz9W0l9veXaTOugzvxGGtCD0HNIXVlImCk3RS6Ssrk2NVKH+gi0uXujXldlCvR81T05LzqXV38NoNFZyzoFTLfo1RVqdCoftDfbnS+IhsXkjsfjNFKi67ir6q6O+zXRTlH6dee8pXt1c+zNaZVUASDyxHwv8/ym6nneRPt09H7+Rz20jiYw7DeZTOz+/t7MnrWjzhnUBVbtdtum02naOBQQmlnieNPpNHG/L1++2GQysV9//dUmk4nd3t6mOYV+O7uq+vH1FwEw4vc5RyOqK68V0e6MWzfhhMWasE615ngDma67T//741X8JTIXpVKaXypa59jp8Jo6CTourGDwkQC0IDwQU8yw3Gw2S5NbVQFU5dvn+a0k1y7k0dOcOmkcrM5VRpV508bwweYqUa2ozynx2Hw6VZpBQew1oh8VYcZKp9Oxx8fHnc2e0IQaskFLnp6empmlWS8EoMfjsY3H4xSIfnh4sE+fPiUNiWap4sW+PqM6iDTfSyTSooxKEZ8tfV6xJlTnopTf6b36/RKpIs5VeajjmHVEnPzrcgA0VDSJFW+R4xr4JhANByQko5MTMMG6yZR2kCaav2n913HBXF0qRprIqy10irieSk5DNTkWhRdy3MhrYJ+XiHdGvVvNcKv1/EYmpnsRuGbp6NPTU1oHg5bcbDb29etXW6/X9vXr1xSInk6ndnt7a9fX13Z7e2ufP39OIGR0qU6DeW5WUp+lUvpsroViNO0kb77aLmcycv+bpKWSA5v/nwNaLj8eCGglzC2mmKUGDEPqbmO8XWCxWNjd3V0admN8WIGIVvRrepUq5DSRXhOVJSeRFYnqqi49gIgVKJXGnJDMNEF6yXVN+F3JdXXPiEIJ+u3jmtzPEoWTk5O0kRKhGJwWPzqkoRycmoeHB7u7u7Pb29s0LowzQhp1ZS6hHrnjr+XY5DzsN9GEEbGPek5pLyw1saV5KpEqLajf9GgFI54tK8nOzs7sp59+SktOzSzNJwSE6kkDROKMt7e39vXrV7u9vbWbm5sERH3Pio485PK+Twd/TeXBddEzXh2EOYdAQbSPBxZxszrZx8GpylcERASA6T6DmGC4oG6kzoRX9RAB32w2s7u7O5tMJjYajdJoCcN06oSUduKqzlxSB1G7Vt2Tc+C8ln0Tc8wEBr+SqoqnVUmTUEtd+hGna9oZ1Akxe56Sj1cL2Nhg6ezszC4uLnbmB15eXtrh4aGt1+u0I1a73U7jwOPx2P71r3/Z7e1tmhVzfX2dJi4w6ZYG9Jo6p3G0DF45VHHIqvry90T/NQrgVyf61Y9Vstf0/pcC8P+H7KOVPakHhGhAtiDhNba6MpDwDNO5ADTgQuMxToxjwgxpjQW+xKP1UgLAqnv88ab+QInszQn1u8m9daJm/TXiWiV5UtDg3QEw9q+5vLy04+Nj++tf/2r/8z//k7hgq/X8jmWG63q9XgIeK+b+8Y9/2Hg8tv/+979JA6ozoqMuddouV2Zvmuvqq8rLrvr42TMc0zjymyx08qDIcZKSe/W+t5Rc6KUqnAMQCDSz8wMeMR/dVlinL/FhjJf1IePxOK0NZnhOQzF1AwB1nTJqBz1WZ26j37lnVEmOBlRJ48XvPq7me2ZdZeYkd76uMCXP83nWY3jChERYC3x+fm7D4dD+8pe/2NnZmX38+NFOT0/t8PDQxuPxzpoT5tE9PDzYeDxO6d/f39v19XXifaPRyL58+ZJA6MMxUQRC8+qPRR5zSd3ouci5jK7NcU/+6zYiTWlF4+n9VWCpenCTaL6vmNL0qq6P7lUgakiE2dDD4dA+fvxoFxcXacknw3atVivNK6ReWNeMJiUoHfFBff9fZCVyAFO68toSgY1n6n9/XvNFGm9ijnOmLcr8PtJU25XyUs1bjhKgyTTscn5+bkdHR2lTdfjeer1OIRq4oG7dweZEDw8Pdnt7a58+fbKrqyv79ddf044MfoWc9yyjGKD/vU9Yq0qLNrk/So/vyOrUyd4jJj4zTTRdJHXmuNQs11UU10SxQALRzHoBfLq5OmPBgJD9DgHier22b9++2W+//Waj0ci+fv1qNzc39vnz57TFmy4FUKLvy1JaP9Hv6PoIiFXg8sdymlL/vykIkTpeUnXfvtfkvMaqe0t6PhUGD2SniOPjY7u4uEj7ILKsFPDpBNWnpye7v79PQ3GLxcL+85//2KdPn+z+/t6urq7Snjd43pEjE+W7tM5eGtqpMsNVoOQan9+6tvLSmBN60xFpyFJnpe5Z+jtXcDUF/v6c+SUt+Bgc8Pj42M7Ozuzs7Mz+9Kc/pdeN8XqJ5XJp5+fn9sc//jFt1zGdTu3z5882mUzs06dPKQzz9etXu7+/t2/fvu3MwvH7EEb5yuW/rn5KnbSonerAFh2L6AIYaQLEvWbR+M2RvEn2D9+Hf+i9XhNGBcyBrsqL1K2KWT/sP/rSR3ZaIADNEBwg/PbtW1qojvers3BKG8Y3cl0nbtLJc23TxKJ58OWsTKk0BqHf+IZMVHHDSOpMr782Uvm5+0uFWB/cj9EQNmaPdv7fbrdpFvQ///lPu7+/t7///e/J+2XdMBMWzL5/Pxz5zGlqL1X1WqIt6wBWCswc+KJOrkO7ddJ4GxAeWqV1Sis3kqr7S4EYpZk7rrOfGZpjnJjjjJszfX2xWNj9/b3d3t6mXRJ0EoKGXlQDRvmIzOq+0rSeq45X1XFkhiMpLUsxCP1718y+f+l2ScaaSI5zlFaQX4TNb5Zcsk0dbwX4+PGj/fnPf7bhcGjn5+fJRLdaLbu6urLpdJo2Jrq7u7N///vfNp/P0wQEv+Sx1XqeiZ3T7lUdNlenkWe/r3NSV4cqOdpVEueskhe9VuwlvbZKcqCOAOVJvm6z4QHJNzNj0Ha6MTtmmF20GCmaz+d2d3dn19fX9uXLlzQplTXD0eKeEsqxTz3Wgbc0zVKN9tay9yskPEharWdPuUorRr0n6uU5LagSTfpUUKqXpnu/EN/jNRO8B48NMVutVnolF2b469ev9u3bt/RhoRIxP+39yvf876gOqrhXncfrr20C7pynXHrPa5his1d4mU7kKSNVDkZVA/hzUWH9UkwfOtLf8Do8XWJ/aEIAyCtjW61WWmwOx2O/6Lu7O7u7u0sjJDSKvt2AOvJb0tV1tlyQujR4XQVmzZdP+yVaMMp/04B1o1k0OT4WbYjoM+LDLCU9L6ow/a0AJG1+6+6rOBhsfs6bAzDLDM2xWElBxGsmmJygi9KZA+jrRAfx+Y7qp67cEWj8NbljPn3fFnxHQKzqGDkN7OVNOGE0h8yf1xfSRCapzsRWBUc1Pd3pVfOku6bqel8cDOb6MSGV+3gTKDNjABH7SbNvNL95SY1/s5HSEh++8lQlqosIOKWRhggIEXWifLm0ckqirtOURAJy0giEaBbPgcgIQI22L9N0EJ+GN7F8+6ni5EN/44ViFjXEggbUF/hoGfTVEOSduX7MftY9snVGtebJp6F1E21opHUQaarouhzFibaLq7I6VUCMOGzOAvKh/VEGbzK9n0bTF9zkFrP4RojMJQXyzoMCj90MdN8/3xi6KF01po6CqCbU/aJJA96HxmLXVN0dn9fJ8lJH5YHUg751ScM1+oqLnGZjVnfkLETXa/3xO3IWNR3qmpnc0TUlAFSro2DzHbNUGnNC/Y8osKocDg9c/a9vblJQqolVM0zv0zFZ1Qi6MZGaZh279ZpXG1E3IdI5f9qZ/BgpC9/1HSi+sV9LcqY0x6P1ntK8lHSCXFpNVtu1tm8V7HuXdymUV9kk813e5SXyDsJ3+eHyDsJ3+eHyDsJ3+eHyDsJ3+eHyDsJ3+eHyDsJ3+eHyDsJ3+eHyDsJ3+eHyv2jykQ8/2HEzAAAAAElFTkSuQmCC\n", "text/plain": ["<Figure size 200x200 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["Epoch 10: 100%|██████████| 63/63 [00:47<00:00,  1.33it/s, loss=0.0131]\n", "Epoch 11: 100%|██████████| 63/63 [00:52<00:00,  1.20it/s, loss=0.0138]\n", "Epoch 12: 100%|██████████| 63/63 [00:52<00:00,  1.21it/s, loss=0.0131]\n", "Epoch 13: 100%|███████████| 63/63 [00:53<00:00,  1.19it/s, loss=0.013]\n", "Epoch 14: 100%|██████████| 63/63 [00:53<00:00,  1.17it/s, loss=0.0127]\n", "100%|████████████████████████████████████████████████████████████████████████| 1000/1000 [00:09<00:00, 103.25it/s]\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 200x200 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["Epoch 15: 100%|██████████| 63/63 [00:51<00:00,  1.22it/s, loss=0.0122]\n", "Epoch 16: 100%|██████████| 63/63 [00:52<00:00,  1.20it/s, loss=0.0122]\n", "Epoch 17: 100%|██████████| 63/63 [00:52<00:00,  1.21it/s, loss=0.0126]\n", "Epoch 18: 100%|██████████| 63/63 [00:54<00:00,  1.15it/s, loss=0.0119]\n", "Epoch 19: 100%|██████████| 63/63 [00:51<00:00,  1.22it/s, loss=0.0115]\n", "100%|████████████████████████████████████████████████████████████████████████| 1000/1000 [00:09<00:00, 103.55it/s]\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 200x200 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["Epoch 20: 100%|██████████| 63/63 [00:50<00:00,  1.24it/s, loss=0.0119]\n", "Epoch 21: 100%|██████████| 63/63 [00:53<00:00,  1.17it/s, loss=0.0117]\n", "Epoch 22: 100%|██████████| 63/63 [00:52<00:00,  1.20it/s, loss=0.0114]\n", "Epoch 23: 100%|███████████| 63/63 [00:50<00:00,  1.25it/s, loss=0.011]\n", "Epoch 24: 100%|██████████| 63/63 [00:55<00:00,  1.14it/s, loss=0.0119]\n", "100%|████████████████████████████████████████████████████████████████████████| 1000/1000 [00:09<00:00, 106.39it/s]\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 200x200 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["Epoch 25: 100%|███████████| 63/63 [00:48<00:00,  1.29it/s, loss=0.011]\n", "Epoch 26: 100%|██████████| 63/63 [00:54<00:00,  1.16it/s, loss=0.0114]\n", "Epoch 27: 100%|██████████| 63/63 [00:50<00:00,  1.26it/s, loss=0.0116]\n", "Epoch 28: 100%|██████████| 63/63 [00:54<00:00,  1.15it/s, loss=0.0104]\n", "Epoch 29: 100%|██████████| 63/63 [00:50<00:00,  1.24it/s, loss=0.0109]\n", "100%|████████████████████████████████████████████████████████████████████████| 1000/1000 [00:09<00:00, 103.31it/s]\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 200x200 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["Epoch 30: 100%|██████████| 63/63 [00:52<00:00,  1.21it/s, loss=0.0112]\n", "Epoch 31: 100%|██████████| 63/63 [00:50<00:00,  1.24it/s, loss=0.0101]\n", "Epoch 32: 100%|██████████| 63/63 [00:52<00:00,  1.21it/s, loss=0.0107]\n", "Epoch 33: 100%|██████████| 63/63 [00:51<00:00,  1.22it/s, loss=0.0107]\n", "Epoch 34: 100%|███████████| 63/63 [00:54<00:00,  1.16it/s, loss=0.011]\n", "100%|████████████████████████████████████████████████████████████████████████| 1000/1000 [00:09<00:00, 105.19it/s]\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 200x200 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["Epoch 35: 100%|███████████| 63/63 [00:49<00:00,  1.28it/s, loss=0.011]\n", "Epoch 36: 100%|██████████| 63/63 [00:52<00:00,  1.19it/s, loss=0.0102]\n", "Epoch 37: 100%|██████████| 63/63 [00:50<00:00,  1.24it/s, loss=0.0105]\n", "Epoch 38: 100%|█████████| 63/63 [00:54<00:00,  1.15it/s, loss=0.00986]\n", "Epoch 39: 100%|██████████| 63/63 [00:52<00:00,  1.19it/s, loss=0.0104]\n", "100%|████████████████████████████████████████████████████████████████████████| 1000/1000 [00:09<00:00, 104.85it/s]\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 200x200 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["Epoch 40: 100%|██████████| 63/63 [00:56<00:00,  1.11it/s, loss=0.0111]\n", "Epoch 41: 100%|██████████| 63/63 [01:03<00:00,  1.01s/it, loss=0.0104]\n", "Epoch 42: 100%|██████████| 63/63 [01:07<00:00,  1.07s/it, loss=0.0105]\n", "Epoch 43: 100%|██████████| 63/63 [01:03<00:00,  1.01s/it, loss=0.0104]\n", "Epoch 44: 100%|██████████| 63/63 [01:08<00:00,  1.09s/it, loss=0.0101]\n", "100%|████████████████████████████████████████████████████████████████████████| 1000/1000 [00:09<00:00, 101.05it/s]\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 200x200 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["Epoch 45: 100%|█████████| 63/63 [02:18<00:00,  2.20s/it, loss=0.00975]\n", "Epoch 46: 100%|█████████| 63/63 [03:12<00:00,  3.05s/it, loss=0.00976]\n", "Epoch 47: 100%|█████████| 63/63 [03:13<00:00,  3.08s/it, loss=0.00931]\n", "Epoch 48: 100%|█████████| 63/63 [03:13<00:00,  3.08s/it, loss=0.00965]\n", "Epoch 49: 100%|█████████| 63/63 [03:13<00:00,  3.08s/it, loss=0.00982]\n", "100%|█████████████████████████████████████████████████████████████████████████| 1000/1000 [00:24<00:00, 40.09it/s]\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 200x200 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["train completed, total time: 3457.8086228370667.\n"]}], "source": ["use_pretrained = False\n", "\n", "if use_pretrained:\n", "    model = torch.hub.load(\"marksgraham/pretrained_generative_models:v0.2\", model=\"ddpm_2d\", verbose=True).to(device)\n", "else:\n", "    n_epochs = 50\n", "    val_interval = 5\n", "    epoch_loss_list = []\n", "    val_epoch_loss_list = []\n", "\n", "    scaler = GradScaler()\n", "    total_start = time.time()\n", "    for epoch in range(n_epochs):\n", "        model.train()\n", "        epoch_loss = 0\n", "        progress_bar = tqdm(enumerate(train_loader), total=len(train_loader), ncols=70)\n", "        progress_bar.set_description(f\"Epoch {epoch}\")\n", "        for step, batch in progress_bar:\n", "            images = batch[\"image\"].to(device)\n", "            optimizer.zero_grad(set_to_none=True)\n", "\n", "            with autocast(enabled=True):\n", "                # Generate random noise\n", "                noise = torch.randn_like(images).to(device)\n", "\n", "                # Create timesteps\n", "                timesteps = torch.randint(\n", "                    0, inferer.scheduler.num_train_timesteps, (images.shape[0],), device=images.device\n", "                ).long()\n", "\n", "                # Get model prediction\n", "                noise_pred = inferer(inputs=images, diffusion_model=model, noise=noise, timesteps=timesteps)\n", "\n", "                loss = F.mse_loss(noise_pred.float(), noise.float())\n", "\n", "            scaler.scale(loss).backward()\n", "            scaler.step(optimizer)\n", "            scaler.update()\n", "\n", "            epoch_loss += loss.item()\n", "\n", "            progress_bar.set_postfix({\"loss\": epoch_loss / (step + 1)})\n", "        epoch_loss_list.append(epoch_loss / (step + 1))\n", "\n", "        if (epoch + 1) % val_interval == 0:\n", "            model.eval()\n", "            val_epoch_loss = 0\n", "            for step, batch in enumerate(val_loader):\n", "                images = batch[\"image\"].to(device)\n", "                with torch.no_grad():\n", "                    with autocast(enabled=True):\n", "                        noise = torch.randn_like(images).to(device)\n", "                        timesteps = torch.randint(\n", "                            0, inferer.scheduler.num_train_timesteps, (images.shape[0],), device=images.device\n", "                        ).long()\n", "                        noise_pred = inferer(inputs=images, diffusion_model=model, noise=noise, timesteps=timesteps)\n", "                        val_loss = F.mse_loss(noise_pred.float(), noise.float())\n", "\n", "                val_epoch_loss += val_loss.item()\n", "                progress_bar.set_postfix({\"val_loss\": val_epoch_loss / (step + 1)})\n", "            val_epoch_loss_list.append(val_epoch_loss / (step + 1))\n", "\n", "            # Sampling image during training\n", "            noise = torch.randn((1, 1, 64, 64))\n", "            noise = noise.to(device)\n", "            scheduler.set_timesteps(num_inference_steps=1000)\n", "            with autocast(enabled=True):\n", "                image = inferer.sample(input_noise=noise, diffusion_model=model, scheduler=scheduler)\n", "\n", "            plt.figure(figsize=(2, 2))\n", "            plt.imshow(image[0, 0].cpu(), vmin=0, vmax=1, cmap=\"gray\")\n", "            plt.tight_layout()\n", "            plt.axis(\"off\")\n", "            plt.show()\n", "\n", "    total_time = time.time() - total_start\n", "    print(f\"train completed, total time: {total_time}.\")"]}, {"cell_type": "markdown", "id": "057f0d69", "metadata": {}, "source": ["### Get masked image for inpainting"]}, {"cell_type": "code", "execution_count": 13, "id": "2cdcda81", "metadata": {"tags": []}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAgMAAAC8CAYAAADl2K3eAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjYuMiwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8o6BhiAAAACXBIWXMAAA9hAAAPYQGoP6dpAABzaElEQVR4nO29ebRmVXkm/nzzfIe6VBVzlQQXCZFlBFcIAjI40JEStRsMojQYY7ATsekV6bYxiCJh0MaGEFE62jgBzYIYk0ijkaScAumVTou4RJZVUFVS46260zefbzq/P+r37HrPvvuc75xvuLeK2s9ad917z7DP3vvs8+x3v9OOua7rwsLCwsLCwuKoRXy1K2BhYWFhYWGxurDCgIWFhYWFxVEOKwxYWFhYWFgc5bDCgIWFhYWFxVEOKwxYWFhYWFgc5bDCgIWFhYWFxVEOKwxYWFhYWFgc5bDCgIWFhYWFxVEOKwxYWFhYWFgc5TiihIFPfvKTiMViA937la98BbFYDNu3bx9tpQS2b9+OWCyGr3zlK4HXff/730csFsP3v//9sdXFwmKc4Bh+/PHHV7sqFocpVmqMbNy4Eddee23f62KxGD75yU+OtS5HMlZEGPj5z3+O973vfTjhhBOQyWRw/PHH473vfS9+/vOfr8TjLSxecaBwG4vF8OMf/3jZedd1cdJJJyEWi2HTpk2rUEOL1YQdHxZRMXZh4Jvf/CbOPPNM/MM//APe//734/7778cHPvABbN68GWeeeSb++q//OnRZf/qnf4pGozFQPa6++mo0Gg1s2LBhoPtHiTe+8Y1oNBp44xvfuNpVsTjCkc1m8fDDDy87/oMf/AA7d+5EJpNZhVpZHC6w4+MQGo0G/vRP/3S1q3HYYqzCwIsvvoirr74ap5xyCp577jncdttt+MAHPoBPf/rTeO6553DKKafg6quvxksvvRRYTq1WAwAkk0lks9mB6pJIJJDNZgc2M4wS8Xgc2WwW8fgRZaWxOAzxtre9DY899hg6nY7n+MMPP4yzzjoLxx577CrVzOJwgB0fh5DNZpFMJle7Goctxjobffazn0W9Xsf/+B//A2vXrvWcO+aYY/DAAw+gVqvhM5/5jDpOv4Dnn38eV111Faanp3Heeed5zkk0Gg185CMfwTHHHINSqYTLLrsMu3btWmYfMvkMbNy4EZs2bcKPf/xj/PZv/zay2SxOOeUUfO1rX/M8Y35+Hh/96EdxxhlnoFgsYmJiAr/7u7+Ln/70pwP1i8ln4MILL8RrXvMaPPfcc7jggguQz+dx6qmnKnvbD37wA5x99tnI5XI47bTT8NRTT3nK3LFjB/7oj/4Ip512GnK5HGZmZnDFFVcYfST4jFwuhxNPPBG33XYbHnzwQaNPxZNPPonzzz8fhUIBpVIJl156qTXvHEZ4z3veg7m5OXzve99Tx1qtFh5//HFcddVVy67/b//tv+ENb3gDZmZmkMvlcNZZZxltut/73vdw3nnnYWpqCsViEaeddhpuuummwLo4joNNmzZhcnISTz/99PCNsxgaUccHsPJjpNfr4Z577sFv/uZvIpvNYv369bjuuuuwsLDgudd1Xdx222048cQTkc/ncdFFF0XiIn1O4Hzyy1/+Eu973/swOTmJtWvX4uabb4brunj55Zfxjne8AxMTEzj22GNx9913e8prtVr4xCc+gbPOOguTk5MoFAo4//zzsXnz5mXPnpubw9VXX42JiQlMTU3hmmuuwU9/+lOjj9kLL7yAyy+/HGvWrEE2m8XrX/96/O3f/m3odg6KsQoDf/d3f4eNGzfi/PPPN55/4xvfiI0bN+KJJ55Ydu6KK65AvV7H7bffjg9+8IO+z7j22mtx33334W1vexvuuusu5HI5XHrppaHruHXrVlx++eV4y1vegrvvvhvT09O49tprPYPspZdewre+9S1s2rQJn/vc53DjjTfiZz/7GS644ALs3r079LP6YWFhAZs2bcLZZ5+Nz3zmM8hkMrjyyivx6KOP4sorr8Tb3vY23HnnnajVarj88stRqVTUvf/yL/+Cp59+GldeeSX+/M//HB/60IfwD//wD7jwwgtRr9fVdbt27VIf0X/9r/8V/+k//Sc89NBDuPfee5fV5+tf/zouvfRSFItF3HXXXbj55pvx/PPP47zzzhurI6ZFeGzcuBHnnHMOHnnkEXXsySefxNLSEq688spl199777143eteh1tvvRW33347kskkrrjiCs83+POf/xybNm2C4zi49dZbcffdd+Oyyy7DP/3TP/nWo9Fo4O1vfzuefvppPPXUU3jDG94w2oZaDISo4wNY+TFy3XXX4cYbb8S5556Le++9F+9///vx0EMP4ZJLLkG73Vb3f+ITn8DNN9+M1772tfjsZz+LU045BW9961uV5nhQ/N7v/R56vR7uvPNOnH322bjttttwzz334C1veQtOOOEE3HXXXTj11FPx0Y9+FD/84Q/VfeVyGV/60pdw4YUX4q677sInP/lJ7N+/H5dccgmeffZZdV2v18Pb3/52PPLII7jmmmvwZ3/2Z9izZw+uueaaZXX5+c9/jt/5nd/BL37xC3zsYx/D3XffjUKhgHe+852RTOoDwR0TFhcXXQDuO97xjsDrLrvsMheAWy6XXdd13VtuucUF4L7nPe9Zdi3PEf/6r//qAnBvuOEGz3XXXnutC8C95ZZb1LEHH3zQBeBu27ZNHduwYYMLwP3hD3+ojs3OzrqZTMb9kz/5E3Ws2Wy63W7X84xt27a5mUzGvfXWWz3HALgPPvhgYJs3b97sAnA3b96sjl1wwQUuAPfhhx9Wx1544QUXgBuPx91//ud/Vse/+93vLntOvV5f9pxnnnnGBeB+7WtfU8euv/56NxaLuT/5yU/Usbm5OXfNmjWe/qlUKu7U1JT7wQ9+0FPm3r173cnJyWXHLVYWHM//8i//4v7FX/yFWyqV1Bi44oor3Isuush13YNj/NJLL1X36eOk1Wq5r3nNa9yLL75YHfvv//2/uwDc/fv3+z6fY/ixxx5zK5WKe8EFF7jHHHOMZ1xZrB4GHR+uu7Jj5Ec/+pELwH3ooYc8937nO9/xHJ+dnXXT6bR76aWXur1eT1130003uQDca665pm+f6HMC55M//MM/VMc6nY574oknurFYzL3zzjvV8YWFBTeXy3me0+l0XMdxPM9YWFhw169f7/7+7/++OvZXf/VXLgD3nnvuUce63a578cUXL+PxN73pTe4ZZ5zhNptNdazX67lveMMb3Fe/+tV92zgMxqYZ4Kq1VCoFXsfz5XLZc/xDH/pQ32d85zvfAQD80R/9kef49ddfH7qep59+ukdzsXbtWpx22mkeP4ZMJqPs+91uF3Nzc0ot9v/+3/8L/ax+KBaLHmn9tNNOw9TUFH7jN34DZ599tjrOv2Udc7mc+rvdbmNubg6nnnoqpqamPHX8zne+g3POOQe/9Vu/pY6tWbMG733vez11+d73vofFxUW85z3vwYEDB9RPIpHA2WefbVSFWawO3v3ud6PRaODb3/42KpUKvv3tb/uqgOU4WVhYwNLSEs4//3zPGJmamgIA/M3f/A16vV7gs5eWlvDWt74VL7zwAr7//e97xpXF4YEo4wNY2THy2GOPYXJyEm95y1s8PHPWWWehWCwqnnnqqafQarVw/fXXe0zFN9xwQ4SeMOMP/uAP1N+JRAKvf/3r4bouPvCBD3jaq88LiUQC6XQawMHV//z8PDqdDl7/+tcv49xUKuXRcMfjcfzxH/+xpx7z8/P4x3/8R7z73e9GpVJRfTE3N4dLLrkEW7Zswa5du4Zurx/G5k3BSV6qsk3wExpe9apX9X3Gjh07EI/Hl1176qmnhq7nySefvOzY9PS0x17V6/Vw77334v7778e2bdvQ7XbVuZmZmdDP6ocTTzxxmU/E5OQkTjrppGXHAHjq2Gg0cMcdd+DBBx/Erl274LquOre0tKT+3rFjB84555xlz9b7bMuWLQCAiy++2FjXiYmJME2yWAGsXbsWb37zm/Hwww+jXq+j2+3i8ssvN1777W9/G7fddhueffZZOI6jjstx93u/93v40pe+hD/4gz/Axz72MbzpTW/Cv/23/xaXX375MqfXG264Ac1mEz/5yU/wm7/5m+NpoMVQiDI+gJUdI1u2bMHS0hLWrVtnrMvs7CyAg7wFAK9+9auXtW16ejpEL/hDnwMmJyeRzWZxzDHHLDs+NzfnOfbVr34Vd999N1544QWPSUPOSTt27MBxxx2HfD7vuVfn3K1bt8J1Xdx88824+eabjXWdnZ3FCSecEL5xETA2YWBychLHHXccnnvuucDrnnvuOZxwwgnLJhcpnY4TiUTCeFxOprfffjtuvvlm/P7v/z4+/elPY82aNYjH47jhhhv6SsWjqEuYOl5//fV48MEHccMNN+Ccc87B5OQkYrEYrrzyyoHqyHu+/vWvGz2OrVfu4YWrrroKH/zgB7F371787u/+rlq5SfzoRz/CZZddhje+8Y24//77cdxxxyGVSuHBBx/0hJ/lcjn88Ic/xObNm/HEE0/gO9/5Dh599FFcfPHF+Pu//3vPeHzHO96B//W//hfuvPNOfO1rX7MRMocpwowPYOXHSK/Xw7p16/DQQw8Z66M7no8DJn4Nw7nf+MY3cO211+Kd73wnbrzxRqxbtw6JRAJ33HEHXnzxxcj1IOd+9KMfxSWXXGK8JspCNyrGyuibNm3CX/7lX+LHP/6xigiQ+NGPfoTt27fjuuuuG6j8DRs2oNfrYdu2bR6JcevWrQPX2YTHH38cF110Eb785S97ji8uLi6THlcLjz/+OK655hqPx2uz2cTi4qLnug0bNhj7Rz/2a7/2awCAdevW4c1vfvPoK2wxUrzrXe/Cddddh3/+53/Go48+arzmr/7qr5DNZvHd737XE1/+4IMPLrs2Ho/jTW96E970pjfhc5/7HG6//XZ8/OMfx+bNmz3j4Z3vfCfe+ta34tprr0WpVMIXvvCF0TfOYmiEGR/Ayo+RX/u1X8NTTz2Fc889N3AByPwwW7ZswSmnnKKO79+/f1nUwUrh8ccfxymnnIJvfvObHq3JLbfc4rluw4YN2Lx5M+r1ukc7oHMu25VKpVaFc8cqxt94443I5XK47rrrlqlX5ufn8aEPfQj5fB433njjQOVTerr//vs9x++7777BKuyDRCLhkQiBg7aucdpvosJUx/vuu89j0gAO9tkzzzzj8Xadn59fJplfcsklmJiYwO233+5RfxH79+8fXeUthkaxWMQXvvAFfPKTn8Tb3/524zWJRAKxWMwzJrZv345vfetbnuvm5+eX3Us7r1QbE//+3/97/Pmf/zm++MUv4r/8l/8yeCMsxoYw4wNY+THy7ne/G91uF5/+9KeX3dPpdNRi5s1vfjNSqRTuu+8+D8/dc889vm0ZN6g9kPX5P//n/+CZZ57xXMeoiL/8y79Ux3q9Hj7/+c97rlu3bh0uvPBCPPDAA9izZ8+y542bc8eqGXj1q1+Nr371q3jve9+LM844Ax/4wAfwqle9Ctu3b8eXv/xlHDhwAI888ohahUbFWWedhX/37/4d7rnnHszNzeF3fud38IMf/AC//OUvAWBkCYY2bdqEW2+9Fe9///vxhje8AT/72c/w0EMPeSTU1camTZvw9a9/HZOTkzj99NPxzDPP4Kmnnlrm0/Cf//N/xje+8Q285S1vwfXXX49CoYAvfelLOPnkkzE/P6/6bGJiAl/4whdw9dVX48wzz8SVV16JtWvX4le/+hWeeOIJnHvuufiLv/iL1WiqhQ9MoUoSl156KT73uc/h3/ybf4OrrroKs7Oz+PznP49TTz3VY8679dZb8cMf/hCXXnopNmzYgNnZWdx///048cQTjRo+APjwhz+McrmMj3/845icnOwbb26x8ug3PoCVHyMXXHABrrvuOtxxxx149tln8da3vhWpVApbtmzBY489hnvvvReXX3451q5di49+9KO44447sGnTJrztbW/DT37yEzz55JOrpp3dtGkTvvnNb+Jd73oXLr30Umzbtg1f/OIXcfrpp6Nararr3vnOd+K3f/u38Sd/8ifYunUrfv3Xfx1/+7d/qwQqOU99/vOfx3nnnYczzjgDH/zgB3HKKadg3759eOaZZ7Bz586Bc9uEwdgNv1dccQV+/dd/HXfccYcSAGZmZnDRRRfhpptuwmte85qhyv/a176GY489Fo888gj++q//Gm9+85vx6KOP4rTTThs4W6GOm266CbVaDQ8//DAeffRRnHnmmXjiiSfwsY99bCTljwL33nsvEokEHnroITSbTZx77rl46qmnltmeTjrpJGzevBkf+chHcPvtt2Pt2rX44z/+YxQKBXzkIx/x9NlVV12F448/HnfeeSc++9nPwnEcnHDCCTj//PPx/ve/f6WbaDEkLr74Ynz5y1/GnXfeiRtuuAGvetWrcNddd2H79u0eor/sssuwfft2/M//+T9x4MABHHPMMbjgggvwqU99SjmvmnDTTTdhaWlJkb3uLW1x+GM1xsgXv/hFnHXWWXjggQdw0003IZlMYuPGjXjf+96Hc889V9172223IZvN4otf/CI2b96Ms88+G3//938fKa/MKHHttddi7969eOCBB/Dd734Xp59+Or7xjW/gscce8ySUSyQSeOKJJ/Af/+N/xFe/+lXE43G8613vwi233IJzzz3Xw7mnn346/u///b/41Kc+ha985SuYm5vDunXr8LrXvQ6f+MQnxtqemKvrll8BePbZZ/G6170O3/jGN5aFzFmYccMNN+CBBx5AtVr1dZ6xsLCwsBgNvvWtb+Fd73oXfvzjH3uEntXCEe/6a9q46J577kE8HrcbAflA77O5uTl8/etfx3nnnWcFAQsLC4sRQ+fcbreL++67DxMTEzjzzDNXqVZeHPHxYZ/5zGfwr//6r7jooouQTCbx5JNP4sknn8Qf/uEfLovPtziIc845BxdeeCF+4zd+A/v27cOXv/xllMtl39hWCwsLC4vBcf3116PRaOCcc86B4zj45je/iaeffhq33377ioXR98MRbyb43ve+h0996lN4/vnnUa1WcfLJJ+Pqq6/Gxz/+cRsL74ObbroJjz/+OHbu3IlYLIYzzzwTt9xyiw0htLCwsBgDHn74Ydx9993YunUrms0mTj31VPyH//Af8OEPf3i1q6ZwxAsDFhYWFhYWFsPhiPcZsLCwsLCwsBgOVhiwsLCwsLA4ymGFAQsLCwsLi6McoT3s1q9fv+xY2Ax/ulvCqDIDxmIxVbbrup6/+9VD/i3rw7/lsX7l6vf2ej11bSwWQywWU5tz8Bx/0uk08vk8MpkMpqenkUwm0el04LouHMdBs9lEIpFAMpn0tDeZTHrCAHu9nrpPXtvtdtHr9dBqtdT/vF7Ws9vtotVqGeuu90dQX8ljpp/t27cH9qGFhcUhDMO7gD/PDYOovCvPmepj4g/92qCyw/KuvCaTySCXy42Md4FDqZx13uV18kfnXdd1VR2j8q7pt97+MLy7Ku72oxIOTAMsysCU/+t1GNav0lQmIQeXH/giGRHBAdhvB0K/Z8bjcfR6vWV5BFie3W3OwsJiNRDElaNCGM4lR5J34/H4Mt4NM0nL/2WZ8vw4eHfYfhyrMGDq/KiTrF/j9Enf73eYunGgBA2YMPXmqltKpzp4LB6Pq2dSumw2m0gmk8jn80ilUigWix6hoNfroVKpoN1ue8rhgKKEyjp0u101oNPpNBKJBFKpFOLxOFqtlpKEKZ222+3Q/Sj7LKi/xv2RW1hYLIfpu5ULiX7fZdB3LbUB8pjfc/vVTdc09Lteh9xUyXXdZROs5CryLq/tdrtoNptIp9PIZrOKd8mryWQS3W53Ge9SC5BKpdRqn+h0Our+TCajriPv8vpOp6N4t9PpLOsHk5YkDO8OyrmHRSD+oA3wG4RRBQ4pCJjqEXaA6x+b38CUz+Tf3W7Xo25Kp9NIp9NqEKVSKTV4ZLul9oBSKH848KUwkMvlkEgklDAgn+unrhukzywsLFYHYSbUYb5bkzDg99wwdRvmWhPv8nc/PpOTslw08UcKA1T3s+2c4FkOOZwLMHI/eTefzyORSMBxnGW8S62BSQAwaazDcnNUhBYGTLaeQV6+6Z5BVuQmQWDQgeb3/KhCBSd+kw0nivliZmYGa9aswfHHH48TTjgBruuqAURJ8vnnn8eOHTvQ6XTUDlkUCqjiYp34sXBLUPadtKNJrQDtXZRu+0mhutRKBJ2zsLDoD5OGbdBJdxQT8bALsFFfB5h5V85RuvAiIe33MzMzmJmZWca7vV4PjuN4eLfdbqNSqSiOlJoEuQCksLGwsOCpBwUR8i6FCanBCDMvma4JO9/oWHEzwTDX6fdEVU9FfX7YMqXK3uT8IesqP26+NDkYUqkU1q5diw0bNuC1r30tzjrrLDXwACCXy6HX6+GRRx7B3r174TgOKpUKYrEY8vm8GpCpVAqpVAqJRAKdTgf1eh3tdhvz8/PodDpIJpOeDI00GQBQEi4HdpDKX9dwmCTboI/RwsKiP6Ks9sIKAqPg3UHLCbpvGN7V+UdOvrxHX8zSzLB27Vps3LgRv/Vbv+XLuw8//DD27NmDVquFarWKeDyObDaLeDyuVv/pdBrJZBLtdjsU77JuJt4dRAugc3JYDCQMjErCHMX9oxiUhEnK6ldmkL+BXjf9I9I97SlRLiwsIJVKoVAoIB6Po9vtol6vI5VKYcOGDUin08jlcli7di1mZmZw8sknK+k1FothamoK2WwW+XweuVwO5XIZv/rVr9ButzE5OakEj16vh0ajgXK5vKxNJo1AkFbHNPjC+GJYWFiEQ9RvaFju9bt2GI2sjkG1skG8Y+Jd/m0yHdBJcHFxEbt371aLqk6ng0ajgVQqhZNPPllFIKxbtw4zMzM46aSTPLw7OTmJXC6HfD6PfD6PpaUlxbtTU1NK0+C6Lur1utLoysWjzrth+0Fvk+lYP4ROR7xu3bq+1wwyyPoNhCjq9SAM6pMQpkyTFynv9yuDaqVUKoVsNotkMolCoeDx9ufAYllTU1O47LLLcPzxx2Pnzp3Yv38/1q9fj40bN6JareL5559Hu93Gxo0bMTU1hfXr12P9+vX45S9/iSeffBLdbhcnnXQSstks9u/fj6WlJWzfvh2/+MUv4DgOarWakk51bYDfINOlcdM5Kei8/PLLgX1qYWFxCOPiXdN9lncPQufd6elpXHbZZTjhhBPw8ssvB/Lu9PQ01q9fj3Xr1mHLli343//7f3t4d3Z2FuVyGdu3b8fzzz8fmXf9fCJM56Ly7kA+A6O4Tl47rPQZVXqSxwctO2iQh62zSVsAQDn3tVot1Go1j02s2Wyi0+kgkUggm82iWCxiZmYGqVQKExMTcBwHxWIRk5OTmJqawtTUFCYmJpDP59Hr9TA9PY1CoYBut4tEIoH9+/f3bUsU6d1PZWU1AxYWw2HYidl0rZ9AMIpVv7x/JZyMo8xRkndZN8m7csthybuMECDvptNpTExMoNVqYWJiAhMTE5icnMT09LSHd9esWaP+TiaTRt7Vbf1+Wg8/x+0opiQTIpsJRjEg+w1A07EwE0+Y54VRfYc51+8efaCZkl/0ej3EYjEVVtLtdpU0Jx1RpNcrz1PVDxx0GMzlcpienka73UY+n0cymYTrumi320gkEpiamkI8HseJJ56IUqmENWvWoNFooF6v42c/+xna7bZHOtU/lrDxsPrk7zdwLSws+mNUK3TTdWH5Vr9v2G+53+Ig6Fy/hYvONTrvkjMl79KWT95NJBLqeqr2JQeSt6ldIO/mcjkVYdButxGPx/vyLn0GovCu3wJrEDO3RCRhwK/gqIMxjCbAdEyf1AeZZMY9McVisb6JgVgPwJuZSkLajlzXVV6vehgLbf9MKCQHCr1Ve72ecibM5XLI5XKIxWLIZDLKPibvMdXTr9/CSqhWILCwWD0MuwAbdoW/Et+/zlV+z+OEq/OuafLlJM1rJe/yHBde5NBut6uiBMi79OGSvKvze1BbwrZ/RTQDpgkurOpYPx9GKgxTziAI49Q2TNnAoYRC+nG9fL9rOOEzsQVDCqvVKrZt24ZGo4EdO3Zg79692LJlC37605+i3W5jbm4OsVgMp556KmZmZlAsFlEoFLBv3z688MILSvotlUool8uo1+t46aWX0Gg00Gq1lGAhP6R+zixhJVRrKrCwiA4/PoxqFgjLu/3KNq2+w2A1eNfvGbINsh2c3Mm7XExJ3t2+fTtmZ2exZcsWPPvss768WywWsXfvXrzwwgtIJBJIJBIoFosol8toNBp46aWX0Gw2lWnCxLthzNh6P6yYZsD0kFEPykHKBKKZDMaluh60PN2UAMBjMgCg7Fh79uxBs9nEiy++iN27d6trmEmLIS3HHnssstksstksDhw4gB07diCRSCCTyaBQKGB+fh61Wg27du1SMbQyWVGQZC1hNQMWFuNDmBV9mDLGwbtRvudx825YjazpXj/eZaRAMplUvLt161Yj76ZSKbRaLRx33HHLeDeZTCr/rvn5eVSrVezcuVP5IUjNLus0CFZMMxDG3jRsOWHv0Y9FWXWGkVAHhbQzmVbGpv/1iZcDkwOD5TCt5ezsLKrVKhYWFlR6TNq4WMbevXtRq9WQTCaRTqdRr9eVI+KOHTuQTqdRq9VUjgKZXEiqumSqY8KkzTAJZFYzYGExPMbFu2Hv93t+1O95NXg3aEEiuY0Tu9yLQOfdffv2oVKpYGlpSfkDUGBgmfv27VMh4KlUSoUPJhIJbN++HZlMZhnvknOlMJNIJJZpOXSO9WvnimoGRqG2Wk2MS0Kl3cevH+Sz5ADgAKRqCoDahwA45PCSzWbhui62bdvmkYBlOmLarrZu3erZp4DaAADYtWuXkn5lfTgApTML69avbbIPgux0VjNgYTEYgjQCg3DvoIswee5w0AyE5V1d/S4nf3lMXwxJ3tUnYJ13t2zZsox3i8UiXNfFzp07jX5hfrxLYcDkSzAuHh1IMzDqgRnlmUHX+dnmJfQBOUh95b1S0g2SSv3qJbfApEe/yaFQpq9kWCCfIQcgy9cleBnFIOuutwfwbqIURr1oYWExHoyLd/ut0MNwb5CJ1k+D2M8HKai+8r6ovKs/i5NvLBZDq9Xy7OsiwcWQzrtMSsTfet34t9wy3m8iP1x4d2jNQBgVfhDCqI3CdkoYqWlUkpUu6eoqKDnRSulV/gCHhIF4PK4yWcny/Zz55EZE/JthLUxHzIFL1RdTZspy2u22ZyetXq+nNumgV2xY/wELC4vxwMS3QdpZ0/caNKH6lRNWg9BPVR1moRbmGePg3WazqbhU51293pJ3ea3kXYYmSqEBgNLMEp1OB47jLONdpo8n745yi+N+GJlmwG8A+A1Qv9W1X3n96hRVCOj3vH4r+zA2sH4Ss1yxU/qU5ZmEATk4eD8HqLxGfiAsj5KthJSG5XMpDARJ/3p/6O3T22I1DBYW0RFGEzCqb2tYbWzQ9UEcEGWRNgjv6vf3413JszqnUhCgKcHkh8DrpEOiPv+YeHcQjXU/DU3Yfh2pA2E/iVCfNPi3rsIG/FNO+j3TdV3PhBiLxTxSoen5ejiHqVNNEq2UCk2dLZ1KdLCOer1oHpDnTH1gOmaSZqVdTN6j36/nOaC6jHXoF1lgeveybNPfFhYW4RAU0h2kndWhT3a8T05K8hy/+X68H4Z39Wf68a7+fP25crMhP22DqS90bUEQ7+r3BNVL5125U6yJd/V75bMl7/L/oHkIWD42TM8LE25JDL1rYb8BGSQQ6NfJly3RT1ryGwD9JNGwnaR/PKbnhlGDmdolhQvTRzQMTBJuv9/8mKW0a6p/FM2K1QhYWIwGUQQA/b6ghY1+XucG0/c+Lt41TZwm3o3CuXrbVop3g/o0DO/6tdFvHAT1Wz8MrRnop1bSB4auEpH/ywx68no/KVMfqPIarnb9Osu0Wu6n4Qjq2DADPMwHNcrVs6msMBqXflqAfs/yE+YsLCyiYRCNbFA5+kSrLxgIkwpb3uP3/CDe1TWTfuXoWtEoi8AwCLNQGwaj5N0w84rfc6O2aSRmAtP/fselV6WuXtIrrzt/8FpgubOIfJacvPWPoJ/qJWr75POCoAszYaXYcSBIFRdmQIbpQ9PHb2FhEQ1ReCjoun68KznJj3d5rx5fH5Z3dbW13wJplPDj3aDF5bgQ9TlBmuewvBsFQ+1aGOVF+k0yckDp6ng5qDj5M6WuHgYSNCD5fF0dFkbdpEupfgg7iE0DciXU6FEHh64NMX3AQQPSwsJieKwU7+qcOwzvyuM6Fweprv141w9ReHO1eDcq/PpRYly8O/QWxoNKqqbj7AgpmcqwOW60w+xOjUbDOID9IO0yumAg62B6ETIG1M8uE+Zl6M9cSUEgikTKPjIJVkFlWU2AhcXoMEre7cc7QbzLnVC5414Y3pV8a+Jdv0WYzrssaxje1RFU51FjXLwbZI4YBEMLA2HPS5ikUh43DbJY7KA/QTKZRCaTQTqd9uzY1+l04LquZxtIv2fyuWEHk18dh2n7IALAsIN0WM2Aflz/W39GGMnewsIiGKPkXXmP37et8yLj5tPptOJdue2uvv2uXp4eaue32PLjGZMmt187hoGf8DQoBlkg6vXwu2bUGFk0QVhI+5SfZEnJlDamZDKJYrGIdDqNNWvWIJvNqh2fWq0Wms0mut0uarUa2u22yhalP1O3izFOVF7Xr416iFwYjURQX4TFMIMhjI1fPofXBbXL72O1AoCFxcog6iJkEN4tFAqKd/P5PJrNpuJeybudTkftfKo/c1je1YUKHvPT7vbrh7AYls+i8m4YwSbMQnfQOg8tDAwDU8NMg5QDM5VKqX2hqSlg9icORmoIOChNDhX6BxHmRQRJqEHtYR1M5awEogzIQRFGILCCgoXF6DDIIqzf/fqqWOfdXC6nHAiZpZQLMuAgB8t9T6LwbpBA0M9PKQy3hHnWKDGIuXS1OXLFhQE/yZSdIJMvcEDyNwUApm3MZrPodDrI5XLo9Xrq/2q1ikajgXa7Dcdx4LoHs0zxObK8KLanfsKC3hb9XJB5JGzfSYxi4PhJmmFNKPLvoA911Oo8CwuL8DDt7KdPWHKy5A9T63Invng8jkwmg263i3w+j06n4+Fdag368a6sS5iFmOlvecyPf0wLsSi8228lHhVBC8qguuntMNVx2PlgaGGg32pZws9eZJKipFTJQURJlT+x2EHHlFwuh263i2QyqaTUeDyOer2utAXcTIK7RFGwoA3M7wPR6xrGphRFUg17/SgRpW5R75PXDir0WFhYBGMUvCvLkf/LhZm+EKMmlhrYTqezjHcBhOZdvR5RebffgkQ/tpqr70F4N+xiTF4fZo4yYWSaAZN0F0Wdrr9UGb5CJ8F2u41ms6kGFgcr/y8UCkqVlc1mkc1mkUwm0el00Gg0PAkxpOOhXg9pWwvTnqA2Rl31v1ImTL8P08LCYnQYJ+9ywibvcgEmt//l5J/P51ecd/00kX7nhrn2SIFJ+AmL0MLAKBwgTC/adK8cOMAhfwAAqNfrAA5JmplMBplMBolEAqVSCbFYDPl8Hq1WC9VqFblcDo7jYHFxUQ3qdrutBqU0Rcj6BLUhat8MgqhlHk4DerVtXxYWrxRE1TD6XROG04J4t9FoAIDHVJvJZBCPx1EqlQAAuVwO7XYblUrFw7sUCkbNu6OC7MMovHW4cdywvDtynwFdIAj7Ev2kQSlNxmIxtc1vKpVStis6C1JAoCorn8/DdV2P/Uo6vZi25x1k0Mk2D6pi1xFFah207KgIq3463D4SC4ujDWHNB37HdN5tNpuIxWJIp9MADpoDaCLgbqj0KWCooeM4AA6aDGKx2Fh4l/eOknfHxV+DtDGKn9iq+wxEhWkABP3mRF+r1ZBMJtW+z8ViUYWp0FQAQEmtcv/oTqeDSqUCx3GwZ88e1Ot1LC4uYmlpSd0rnyUdGE02HD9/h6BdpF7p0P09TL8tLCxWB4Pybr1eRyKR8PBusVj08G6hUECv10Mmk0EymcTExATi8bjSEjSbTezbtw/VanXkvCvrrZ8bVDg43DWb/dql+9uFxcBmgn5STlSPx34qIg4YOqbQu5USJ21UAJSESkdD2qjS6TQqlQoAoNlsKtWXrsnQ1WpRnDr0a6K+FJMTXr/nRLGpDQrT+wvqFysQWFgMj2H9boaZ2OgA6Lquh3czmQwAoN1uK40rIcMRU6mUh3d7vV5k3o3qZD0q3jmS/AlGxbEj1wyYTASmlx7kxapfC0AlE6Kq33Ec1Ot1pNNp5PN5pFIpNJtNpFIpTE5OotvtolAoIJ/PI51Oq4iDRCKBZrOJmZkZLC0toVqtYnZ21jOgqSKTiS749yA+A8MOyKAygj6SUUm4YYWjQYQmCwuL0UF+q6bfpm/SL6EPEMy7hUIByWQSpVIJqVQKU1NTyOfzio9TqRRyuZwyJ9TrdczMzKBSqaBcLmN2dhadTkct4ky8O4xaPAr/jNonYZS8249fwx7vh8jpiOWDghxW+h33a6SpAVIzwN88lk6nlcTKtJnZbBapVEpN/jKnQKfTUZ6xTJwxNzen1F4m9ZNJSFnpie5wVl2F1RZYWFgMhyBnwTAOeP14V5o7w/Iuta/k3V6vp/ITcGKX59PpNGKxGObm5lTGwzC8GxYmNfk4HRDHjX6L51H4SgARhAEOEFbCtOrX1Tr6ebl9ZRBMNiBO5kS324XjOCr0hbGu6XQa7XYbtVoNzWZTDdhisYhEIoFMJqMGcDabVQNTRhzUajU4juPJw21aGUttQb/JOmhQ+glWel/r9/N8kP0s6kdgqmdYk5Cd+C0sRgs9k6of7/J30Pl+MGkAw/Buu91GKpVCp9NRG8hRYCiVSirkkMniGAEGIDLvygiEMLwbhEF4V/Z1kAknCu/68XcY4W6UnBtZGNAnQAld0vSbuPo10lSuPgDo6UrE43G14m+1WqjVasrOlcvl1CZHDIehmiufzyOXy6HZbGLv3r1oNptqwDmOo+xi8jn8TeGGH6yubjPB7+UF9RfPyb7Rz+t96Ne3Yerid4+d7C0sVhZheZccEcQjvN8PfgKGfG4/3s3n8yqKgDvMMgyRmttCoaBywjSbTezZs0fxLsvz410AyqFR8m6/eSVo9WzqL3K7yZdBJmMyCV9+fRpUF7/7+l07So3x0HkGZKVNg1E/b7pXlq+/oKDJU7fj09mFWxvTFMCUxTxPEwJwMPqAwsL09LQSINLpNBqNhrqW4Yny5dO0ILfZ1OseNFGb1Dz6y5X9Cni3Upbl6XXTnxEGQR+U/n71evndY2FhMTjC8q5+PAzvmlTP/Va0QbxLTSwXXuTdTCajeFdmJGRyIvIugFC8K/MUBPVR2Db3ay9g5l255XPQM4aFfKaJd01zxiAILQz0W/VKlY1JojIJCn4ImkApKdMmJVfmcsMM5sluNpsoFApwXRfZbBbT09PIZrOe5BnZbBa9Xg+Tk5PodDqYmJhAtVpFpVLB4uIims0mlpaW1F4HMoUxy4jFYp7QGL0dfgNGFxhMfWRSA8o+l1uI6s8Pem96XfoJEWEFAlP7LSwsokN3pvOb2HXODcO7/SaRQXiXDtrNZhPFYtHDuww9JGfmcjm4rouJiQl0Oh1MTU0p58LFxUW0Wi0sLCyE4l0JXZuqt0UXIvRr9f4w8aO+dfMoeLcf5/LaoAXyMNw70miCoEqaEKaRQWoYk/QrJ0cOong8jlqtpvYxAKAEAgAqSRETZjCjluseDKmh6oqbKAHwqKlMqiTZdtMH7Pfh9ZPiw6jDxjUZm4hF/q//bWFhMTr4fXd+WG3eTSQSqNfr6Ha7yGaz6n65+RHL6Xa7KBaLqqx2u41EIoFGo6HMAkB43g2CSbMadI1f+yVGwbtB84If/LQGUfsEGCCawFQZv441dZBJve33rKAXzXzXprJ5rlqtwnEcpNNpOI6DTCYDx3FQKBSwZs0aTE1NIZVKqS2ReX+xWITjODhw4AAymQyazaZKrzk/P49GowHHcZRJgVkRaUsKWu3zt1532dZkMumxWfH6IIFASrush+yjYUCVmGyTfJ6UxIPeuYWFRTSE4V39upXgXZNGgefoCJhOp9FsNpHJZNBqtVAsFjE9Pa3yEJB3yXXk3bm5OcXZ2Wx2KN7V22NarJAjybumPvCb40bJuyZBwPReda2yzr+mssJgYGFA73ge85Oa+pUbRZqRah2/iZeSqhyg7XZbZdRqtVqeNMZSUgWgBmO1WlXxtYlEAtVqVZVJKVU61Mh2hZXM+TvoA5X3Bamz5KAMIhI/RHlXQZKo6RoLC4vhEaS+DqNOlvcMw7smE4brHrTpk3trtRo6nQ5qtZraTM7Eu6wrebdQKCieTiaTA/Nu0Mqfv03a2DDz2Kh4d5D31E+zEaZsHUNpBkwr8n4dG1ZaMzVCSkEcbBJU+8vyKRRUq1VlZ+JmR7FYTHm+ptNppSnIZrPq+VJVRUmVmyBxi2SG0vC3qb0miY1/6yGXUuUWJO35SZJB0v8gMNXZJITZCd/CYrTws4dLmL75w4l3mQxO8m6z2QQAZDIZTE5OqgRFRDKZRK1WUz4I1C6Qd7mwi8q7+mpa7zO9HL3vTP0ThncH4Ua/BbdJOzAKDO0zYBqY+t+yo/x2qpID06TmIkyOGVJSlddzEyNKkfF4HOVyGa1WC7lcDtlsFq7rqg2PJiYmAEDlIQCgtAG1Wg2ZTAbZbFbZwDjQKcHSm9YkFMlwFNPAlOopKQjI9vn1uZQa+XxZvt99/Y7Lc36DUj9nBQILi/FCfnd+fMvfo+RdCgOyjCi82263Fe/2ej3FpdxtlonjAHgWbuRd3kPe5aZzw/CunOh13tX7QddyhuHdQbWwQVw9Lt6NHFpoWvnzt7Qn6Z0tpUm9w02qcNN9+uCXggFfpqlu8n/HcdDr9dRmGZRQ6VCYTqcxMTGBbDarttxMpVKYmZlRW3oy1XGj0UCj0VCRBoVCQWU4bLfbapcuOVDlgGFfmUITdR8B04dsst2xXNPH7iexRtUg6JKybvaQx/zMCBarg16vh6effhrPPvvsalfliMOHP/zhFX9mEO/yZxy8K68dFe+6roulpSW12o/FYop3M5kMSqWSh3fT6TTWrFmDbreLVquFTqeDhYUFlT6eyY9Mk7hfm8mP/Ftvr58wYJqA/eZC/ToThjlvEgJMi7WoGDrPAHDIPMCXIqUxSnr6NSbox02SHiGT/pheIuvMH56nEwpwcI/uSqWCdrutBqGUVlmHVCqFY489Fq7rKmGA+bprtZqSemkfW1hYQL1eR7VaxdLSErrdLprNpvpo9UFj0g7oKjx9EEoTQj+pXu9bvaygd2tSJcr7/CZ7k0Bgsfro9Xr49re/jXvvvXe1q3LEYTWEAd2EKL+j1eRdeV0U3uXfpVJJaQrIu+vWrcPExIQqN5lM4rjjjlO822638Ytf/AIvv/yysd5BbdMFFovliCQMmNTWpolY7hhIpzxdiuUxGa8ZJk4/SNXlV2/9vl6vh3a7rTJnUTvAZEWVSgWxWEy1gR8XPzS5IqcQwXZKqZaZt9rttrJrNZtNpT6TG3T0W8Gb3oHfRB0EOykf3XDdQ/tzWBxZ0FfyryTeZfSX5F06GEoBJ2xKe4voGNhnQDcPELFYTNl4stksCoWCcVXLtJbdblc5gjCxRNCgl6pwmbfbNECkhCwTZVA6pTqf6iruyd1oNDA3N4epqSnMzMwoNZYcnOVyGbVaTe1xQEcWmVO73W4rFVelUkGr1cLs7KwyLVQqFWV6AA6p/XUbnG4m0N9BUNhhWPXRqIQEXUPhV3cLC4voCMu7jNnX7+NCRPKVH+8Cy/ekGQfvuq6rwhCz2SympqZwzDHHLONdmSfGYvSIHE0g1dNykpaTNcNFmN3PpGKmVypt6lwly0GpD3wpvepqH70+/cBBTts+AE9kQSwWU6t8+UFQGJCSazKZ9EjZ0j+AwgD9ExqNhrqekjK1DXobZX8NIg0HTcRhbVJ8ftT7wpguLCwsgjEo72YyGY/pUZoLuBA7XHiXDoDUFNAUK/dlkBoCi/Eg8kZFcnDoA4H2pFwuh1KphGKxiDVr1qjjsVhMrZylpElbEidfQg5e2tx5jUw+IVfinIBlTKrJCYfHWq0W5ufnlVqNHxEAZfd33YMpM+XElk6nMTk5qT5Ak4aDAz6bzSKXy6HX62Ht2rXqWm6otLCwAMdxsLCwoMJn+EFIKRyA+iikYGKaePt9nEF2fP2cLlT4nTNpBKwwYGExOIblXa7QyYm8n/zEBVlY3m21WoqvR8m7XDTGYjE0Gg0P77IMXRtiMVpE0gyYJFIJSm4MC2H+aa6gAShhQDqiSGlVH0RUazGdMD1Ieb8+CClgyDrqg1G3YzWbTXQ6HeUz4DiO2tiI2yJLDQGfTdtcJpNRz+ZKn3XkM2TqTfZDu91GpVJRm3PQlislY10VKLNtyfYEreZN7ynoHZr6TP4OKtNPOLCwsIgOE++aTH+Sd7PZrMruZ+JduesfJ3NZLs8xTwB5t9vtqgXIqHiX5grWg7zYarWQSqU8WladDy1Gi6HTEesqIg4q6cwiByUdQDiRyvv5omVaSABqktT30ma4iX5Od5qRki0/Ct2BRqYjlhIwQ2AWFxfV9pupVMrj0MK6ptNptWMXADW4WUepnuN1FJharRbWrFmDVquF/fv3o16vo1wuK18DCkNU7ZlW6vpEbpqEo5gH5PEw5gY/AcUKAxYWg2EQ3iU3kXuBYN6VO7jyei7E6PhMYYBe/XQE5DnJtVF4V/pHSW1qo9FYxrtybxiL0SOSA2HQwNTPy9Wzyd5DzQGwfDLLZDIqRSU3D+JkKnev4sq9Uql4PPUJXsNVv3Sa4Q8HpxQGWD4TWgBAuVxWk30sdihBhgzn4eqfbWY57XYb5XJZ1YMfQTKZRKFQwPT0tEdDsWbNGlQqFezfvx+zs7MqlTJ//D6IsKvyQQSCfuf0a6x2wMJiNAjLu+Qhmi6lrZ2gT4Eu4MfjcaTT6UDepeMheYgO0DxHcDHFRZD8zQWSH+9SGOC+A5J3KcRYjAcDRROYbMoEpUCGj1SrVSWlSju3LIcTqj65coKlGooTfafTQTKZ9Kjfe72e2qqYA51Jf2hm6HQ6KhSQEi4l5EQigYmJCaRSKRQKBY9AQhVWLBZTg5RaD7aFkztwSBiQ+3rz46JJQX7IlJTZxunpaeRyOZUauV6vI5/PK38CqRWRtjs/Rz+TDV++S/ke2Jd6Ofr9po2K+KO/Q10QtLCwiI4g3iVHNZtNpNNpVCoVxVEAjGF55GK5K6DUKui8y+vS6TTa7TaAQ9kGXddV37jUHpB3KQzIRGzUVJRKJbVxEXmPvEvna2ofTPsRWIwGkYUBXTIj0UvbFlU80rafTqdVhr9kMqli8zmJxuNxpV6nEED7FweF67pIpVJqIPIZhUIBADzSMPcUoKMepViGtXDPbTlpFYtFFSooV/70dO12u0ilUmqASjMB6ywnQHr1SoccGc7DCV06QgLA5OSkeibbsH//fjQaDezevRu1Wg1LS0uqLPoYsBx9wmcfmwQCSugUcIDlfh26wCbbLsMt+Zv9IPuQZVtYWERHP96l1pOcCEBxKn9TOKBfk+RdGRnFEEXpHGji3Xw+D+CglleaTZvN5jLeJVfJpG98Zj6fRzKZRC6X8yyyyPt8fiKRUHWwGD0iMXSQ4xmPU5KkSog5pDm59Ho9JJNJpaaXkyht99KxUNrxpTqMP1LrwEEpJyEOIDr1pVIp9ZtaBV6bzWaVEw4/FDnI2TaWyT23WT7VWHKFLONtZXgPP0ZqHNh/0teBEzT3PqCjIT9smg+kx6/ucAQsFwb0dymfxX6Xzkasvy4MyPqZjnGPBwoIFhYW0TFK3mXkkxQGyLsy8kCW7ce7XMGTb/mbP5J3GU6dSqXQaDQ8iww6O0ozrORc4JC20poJxofQwoA+UEw2YE4s0tmk1WotWz1T+uNg1O3wuVxOSaf5fF6py+XETFDrIO/P5/NKIqa6i7Yqhu3V63XlD8CPSbevmc7RHlav19HpdNRuWqx3IpFAoVBQ/hD6RMkkTDLPQr1e9zjZVKtVpfFIJpNwHAelUgmdTgfHH388Op0OKpWK2huhUql4NAK674RJTa9/bHw3bCMAj5SuawbkOzVpD6TWQAoVFhYW4TEs70ptnYl3pV8XeZPJ4shpfrwrhYFkMqnulxpVJl6jIzR5VwoYOu/qPhBS0LFphceHyLpbqm9M0L1Tdfsx1UtczUv7lFxR0kOVKqJkMqnsS5QeWb6sl+5Jq6vpqWng5C39DeSqWa6u5Tl5jdyRy3Ecj58A26jb49lG9iHtbLrnLdX9dOihvwF9DrrdrsrYVa/XlV8F+4kqOdlHOonIHcIAePqDfcCPPMhcIM9JbQivkX9bWFgMBsm7Ov9G5V09xbrkXTrvSX8smQlQ1y5KQSGIdwEonmSotslXycS7evstxoOBkw7JlahcXcqBycEgoQ9S+cNBSpt9JpNRdqRSqaSkT13KpXpfhsbwWTJHNyfdVqul9gzgJByPx5UULFftjuN4JjzpNEjQ9k91G/0h6vU6arUa0um08kegfYx1YX/F43GPAyQdFflMmiB438TEhPIpYDQF68AVAj88k88AhQH5LvnBUZCQJgC+Z32i11f/8lm6CcHCwiIa+vGubkIYhHe5wjfx7sTEhNJ2Ss6ltlbugyCfRc2oDDOkDxi1BVxMmXiXflmSd13XtemIx4iBkg6ZvNZNah7paRpGqpMOLbTpc4AyOqFUKqmwRP6YJmjWSY9e4ACkowyl4Xg8rgQO+jNQEJCqb06uVFlJ9RVX+pTCeZxtkP0gowAAr7mDNj3pO0DnSiKdTsNxHFW2zLfAdjFPuQw1YnsoUMloBv5QSNLjjmU/SBWjrnaUjqF6LgYLC4vw6Me7/FuunEfNu9RCSs6lICDNirI+0tdI8i59Fsi7iURCLZTIl9R4AlCCSjqdVtpKi/FgoL0J9GOAf+Ibaf8JgnQUkdsEc8A4joNEIqHU4pRgs9msylbVaDSQSqVQqVSQSqWQy+WU7Uvaw6V0zUmYKi4OYk5enMjkCpsTvh7SJ6VxTrr0fi2Xy2og83l+nvbZbFaF71BQkR769D3gh0NHQuYyYBhPJpNRHzilb/nhUxjwe0fSyZLn5QpDmmV0XwH5v+xPCwuL8AjLu/x7lLxLfyX6E0jezeVycBxH8S41tEyiJnmX9dGdq8mD0mQZxLs0nVqMB5E1A/J//uYL1AUBCanS6vcMfQctObFI1VQ6nUY+n0ej0VBZquRKlY4wqVQKxWJR2b6oPtdD4PRwQumrQOdAad9iZAHV/kxsJJNqOI7j2baz1zu4M2KxWEQul1NSr+w3PqvRaACA+hiprpMqd2brajabOHDggGdvA2Y4zGazSuvBD5CSv+xj3aGIwoSE/FhNZiL5rvu9bwsLi2D4+Qf48a5uWw/LuwCUr5K81xRpxZwAXHxxZc8FjuRdcjLvk7wro78oINBXgc/L5/NKMKC202I8iKwZ0M0BJgFADlh5DaEP2DCqLK7C5U6CFBo4kVNDINXyHNBU3VNtzogFmgv4DFlvWV+/yU96zUubu8zvzTL58dI2xmP6JMqPRF7DZ1A6l7Y0TuDtdls5//Ajk3tESGGAGgPZRn748mPV91SQfwf9toKAhcXwiMK7/D8s75ru12HiXZlSnbwjNQPSMZDnyLWSdwEox0UuwHQNgu5EaDE+RI4m4OQlN43g76ABKtXEug1Mv156mPIcn8FEQbwuFothdnZWrZpTqRRmZmYwMTGBTCaDWq2m7qftnY4xpVIJuVwO09PTnvhW3VtWb4v80GR8LKVY6cRDB0WmIWZdeI5StoyqYNncl1xqIYrFosdXQqb4nJqaguM42LlzJ8rlsvpYC4UCZmZmPNEYFArYJt1ZRzoJ6u8mDExkZGFhMRiG5V15zTh5d3JyUmlspU8UV/qlUgmlUgn5fN7Du9JUKushOcTuSzBeRNIM8CVJVbpJEtUHnL6y1gejaVCarjVNMHIlzlUxnV2kTV865enqKqrHKPFKuxYAzwcohQOT7dxkZqATDU0IshzZN3okhFSdAd7cAHKi5nVUobFt0vNXbqIkQ4zkB6bnRJDvbBCEtVtaWFiYMUreBYJDFAflXYaAk3elmZeOg3T+IxclEonQvKsvJC3Gg8iaAXqoS+cynfSpHqczikmtJSczkzSqD3hCD42R5gLXPeihv7CwgKWlJc8EzZzX09PTKt8/AGXTT6VSmJycVJOnnIxlHL8MY5STLVViHPzUQkgThPQrkBO71CTI9snMjPybZclraSpgaGOxWFS5CJLJJIrFIqamppaF+un9rkvjYaRyk/nAwsJitDDxrg6ddyV03jVN/DrvynNhebdcLnsWK5J36QsFLOddU04Tybs2pHD8iJyOWCaVyGazStKUE4HcFdBPzS7jU6ki4oQjber6JCRXrbyHEqpM2iPt/zLmleErck8A2sdpR+dqngOfUi1jZaXULSVvmT6Z5+R+C6wTtQPS4Uf+yDbK0Dx9BcC68zg1EVLzwb9lZjCTpiZoIpfvwe86kz3SCgcWFsODEyMXF35OdJJ3qY0kyBecdIFDvCu1pybelXwhFxAyT4vOu8ChsEDyLn/kHgfkJi5eTLxLDrcRSeNFaGGA+0lPT0+jVCohm80qaU1XocvBJeP6eS0nPBmLKiEHoxzQcgKWTnryWfrOWEy+UywWkclkMD09jXw+r5xd+JHJlbKe9pK7cMXjcVQqFaUtoEDD1MdcvVOCp5+CbBdzAbD9JlW61GhI+73sa/nxsj9qtZpKgMRyZDIm3R9C9jXLk+/IlCVMjybwc/CxgoCFxfAg705NTUXiXXIPv3m586A0bRJyhc//gUPfMcuRIdUyzwrNBDrvcidYybvUrsqshkAw75bLZbiui0qlMs7uPqoRWhhgnHqpVMLU1BRyuZxyEpGDUk4SBFfeHJRUZ+sTDGBOXiSP8z7HceA4jkfFxcxWDK2jcEDnvnQ6jampKWSzWSVly0mcz5QqNpYv9zEAvNsUM6sgIxUoeEjHGH5oNJuwP0yrcl0rICMeGAtMWxvtce12W+0Uxg9KChWyv/U+5sdLCZ/vSDouyvAf3a/A7/1ZWFgMBxPvMlOpnn5YXzlTM0ne1c2TOk8HgYsSpomX2klyDvcdIJdwEZZKpRTvknupFWC9yWeA1yTJ8Goep0O4xegRWhig6gk4FIZGNb+UOnUVNP/Xk/ZIVZCErtoiWDZzAUhnPE5cnMy4TTHVU9LjnxM11VMEPxwp0EhBJ8jphgOZO4W57sH9AbizIQUO+QFKSZzXyElWagakicFkLqBkTsFD7gkuTSKJRMKz2ud7lf2n979OGPp7lSRk0hBYWFgMDp13ZWpyfnd+grnkNMm7Ovw4V5YteVc3J0jepamAPk1c/cvMsdLMYeJdKehI3qUpwWI8CC0McJMJqviZ1Y4vSTqlSekTOLQJDp1FcrmcmoB4v1RL6w4snEyZJ0A670mNAMuU5ci/5aDmwOQknkgklEqLgoN0gJR2fq7E+cMthPkbgDIXcCcvlk1JGTikzZAfvAy1YV0o1ABQWgzWw3EctQsY25TJZJQUXavVkEwmVWiQnPCp9pNevfIZ7GN9FaGrGq0tz8JiPCDvAlCLA06mchEmuZeQocSSd8mL+j4GJt7lIo6mT5lkTXKjblqU4Y8673JfGO6/QudmCg564jaW0+12USqVVqbjj0JECi3kpERbTqvV8lwjnecIk1pZd0SRg5CDRx+U+o+UkCmkyFWubv/mREcnFU6c0guffgA81263lYbA1EbuCUD/AD3HPz9EGc8vP0R9EtU/KP3jNEn1cqKWK3RqCvgRsl8pNPF5st6mMnWfAPmMMGpG61RoYTE45Opb/vAcv0mZuEfyreQG/qafkc4v0sdJagWlJld3/Ca3ykWd5EG5wOCELx0LKVxIZ+dOp6POcy7g4kz3L7MYHSLvWlipVNSKlKpnmVaS0qSMc5eTHr1J5WSnO68Ay+Nh5SQKHIql5/NZtnTS4/X0HQCgbFac8LmxTywWU5JpsVhENptVq24p+dI/QA/Tk30kbfr8cDmwqVKT+xNw9S1t95SapYpQfqhyUpfplAGo9suUx6w/tSj8zbKlKlE6HOo+ICaCsbCwGA8k7wKHQvLkql0K/KYQPQBot9vKYdn04wfyruRePo9mVplvQGYqpNYTgMo82I93ueeBNDkAB3lbRmdZjB6R9ybgRMWdrGSuAJoQ5IRBBz4OJD3+VX+G6Zm6jV13WJPSouse2tmKH4IUMnSnOgBGKZrZ/XSnSGny0CdhQt+Kk3sUmNqpS9562/m3FAR06CpCfvSsn5TWTeXLuoRZ8UdxPDL5hVhYWISDzrsyegk4FIklBXse132DdE2uX+4QE+/qizXdJEFhQQ+v1q838S45Vi5A+D/Lkg7MFuNBaGGAE1y9Xlcvnmp06TnPlbqcKHmO11Jy1XfiM03OnCgpGVI1xUgCTtpy9aubC6T6i/XmgONHwjoCUNsXsxwAnjrKskzeuTJZkNyuU36clOClZoVlMexRalWknd/PNMCy2dfyPgprUmrXE0PxWul5LN8L1ZG6w6jp45ew2gMLi8FA/mI6c2obJbdKDYHkXckpeuZRqQmUP5JX9Pvi8fgy3gW8jsy671Y/3pX1YLtkiCG1vlJDajEeRDYTUAAADq1YOZnKyV06wkkpUg5WTnhMXqRPilJa1J1cpJ0K8K5ApUOiPiil6UB6x0obGs9LVbycEPUVPJ/PyZFtlhI1hRjeI0MT2W/SF0KaIST8/telcP0amfsbOJSghA6Qemih/hzpd2CKbmD/24/VwmJ04HcmV/XUwOqTuuQN8q/kFZoW9NTkcqEjuZeCA5/Zj3cl3/rxrv6jax7IuyxTag+shnG8iJyOWA4GCga6k6BcLUuJjoNRFwakECAHIycxGcPPTX2kapyDWaq3dEhHG+5sKCdD3s9Jjx+fLE+urunYyOP6Kpr3StuZXH3LVT3vpfqPbQOWq/J0EwN/yzbLsmU9Jfixy4lcClH6tbrjp6k+/Pj1ulhYWAyHUfAuuZRCgAw7lPdLLpZbCeuaBJkrJSzvttttFU0gQ7HphJ3JZDwcxLbJKDGL8SCyMMCJzWQDlyprQv+bISpy5zwOKql2lqts5qZOJBJqK145OUkHPk7oUliQg5ne/61WC9lsViXrMQ1KXfqWg1JOosChMB6pNZFmCq66CVlvalVk6I4uDOjPk32qT8BSivb7SKX0T6ccqRkxhQjxPlNdpPdxkE+BFQ4sLKIjCu+atIdyXwPpeC0FfV2oIO/KPVn061kPLjrkNdyRVfIuo9Da7TZqtZpafNGpkE7pej4aU9STxWgRWRgAzI5+Uk2sTxJELBbzSLhUz9N2pNvB5aROZ8V6va5UYTIeXj5LDlgAng0vOPBkjgM6CtJ7lfU0aSrYDl2CTiYP7etNvwjdji8laLaB5UhItZjUJJjegVTR6QialE3QTQ66VkReY9IA6GRkJ34Li9EhLO+aFgySd8l3UsMAwMO75K9ms+nhNN3XSz5PchkXONJEyv+lycB1XU/UADlUmgfILcwJYzEehBYG/CYj+X/QNRysTJBjmjD0+6VKSreP5XI5T45r08RHB8B0Oo1jjjkG6XQahUIB6XQaxWIRhULB8zEwUqFSqXgkXKlCZ5voJCk1GdQmMA93oVBAsVhEPB5HPp/31E1K31KbICVs6aynQ3740nFSrtD1Hc78pGr9HUiP3zD3hIGV6C0souNw5F1qaWVUgwRt/zrvZjIZD++Se8m71WrVs0DSebfb7WJxcXGgfrToj8jCQFhS91NTS4eRMM+kZChX7HLlKsMW9YEpHQGpXuMAll65vFeqoSg166F5un1MDmheT1UX1WLyGVLFpmse9P6Vz9Klf5PJwGS7C5q0wzj8jWp1b6qvhYVFMA533pUaU5YtUxYzXwo1rYwi4OpfV/9TMPATBvzCIS2GR2QzgT7AdPWwSV1lUjdLmNRb+mqZDn18Bp1PpO3dZMMGgGKxqNJY0gYmB6V+L58j66qr7OUHo5sSuC9Bs9lEpVJBKpVCoVDw7N3NHbykSYAqNIZKymRJMiWpbpdnn7MMGcGhCwZhyMD0HkznoggeFhYWg2MY3tW5TJYjf8vnDMq7spxisYhisQgASosrk84B3q3deZ8UDMIKMBbDYyCfAcDspDLMKtNvlepnB5NlSjW/vE56vcqcAbyWP/L6sPWXwo38icViHkmXEq1U11Ptpicr0tMQy5W+TM0pn02zgtRM+PkisN5+fgisrx9M7yKMdsHCwmJ4mL6zsH5B+nV+GgSJoIk4DO8yXbuc+MmHkpvCzhuWZ8aLSBkIgeFfiGlwBU34+jF9ZSwdCfVBQ0eYQqGgtv/khh20+TPMUEYsmD4wOdHL/2X+f4Y/0nM3k8l4ypa/Y7GYJ2eDfA4ndD/JXdcM8IPWzRFSmAi7gg9zTtcw2I/UwmI8CDILRi3HZFaUv03nCBPvMnpK11JwsUPezefznq3jGU0mOdGUU0UuaGhOyOVyA/eBRTAiawb8nFB4Tv423et3rN+9fB4nS+ngQnW7DEcBoMISC4UCCoUCcrmcyn8ts/8x/KVUKnkc+qg5oJQrV/20X0lPW5bDbZLz+Tzy+Tzi8UPbdMoJnOYAtk22S1fD6SGGfloK/iZ5SI1BEKIKe2Gvsyo+C4vhcbjyrnSC1nmXZgLJu1ygkXe5k6vuSE3tAXmXvl92C+PxYWTRBEHX6VKtn41J/+GkJgeiKf6fEqop61Ymk0Eul1OSKSVVXkdPVw5ePd0wHV/0PbcpFKTTaeRyOVUWr6UELHfo0lN1yola3m9KrqFnSdQ/HAkKMDJ80/Ru9H7XQWFCrir8yCPM6iIWi3n2MrewsAjGOHnXxLk678o08ibelb5JMjdMVN6VGVJ13mXZ3W7X8scYMVQ0QVjbcT8nEA5CGccq4/eZbIi/mVJTbqUpJ1WWwwGZTqcxPT2NdDqN9evXI5/Pq4kuk8moQVkoFDwr6FarpTYH4TlmQCQYLiM/PJk0iIKD4zgqx7jcF4DaCX4M9NLlJExBgpO+nlaYGgbgkEZAz6Eg+0h/RzKMUC9X31dcFw78fAj0v+Uz7cdsYREe4+RdAB6+1HlXZn6lAzR/pBCghyFms1llEjDxrusezDlQKpVU2ZKDuCkTta2pVEqZByYnJwfuS4tgRBIG+kmlJk/3fvfIwSQzEsrEQrp9SebXNg1KudKmCp+DmINchrCwvlKtzrpzUpR7I+jt0Nuslys1A/QFkM/y8wOQ5evaAOlxa6pbLBZTmb6oUWAf8T6CDoi6Fy+jGdgH0lzhJwz4vWvWfe3atYHXWlhYHMJq8K70g5K8S20nNbHSb0rXcLIsybkyU6wpvwrrpWtP9R0ZLcaDgaMJCDkIKEXy5esvT3c0kSqjeDyu1EX6wJP2+Hw+79nTQNrD9WfqkzzVTsxAyGOc7BqNhmcidxwHjUZDqag4oKX9X+bYlj4Lsh9c10Umk1EfATNpOY6j7pM/Jtu/SUABoOoor+HEXa/X0Wg0VChjLHZoMyhurdxqtdSOaM1mU+3LQAGImgVCT09M6A6cfqrHjRs3DjvkLCyOeph4lxO4SQMYlnfp/EyOzeVyij+k07W++PLjXdd1lZY2m80qrSfNoQDU3jAm3mUaY2os/JKwWQyPyNEEOjgo5SqevwF41D+8Xq6ape2pWCyqgUcblb5BEdVGHJi6ZkCvs4zd11frhFTJ8x7a3Ll613fd4iBn3KycwKWJQK+TnyaA50x/y/6XK3Pg0HbLEmwzPyo6KvIDpNaAQgmv4e6FFAbotCPrYlI9So2I/ptt7OfAaLEyiMViyOfzmJmZWe2qWITAKHhX11gC8PBuqVRSqnhGP8lFWDweV2YCE+/yt9QcklPJjbowIq+VHCM1AzrvAgczv05MTPQ1O4c5Js/JH5NJxuQjZTon7+9nPtX/13k2ShvCnO+HofIMxGIHHcKmp6eRSqUwMTGhpEbg0ICT18tNMvQti3lvNptd5pwiVVK8VobwyZfASazVaqHVanm8U5kRS+6C5boHNzeamJhQJgSqx2XUQSKRUPZ+vR/kRyHbQ78GPp/XcMI1tYvtkYIMbfdMK8qNj5rNJmq1mmovcEht2Gw21fX6xkNsB/0iuJmSNGEA5j0PZLvl86Tjkf7x82+L1UUikcAVV1yB1772tatdFYsBEIV3JTdJHtN3K+TkTj6Vqn8TP8nNiwiaFcPyLnDIXMAoLvIPj7MdzOoai8Vwzjnn4IQTTvBwlL4Y0U0SMppLmkMpHNGUXCwWldaYC0G2g3xLHm61Wh5Naq/XU/1IvmZiO/YN+4Pl85jJZOsHtktfVOvnBkEkYUCXVjigmO+fg5LXyomNFefLzefzyOVynlUjG0Hp1CRFSjU8J1s6l/h54Uu1OlfycnJst9tIpVLKpi/TXnIC5wRPtblcqctrZR2lpE7NA80dUuvA+1i2XP1LbYRU4cuVfbVaVX/TKTKRSMBxHOU3QFOAHNTcDlV+uLL++kA1tVGaVeS70a+3ODwQi8Vwxhln4IwzzljtqliExCC8y3Br/nDCy2azymSof6f6fis67wKHcrek02n1DNNugrqPkcw8KBc5bIecKHUTBLW0rutiw4YNOPnkk5cJA7rgwv6gSYICjOkZNJPMzMwgl8upiZqOjNSykn87nQ4ajQaq1aoyL7uu6xFcHMdBp9NBvV7vy7vkfalhCdK++vGuPDYIBt61UK5+6T3KwckXSylUVpQSpi6hSkgnN1OnSKmPnvpyUMrJXNcCUBUuJ1iGsVAToDuwAFgmKeuJgaSEzQ+P+xLIj5KSqkSv1/P0h+M4Ho99mgK63a6qf6PRUGaAcrmsBioA1Go1VY7jOB5hgMKE/CDb7bZ6r7L/+zkGst36x6ibY6xGwMJieATxLn2S9EWY5Cqu/skzuulOOjebeJc8Td4g3/jxrpz4m82mJzLJj3flAkhqLyTvSn7SeZfCikyjTGFC8i7rSaEmkUig0Wh4nLE56VMY4P+tVgv1eh2VSkVN7Hw31MjqizBqD2Tbg3hX+oCZEKSZHZR3B0o6xMqw47PZLHK5nMq3z5cq40SlGotqLsbnSylODiApxemTDYBlXqa6hCr/Ztl8mdwfXPoT0KFPetPLl6gPPGkCkWq2QqHgUaHJQU1nQynxdjodZLNZTE5OqkHJbZ3j8bgaVBQCOp0OqtUqGo0GarUalpaWPAIP264LA9QoyP6VGgG2Ra4M9EEpyUK3lfUTCiwsLAaDiXe5gyDV29JRmgmB5ORJzax09GbZkncl3+mrTsC7CAPMvEv+4DnpEMhVMhdL3J6edTDxrnTe1nmXq/5isYhsNusRBCTvSsGIXJjL5ZR5uF6ve7Z1pmaAQkAQ78p3ROfwdrutNAOSd+VCjPUEvBpZ3Y9APiOIdwFzdEkYDORAyJfGiYwTF18Cr6EWIB6PeyYVqcqgul82VNrypXMJJ+pUKqUmbjlI5Ra+ADxqHtaNH4l0TJFOdVJ6lI4rfL40G8hO10Mi5fWmlyJfLsuU6i0pIdM+RVMANQX8oV8APzxpu2u328uEAWmromQr+1+2XwoX+o/JhGAS2oZRXVlYHM3w412pEeWkLDmFkxonfH1S1zWyul+RbpYlnzuOs8xhm/xBkJ8k71JbqmtkOW/ovCtzpwDwcK6Jdyn0kE/9eFfypO6AqYd9M9KKfc35hD4RbGMY3pXX6j4DuhZGLuykOVqeI/S5VBcMophpIwsD/M2XKm3WzDzFCjGMTkpo/Gk2m6jX6x61v1yR8lkmXwGuqOWLkM4eHAyyw1kPSpGyXXq4ivSW5UdD+xgdH3UHSCbPYD0ptGQymWX2ePYf1WQUAujNS+cTXtNoNLCwsKAEL9qiHMdBrVZTDoSUuCuVikey1gcly6V0zmulpoS/ZV3k+9AlYqn1kR+iXJVYgcDCIhrC8i41kZJ3geXmTd4nzQSmCVNfmbIcfXUrV70sU/ILeY8hivIZ+mJCLh7JqTK6QeaN4XlGPLAtUiOt+xWw/6g9YXSE5F3W3cS73W4X9XpdOW3TZ4C8Wy6XlWM2+4VmXfpzyWfIqC0T75Kf9QWY3m9sn4l3xyIM+IEVjsfjaDab6riMI+XEbFIlR60475OTk5S0+KL9hIFud3kWP71zGfEg8wRQQJCQth59NSzhNwlKm5defjwe9wwcmjVkFID84UTf6XRQq9U8kz4HtkkYkGXKvpJ+FboUq2sGTMKAfFdWELCwGC04CUmNrFzFy0WJ/P5MXCXPAeYIIq46/YQB6c8gtY3SKVr3UZCCDnlQX63T/KHfI9srtcNsjx/fcMVN87Q0X/NeLt7InVzlS97lIkryLhdo0klQCgM6n0blXdZffz+6xl3/CYvIwgALZ+Xa7TYWFxeVCkmqXGS2Kv1+djTLkgKCTGIhX47sDKmO11UpHFByVUunPJMDCsHn0/YvnXQAqMQZrFOn0/GoqFSnCmlcCkSyjmxHJpPB2rVrkU6n1WCjZEzHFToKcoXf6XRQLpeV7WpxcRGtVktpBObm5tBoNJQUKyMNpO3KNKnLOvJc0KCSKwj5zuTf+ru3sLCIhrC8y59MJuO5j5C8S0i1v+Qxk9mWmlKT9pYre06E0q9AcroOnXfJs+RaP96VPg98hlwsmvqN9SXvMopM512GZUvebbfbqFQqKpJgcXERjuOgUqmg3W5jbm4O9Xp9YN7VF1r9JnPOX0G8OxZhwFSoVM93u10Vh8+BydUnsFzalE4ihJ7EQk6g7CSWKXMYLGvU/z8opfRFYUA3RbBtUkvR6XTUoCTo7CLNE6Z2SVsWJWk/jQdVenToodAjJXz5YUkplSooTvoclI7jYGlpSQkK9XrdY8OTYZV8h37vWE70JpWi/Fvv20EGo4WFhRdReZfcK3lJ/q2H+AH+vAsc8hsi78pJWK+nDL/mjx/v6hOX5F05b1D7If0M2Cbd3u6nmdX7gXWlyYH9woWr9NeS+QLYnkajsYx3W60WyuWyMtuOknf18+Pi3aHNBHRW4Qpc2tqlXUq+CEpuesy+yemDjZSrVn2Fr0OqyPw85iX0/6mu4u6DxWIRAFRKTgAqPEeqxCj5SadItk+GrMRiMRUORO9XXbVOJ0lO7IwwqNVqaLVamJ2dxfz8PObn57Fr1y60Wi0sLS15bFFUaem+FXLQ6Oo1WWdZX3k8rDRqBQELi/FgVLzrp1I28S6AZdoDCcm70nzL8iRM3CBNA+TdXq+nQtYBqPBB2Q45J5B39XoQjDjIZDKq/6RWJJVKodlsYmlpyaMZ0Hl3bm5uxXg3jPZ1FBrYgYQBKeFRagQOeZWa1Bu6utzkRTkMTIPN7xl+k5U+4KkdcBwH2WwW7XYb09PTns2O5GpevnBpwjBJsQxpAeD5OOPxQ3kKOPnrIYKO42BhYQH79+/H3r17sX37djiOg3K5rHwiWB8/CZrt5P9+KxD529SPOpFYQcDCYjwYlnc5QR5JvJvP59HpdNTuhzRRSD+wMCYMgiHcUstAzg7iXZoNFhYWMDs768u7Unvcj3f9hKqwvKv7fJj6NgoGDi3sN6D081LK8auwLgnpx3UJys+xxHQsaLLzqwdVcfV6XdnlJycnPQ4oVCfJsBRdWyBtQPSOleophggxFWa5XEYsFsPi4qIalFRF7d27F7VaDS+//DL279+PxcVFJb3qtqd+bdc9bf2EgjADjO9V/x2lDAsLCy9Gybt+OJx5d35+Hs1mU03g1JpyscOJl5M7wwvJbzrvypw25F2Wt7S0pH5zk7darYZms4k9e/agWq1i586d2Ldvn+JdOvixXVL46Me7/dT6/TjT9L6G4d2hNyoyPdB0ra4RMDXEpI7mOV0K0hNd9KuvqY792sSJmt6jTPLBWFumQaZ6joNP2uTkMxgGQ0cZAMopRWYJ4wdQqVRQLpfRbDaVYLB161YsLi7i5ZdfxuzsrFJP6YNMn+hN7fZ7B34xun59Kcv2s11ZYcDCIjqOdt5lQrZ8Pg/HcVSyJWB5uKNpb5VYLKZSHlMgAKAcrhOJBEqlElzXVSbZSqWCSqWieLdarWLr1q1YWFiIzLtSczEq3u3Hv/q1Ybk3sgPhMComXTtgkjCDGjrsc6NAf3l8+bQjMeUk4HVYpIlADlK5jwLjZV330OYXHNS0gzEHA3+YcrharWJpaUlFEuhOKX4fNc/162PTwAnquyj9aoUBC4voGDfvyuMm+N0T5blRoPMuHQgbjQYymYziXdd1FW9K3pUaAxPvSqFBhkIy/I+OgbVaDY1GA0tLS6jVaiiXyyqSIArvhumPKLzrp23w0yyMRTNAG3g/ydAPuqRiEgT8VFDDfghBai5ZNx7XQz663UNpgA8cOIBms6kGF/foZppMOrrI3Ae5XA7T09PIZDKYmJhAJpPB4uIiKpWKSqPJZzBEhaEri4uLKJfL2LNnD2q1GrZt24ZKpaI8WaX0TpgiONgPfit2vY9M/TgorCBgYTEYxs27Jo49XHnXcZxlvMsdbsm7cpHFVM2SdxcWFjy82+v1PLxbqVQU71YqFezevRvVahUvvvji2HlXtl3vI1P/BmEQU8HQ0QTA4INmUIlzGIR5np8djJ6ilE5pLtCTRNChkL4FUkKVajtp0wLgSS9MCbVaraJWq6FSqShplTGsQRJh2EFlOifvD5J0LSwsVg9HO+9ywaX/AIdMHHpiIanKpxZBanwl79JMS78BE+/qGtdheFdv96CCgH5/2HcdWhiQoXF6pfxsIFIa1L069ev9Kq2f0yXcsIPMVEdO2EHP5jWcuGnDL5VKKoVlsVhU8bvAwV0D0+k0JicnPRqBWOygUyCFhXw+r1Jc1ut17NixA/V6HXNzc6jVajhw4ABmZ2extLSE3bt3K89V7u2tx/vKNsi2mfqRfRKkStL7PIq0aVJFWlhYRMM4eFdfla4G7/qdl9eYeJehgd1uV2lUZTRFu9328G6pVFJOgeTdQqGAbrer8gFI3q3X65idnQ3Fu3pfAyvDu0Hm3igmIB0jcSDUIRvQb7LtV47+d1i1dr9zpjrqkp1J1cVYUkqQuVxOfXAcbEwRygmfJgRmtmq1WsqhhZJpo9FQzir0VF1YWMDc3BzK5TLm5uZUwiHau+QWykH1DuqTfn4Epj5kP4UdZFa7YGExGMbBu7zWdN7Pdj1q3tX5Kirv5vN5JSyQdyU3cidHbu3MnQQLhYLibIYM6ry7uLioeHd+fl45kJt419S2IOjvZRS8GyRURNECDWUm8HuIn8QX9v6w56NOMPr10kZl6mQely+Oan8mx2CYIHfPSqVSKjkRMxhyvwCWw4xh3W4XCwsLyh9gdnYW9Xodu3fvVgNxbm5Oxbpyfwe9LWEGVlCfhTnWz9bFY7oAZ4UAC4vRYhS8G1RGP4ySd4PqZ+JdOgOSc8nDOu/Sd6BarapymBW3Vqthfn7ew7u1Ws3Du4zoohAyKO8GLZyG4d0gPh6Ud0fiM6BjnCrhIPtMEEwTvfyt/+13TAoDzJilD1Su+qUwwE2ceC9X+QcOHMCLL76oJNJms4mXX35ZORGWy2VPJq+gDGB6O8NIqX6Te1A5QQMyjNBgYWExeqwU70aBrq0Ia2aIyruZTEbxLrWwzCUAHNqvhlkHV5J3Zfv78XO/xZbf71Hw7liEgZVAVBWIXxlRrovH42qSZ84BpiWmZoDpMumcwtzczHZF72B6ps7Nzan4VkYqVKtVpQ0IyhgmJego0qAurfab7E3ngvpKlm99BSwsXjmI4jOg3+P3f7/7JO8y50Amk0EsFlO8K7UFci8AybuxWEw5BJJ36/U69u/fr7YlXgneDTovf5vOBfXVsLx7xAoDwHADMuw9UiJOJBKYmJhAsVjEMcccgzVr1iCXyylTARMKUSBot9tqowp6oVIY2Llzp8pzvWvXLpXdijYx2qnkngqsB1+2rtWIIg2G0QT0U3+Z+ksfkFYzYGHxysJq8W6pVPLwLjUE+XweuVxO7SArNwiiMMBVPTMILiwsYOfOnSqbK4WB1eLdMBrdqAJBVBzRwsBKgTmvGd/KgSi3azbtLiUlVHq8cpAybIWSKpMLNZtNNRhNOyJKmFR3UaTCsANTd+4JgtUMWFhYjAKSd3O5nFpoReFduVEdeZc/rxTe9fN3iworDAhI245EOp3GzMwMcrkcjj/+eExMTGDNmjVKMKDjCuNhOVAdx1EOLHRoOXDgAOr1Onbt2oXZ2VmUy2VlHpifn/ek1JT1YKpKgp60lFjpZGOqf1Bbw2gEokqZVhCwsLAIizC8e9xxx6mQwSDeZeSA5N1Op4P9+/d7eHdpaUnx7sLCgspq6Me7so6HI++OgnMjpyMOU6lXGhKJBLLZrLJXcRBSMuWWx3IwUSrl5M6/9bTCTCrEbS919RRgDrUJ44DTz54V1TTQ71kWFhajxdHOu5z0GRJIriXvMuZfblUseZdaAnLt4uIilpaWPLzrOE5f3vXzlRhEpR9FIIiCYXn4iNYMDCMJ6ZOrBKVBeq4Wi0VMTU2p9JbSN4D+A3QYBKC2Gabtv9lsYm5uDo1GAzt37kS1WsXs7CwWFhbgOA5qtZpRPSXh105KplEcWYIw6IDSVWeDeh9bWFgc3lhJ3p2cnFS8WygUlPNgsVjEzMyMWoTJXAKNRmMZ77788suoVqvYv38/5ufnx8a7w/DnoP1q6tOx+gyMwiYxSkhJKmpdpIrHdC9fdCqVUhqBXC7nCV1hbCuzXAFQebSZkpi7bjUaDaWm2rdvH6rVqkpqIXe/CmqHXz3HAbvSt7A4PHA48q6fWj/svVF4VwoA0k8rm81icnISAFCv1wFA2fwdx1G8e+DAAZVLoFKpYH5+HktLS0Pz7jj4cVhBYlgMnYFwtQfnIDCpemSoCONZs9ksisWikkrpxMItiKkNqFQqavcr/i93vyqXy9i9e7dKMcz0wxy8+oAc1Ds16ruQg0h/fpSyBvV+tbCwCMYrnXcBKBs8eTeXy6FUKqkoAXIueTeZTKLb7aJSqSAWi3l4l1EB5N1du3ap8EHy8Sh4N6hdYa43cazfMb9njJp3j2gzwTCQ6hT+cIDQg5WqKKqpdJ8BJg9aXFxEPB5Xma8WFhbUboO0UW3fvl0luGi1WiqO1TQgpfTsd97UlkEkxLCeqFHLHYUNzMLC4pUFnXflcfJuoVDA9PS0EgrIu4VCwRM+OD8/j0Qi4eFd7kpI3t22bZvHSXCUvCvbI68L2w/j0PoMw7urIgys5AQRtMrVXzaTBTGEkKopuT0xVVXMF+C6rtrSkmGDcuvh+fl5JbHSQdDPRmUaHH6DzXStbOsgQoEf+pVlzQoWFoc/RvWNhvWaH4Z3mWCIfEvuZSghHQPb7bbiVTplLy4ueniXDoKj5t1RaWeC/BL63TdK3j3iNQNhVrH9BqbrumpToWQyiampKRQKBZRKJUxOTiKfz2NychLZbBalUgm5XE7dy8RCrVYLlUoFrVYLe/fuVaErs7Ozyj5FP4Jer6c8YmXKS6kSMsW6yh9Zf17L43oY4rAIM+hM11jtgIXFKxPj5t2JiQnkcjlMTU2pXV9zuZy6j7zLSIFWq4U9e/ZgaWkJc3Nz2Ldvn/IfOBx5N4zfxTC8e8RoBkaBflJZWLs3O42hKnReoXQqpVRqCFKplCfdpeM4KqdAs9lEpVJRoSv1et2T/EI+M0rb5MTqJ53KgRHU/pV0UpH3WKHAwuLIxrh4l1oBXTOgJxqiczZ5l4swx3FUIqFqtbqivBu2/avBf1GeeUQKA1HUM37XcuMJ2pzS6TTWrVuHbDarpNBSqYSpqSmUSiWceOKJShCIxWJYWFhQ2QMXFhbQaDSwa9cuNBoNtdcAQwulFEmVGAc01WQAfPNhB0l6o5IKgwZ6mDLtRG9hYUEMy7vT09MoFos46aSTFO/G43HMz8/78i63ID4ceTdsOVEdGg8rM8FqSTv9BIJ+NvBYLLYszXCxWFTOgtwFq1AooFgsYmJiQu2GxUHWbDbRaDSUFmBhYQH1et2z0ZD0WuUzOShZx3g87hviYlpZ65JuVNWU3/8se1BnQQsLi5XB4frNReVdTv7SSZucSzOtnlhI5935+fmBedevzmE0moNO1n73ReXeUY+BI1IzAATbXIIGJB1Q6IxSKpWUZLp27VolBGQyGZX+kpsRua6LpaUlNJtN7N69G3v37kW5XMbevXtVgguZSdAUvkKpVKa09Bucfqop2tpM14TpA9l/+rGwaj4/jCpc0cLC4vDDKHg3mUyiWCx6eJdCAP0DuBkRV+/cZnj37t3Ys2dPIO/KiAFiFLzb6/U8iYZGybv9rg+z+A17rR9WRBgYtpJ6OcQgHaDvi10oFHDssccim81iamoK6XRahQ5yUOo5BcrlMvbt24ddu3ZhaWkJu3fvRrvdRr1eN6a1lPWVTithVvp+TitsO6VeeU3YATmMxBsVNuLAwmJlMS7eNaHfMyTvptNplEolxbvT09NIpVIqudDk5KTiXfoJkHf37t2L3bt3Y3FxEXv27EGr1YrMu7om1sSHvIb3j5t3Dwcz7FiFAT+19ChWiCa7dljHlVgsplRR9FZNp9OeEELmEaDnKnNY7927F4uLi5idncXi4qLKb93pdALTWo4KUVRTUe1P/coc5r1ZQcDCYmWwkrwbtlzJuxMTE5iYmPA4C0r+pTaAvMsIrcXFRRw4cACLi4uoVqsD826Y+vqFFga1L0r5YTHKd9cPYxEGotqygWgTl5TYpISmh4qYyuX1a9aswUknnaTMAnRgYTQBNQeu66odBavVKn7xi1/gwIEDWFhYwNLSEtrtNhqNRijJsF+bTX3gNyjleb++M03gYQa1nwOhXqd+q31ZVysMWFiMF+PmXXnPsLxLHy1qCSgUkHd7vR4cx/Hw7v79+1eFd/38tkxt1Msc1gfAVGaYcgbh3pHvWjgIwg5IPYwuDPQXo2sAZL5rqrDobEJnlXq9rna6YtggN8Mw+QWEqVPQ/2HuMR03CQKDvLewAkOYgToIQVlYWBzE4cC7+rVhoPMuOZd7u0jOlYncJO8yMqtSqSjelcnbVpJ3g+4dlnf7LahkP0b1w4pSj7GaCUydold+UPVHUEf4rWxTqRTWr1+vbFJUU+VyObXhEFNbAlBbDTOdcK1Ww69+9StUKhW02210Op2RDsgoE77f72EwLh8CKwhYWKwcRs27Ya8N4t1169Yp3s3lciqSIIh3y+UyXnrpJdTrdezYsWMsvBtGs2m63nT/MIu6sNcGLcQGEXYkxr5rYdgJO0q5QY0PWqHG43G1AyFtU5RG5d/0SG21WqhWq2pwMolQs9kMjE3th3EJBP2eP8hkH/aeUX8IFhYWryzeZbQAuVZyr+RdJnArl8tHPO8Owq1Rzw1ajo4jLrRQNxXoLyQWixlTSnKPAaa7pF+ANA1wF6xaraZCVnbv3o1qtYrdu3ervbIZniJDUoZRDYV5uWHUVWGfZ2FhYREFQcICeddkR5e8S98s3TRA3qVT4IEDB1T44JHAu68Ubh1YGGAHrISXIxEk1XJAsm76oOTELzfAoHSaTCYRi8VQr9fhugdzCdTrdczOzmLv3r2o1+uYn59Hp9NRZVKi1fNTR8GoBmaUY2HPj8rmZWFhMToczrxrMhME8S5zD3CiX1xcVLy7e/fusfNuVB4NuxA7Unn3iNMM6PDzbJcCQSKRQKFQUOaBZDKp1FZ0WOl0OlhcXPR4sHIbTG5yAcCTdEI+m88Mi3ELAsPCLwphGO/jlSQwCwuL8aCfdjYK73ILePKu3H5Y5109OdCoPPaDzgXx7jjg5yA4aJh4FM5dNWFgkMnB7x7T4JCTVzKZRKlU8qipcrkcJiYm1M5WzG5VqVQwOzurtr/Uw1eSyaQayEFhfWHtWlGkzpUemBLDTuSDRjZYWFiExyi/MT8vdv18P95lmuFkMql2gOVGQ81mE7t27UK1WsW+fftC8+4gbQ6zkDqceHfYRVRUzh2bMDDqjjNJgX4dpa/cgUPpMHk/pcx2u612ufILX5FlyvqYVFV87qhUQYNgkFDNftcOA73vLCwsVgdRJhd57TC8y1W93OK30+movQWi8C6AQN6NgjCcNOgqfFS8O8wiLCrnHhZJh8I0WJ+I5b7Ufhmo5MRM3wD+Zjn1eh27du1CpVLBzp07lfNgu91WZdBpRQ52bo/JTTdk3aIOzFEOymEGz6hX71YAsLAYD0wcs5K867qubwpgnXflToXAwcm8Vqth586dqFaroXmX9w7Cu4Nw7EqaBkbNu0FmaD+MbdfCfgNTvryo0pfeUL0c04Cg0EA1k1RTUULl/tgMcTENRlM75XOjtGlQtdWRhNU0bVhYvJIxiEp7VLzLZ4ThXar7ybutVkvxLncaDMO70mxgefcQgvp/1cwEpoGoV0ZXO4VdRev3UTLVBwjPyeOdTkelsOQGGLVaDQCwf/9+bNu2Dc1mE5VKRe1uxQgDU0fLwSodWzjww7RpnCr5QbUDo6hT0IrFwsJi9AjDu4B3Ql8p3i2Xy2i322ozonq9jn379mFubg7btm1TOQUYMbAavLva/BTl+eO6FhihMKAPSNPANEluYaQ5v/t0Bz79PMEEQvF4XGWwchwHjuOoxBbtdhvtdlupwuQWwaaBzwHrpzUYVvU/zAANe++4B+Gg6ioLC4tw8OPdfjy7UrzrOI7i3Xa7rXiXmV25GZHkXant1f0D/Hh3WM1AmHPDlDvoM4bl3bFoBobtZP18P09V/b4w9jEZeiK3qez1eqjX62i329i3bx9qtRoajQYajQbq9boKYWEnSmFA95jVodvOTG3s1xdRzq0G/CR1njP9rd8nt/u0sLAIh1HwrrxmNXi30+l4eLfZbKrdXoN4N0joALDMZyFIXa5fM8pz40JY3vW7z2+h6oeRb1QUtUMHlVBNZcuJR0qV3Pii1Wphbm4OtVpNpbjsdrtKRSVVUHRO1Ack6yJfkp6DICxGOfhWQqL1u7afQDDIcywsLF45vDs/P496va4it8LwrqkuuklC3h8Wo+LdYfksrBDn9x6CVv9j1QyMArqqfViYVFg8znNS8ozFYkoalV6r9HLl/6bVPge6/nKOhskuSELVrzP9H1VCtbCwGB0OB951HAfdbjcS7wJYZq4dlnePJB6SbR0kOi1qW4fSDIRRXYSRLAdVV+kqeUqUTFDBzS848FzXRbVaRa/X81yjq6b87FR+bfZro1/bwvTJoPD7UEf1LL1N+sDzMxUcSR+hhcXhgnFpCIP4zOQYKM8Py7sy3JBl6Lyr80YUzgnb9lFi0Inbr5wwx/tpBqJy71C7Fo5S3RI0KMNeY7pelyDD2KJkeWHbOIgqbpwTZJB6Kez9Yc9FJSALC4twiCrcrwTC8q4+Qeq86zdxjsIR8JWCcQkXxmvdw2F0WVhYWFhYWKwarIu3hYWFhYXFUQ4rDFhYWFhYWBzlsMKAhYWFhYXFUQ4rDFhYWFhYWBzlsMKAhYWFhYXFUQ4rDFhYWFhYWBzlsMKAhYWFhYXFUQ4rDFhYWFhYWBzlsMKAhYWFhYXFUY7/D4AJ3eMkLl4EAAAAAElFTkSuQmCC\n", "text/plain": ["<Figure size 640x480 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["image_idx = 1\n", "\n", "val_batch = first(val_loader)[\"image\"]\n", "val_image = val_batch[image_idx, None, ...]\n", "\n", "mask = torch.ones_like(val_image)\n", "mask[:, :, 20:40, 30:80] = 0\n", "val_image_masked = val_image * mask\n", "\n", "# plot\n", "plt.subplot(1, 3, 1)\n", "plt.imshow(val_image[0, 0, ...], cmap=\"gray\")\n", "plt.title(\"Original image\")\n", "plt.axis(\"off\")\n", "plt.subplot(1, 3, 2)\n", "plt.imshow(mask[0, 0, ...], cmap=\"gray\")\n", "plt.axis(\"off\")\n", "plt.title(\"Mask\")\n", "plt.subplot(1, 3, 3)\n", "plt.imshow(val_image_masked[0, 0, ...], cmap=\"gray\")\n", "plt.axis(\"off\")\n", "plt.title(\"Masked image\")\n", "plt.show()"]}, {"cell_type": "markdown", "id": "9128242f-8efd-486e-94d8-c060f1218983", "metadata": {}, "source": ["### Inpaint\n", "Inpaint using Algorithm 1 in https://arxiv.org/pdf/2201.09865.\n", "\n", "`num_resample_steps` can be increased to improve the quality of inpainting, with an associated linear increase in inpainting time."]}, {"cell_type": "code", "execution_count": 22, "id": "1427e5d4", "metadata": {"lines_to_next_cell": 2, "tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|█████████████████████████████████████████████████████████████████████████| 1000/1000 [01:10<00:00, 14.26it/s]\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 640x480 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["model.eval()\n", "mask = mask.to(device)\n", "val_image_masked = val_image_masked.to(device)\n", "timesteps = torch.Tensor((999,)).to(noise.device).long()\n", "val_image_inpainted = torch.randn((1, 1, 64, 64)).to(device)\n", "\n", "scheduler.set_timesteps(num_inference_steps=1000)\n", "progress_bar = tqdm(scheduler.timesteps)\n", "\n", "num_resample_steps = 4\n", "with torch.no_grad():\n", "    with autocast(enabled=True):\n", "        for t in progress_bar:\n", "            for u in range(num_resample_steps):\n", "                # get the known portion at t-1\n", "                if t > 0:\n", "                    noise = torch.randn((1, 1, 64, 64)).to(device)\n", "                    timesteps_prev = torch.Tensor((t - 1,)).to(noise.device).long()\n", "                    val_image_inpainted_prev_known = scheduler.add_noise(\n", "                        original_samples=val_image_masked, noise=noise, timesteps=timesteps_prev\n", "                    )\n", "                else:\n", "                    val_image_inpainted_prev_known = val_image_masked\n", "\n", "                # perform a denoising step to get the unknown portion at t-1\n", "                if t > 0:\n", "                    timesteps = torch.Tensor((t,)).to(noise.device).long()\n", "                    model_output = model(val_image_inpainted, timesteps=timesteps)\n", "                    val_image_inpainted_prev_unknown, _ = scheduler.step(model_output, t, val_image_inpainted)\n", "\n", "                # combine known and unknown using the mask\n", "                val_image_inpainted = torch.where(\n", "                    mask == 1, val_image_inpainted_prev_known, val_image_inpainted_prev_unknown\n", "                )\n", "\n", "                # perform resampling\n", "                if t > 0 and u < (num_resample_steps - 1):\n", "                    # sample x_t from x_t-1\n", "                    noise = torch.randn((1, 1, 64, 64)).to(device)\n", "                    val_image_inpainted = (\n", "                        torch.sqrt(1 - scheduler.betas[t - 1]) * val_image_inpainted\n", "                        + torch.sqrt(scheduler.betas[t - 1]) * noise\n", "                    )\n", "\n", "\n", "# plot\n", "plt.subplot(1, 3, 1)\n", "plt.imshow(val_image[0, 0, ...].cpu(), cmap=\"gray\")\n", "plt.title(\"Original image\")\n", "plt.axis(\"off\")\n", "plt.subplot(1, 3, 2)\n", "plt.imshow(val_image_masked[0, 0, ...].cpu(), cmap=\"gray\")\n", "plt.axis(\"off\")\n", "plt.title(\"Masked image\")\n", "plt.subplot(1, 3, 3)\n", "plt.imshow(val_image_inpainted[0, 0, ...].cpu(), cmap=\"gray\")\n", "plt.axis(\"off\")\n", "plt.title(\"Inpainted image\")\n", "plt.show()"]}, {"cell_type": "markdown", "id": "753a8d61-b742-4e0b-b044-9d2d910d4d15", "metadata": {}, "source": ["### Plot"]}, {"cell_type": "markdown", "id": "1c45cead", "metadata": {}, "source": ["### Cleanup data directory\n", "\n", "Remove directory if a temporary was used."]}, {"cell_type": "code", "execution_count": null, "id": "bab2d719", "metadata": {}, "outputs": [], "source": ["if directory is None:\n", "    shutil.rmtree(root_dir)"]}], "metadata": {"jupytext": {"formats": "ipynb,py:percent"}, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.6"}}, "nbformat": 4, "nbformat_minor": 5}