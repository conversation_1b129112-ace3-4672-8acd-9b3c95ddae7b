{"cells": [{"cell_type": "code", "execution_count": null, "id": "bc11fdc9", "metadata": {}, "outputs": [], "source": ["# Copyright (c) MONAI Consortium\n", "# Licensed under the Apache License, Version 2.0 (the \"License\");\n", "# you may not use this file except in compliance with the License.\n", "# You may obtain a copy of the License at\n", "#     http://www.apache.org/licenses/LICENSE-2.0\n", "# Unless required by applicable law or agreed to in writing, software\n", "# distributed under the License is distributed on an \"AS IS\" BASIS,\n", "# WITHOUT WAR<PERSON><PERSON>IES OR CONDITIONS OF ANY KIND, either express or implied.\n", "# See the License for the specific language governing permissions and\n", "# limitations under the License."]}, {"cell_type": "markdown", "id": "95c08725", "metadata": {}, "source": ["# Super-resolution using Stable Diffusion v2 Upscalers\n", "\n", "Tutorial to illustrate the super-resolution task on medical images using Latent Diffusion Models (LDMs) [1]. For that, we will use an autoencoder to obtain a latent representation of the high-resolution images. Then, we train a diffusion model to infer this latent representation when conditioned on a low-resolution image.\n", "\n", "To improve the performance of our models, we will use a method called \"noise conditioning augmentation\" (introduced in [2] and used in Stable Diffusion v2.0 and Imagen Video [3]). During the training, we add noise to the low-resolution images using a random signal-to-noise ratio, and we condition the diffusion models on the amount of noise added. At sampling time, we use a fixed signal-to-noise ratio, representing a small amount of augmentation that aids in removing artefacts in the samples.\n", "\n", "\n", "[1] - <PERSON><PERSON><PERSON> et al. \"High-Resolution Image Synthesis with Latent Diffusion Models\" https://arxiv.org/abs/2112.10752\n", "\n", "[2] - <PERSON> et al. \"Cascaded diffusion models for high fidelity image generation\" https://arxiv.org/abs/2106.15282\n", "\n", "[3] - <PERSON> et al. \"High Definition Video Generation with Diffusion Models\" https://arxiv.org/abs/2210.02303"]}, {"cell_type": "markdown", "id": "b839bf2d", "metadata": {}, "source": ["## Set up environment using Colab\n"]}, {"cell_type": "code", "execution_count": 2, "id": "77f7e633", "metadata": {}, "outputs": [], "source": ["!python -c \"import monai\" || pip install -q \"monai-weekly[tqdm]\"\n", "!python -c \"import matplotlib\" || pip install -q matplotlib\n", "%matplotlib inline"]}, {"cell_type": "markdown", "id": "214066de", "metadata": {}, "source": ["## Set up imports"]}, {"cell_type": "code", "execution_count": 3, "id": "de71fe08", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/media/walter/Storage/Projects/GenerativeModels/venv-pytorch/lib/python3.8/site-packages/tqdm/auto.py:22: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}, {"name": "stdout", "output_type": "stream", "text": ["MONAI version: 1.1.dev2248\n", "Numpy version: 1.24.1\n", "Pytorch version: 1.8.0+cu111\n", "MONAI flags: HAS_EXT = False, USE_COMPILED = False, USE_META_DICT = False\n", "MONAI rev id: 3400bd91422ccba9ccc3aa2ffe7fecd4eb5596bf\n", "MONAI __file__: /media/walter/Storage/Projects/GenerativeModels/venv-pytorch/lib/python3.8/site-packages/monai/__init__.py\n", "\n", "Optional dependencies:\n", "Pytorch Ignite version: NOT INSTALLED or UNKNOWN VERSION.\n", "Nibabel version: 4.0.2\n", "scikit-image version: NOT INSTALLED or UNKNOWN VERSION.\n", "Pillow version: 9.4.0\n", "Tensorboard version: 2.11.0\n", "gdown version: NOT INSTALLED or UNKNOWN VERSION.\n", "TorchVision version: 0.9.0+cu111\n", "tqdm version: 4.64.1\n", "lmdb version: NOT INSTALLED or UNKNOWN VERSION.\n", "psutil version: 5.9.4\n", "pandas version: NOT INSTALLED or UNKNOWN VERSION.\n", "einops version: 0.6.0\n", "transformers version: NOT INSTALLED or UNKNOWN VERSION.\n", "mlflow version: NOT INSTALLED or UNKNOWN VERSION.\n", "pynrrd version: NOT INSTALLED or UNKNOWN VERSION.\n", "\n", "For details about installing the optional dependencies, please visit:\n", "    https://docs.monai.io/en/latest/installation.html#installing-the-recommended-dependencies\n", "\n"]}], "source": ["import os\n", "import shutil\n", "import tempfile\n", "\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "import torch\n", "import torch.nn.functional as F\n", "from monai import transforms\n", "from monai.apps import MedNISTDataset\n", "from monai.config import print_config\n", "from monai.data import CacheDataset, DataLoader\n", "from monai.utils import first, set_determinism\n", "from torch import nn\n", "from torch.cuda.amp import GradScaler, autocast\n", "from tqdm import tqdm\n", "\n", "from generative.losses import PatchAdversarialLoss, PerceptualLoss\n", "from generative.networks.nets import AutoencoderKL, DiffusionModelUNet, PatchDiscriminator\n", "from generative.networks.schedulers import DDPMScheduler\n", "\n", "print_config()"]}, {"cell_type": "code", "execution_count": 4, "id": "9f0a17bc", "metadata": {}, "outputs": [], "source": ["# for reproducibility purposes set a seed\n", "set_determinism(42)"]}, {"cell_type": "markdown", "id": "c0dde922", "metadata": {}, "source": ["## Setup a data directory and download dataset\n", "Specify a MONAI_DATA_DIRECTORY variable, where the data will be downloaded. If not specified a temporary directory will be used."]}, {"cell_type": "code", "execution_count": 5, "id": "ded618a7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/tmp/tmpey9e4kmo\n"]}], "source": ["directory = os.environ.get(\"MONAI_DATA_DIRECTORY\")\n", "root_dir = tempfile.mkdtemp() if directory is None else directory\n", "print(root_dir)"]}, {"cell_type": "markdown", "id": "d80e045b", "metadata": {}, "source": ["## Download the training set"]}, {"cell_type": "code", "execution_count": 6, "id": "c8cf204a", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["MedNIST.tar.gz: 59.0MB [00:03, 15.5MB/s]                                                                                                                                                                                       "]}, {"name": "stdout", "output_type": "stream", "text": ["2023-01-06 00:54:31,600 - INFO - Downloaded: /tmp/tmpey9e4kmo/MedNIST.tar.gz\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["2023-01-06 00:54:31,697 - INFO - Verified 'MedNIST.tar.gz', md5: 0bc7306e7427e00ad1c5526a6677552d.\n", "2023-01-06 00:54:31,697 - INFO - Writing into directory: /tmp/tmpey9e4kmo.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Loading dataset: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 47164/47164 [00:13<00:00, 3508.10it/s]\n"]}], "source": ["train_data = MedNISTDataset(root_dir=root_dir, section=\"training\", download=True, seed=0)\n", "train_datalist = [{\"image\": item[\"image\"]} for item in train_data.data if item[\"class_name\"] == \"HeadCT\"]"]}, {"cell_type": "markdown", "id": "cacdb233", "metadata": {}, "source": ["## Create data loader for training set\n", "\n", "Here, we create the data loader that we will use to train our models. We will use data augmentation and create low-resolution images using MONAI's transformations."]}, {"cell_type": "code", "execution_count": 7, "id": "c7997edf", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Loading dataset: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 7991/7991 [00:04<00:00, 1974.25it/s]\n"]}], "source": ["image_size = 64\n", "train_transforms = transforms.Compose(\n", "    [\n", "        transforms.LoadImaged(keys=[\"image\"]),\n", "        transforms.EnsureChannelFirstd(keys=[\"image\"]),\n", "        transforms.ScaleIntensityRanged(keys=[\"image\"], a_min=0.0, a_max=255.0, b_min=0.0, b_max=1.0, clip=True),\n", "        transforms.RandA<PERSON>ed(\n", "            keys=[\"image\"],\n", "            rotate_range=[(-np.pi / 36, np.pi / 36), (-np.pi / 36, np.pi / 36)],\n", "            translate_range=[(-1, 1), (-1, 1)],\n", "            scale_range=[(-0.05, 0.05), (-0.05, 0.05)],\n", "            spatial_size=[image_size, image_size],\n", "            padding_mode=\"zeros\",\n", "            prob=0.5,\n", "        ),\n", "        transforms.CopyItemsd(keys=[\"image\"], times=1, names=[\"low_res_image\"]),\n", "        transforms.Resized(keys=[\"low_res_image\"], spatial_size=(16, 16)),\n", "    ]\n", ")\n", "train_ds = CacheDataset(data=train_datalist, transform=train_transforms)\n", "train_loader = DataLoader(train_ds, batch_size=32, shuffle=True, num_workers=4, persistent_workers=True)"]}, {"cell_type": "markdown", "id": "166e4242", "metadata": {}, "source": ["## Visualise examples from the training set"]}, {"cell_type": "code", "execution_count": 8, "id": "8c0fe41c", "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 640x480 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Plot 3 examples from the training set\n", "check_data = first(train_loader)\n", "fig, ax = plt.subplots(nrows=1, ncols=3)\n", "for i in range(3):\n", "    ax[i].imshow(check_data[\"image\"][i, 0, :, :], cmap=\"gray\")\n", "    ax[i].axis(\"off\")"]}, {"cell_type": "code", "execution_count": 9, "id": "76412555", "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 640x480 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Plot 3 examples from the training set in low resolution\n", "fig, ax = plt.subplots(nrows=1, ncols=3)\n", "for i in range(3):\n", "    ax[i].imshow(check_data[\"low_res_image\"][i, 0, :, :], cmap=\"gray\")\n", "    ax[i].axis(\"off\")"]}, {"cell_type": "markdown", "id": "6a47b43b", "metadata": {}, "source": ["## Create data loader for validation set"]}, {"cell_type": "code", "execution_count": 10, "id": "8110645e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2023-01-06 00:54:54,252 - INFO - Verified 'MedNIST.tar.gz', md5: 0bc7306e7427e00ad1c5526a6677552d.\n", "2023-01-06 00:54:54,252 - INFO - File exists: /tmp/tmpey9e4kmo/MedNIST.tar.gz, skipped downloading.\n", "2023-01-06 00:54:54,253 - INFO - Non-empty folder exists in /tmp/tmpey9e4kmo/MedNIST, skipped extracting.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Loading dataset: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 5895/5895 [00:01<00:00, 3464.14it/s]\n", "Loading dataset: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 7991/7991 [00:07<00:00, 1077.50it/s]\n"]}], "source": ["val_data = MedNISTDataset(root_dir=root_dir, section=\"validation\", download=True, seed=0)\n", "val_datalist = [{\"image\": item[\"image\"]} for item in val_data.data if item[\"class_name\"] == \"HeadCT\"]\n", "val_transforms = transforms.Compose(\n", "    [\n", "        transforms.LoadImaged(keys=[\"image\"]),\n", "        transforms.EnsureChannelFirstd(keys=[\"image\"]),\n", "        transforms.ScaleIntensityRanged(keys=[\"image\"], a_min=0.0, a_max=255.0, b_min=0.0, b_max=1.0, clip=True),\n", "        transforms.CopyItemsd(keys=[\"image\"], times=1, names=[\"low_res_image\"]),\n", "        transforms.Resized(keys=[\"low_res_image\"], spatial_size=(16, 16)),\n", "    ]\n", ")\n", "val_ds = CacheDataset(data=val_datalist, transform=val_transforms)\n", "val_loader = DataLoader(val_ds, batch_size=32, shuffle=True, num_workers=4)"]}, {"cell_type": "markdown", "id": "9fc99896", "metadata": {}, "source": ["## Define the autoencoder network and training components"]}, {"cell_type": "code", "execution_count": 11, "id": "610bd118", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Using cuda\n"]}], "source": ["device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "print(f\"Using {device}\")"]}, {"cell_type": "code", "execution_count": 12, "id": "0e4ef480", "metadata": {"lines_to_next_cell": 2}, "outputs": [], "source": ["autoencoderkl = AutoencoderKL(\n", "    spatial_dims=2,\n", "    in_channels=1,\n", "    out_channels=1,\n", "    num_channels=(256, 512, 512),\n", "    latent_channels=3,\n", "    num_res_blocks=2,\n", "    norm_num_groups=32,\n", "    attention_levels=(False, False, True),\n", ")\n", "autoencoderkl = autoencoderkl.to(device)\n", "\n", "discriminator = PatchDiscriminator(spatial_dims=2, in_channels=1, num_layers_d=3, num_channels=64)\n", "discriminator = discriminator.to(device)"]}, {"cell_type": "code", "execution_count": 13, "id": "dfd826c6", "metadata": {}, "outputs": [], "source": ["perceptual_loss = PerceptualLoss(spatial_dims=2, network_type=\"alex\")\n", "perceptual_loss.to(device)\n", "perceptual_weight = 0.002\n", "\n", "adv_loss = PatchAdversarialLoss(criterion=\"least_squares\")\n", "adv_weight = 0.005\n", "\n", "optimizer_g = torch.optim.Adam(autoencoderkl.parameters(), lr=5e-5)\n", "optimizer_d = torch.optim.Adam(discriminator.parameters(), lr=1e-4)"]}, {"cell_type": "code", "execution_count": 14, "id": "410911c9", "metadata": {}, "outputs": [], "source": ["scaler_g = GradScaler()\n", "scaler_d = GradScaler()"]}, {"cell_type": "markdown", "id": "c16de505", "metadata": {}, "source": ["## Train Autoencoder"]}, {"cell_type": "code", "execution_count": 15, "id": "830a3979", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Epoch 0: 100%|██████████████████| 250/250 [01:33<00:00,  2.66it/s, recons_loss=0.134, gen_loss=0, disc_loss=0]\n", "Epoch 1: 100%|█████████████████| 250/250 [01:35<00:00,  2.63it/s, recons_loss=0.0626, gen_loss=0, disc_loss=0]\n", "Epoch 2: 100%|█████████████████| 250/250 [01:35<00:00,  2.61it/s, recons_loss=0.0506, gen_loss=0, disc_loss=0]\n", "Epoch 3: 100%|█████████████████| 250/250 [01:36<00:00,  2.59it/s, recons_loss=0.0425, gen_loss=0, disc_loss=0]\n", "Epoch 4: 100%|█████████████████| 250/250 [01:36<00:00,  2.58it/s, recons_loss=0.0393, gen_loss=0, disc_loss=0]\n", "Epoch 5: 100%|█████████████████| 250/250 [01:36<00:00,  2.60it/s, recons_loss=0.0375, gen_loss=0, disc_loss=0]\n", "Epoch 6: 100%|█████████████████| 250/250 [01:35<00:00,  2.60it/s, recons_loss=0.0346, gen_loss=0, disc_loss=0]\n", "Epoch 7: 100%|█████████████████| 250/250 [01:35<00:00,  2.61it/s, recons_loss=0.0319, gen_loss=0, disc_loss=0]\n", "Epoch 8: 100%|█████████████████| 250/250 [01:36<00:00,  2.60it/s, recons_loss=0.0295, gen_loss=0, disc_loss=0]\n", "Epoch 9: 100%|██████████████████| 250/250 [01:36<00:00,  2.60it/s, recons_loss=0.029, gen_loss=0, disc_loss=0]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["epoch 10 val loss: 0.0282\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 200x200 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["Epoch 10: 100%|█████████████████| 250/250 [01:36<00:00,  2.60it/s, recons_loss=0.027, gen_loss=0, disc_loss=0]\n", "Epoch 11: 100%|████████| 250/250 [01:39<00:00,  2.52it/s, recons_loss=0.0261, gen_loss=0.373, disc_loss=0.296]\n", "Epoch 12: 100%|█████████| 250/250 [01:39<00:00,  2.52it/s, recons_loss=0.0261, gen_loss=0.42, disc_loss=0.232]\n", "Epoch 13: 100%|████████| 250/250 [01:39<00:00,  2.52it/s, recons_loss=0.0264, gen_loss=0.367, disc_loss=0.225]\n", "Epoch 14: 100%|████████| 250/250 [01:39<00:00,  2.52it/s, recons_loss=0.0258, gen_loss=0.377, disc_loss=0.228]\n", "Epoch 15: 100%|█████████| 250/250 [01:39<00:00,  2.52it/s, recons_loss=0.0245, gen_loss=0.366, disc_loss=0.22]\n", "Epoch 16: 100%|██████████| 250/250 [01:39<00:00,  2.52it/s, recons_loss=0.0238, gen_loss=0.37, disc_loss=0.22]\n", "Epoch 17: 100%|████████| 250/250 [01:39<00:00,  2.51it/s, recons_loss=0.0236, gen_loss=0.359, disc_loss=0.226]\n", "Epoch 18: 100%|█████████| 250/250 [01:39<00:00,  2.51it/s, recons_loss=0.0225, gen_loss=0.339, disc_loss=0.23]\n", "Epoch 19: 100%|████████| 250/250 [01:39<00:00,  2.51it/s, recons_loss=0.0219, gen_loss=0.345, disc_loss=0.232]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["epoch 20 val loss: 0.0234\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 200x200 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["Epoch 20: 100%|████████| 250/250 [01:39<00:00,  2.52it/s, recons_loss=0.0216, gen_loss=0.352, disc_loss=0.224]\n", "Epoch 21: 100%|████████| 250/250 [01:39<00:00,  2.51it/s, recons_loss=0.0211, gen_loss=0.351, disc_loss=0.222]\n", "Epoch 22: 100%|████████| 250/250 [01:39<00:00,  2.51it/s, recons_loss=0.0208, gen_loss=0.357, disc_loss=0.222]\n", "Epoch 23: 100%|█████████| 250/250 [01:39<00:00,  2.51it/s, recons_loss=0.0205, gen_loss=0.374, disc_loss=0.22]\n", "Epoch 24: 100%|████████| 250/250 [01:39<00:00,  2.51it/s, recons_loss=0.0201, gen_loss=0.368, disc_loss=0.221]\n", "Epoch 25: 100%|██████████| 250/250 [01:39<00:00,  2.51it/s, recons_loss=0.02, gen_loss=0.352, disc_loss=0.222]\n", "Epoch 26: 100%|████████| 250/250 [01:39<00:00,  2.51it/s, recons_loss=0.0196, gen_loss=0.365, disc_loss=0.223]\n", "Epoch 27: 100%|████████| 250/250 [01:39<00:00,  2.51it/s, recons_loss=0.0195, gen_loss=0.361, disc_loss=0.225]\n", "Epoch 28: 100%|████████| 250/250 [01:39<00:00,  2.51it/s, recons_loss=0.0194, gen_loss=0.356, disc_loss=0.226]\n", "Epoch 29: 100%|████████| 250/250 [01:39<00:00,  2.51it/s, recons_loss=0.0191, gen_loss=0.348, disc_loss=0.223]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["epoch 30 val loss: 0.0213\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 200x200 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["Epoch 30: 100%|████████| 250/250 [01:39<00:00,  2.51it/s, recons_loss=0.0188, gen_loss=0.353, disc_loss=0.226]\n", "Epoch 31: 100%|████████| 250/250 [01:39<00:00,  2.51it/s, recons_loss=0.0185, gen_loss=0.336, disc_loss=0.228]\n", "Epoch 32: 100%|████████| 250/250 [01:39<00:00,  2.51it/s, recons_loss=0.0183, gen_loss=0.339, disc_loss=0.231]\n", "Epoch 33: 100%|████████| 250/250 [01:39<00:00,  2.51it/s, recons_loss=0.0181, gen_loss=0.333, disc_loss=0.229]\n", "Epoch 34: 100%|████████| 250/250 [01:39<00:00,  2.50it/s, recons_loss=0.0184, gen_loss=0.338, disc_loss=0.231]\n", "Epoch 35: 100%|████████| 250/250 [01:39<00:00,  2.51it/s, recons_loss=0.0178, gen_loss=0.334, disc_loss=0.229]\n", "Epoch 36: 100%|████████| 250/250 [01:39<00:00,  2.51it/s, recons_loss=0.0178, gen_loss=0.334, disc_loss=0.233]\n", "Epoch 37: 100%|████████| 250/250 [01:39<00:00,  2.51it/s, recons_loss=0.0175, gen_loss=0.329, disc_loss=0.231]\n", "Epoch 38: 100%|████████| 250/250 [01:39<00:00,  2.51it/s, recons_loss=0.0173, gen_loss=0.329, disc_loss=0.232]\n", "Epoch 39: 100%|████████| 250/250 [01:39<00:00,  2.51it/s, recons_loss=0.0177, gen_loss=0.327, disc_loss=0.236]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["epoch 40 val loss: 0.0194\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 200x200 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["Epoch 40: 100%|████████| 250/250 [01:39<00:00,  2.51it/s, recons_loss=0.0169, gen_loss=0.331, disc_loss=0.233]\n", "Epoch 41: 100%|█████████| 250/250 [01:39<00:00,  2.51it/s, recons_loss=0.017, gen_loss=0.328, disc_loss=0.233]\n", "Epoch 42: 100%|█████████| 250/250 [01:39<00:00,  2.51it/s, recons_loss=0.0167, gen_loss=0.32, disc_loss=0.231]\n", "Epoch 43: 100%|████████| 250/250 [01:39<00:00,  2.51it/s, recons_loss=0.0166, gen_loss=0.325, disc_loss=0.233]\n", "Epoch 44: 100%|████████| 250/250 [01:39<00:00,  2.50it/s, recons_loss=0.0165, gen_loss=0.321, disc_loss=0.234]\n", "Epoch 45: 100%|████████| 250/250 [01:39<00:00,  2.51it/s, recons_loss=0.0164, gen_loss=0.317, disc_loss=0.235]\n", "Epoch 46: 100%|████████| 250/250 [01:39<00:00,  2.50it/s, recons_loss=0.0163, gen_loss=0.324, disc_loss=0.236]\n", "Epoch 47: 100%|████████| 250/250 [01:39<00:00,  2.50it/s, recons_loss=0.0162, gen_loss=0.316, disc_loss=0.235]\n", "Epoch 48: 100%|████████| 250/250 [01:40<00:00,  2.50it/s, recons_loss=0.0157, gen_loss=0.319, disc_loss=0.234]\n", "Epoch 49: 100%|████████| 250/250 [01:40<00:00,  2.50it/s, recons_loss=0.0159, gen_loss=0.311, disc_loss=0.235]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["epoch 50 val loss: 0.0172\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 200x200 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["Epoch 50: 100%|████████| 250/250 [01:40<00:00,  2.50it/s, recons_loss=0.0158, gen_loss=0.312, disc_loss=0.237]\n", "Epoch 51: 100%|████████| 250/250 [01:39<00:00,  2.50it/s, recons_loss=0.0156, gen_loss=0.313, disc_loss=0.236]\n", "Epoch 52: 100%|████████| 250/250 [01:39<00:00,  2.50it/s, recons_loss=0.0156, gen_loss=0.308, disc_loss=0.237]\n", "Epoch 53: 100%|████████| 250/250 [01:39<00:00,  2.50it/s, recons_loss=0.0155, gen_loss=0.313, disc_loss=0.237]\n", "Epoch 54: 100%|████████| 250/250 [01:39<00:00,  2.50it/s, recons_loss=0.0152, gen_loss=0.305, disc_loss=0.236]\n", "Epoch 55: 100%|█████████| 250/250 [01:39<00:00,  2.50it/s, recons_loss=0.0152, gen_loss=0.31, disc_loss=0.237]\n", "Epoch 56: 100%|████████| 250/250 [01:40<00:00,  2.50it/s, recons_loss=0.0152, gen_loss=0.306, disc_loss=0.238]\n", "Epoch 57: 100%|████████| 250/250 [01:40<00:00,  2.50it/s, recons_loss=0.0148, gen_loss=0.311, disc_loss=0.237]\n", "Epoch 58: 100%|████████| 250/250 [01:40<00:00,  2.50it/s, recons_loss=0.0148, gen_loss=0.306, disc_loss=0.237]\n", "Epoch 59: 100%|████████| 250/250 [01:40<00:00,  2.50it/s, recons_loss=0.0149, gen_loss=0.306, disc_loss=0.239]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["epoch 60 val loss: 0.0164\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 200x200 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["Epoch 60: 100%|████████| 250/250 [01:39<00:00,  2.50it/s, recons_loss=0.0145, gen_loss=0.308, disc_loss=0.238]\n", "Epoch 61: 100%|████████| 250/250 [01:40<00:00,  2.50it/s, recons_loss=0.0145, gen_loss=0.304, disc_loss=0.237]\n", "Epoch 62: 100%|████████| 250/250 [01:40<00:00,  2.50it/s, recons_loss=0.0147, gen_loss=0.308, disc_loss=0.237]\n", "Epoch 63: 100%|████████| 250/250 [01:40<00:00,  2.49it/s, recons_loss=0.0145, gen_loss=0.307, disc_loss=0.237]\n", "Epoch 64: 100%|████████| 250/250 [01:40<00:00,  2.50it/s, recons_loss=0.0144, gen_loss=0.305, disc_loss=0.237]\n", "Epoch 65: 100%|████████| 250/250 [01:40<00:00,  2.50it/s, recons_loss=0.0141, gen_loss=0.309, disc_loss=0.236]\n", "Epoch 66: 100%|████████| 250/250 [01:40<00:00,  2.50it/s, recons_loss=0.0142, gen_loss=0.304, disc_loss=0.235]\n", "Epoch 67: 100%|██████████| 250/250 [01:40<00:00,  2.50it/s, recons_loss=0.014, gen_loss=0.31, disc_loss=0.238]\n", "Epoch 68: 100%|████████| 250/250 [01:40<00:00,  2.49it/s, recons_loss=0.0139, gen_loss=0.309, disc_loss=0.234]\n", "Epoch 69: 100%|█████████| 250/250 [01:40<00:00,  2.50it/s, recons_loss=0.0138, gen_loss=0.31, disc_loss=0.233]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["epoch 70 val loss: 0.0145\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 200x200 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["Epoch 70: 100%|████████| 250/250 [01:40<00:00,  2.50it/s, recons_loss=0.0139, gen_loss=0.315, disc_loss=0.234]\n", "Epoch 71: 100%|████████| 250/250 [01:40<00:00,  2.50it/s, recons_loss=0.0138, gen_loss=0.314, disc_loss=0.232]\n", "Epoch 72: 100%|█████████| 250/250 [01:40<00:00,  2.49it/s, recons_loss=0.0138, gen_loss=0.32, disc_loss=0.233]\n", "Epoch 73: 100%|████████| 250/250 [01:40<00:00,  2.49it/s, recons_loss=0.0141, gen_loss=0.314, disc_loss=0.231]\n", "Epoch 74: 100%|█████████| 250/250 [01:40<00:00,  2.49it/s, recons_loss=0.0136, gen_loss=0.32, disc_loss=0.229]\n"]}], "source": ["kl_weight = 1e-6\n", "n_epochs = 75\n", "val_interval = 10\n", "autoencoder_warm_up_n_epochs = 10\n", "\n", "for epoch in range(n_epochs):\n", "    autoencoderkl.train()\n", "    discriminator.train()\n", "    epoch_loss = 0\n", "    gen_epoch_loss = 0\n", "    disc_epoch_loss = 0\n", "    progress_bar = tqdm(enumerate(train_loader), total=len(train_loader), ncols=110)\n", "    progress_bar.set_description(f\"Epoch {epoch}\")\n", "    for step, batch in progress_bar:\n", "        images = batch[\"image\"].to(device)\n", "        optimizer_g.zero_grad(set_to_none=True)\n", "\n", "        with autocast(enabled=True):\n", "            reconstruction, z_mu, z_sigma = autoencoderkl(images)\n", "\n", "            recons_loss = F.l1_loss(reconstruction.float(), images.float())\n", "            p_loss = perceptual_loss(reconstruction.float(), images.float())\n", "            kl_loss = 0.5 * torch.sum(z_mu.pow(2) + z_sigma.pow(2) - torch.log(z_sigma.pow(2)) - 1, dim=[1, 2, 3])\n", "            kl_loss = torch.sum(kl_loss) / kl_loss.shape[0]\n", "            loss_g = recons_loss + (kl_weight * kl_loss) + (perceptual_weight * p_loss)\n", "\n", "            if epoch > autoencoder_warm_up_n_epochs:\n", "                logits_fake = discriminator(reconstruction.contiguous().float())[-1]\n", "                generator_loss = adv_loss(logits_fake, target_is_real=True, for_discriminator=False)\n", "                loss_g += adv_weight * generator_loss\n", "\n", "        scaler_g.scale(loss_g).backward()\n", "        scaler_g.step(optimizer_g)\n", "        scaler_g.update()\n", "\n", "        if epoch > autoencoder_warm_up_n_epochs:\n", "            optimizer_d.zero_grad(set_to_none=True)\n", "\n", "            with autocast(enabled=True):\n", "                logits_fake = discriminator(reconstruction.contiguous().detach())[-1]\n", "                loss_d_fake = adv_loss(logits_fake, target_is_real=False, for_discriminator=True)\n", "                logits_real = discriminator(images.contiguous().detach())[-1]\n", "                loss_d_real = adv_loss(logits_real, target_is_real=True, for_discriminator=True)\n", "                discriminator_loss = (loss_d_fake + loss_d_real) * 0.5\n", "\n", "                loss_d = adv_weight * discriminator_loss\n", "\n", "            scaler_d.scale(loss_d).backward()\n", "            scaler_d.step(optimizer_d)\n", "            scaler_d.update()\n", "\n", "        epoch_loss += recons_loss.item()\n", "        if epoch > autoencoder_warm_up_n_epochs:\n", "            gen_epoch_loss += generator_loss.item()\n", "            disc_epoch_loss += discriminator_loss.item()\n", "\n", "        progress_bar.set_postfix(\n", "            {\n", "                \"recons_loss\": epoch_loss / (step + 1),\n", "                \"gen_loss\": gen_epoch_loss / (step + 1),\n", "                \"disc_loss\": disc_epoch_loss / (step + 1),\n", "            }\n", "        )\n", "\n", "    if (epoch + 1) % val_interval == 0:\n", "        autoencoderkl.eval()\n", "        val_loss = 0\n", "        with torch.no_grad():\n", "            for val_step, batch in enumerate(val_loader, start=1):\n", "                images = batch[\"image\"].to(device)\n", "                reconstruction, z_mu, z_sigma = autoencoderkl(images)\n", "                recons_loss = F.l1_loss(images.float(), reconstruction.float())\n", "                val_loss += recons_loss.item()\n", "\n", "        val_loss /= val_step\n", "        print(f\"epoch {epoch + 1} val loss: {val_loss:.4f}\")\n", "\n", "        # ploting reconstruction\n", "        plt.figure(figsize=(2, 2))\n", "        plt.imshow(torch.cat([images[0, 0].cpu(), reconstruction[0, 0].cpu()], dim=1), vmin=0, vmax=1, cmap=\"gray\")\n", "        plt.tight_layout()\n", "        plt.axis(\"off\")\n", "        plt.show()\n", "\n", "progress_bar.close()\n", "\n", "del discriminator\n", "del perceptual_loss\n", "torch.cuda.empty_cache()"]}, {"cell_type": "markdown", "id": "c7108b87", "metadata": {}, "source": ["## Rescaling factor\n", "\n", "As mentioned in <PERSON><PERSON><PERSON> et al. [1] Section 4.3.2 and D.1, the signal-to-noise ratio (induced by the scale of the latent space) became crucial in image-to-image translation models (such as the ones used for super-resolution). For this reason, we will compute the component-wise standard deviation to be used as scaling factor."]}, {"cell_type": "code", "execution_count": 16, "id": "ccb6ba9f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Scaling factor set to 0.9853364825248718\n"]}], "source": ["with torch.no_grad():\n", "    with autocast(enabled=True):\n", "        z = autoencoderkl.encode_stage_2_inputs(check_data[\"image\"].to(device))\n", "\n", "print(f\"Scaling factor set to {1/torch.std(z)}\")\n", "scale_factor = 1 / torch.std(z)"]}, {"cell_type": "markdown", "id": "b386a0c2", "metadata": {}, "source": ["## Train Diffusion Model\n", "\n", "In order to train the diffusion model to perform super-resolution, we will need to concatenate the latent representation of the high-resolution with the low-resolution image. For this, we create a Diffusion model with `in_channels=4`. Since only the outputted latent representation is interesting, we set `out_channels=3`."]}, {"cell_type": "code", "execution_count": 17, "id": "92f3e348", "metadata": {}, "outputs": [], "source": ["unet = DiffusionModelUNet(\n", "    spatial_dims=2,\n", "    in_channels=4,\n", "    out_channels=3,\n", "    num_res_blocks=2,\n", "    num_channels=(256, 256, 512, 1024),\n", "    attention_levels=(False, False, True, True),\n", "    num_head_channels=(0, 0, 64, 64),\n", ")\n", "unet = unet.to(device)\n", "\n", "scheduler = DDPMScheduler(num_train_timesteps=1000, schedule=\"linear_beta\", beta_start=0.0015, beta_end=0.0195)"]}, {"cell_type": "markdown", "id": "8fb22b1a", "metadata": {}, "source": ["As mentioned, we will use the conditioned augmentation (introduced in [2] section 3 and used on Stable Diffusion Upscalers and Imagen Video [3] Section 2.5) as it has been shown critical for cascaded diffusion models, as well for super-resolution tasks. For this, we apply Gaussian noise augmentation to the low-resolution images. We will use a scheduler `low_res_scheduler` to add this noise, with the `t` step defining the signal-to-noise ratio and use the `t` value to condition the diffusion model (inputted using `class_labels` argument)."]}, {"cell_type": "code", "execution_count": 18, "id": "25d9d3e3", "metadata": {}, "outputs": [], "source": ["low_res_scheduler = DDPMScheduler(num_train_timesteps=1000, schedule=\"linear_beta\", beta_start=0.0015, beta_end=0.0195)\n", "\n", "max_noise_level = 350"]}, {"cell_type": "code", "execution_count": 19, "id": "aa959db4", "metadata": {"lines_to_next_cell": 2}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Epoch 0: 100%|██████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.40it/s, loss=0.285]\n", "Epoch 1: 100%|███████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.16]\n", "Epoch 2: 100%|██████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.147]\n", "Epoch 3: 100%|██████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.43it/s, loss=0.147]\n", "Epoch 4: 100%|██████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.43it/s, loss=0.148]\n", "Epoch 5: 100%|██████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.143]\n", "Epoch 6: 100%|██████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.41it/s, loss=0.137]\n", "Epoch 7: 100%|███████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.40it/s, loss=0.14]\n", "Epoch 8: 100%|██████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.138]\n", "Epoch 9: 100%|██████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.41it/s, loss=0.142]\n", "Epoch 10: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.137]\n", "Epoch 11: 100%|█████████████████████████████████████████████████| 250/250 [00:45<00:00,  5.44it/s, loss=0.136]\n", "Epoch 12: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.41it/s, loss=0.136]\n", "Epoch 13: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.40it/s, loss=0.134]\n", "Epoch 14: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.41it/s, loss=0.139]\n", "Epoch 15: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.43it/s, loss=0.136]\n", "Epoch 16: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.134]\n", "Epoch 17: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.136]\n", "Epoch 18: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.135]\n", "Epoch 19: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.40it/s, loss=0.132]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 19 val loss: 0.1380\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|█████████████████████████████████████████████████████████████████████| 1000/1000 [00:37<00:00, 26.64it/s]\n", "/media/walter/Storage/Projects/GenerativeModels/venv-pytorch/lib/python3.8/site-packages/torch/nn/functional.py:3451: UserWarning: Default upsampling behavior when mode=bicubic is changed to align_corners=False since 0.4.0. Please specify align_corners=True if the old behavior is desired. See the documentation of nn.Upsample for details.\n", "  warnings.warn(\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 200x200 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["Epoch 20: 100%|█████████████████████████████████████████████████| 250/250 [00:45<00:00,  5.45it/s, loss=0.131]\n", "Epoch 21: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.43it/s, loss=0.132]\n", "Epoch 22: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.43it/s, loss=0.133]\n", "Epoch 23: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.41it/s, loss=0.136]\n", "Epoch 24: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.43it/s, loss=0.131]\n", "Epoch 25: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.43it/s, loss=0.131]\n", "Epoch 26: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.40it/s, loss=0.128]\n", "Epoch 27: 100%|██████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.13]\n", "Epoch 28: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.43it/s, loss=0.129]\n", "Epoch 29: 100%|██████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.13]\n", "Epoch 30: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.131]\n", "Epoch 31: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.43it/s, loss=0.125]\n", "Epoch 32: 100%|██████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.40it/s, loss=0.13]\n", "Epoch 33: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.41it/s, loss=0.127]\n", "Epoch 34: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.128]\n", "Epoch 35: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.124]\n", "Epoch 36: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.41it/s, loss=0.125]\n", "Epoch 37: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.128]\n", "Epoch 38: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.40it/s, loss=0.127]\n", "Epoch 39: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.127]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 39 val loss: 0.1311\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|█████████████████████████████████████████████████████████████████████| 1000/1000 [00:37<00:00, 26.47it/s]\n", "/media/walter/Storage/Projects/GenerativeModels/venv-pytorch/lib/python3.8/site-packages/torch/nn/functional.py:3451: UserWarning: Default upsampling behavior when mode=bicubic is changed to align_corners=False since 0.4.0. Please specify align_corners=True if the old behavior is desired. See the documentation of nn.Upsample for details.\n", "  warnings.warn(\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 200x200 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["Epoch 40: 100%|█████████████████████████████████████████████████| 250/250 [00:45<00:00,  5.45it/s, loss=0.124]\n", "Epoch 41: 100%|██████████████████████████████████████████████████| 250/250 [00:45<00:00,  5.44it/s, loss=0.13]\n", "Epoch 42: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.126]\n", "Epoch 43: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.41it/s, loss=0.127]\n", "Epoch 44: 100%|██████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.40it/s, loss=0.13]\n", "Epoch 45: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.41it/s, loss=0.126]\n", "Epoch 46: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.124]\n", "Epoch 47: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.43it/s, loss=0.124]\n", "Epoch 48: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.127]\n", "Epoch 49: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.41it/s, loss=0.121]\n", "Epoch 50: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.126]\n", "Epoch 51: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.40it/s, loss=0.123]\n", "Epoch 52: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.125]\n", "Epoch 53: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.121]\n", "Epoch 54: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.41it/s, loss=0.125]\n", "Epoch 55: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.119]\n", "Epoch 56: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.41it/s, loss=0.128]\n", "Epoch 57: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.40it/s, loss=0.125]\n", "Epoch 58: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.126]\n", "Epoch 59: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.43it/s, loss=0.126]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 59 val loss: 0.1261\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|█████████████████████████████████████████████████████████████████████| 1000/1000 [00:36<00:00, 27.19it/s]\n", "/media/walter/Storage/Projects/GenerativeModels/venv-pytorch/lib/python3.8/site-packages/torch/nn/functional.py:3451: UserWarning: Default upsampling behavior when mode=bicubic is changed to align_corners=False since 0.4.0. Please specify align_corners=True if the old behavior is desired. See the documentation of nn.Upsample for details.\n", "  warnings.warn(\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 200x200 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["Epoch 60: 100%|█████████████████████████████████████████████████| 250/250 [00:45<00:00,  5.45it/s, loss=0.124]\n", "Epoch 61: 100%|█████████████████████████████████████████████████| 250/250 [00:45<00:00,  5.44it/s, loss=0.121]\n", "Epoch 62: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.124]\n", "Epoch 63: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.41it/s, loss=0.127]\n", "Epoch 64: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.43it/s, loss=0.125]\n", "Epoch 65: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.123]\n", "Epoch 66: 100%|██████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.43it/s, loss=0.12]\n", "Epoch 67: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.125]\n", "Epoch 68: 100%|██████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.41it/s, loss=0.12]\n", "Epoch 69: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.128]\n", "Epoch 70: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.40it/s, loss=0.121]\n", "Epoch 71: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.43it/s, loss=0.126]\n", "Epoch 72: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.123]\n", "Epoch 73: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.43it/s, loss=0.124]\n", "Epoch 74: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.121]\n", "Epoch 75: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.43it/s, loss=0.125]\n", "Epoch 76: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.40it/s, loss=0.119]\n", "Epoch 77: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.123]\n", "Epoch 78: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.39it/s, loss=0.125]\n", "Epoch 79: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.121]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 79 val loss: 0.1266\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|█████████████████████████████████████████████████████████████████████| 1000/1000 [00:37<00:00, 26.56it/s]\n", "/media/walter/Storage/Projects/GenerativeModels/venv-pytorch/lib/python3.8/site-packages/torch/nn/functional.py:3451: UserWarning: Default upsampling behavior when mode=bicubic is changed to align_corners=False since 0.4.0. Please specify align_corners=True if the old behavior is desired. See the documentation of nn.Upsample for details.\n", "  warnings.warn(\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 200x200 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["Epoch 80: 100%|█████████████████████████████████████████████████| 250/250 [00:45<00:00,  5.45it/s, loss=0.118]\n", "Epoch 81: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.43it/s, loss=0.123]\n", "Epoch 82: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.41it/s, loss=0.122]\n", "Epoch 83: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.123]\n", "Epoch 84: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.41it/s, loss=0.124]\n", "Epoch 85: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.122]\n", "Epoch 86: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.41it/s, loss=0.119]\n", "Epoch 87: 100%|██████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.12]\n", "Epoch 88: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.41it/s, loss=0.123]\n", "Epoch 89: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.39it/s, loss=0.121]\n", "Epoch 90: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.41it/s, loss=0.121]\n", "Epoch 91: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.43it/s, loss=0.122]\n", "Epoch 92: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.41it/s, loss=0.121]\n", "Epoch 93: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.119]\n", "Epoch 94: 100%|██████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.12]\n", "Epoch 95: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.40it/s, loss=0.122]\n", "Epoch 96: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.125]\n", "Epoch 97: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.41it/s, loss=0.121]\n", "Epoch 98: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.41it/s, loss=0.117]\n", "Epoch 99: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.117]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 99 val loss: 0.1227\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|█████████████████████████████████████████████████████████████████████| 1000/1000 [00:37<00:00, 26.90it/s]\n", "/media/walter/Storage/Projects/GenerativeModels/venv-pytorch/lib/python3.8/site-packages/torch/nn/functional.py:3451: UserWarning: Default upsampling behavior when mode=bicubic is changed to align_corners=False since 0.4.0. Please specify align_corners=True if the old behavior is desired. See the documentation of nn.Upsample for details.\n", "  warnings.warn(\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 200x200 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["Epoch 100: 100%|█████████████████████████████████████████████████| 250/250 [00:45<00:00,  5.45it/s, loss=0.12]\n", "Epoch 101: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.40it/s, loss=0.12]\n", "Epoch 102: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.12]\n", "Epoch 103: 100%|████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.118]\n", "Epoch 104: 100%|████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.118]\n", "Epoch 105: 100%|████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.121]\n", "Epoch 106: 100%|████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.121]\n", "Epoch 107: 100%|████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.41it/s, loss=0.124]\n", "Epoch 108: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.40it/s, loss=0.12]\n", "Epoch 109: 100%|████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.41it/s, loss=0.123]\n", "Epoch 110: 100%|████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.41it/s, loss=0.122]\n", "Epoch 111: 100%|████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.121]\n", "Epoch 112: 100%|████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.119]\n", "Epoch 113: 100%|████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.117]\n", "Epoch 114: 100%|████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.40it/s, loss=0.123]\n", "Epoch 115: 100%|████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.118]\n", "Epoch 116: 100%|████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.41it/s, loss=0.117]\n", "Epoch 117: 100%|████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.118]\n", "Epoch 118: 100%|████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.119]\n", "Epoch 119: 100%|████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.119]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 119 val loss: 0.1202\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|█████████████████████████████████████████████████████████████████████| 1000/1000 [00:36<00:00, 27.05it/s]\n", "/media/walter/Storage/Projects/GenerativeModels/venv-pytorch/lib/python3.8/site-packages/torch/nn/functional.py:3451: UserWarning: Default upsampling behavior when mode=bicubic is changed to align_corners=False since 0.4.0. Please specify align_corners=True if the old behavior is desired. See the documentation of nn.Upsample for details.\n", "  warnings.warn(\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 200x200 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["Epoch 120: 100%|█████████████████████████████████████████████████| 250/250 [00:45<00:00,  5.44it/s, loss=0.12]\n", "Epoch 121: 100%|████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.43it/s, loss=0.115]\n", "Epoch 122: 100%|████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.118]\n", "Epoch 123: 100%|████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.43it/s, loss=0.121]\n", "Epoch 124: 100%|████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.41it/s, loss=0.116]\n", "Epoch 125: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.12]\n", "Epoch 126: 100%|████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.118]\n", "Epoch 127: 100%|████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.41it/s, loss=0.118]\n", "Epoch 128: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.41it/s, loss=0.12]\n", "Epoch 129: 100%|████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.41it/s, loss=0.119]\n", "Epoch 130: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.12]\n", "Epoch 131: 100%|████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.115]\n", "Epoch 132: 100%|████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.116]\n", "Epoch 133: 100%|████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.41it/s, loss=0.117]\n", "Epoch 134: 100%|████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.41it/s, loss=0.116]\n", "Epoch 135: 100%|████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.119]\n", "Epoch 136: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.43it/s, loss=0.12]\n", "Epoch 137: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.12]\n", "Epoch 138: 100%|████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.43it/s, loss=0.118]\n", "Epoch 139: 100%|████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.40it/s, loss=0.119]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 139 val loss: 0.1232\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|█████████████████████████████████████████████████████████████████████| 1000/1000 [00:37<00:00, 26.89it/s]\n", "/media/walter/Storage/Projects/GenerativeModels/venv-pytorch/lib/python3.8/site-packages/torch/nn/functional.py:3451: UserWarning: Default upsampling behavior when mode=bicubic is changed to align_corners=False since 0.4.0. Please specify align_corners=True if the old behavior is desired. See the documentation of nn.Upsample for details.\n", "  warnings.warn(\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAKIAAABDCAYAAAAf6t48AAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjYuMiwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8o6BhiAAAACXBIWXMAAA9hAAAPYQGoP6dpAAALtklEQVR4nO1dSW8TPRh+Zk8yVbekC61aVCGkcoQLHPoH+MVwgR9AOSBxLFAk1O1LuqVpZk3mO6DXddxZPEnaJsGPFCWTzNjvjB+/mx1bS5IkgYLCE0N/agEUFABFRIUJgSKiwkRAEVFhIqCIqDARUERUmAgoIipMBBQRFSYCpuyJGxsbY6lQ13VEUQTDMAAAQRDAcRzweXVN09h7v9+/V4amadA0jZVjWRZ6vR7iOGblJknCyuE/jwNJkqDf77MXL5OmadB1HYZhsM90rOt3/b7f7yOKIsRxjDiOB+5T1/WB8sYhO/98kyRhL/63vLENvk3y5BK/C8MQrVarUD5pIo4DSZIgiiLouo4kSaDrOmzbhmEYiKKI3Rw1dB6BkiQZIDM1dr/fZwTo9XoAAMMwch/yMCCyEOEADBCR/0wvuq7f7zN56Tqe0Fn3S7/LECatY/PHZcmdVq74O0/usnhUIgKAaZowDANhGAL4e2OdTgeO48A0Tdi2Dcuy2I3XajXoug7HcWDbNru+3++j3W7j8vISnU4HcRwDACMgkTyOY3Y8CviHbBgGTNOEaZr3CJUHUQbTTH/8/X4fYRii1+uh1+sNkFCsR+ysRSQlmUl+6ixUb5IkrE7S1iSHWL/YKdLIKkvMRycimdBqtYrXr1/j1atXmJ+fZ41CWjMMQ8RxPPAeRRGiKEKSJLBtG67rYmlpCUmS4PT0FL9//8bh4SE8z2PX8JpzVNBDJZK7rotKpTJQPt+YdEzajicUb7J5ghAJu90uut0ue2bUEbIanz8WG5+ewdraGra2tlCpVFiHJuuTRngiYhRF6Ha7rNN7njdwvogirZ16jeykh3H4iPyNGoaBxcVFGIbBiAOAPRR6pfmI1CiWZcE0TczNzWFrawvb29tYXl7G1dUV9vf3cXZ2xkhCxBhFdiqjUqnAdV3Mzc2hVqsNEJE0Ce9D0r3wII3Ev4iIvu/D8zzc3t4iiiIEQYA4jlM1n0jEPHI4jgPHcQaeR5o5pU5CJtxxHMzNzcF1Xei6jk6ng8vLS7TbbURRdK8eXo4wDHF+fl74fB+ViMBdb4njmJkEPsAQH0yaOQLA/EyR3I7j4MWLF9jb28OvX7/w+fNneJ43slYkzQEAtm2jVquhWq2iWq3eC0JE8sVxfE9W0oL0Is1IhOeDmHa7Dd/3S/l1RSazLDRNg2VZqFarWFxcxOLiIsIwxMnJCdrt9oDC4DXixBKRMO5IFvjby/nA5P3791hdXcWnT59wdHQ0UtlpPiJpZFFT8aQVjwl8ECMShXxl27ah6zparRY6nc7QWQAxg5AHnkRZJpbKq9frWFlZge/7aLVauL29vec+yBLx0X1EwrhJCACWZbG0iOM4+PjxI3Z3d/Hy5cuRichHmuTHimZpFJAm1DQN1WoVAFhwNmq5Mn4kf37aZx5kjZrNJm5ubrC8vIz19XW0222cn58PuBGy7fxkGvEhQA+dcpW9Xo+ZvjRfc5R6xBchrdHzyiGQOdc0jfmgruvCsixcXFyg2+1KNWpaFE3EKxtE8NeJ8opwXReNRgNBEKDZbDLXK45j/Pfff4V1PZlGfAhQTw3DkBEQeBjtm5XCKFMf38DkI1J5QRCw78eheYu0YpHMYnAkHnc6HQRBgEajgc3NTRwfH7MAVEq+WdKIpFFs24bneSwICIIAlmWNvb6H/JeFSPRx+dRZhJKVh67Jut40TTQaDSRJglarhTAMpTTiTI0188lyIl6v12Mpi3FDHO4a1wu4M9WUN+VRdgQjzYUoS8CsKFzsHFEUodVqQdM0LC0tSWcrZso0Z41AjNM/fCzkOfvDDs+V/W2YcjVNQxzHuLy8xNraGtbX16XKmimNSHgIn/AxkaUlHwPUmUUNyLsIWYEajzAMcXV1JR31z5RGnDUMEyXLIO8aMVJO+00WNzc3CIJA6tyZ1Ij/EtIIk0eWNI2Xd21WRiDPv+WvU0RUAJBPpCwUadis37OS5zJQRJwSlDG/RVpSjIRl84lZfmFRUl8GykecEoySR0xL22SZUkKWr5h1TdkEuQhFxBnHsASRCUpkSCpbnyLiFKFs4xKG9flGQdkIW/mIU4RRcoqiPygzOpMV6GRNDRsFioj/ALJSNnk+Ytr3eRp51BEgZZqnFEVmOu93kZh5mq4oih6m/jQojTjFKIp6xZxhEWmyyqZzyuYIy2hERcQZg4wZLjtfUvwsY8ZV+uYfQZZ2Et/z/nOSRSj+N9nx7qzvVPpmBpE2IbWMuaXzZa4tMs38cR7pZaFM8xQhrcGHmWFN1xVN55Ix81nflzXNiohTCJkGT5sVM4xvKDOTR+bcIijTPKUYJrFN5rRoKE40w0XEzCKkipr/ARTNkE5DVtK5KPDJiobFuYmjQBFxylDkz40bWUTN06rDBC/KNE8R0iJXmWtEZJnhUaeajRJMKY04RcjSTHmNLZrSvNGRMjNmsrQy74cmSaL+PAXcX25jWlDWzI5zqG2YyDrt+yRJ4DgOnj17JlXeTGlEWhLONE22tBsw+tqITwHSLln/yS4bqIziQ+bVJUbidK6u69jY2ECtVpOqY6Y0Ii1pTMuN0BLIZSdpPgXyRkJGLaPonKL/uMheS885SRIsLy/DcRzpVdhmioi0bLBt29A0ja0IZhjG2JYvfkjwmqcoNSJrjovmE2Zpu7zUTVaekL53XRcLCws4OzvD9fV1rpyEmSJiktytl+37PlvNNWsJ5EkCmWFqTH59bT4IKJpVk+UXi6Yza7RFRmPmdQLbtrG6uorr62tcXV1J3PlfzBQRgTt/kBZe6nQ6sCxr5F0FRIzb1GuaxjoRkZCOaRljOi9v9kzecF5eWoW/rkzkzBPcsiwWnFxcXKTWk4WJJaLMMJHY64lstDyd53lYXV3Fzs4Ovn37NlbZZIMFmeQukdCyLLYcMr9hULvdZkTM0nhZeTwqXzbFw5eXd8x/T+7Qzs4OHMfBz58/B7bDkMGjb/hDPg+ZSj7KJQ3AmyjRpBBoUU76TIiiiG0i9ObNG+zt7WF/f/9B7oWXk5eVPy5K7lKEads2qtUq2zKDL4u2ueDrKJs8LjvHsOg8vrylpSVsb2/DMAwcHByg2+2yNplojZjmgItbl9GWYbR2StqwEr91BRFb13U8f/4cb9++RRzH+PDhA378+DHWhTpJUxXNapHxv3giOo6DSqXCFnHn7ylPlrQyxeM07SkTqadNBSN55ufnsbGxgZWVFbTbbRwcHNxbdF4Wj0pETfu7RQLtG0KLrwdBwEwRv/WZYRiwbRvAXY6Qd7b7/T7bf69er2NnZwfv3r1Dt9vFly9f8OfPHzSbTdTrdXieN5LsYkOQ3yk7clCkrSjS500wEbFMHrSs3yiaeV5r0qY+ZH3oHNd1sba2hs3NTfi+j4ODA7RaLba9nex9D8iRSJ45zqWL4zhGvV7H7u4uGo0GTNPEzc0NW/w7DEMEQcB2oCIRiYzkzFcqFayvr2N1dRWmaeL8/Bxfv37F0dERPM+DZVmoVCrwfX8sq/OTHLz2qlQq9/ZZEc8nmUXwWp72VKF3vlk8z0MYhoX+chaKTGyWXNThNO3vAvO1Wg22bbN7vri4wPHxMcIwHNj0kpcljmM0m83M+glPZpp938fp6Slub2/ZniKO4zDtQGaXNKK4J0mv14Pv+zg5OcH379/RarUGfKm5uTnWsKOSkEBagfYGdF2X7RVIIBLxW5+JoPP5ZLvv+7i9vUW322ULuWeZdtGsyhCNXJmFhQUmM9VPu15Rufw2bqT1SUl0u120Wi1cX18zDci3y7B4kmDFsiz4vo/Dw0MWrJCJFXNfZJbSfETgruHpgdHupLxG4v2tYWROuwc+0uU3GeJJWBTMUCPyMvPrZtP54ha7aWXJEEHXdVQqFSwtLaFard5L8pPMURTB933EcQzf9xEEATzPY5qZ6iMCZslWxkeUNs0KCg+JmZr0oDC9UERUmAgoIipMBBQRFSYCiogKEwFFRIWJgCKiwkRAEVFhIqCIqDAR+B8WhMcZwF1ZmQAAAABJRU5ErkJggg==\n", "text/plain": ["<Figure size 200x200 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["Epoch 140: 100%|████████████████████████████████████████████████| 250/250 [00:45<00:00,  5.46it/s, loss=0.114]\n", "Epoch 141: 100%|████████████████████████████████████████████████| 250/250 [00:45<00:00,  5.44it/s, loss=0.121]\n", "Epoch 142: 100%|████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.43it/s, loss=0.116]\n", "Epoch 143: 100%|████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.119]\n", "Epoch 144: 100%|████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.117]\n", "Epoch 145: 100%|████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.41it/s, loss=0.117]\n", "Epoch 146: 100%|████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.119]\n", "Epoch 147: 100%|████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.43it/s, loss=0.118]\n", "Epoch 148: 100%|████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.43it/s, loss=0.117]\n", "Epoch 149: 100%|████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.43it/s, loss=0.116]\n", "Epoch 150: 100%|████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.116]\n", "Epoch 151: 100%|████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.117]\n", "Epoch 152: 100%|████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.41it/s, loss=0.114]\n", "Epoch 153: 100%|████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.43it/s, loss=0.121]\n", "Epoch 154: 100%|████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.116]\n", "Epoch 155: 100%|████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.114]\n", "Epoch 156: 100%|████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.116]\n", "Epoch 157: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.12]\n", "Epoch 158: 100%|████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.40it/s, loss=0.116]\n", "Epoch 159: 100%|████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.41it/s, loss=0.116]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 159 val loss: 0.1176\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|█████████████████████████████████████████████████████████████████████| 1000/1000 [00:36<00:00, 27.39it/s]\n", "/media/walter/Storage/Projects/GenerativeModels/venv-pytorch/lib/python3.8/site-packages/torch/nn/functional.py:3451: UserWarning: Default upsampling behavior when mode=bicubic is changed to align_corners=False since 0.4.0. Please specify align_corners=True if the old behavior is desired. See the documentation of nn.Upsample for details.\n", "  warnings.warn(\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 200x200 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["Epoch 160: 100%|████████████████████████████████████████████████| 250/250 [00:45<00:00,  5.45it/s, loss=0.119]\n", "Epoch 161: 100%|████████████████████████████████████████████████| 250/250 [00:45<00:00,  5.44it/s, loss=0.115]\n", "Epoch 162: 100%|████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.115]\n", "Epoch 163: 100%|████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.43it/s, loss=0.116]\n", "Epoch 164: 100%|████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.116]\n", "Epoch 165: 100%|████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.41it/s, loss=0.111]\n", "Epoch 166: 100%|████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.115]\n", "Epoch 167: 100%|████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.43it/s, loss=0.118]\n", "Epoch 168: 100%|████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.115]\n", "Epoch 169: 100%|████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.40it/s, loss=0.115]\n", "Epoch 170: 100%|████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.115]\n", "Epoch 171: 100%|████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.41it/s, loss=0.117]\n", "Epoch 172: 100%|████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.116]\n", "Epoch 173: 100%|████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.43it/s, loss=0.119]\n", "Epoch 174: 100%|████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.117]\n", "Epoch 175: 100%|████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.117]\n", "Epoch 176: 100%|████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.118]\n", "Epoch 177: 100%|████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.41it/s, loss=0.115]\n", "Epoch 178: 100%|████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.41it/s, loss=0.114]\n", "Epoch 179: 100%|████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.113]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 179 val loss: 0.1195\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|█████████████████████████████████████████████████████████████████████| 1000/1000 [00:37<00:00, 26.64it/s]\n", "/media/walter/Storage/Projects/GenerativeModels/venv-pytorch/lib/python3.8/site-packages/torch/nn/functional.py:3451: UserWarning: Default upsampling behavior when mode=bicubic is changed to align_corners=False since 0.4.0. Please specify align_corners=True if the old behavior is desired. See the documentation of nn.Upsample for details.\n", "  warnings.warn(\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 200x200 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["Epoch 180: 100%|████████████████████████████████████████████████| 250/250 [00:45<00:00,  5.45it/s, loss=0.115]\n", "Epoch 181: 100%|████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.43it/s, loss=0.112]\n", "Epoch 182: 100%|████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.115]\n", "Epoch 183: 100%|████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.116]\n", "Epoch 184: 100%|████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.115]\n", "Epoch 185: 100%|████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.41it/s, loss=0.115]\n", "Epoch 186: 100%|████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.41it/s, loss=0.117]\n", "Epoch 187: 100%|████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.119]\n", "Epoch 188: 100%|████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.115]\n", "Epoch 189: 100%|████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.117]\n", "Epoch 190: 100%|████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.40it/s, loss=0.114]\n", "Epoch 191: 100%|████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.41it/s, loss=0.114]\n", "Epoch 192: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.43it/s, loss=0.11]\n", "Epoch 193: 100%|████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.112]\n", "Epoch 194: 100%|████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.112]\n", "Epoch 195: 100%|█████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.11]\n", "Epoch 196: 100%|████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.116]\n", "Epoch 197: 100%|████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.41it/s, loss=0.112]\n", "Epoch 198: 100%|████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.42it/s, loss=0.111]\n", "Epoch 199: 100%|████████████████████████████████████████████████| 250/250 [00:46<00:00,  5.43it/s, loss=0.115]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 199 val loss: 0.1122\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|█████████████████████████████████████████████████████████████████████| 1000/1000 [00:36<00:00, 27.27it/s]\n", "/media/walter/Storage/Projects/GenerativeModels/venv-pytorch/lib/python3.8/site-packages/torch/nn/functional.py:3451: UserWarning: Default upsampling behavior when mode=bicubic is changed to align_corners=False since 0.4.0. Please specify align_corners=True if the old behavior is desired. See the documentation of nn.Upsample for details.\n", "  warnings.warn(\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 200x200 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["optimizer = torch.optim.Adam(unet.parameters(), lr=5e-5)\n", "\n", "scaler_diffusion = GradScaler()\n", "\n", "n_epochs = 200\n", "val_interval = 20\n", "epoch_loss_list = []\n", "val_epoch_loss_list = []\n", "\n", "for epoch in range(n_epochs):\n", "    unet.train()\n", "    autoencoderkl.eval()\n", "    epoch_loss = 0\n", "    progress_bar = tqdm(enumerate(train_loader), total=len(train_loader), ncols=110)\n", "    progress_bar.set_description(f\"Epoch {epoch}\")\n", "    for step, batch in progress_bar:\n", "        images = batch[\"image\"].to(device)\n", "        low_res_image = batch[\"low_res_image\"].to(device)\n", "        optimizer.zero_grad(set_to_none=True)\n", "\n", "        with autocast(enabled=True):\n", "            with torch.no_grad():\n", "                latent = autoencoderkl.encode_stage_2_inputs(images) * scale_factor\n", "\n", "            # Noise augmentation\n", "            noise = torch.randn_like(latent).to(device)\n", "            low_res_noise = torch.randn_like(low_res_image).to(device)\n", "            timesteps = torch.randint(0, scheduler.num_train_timesteps, (latent.shape[0],), device=latent.device).long()\n", "            low_res_timesteps = torch.randint(\n", "                0, max_noise_level, (low_res_image.shape[0],), device=low_res_image.device\n", "            ).long()\n", "\n", "            noisy_latent = scheduler.add_noise(original_samples=latent, noise=noise, timesteps=timesteps)\n", "            noisy_low_res_image = scheduler.add_noise(\n", "                original_samples=low_res_image, noise=low_res_noise, timesteps=low_res_timesteps\n", "            )\n", "\n", "            latent_model_input = torch.cat([noisy_latent, noisy_low_res_image], dim=1)\n", "\n", "            noise_pred = unet(x=latent_model_input, timesteps=timesteps, class_labels=low_res_timesteps)\n", "            loss = F.mse_loss(noise_pred.float(), noise.float())\n", "\n", "        scaler_diffusion.scale(loss).backward()\n", "        scaler_diffusion.step(optimizer)\n", "        scaler_diffusion.update()\n", "\n", "        epoch_loss += loss.item()\n", "\n", "        progress_bar.set_postfix({\"loss\": epoch_loss / (step + 1)})\n", "    epoch_loss_list.append(epoch_loss / (step + 1))\n", "\n", "    if (epoch + 1) % val_interval == 0:\n", "        unet.eval()\n", "        val_loss = 0\n", "        for val_step, batch in enumerate(val_loader, start=1):\n", "            images = batch[\"image\"].to(device)\n", "            low_res_image = batch[\"low_res_image\"].to(device)\n", "\n", "            with torch.no_grad():\n", "                with autocast(enabled=True):\n", "                    latent = autoencoderkl.encode_stage_2_inputs(images) * scale_factor\n", "                    # Noise augmentation\n", "                    noise = torch.randn_like(latent).to(device)\n", "                    low_res_noise = torch.randn_like(low_res_image).to(device)\n", "                    timesteps = torch.randint(\n", "                        0, scheduler.num_train_timesteps, (latent.shape[0],), device=latent.device\n", "                    ).long()\n", "                    low_res_timesteps = torch.randint(\n", "                        0, max_noise_level, (low_res_image.shape[0],), device=low_res_image.device\n", "                    ).long()\n", "\n", "                    noisy_latent = scheduler.add_noise(original_samples=latent, noise=noise, timesteps=timesteps)\n", "                    noisy_low_res_image = scheduler.add_noise(\n", "                        original_samples=low_res_image, noise=low_res_noise, timesteps=low_res_timesteps\n", "                    )\n", "\n", "                    latent_model_input = torch.cat([noisy_latent, noisy_low_res_image], dim=1)\n", "                    noise_pred = unet(x=latent_model_input, timesteps=timesteps, class_labels=low_res_timesteps)\n", "                    loss = F.mse_loss(noise_pred.float(), noise.float())\n", "\n", "            val_loss += loss.item()\n", "        val_loss /= val_step\n", "        val_epoch_loss_list.append(val_loss)\n", "        print(f\"Epoch {epoch} val loss: {val_loss:.4f}\")\n", "\n", "        # Sampling image during training\n", "        sampling_image = low_res_image[0].unsqueeze(0)\n", "        latents = torch.randn((1, 3, 16, 16)).to(device)\n", "        low_res_noise = torch.randn((1, 1, 16, 16)).to(device)\n", "        noise_level = 20\n", "        noise_level = torch.Tensor((noise_level,)).long().to(device)\n", "        noisy_low_res_image = scheduler.add_noise(\n", "            original_samples=sampling_image,\n", "            noise=low_res_noise,\n", "            timesteps=torch.Tensor((noise_level,)).long().to(device),\n", "        )\n", "\n", "        scheduler.set_timesteps(num_inference_steps=1000)\n", "        for t in tqdm(scheduler.timesteps, ncols=110):\n", "            with torch.no_grad():\n", "                with autocast(enabled=True):\n", "                    latent_model_input = torch.cat([latents, noisy_low_res_image], dim=1)\n", "                    noise_pred = unet(\n", "                        x=latent_model_input, timesteps=torch.Tensor((t,)).to(device), class_labels=noise_level\n", "                    )\n", "                latents, _ = scheduler.step(noise_pred, t, latents)\n", "\n", "        with torch.no_grad():\n", "            decoded = autoencoderkl.decode_stage_2_outputs(latents / scale_factor)\n", "\n", "        low_res_bicubic = nn.functional.interpolate(sampling_image, (64, 64), mode=\"bicubic\")\n", "        plt.figure(figsize=(2, 2))\n", "        plt.style.use(\"default\")\n", "        plt.imshow(\n", "            torch.cat([images[0, 0].cpu(), low_res_bicubic[0, 0].cpu(), decoded[0, 0].cpu()], dim=1),\n", "            vmin=0,\n", "            vmax=1,\n", "            cmap=\"gray\",\n", "        )\n", "        plt.tight_layout()\n", "        plt.axis(\"off\")\n", "        plt.show()"]}, {"cell_type": "markdown", "id": "30f24595", "metadata": {}, "source": ["### Plotting sampling example"]}, {"cell_type": "code", "execution_count": 20, "id": "155be091", "metadata": {}, "outputs": [], "source": ["# Sampling image during training\n", "unet.eval()\n", "num_samples = 3\n", "validation_batch = first(val_loader)\n", "\n", "images = validation_batch[\"image\"].to(device)\n", "sampling_image = validation_batch[\"low_res_image\"].to(device)[:num_samples]"]}, {"cell_type": "code", "execution_count": 21, "id": "aaf61020", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|█████████████████████████████████████████████████████████████████████| 1000/1000 [00:36<00:00, 27.49it/s]\n"]}], "source": ["latents = torch.randn((num_samples, 3, 16, 16)).to(device)\n", "low_res_noise = torch.randn((num_samples, 1, 16, 16)).to(device)\n", "noise_level = 10\n", "noise_level = torch.Tensor((noise_level,)).long().to(device)\n", "noisy_low_res_image = scheduler.add_noise(\n", "    original_samples=sampling_image, noise=low_res_noise, timesteps=torch.Tensor((noise_level,)).long().to(device)\n", ")\n", "scheduler.set_timesteps(num_inference_steps=1000)\n", "for t in tqdm(scheduler.timesteps, ncols=110):\n", "    with torch.no_grad():\n", "        with autocast(enabled=True):\n", "            latent_model_input = torch.cat([latents, noisy_low_res_image], dim=1)\n", "            noise_pred = unet(x=latent_model_input, timesteps=torch.Tensor((t,)).to(device), class_labels=noise_level)\n", "\n", "        # 2. compute previous image: x_t -> x_t-1\n", "        latents, _ = scheduler.step(noise_pred, t, latents)\n", "\n", "with torch.no_grad():\n", "    decoded = autoencoderkl.decode_stage_2_outputs(latents / scale_factor)"]}, {"cell_type": "code", "execution_count": 30, "id": "32e16e69", "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 800x800 with 9 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["low_res_bicubic = nn.functional.interpolate(sampling_image, (64, 64), mode=\"bicubic\")\n", "fig, axs = plt.subplots(num_samples, 3, figsize=(8, 8))\n", "axs[0, 0].set_title(\"Original image\")\n", "axs[0, 1].set_title(\"Low-resolution Image\")\n", "axs[0, 2].set_title(\"Outputted image\")\n", "for i in range(0, num_samples):\n", "    axs[i, 0].imshow(images[i, 0].cpu(), vmin=0, vmax=1, cmap=\"gray\")\n", "    axs[i, 0].axis(\"off\")\n", "    axs[i, 1].imshow(low_res_bicubic[i, 0].cpu(), vmin=0, vmax=1, cmap=\"gray\")\n", "    axs[i, 1].axis(\"off\")\n", "    axs[i, 2].imshow(decoded[i, 0].cpu(), vmin=0, vmax=1, cmap=\"gray\")\n", "    axs[i, 2].axis(\"off\")\n", "plt.tight_layout()"]}, {"cell_type": "markdown", "id": "7fa52acc", "metadata": {}, "source": ["### Clean-up data directory"]}, {"cell_type": "code", "execution_count": null, "id": "3a6f6d5a", "metadata": {}, "outputs": [], "source": ["if directory is None:\n", "    shutil.rmtree(root_dir)"]}], "metadata": {"jupytext": {"cell_metadata_filter": "-all", "formats": "ipynb,py:percent"}, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.6"}}, "nbformat": 4, "nbformat_minor": 5}