{"cells": [{"cell_type": "code", "execution_count": null, "id": "8f4a5032", "metadata": {}, "outputs": [], "source": ["# Copyright (c) MONAI Consortium\n", "# Licensed under the Apache License, Version 2.0 (the \"License\");\n", "# you may not use this file except in compliance with the License.\n", "# You may obtain a copy of the License at\n", "#     http://www.apache.org/licenses/LICENSE-2.0\n", "# Unless required by applicable law or agreed to in writing, software\n", "# distributed under the License is distributed on an \"AS IS\" BASIS,\n", "# WITHOUT WAR<PERSON><PERSON>IES OR CONDITIONS OF ANY KIND, either express or implied.\n", "# See the License for the specific language governing permissions and\n", "# limitations under the License."]}, {"cell_type": "markdown", "id": "c862ce1e", "metadata": {}, "source": ["# 2D Latent Diffusion Model\n", "\n", "In this tutorial, we will walk through the process of using the MONAI Generative Models package to generate synthetic data using Latent Diffusion Models (LDM)  [1, 2]. Specifically, we will focus on training an LDM to create synthetic X-ray images of hands from the MEDNIST dataset.\n", "\n", "[1] - <PERSON><PERSON><PERSON> et al. \"High-Resolution Image Synthesis with Latent Diffusion Models\" https://arxiv.org/abs/2112.10752\n", "\n", "[2] - <PERSON><PERSON><PERSON> et al. \"Brain imaging generation with latent diffusion models\" https://arxiv.org/abs/2209.07162\n"]}, {"cell_type": "markdown", "id": "c6801e8b", "metadata": {}, "source": ["### Set up environment"]}, {"cell_type": "code", "execution_count": 1, "id": "10499760", "metadata": {}, "outputs": [], "source": ["!python -c \"import monai\" || pip install -q \"monai-weekly[tqdm]\"\n", "!python -c \"import matplotlib\" || pip install -q matplotlib\n", "%matplotlib inline"]}, {"cell_type": "markdown", "id": "f3da50af", "metadata": {}, "source": ["### Setup imports"]}, {"cell_type": "code", "execution_count": 2, "id": "424a5eb8", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/media/walter/Storage/Projects/GenerativeModels/venv/lib/python3.10/site-packages/tqdm/auto.py:22: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}, {"name": "stdout", "output_type": "stream", "text": ["2023-03-16 19:58:38,469 - A matching Triton is not available, some optimizations will not be enabled.\n", "Error caught was: No module named 'triton'\n", "MONAI version: 1.2.dev2304\n", "Numpy version: 1.23.5\n", "Pytorch version: 1.13.1+cu117\n", "MONAI flags: HAS_EXT = False, USE_COMPILED = False, USE_META_DICT = False\n", "MONAI rev id: 9a57be5aab9f2c2a134768c0c146399150e247a0\n", "MONAI __file__: /media/walter/Storage/Projects/GenerativeModels/venv/lib/python3.10/site-packages/monai/__init__.py\n", "\n", "Optional dependencies:\n", "Pytorch Ignite version: 0.4.10\n", "ITK version: 5.3.0\n", "Nibabel version: 4.0.2\n", "scikit-image version: 0.19.3\n", "Pillow version: 9.3.0\n", "Tensorboard version: 2.11.0\n", "gdown version: 4.6.0\n", "TorchVision version: 0.14.1+cu117\n", "tqdm version: 4.64.1\n", "lmdb version: 1.4.0\n", "psutil version: 5.9.4\n", "pandas version: 1.5.3\n", "einops version: 0.6.0\n", "transformers version: 4.21.3\n", "mlflow version: 2.1.1\n", "pynrrd version: 1.0.0\n", "\n", "For details about installing the optional dependencies, please visit:\n", "    https://docs.monai.io/en/latest/installation.html#installing-the-recommended-dependencies\n", "\n"]}], "source": ["import os\n", "import shutil\n", "import tempfile\n", "\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "import torch\n", "import torch.nn.functional as F\n", "from monai import transforms\n", "from monai.apps import MedNISTDataset\n", "from monai.config import print_config\n", "from monai.data import DataLoader, Dataset\n", "from monai.utils import first, set_determinism\n", "from torch.cuda.amp import GradScaler, autocast\n", "from tqdm import tqdm\n", "\n", "from generative.inferers import LatentDiffusionInferer\n", "from generative.losses.adversarial_loss import PatchAdversarialLoss\n", "from generative.losses.perceptual import PerceptualLoss\n", "from generative.networks.nets import AutoencoderKL, DiffusionModelUNet, PatchDiscriminator\n", "from generative.networks.schedulers import DDPMScheduler\n", "\n", "print_config()"]}, {"cell_type": "markdown", "id": "35141304", "metadata": {}, "source": ["### Set deterministic training for reproducibility"]}, {"cell_type": "code", "execution_count": 3, "id": "1a52c24b", "metadata": {}, "outputs": [], "source": ["set_determinism(42)"]}, {"cell_type": "markdown", "id": "4031e219", "metadata": {}, "source": ["### Setup a data directory and download dataset\n", "Specify a MONAI_DATA_DIRECTORY variable, where the data will be downloaded. If not specified a temporary directory will be used."]}, {"cell_type": "code", "execution_count": 4, "id": "f8a9b6c6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/tmp/tmp2pswnk7b\n"]}], "source": ["directory = os.environ.get(\"MONAI_DATA_DIRECTORY\")\n", "root_dir = tempfile.mkdtemp() if directory is None else directory\n", "print(root_dir)"]}, {"cell_type": "markdown", "id": "bb5ad19e", "metadata": {}, "source": ["## Prepare training set data loader\n", "\n", "Here we will download the MEDNIST dataset and prepare the training set data loader. The MEDNIST dataset contains images of different body parts, including the hand, chest, and abdomen, in grayscale format. For this tutorial, we will use only the `Hand` class. We include data augmentation performed by the `RandAffine` transformation in the data transformations."]}, {"cell_type": "code", "execution_count": 5, "id": "b253ff61", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["MedNIST.tar.gz: 59.0MB [00:03, 15.5MB/s]                                                                                                                                                                                       "]}, {"name": "stdout", "output_type": "stream", "text": ["2023-03-16 19:58:42,503 - INFO - Downloaded: /tmp/tmp2pswnk7b/MedNIST.tar.gz\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["2023-03-16 19:58:42,577 - INFO - Verified 'MedNIST.tar.gz', md5: 0bc7306e7427e00ad1c5526a6677552d.\n", "2023-03-16 19:58:42,577 - INFO - Writing into directory: /tmp/tmp2pswnk7b.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Loading dataset: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 47164/47164 [00:14<00:00, 3270.92it/s]\n"]}], "source": ["train_data = MedNISTDataset(root_dir=root_dir, section=\"training\", download=True, seed=0)\n", "train_datalist = [{\"image\": item[\"image\"]} for item in train_data.data if item[\"class_name\"] == \"Hand\"]\n", "image_size = 64\n", "train_transforms = transforms.Compose(\n", "    [\n", "        transforms.LoadImaged(keys=[\"image\"]),\n", "        transforms.EnsureChannelFirstd(keys=[\"image\"]),\n", "        transforms.ScaleIntensityRanged(keys=[\"image\"], a_min=0.0, a_max=255.0, b_min=0.0, b_max=1.0, clip=True),\n", "        transforms.RandA<PERSON>ed(\n", "            keys=[\"image\"],\n", "            rotate_range=[(-np.pi / 36, np.pi / 36), (-np.pi / 36, np.pi / 36)],\n", "            translate_range=[(-1, 1), (-1, 1)],\n", "            scale_range=[(-0.05, 0.05), (-0.05, 0.05)],\n", "            spatial_size=[image_size, image_size],\n", "            padding_mode=\"zeros\",\n", "            prob=0.5,\n", "        ),\n", "    ]\n", ")\n", "train_ds = Dataset(data=train_datalist, transform=train_transforms)\n", "train_loader = DataLoader(train_ds, batch_size=64, shuffle=True, num_workers=4, persistent_workers=True)"]}, {"cell_type": "markdown", "id": "e81f2290", "metadata": {}, "source": ["### Visualise examples from the training set"]}, {"cell_type": "code", "execution_count": 6, "id": "b4e725bb", "metadata": {"lines_to_next_cell": 2}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 640x480 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Plot 3 examples from the training set\n", "check_data = first(train_loader)\n", "fig, ax = plt.subplots(nrows=1, ncols=3)\n", "for image_n in range(3):\n", "    ax[image_n].imshow(check_data[\"image\"][image_n, 0, :, :], cmap=\"gray\")\n", "    ax[image_n].axis(\"off\")"]}, {"cell_type": "markdown", "id": "dfe1292d", "metadata": {}, "source": ["## Prepare validation set data loader"]}, {"cell_type": "code", "execution_count": 7, "id": "e40efd25", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2023-03-16 19:59:01,645 - INFO - Verified 'MedNIST.tar.gz', md5: 0bc7306e7427e00ad1c5526a6677552d.\n", "2023-03-16 19:59:01,646 - INFO - File exists: /tmp/tmp2pswnk7b/MedNIST.tar.gz, skipped downloading.\n", "2023-03-16 19:59:01,647 - INFO - Non-empty folder exists in /tmp/tmp2pswnk7b/MedNIST, skipped extracting.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Loading dataset: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 5895/5895 [00:01<00:00, 3340.67it/s]\n"]}], "source": ["val_data = MedNISTDataset(root_dir=root_dir, section=\"validation\", download=True, seed=0)\n", "val_datalist = [{\"image\": item[\"image\"]} for item in val_data.data if item[\"class_name\"] == \"Hand\"]\n", "val_transforms = transforms.Compose(\n", "    [\n", "        transforms.LoadImaged(keys=[\"image\"]),\n", "        transforms.EnsureChannelFirstd(keys=[\"image\"]),\n", "        transforms.ScaleIntensityRanged(keys=[\"image\"], a_min=0.0, a_max=255.0, b_min=0.0, b_max=1.0, clip=True),\n", "    ]\n", ")\n", "val_ds = Dataset(data=val_datalist, transform=val_transforms)\n", "val_loader = DataLoader(val_ds, batch_size=64, shuffle=True, num_workers=4, persistent_workers=True)"]}, {"cell_type": "markdown", "id": "c26cfc67", "metadata": {}, "source": ["## Autoencoder KL\n", "\n", "### Define Autoencoder KL network, losses and optimiser\n", "\n", "In this section, we will define an autoencoder with KL-regularization for the LDM. The autoencoder's primary purpose is to transform input images into a latent representation that the diffusion model will subsequently learn. By doing so, we can decrease the computational resources required to train the diffusion component, making this approach suitable for learning high-resolution medical images. We will also specify the perceptual and adversarial losses, including the involved networks, and the optimizers to use during the training process."]}, {"cell_type": "code", "execution_count": 8, "id": "f9044d57", "metadata": {"lines_to_next_cell": 2}, "outputs": [], "source": ["device = torch.device(\"cuda\")\n", "\n", "autoencoderkl = AutoencoderKL(\n", "    spatial_dims=2,\n", "    in_channels=1,\n", "    out_channels=1,\n", "    num_channels=(128, 128, 256),\n", "    latent_channels=3,\n", "    num_res_blocks=2,\n", "    attention_levels=(False, False, False),\n", "    with_encoder_nonlocal_attn=False,\n", "    with_decoder_nonlocal_attn=False,\n", ")\n", "autoencoderkl = autoencoderkl.to(device)"]}, {"cell_type": "code", "execution_count": 9, "id": "177b96fb", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["The parameter 'pretrained' is deprecated since 0.13 and may be removed in the future, please use 'weights' instead.\n", "Arguments other than a weight enum or `None` for 'weights' are deprecated since 0.13 and may be removed in the future. The current behavior is equivalent to passing `weights=AlexNet_Weights.IMAGENET1K_V1`. You can also use `weights=AlexNet_Weights.DEFAULT` to get the most up-to-date weights.\n"]}], "source": ["perceptual_loss = PerceptualLoss(spatial_dims=2, network_type=\"alex\")\n", "perceptual_loss.to(device)\n", "perceptual_weight = 0.001"]}, {"cell_type": "code", "execution_count": 10, "id": "4aef349e", "metadata": {}, "outputs": [], "source": ["discriminator = PatchDiscriminator(spatial_dims=2, num_layers_d=3, num_channels=64, in_channels=1, out_channels=1)\n", "discriminator = discriminator.to(device)\n", "\n", "adv_loss = PatchAdversarialLoss(criterion=\"least_squares\")\n", "adv_weight = 0.01"]}, {"cell_type": "code", "execution_count": 11, "id": "ae58c6a0", "metadata": {}, "outputs": [], "source": ["optimizer_g = torch.optim.Adam(autoencoderkl.parameters(), lr=1e-4)\n", "optimizer_d = torch.optim.Adam(discriminator.parameters(), lr=5e-4)\n", "\n", "# For mixed precision training\n", "scaler_g = torch.cuda.amp.GradScaler()\n", "scaler_d = torch.cuda.amp.GradScaler()"]}, {"cell_type": "markdown", "id": "edb27c18", "metadata": {}, "source": ["### Train model"]}, {"cell_type": "markdown", "id": "ba88de6d", "metadata": {}, "source": ["It takes about ~55 min to train the model."]}, {"cell_type": "code", "execution_count": 12, "id": "4794275a", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Epoch 0: 100%|██████████████████| 125/125 [00:23<00:00,  5.24it/s, recons_loss=0.107, gen_loss=0, disc_loss=0]\n", "Epoch 1: 100%|█████████████████| 125/125 [00:21<00:00,  5.82it/s, recons_loss=0.0513, gen_loss=0, disc_loss=0]\n", "Epoch 2: 100%|█████████████████| 125/125 [00:21<00:00,  5.74it/s, recons_loss=0.0427, gen_loss=0, disc_loss=0]\n", "Epoch 3: 100%|█████████████████| 125/125 [00:21<00:00,  5.75it/s, recons_loss=0.0381, gen_loss=0, disc_loss=0]\n", "Epoch 4: 100%|█████████████████| 125/125 [00:21<00:00,  5.72it/s, recons_loss=0.0349, gen_loss=0, disc_loss=0]\n", "Epoch 5: 100%|█████████████████| 125/125 [00:21<00:00,  5.78it/s, recons_loss=0.0326, gen_loss=0, disc_loss=0]\n", "Epoch 6: 100%|█████████████████| 125/125 [00:21<00:00,  5.76it/s, recons_loss=0.0306, gen_loss=0, disc_loss=0]\n", "Epoch 7: 100%|██████████████████| 125/125 [00:21<00:00,  5.72it/s, recons_loss=0.029, gen_loss=0, disc_loss=0]\n", "Epoch 8: 100%|█████████████████| 125/125 [00:22<00:00,  5.56it/s, recons_loss=0.0277, gen_loss=0, disc_loss=0]\n", "Epoch 9: 100%|█████████████████| 125/125 [00:22<00:00,  5.68it/s, recons_loss=0.0266, gen_loss=0, disc_loss=0]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["epoch 10 val loss: 0.0273\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Epoch 10: 100%|████████████████| 125/125 [00:23<00:00,  5.41it/s, recons_loss=0.0252, gen_loss=0, disc_loss=0]\n", "Epoch 11: 100%|████████| 125/125 [00:24<00:00,  5.05it/s, recons_loss=0.0245, gen_loss=0.602, disc_loss=0.436]\n", "Epoch 12: 100%|████████| 125/125 [00:24<00:00,  5.19it/s, recons_loss=0.0234, gen_loss=0.255, disc_loss=0.254]\n", "Epoch 13: 100%|█████████| 125/125 [00:24<00:00,  5.05it/s, recons_loss=0.0223, gen_loss=0.26, disc_loss=0.258]\n", "Epoch 14: 100%|████████| 125/125 [00:24<00:00,  5.18it/s, recons_loss=0.0216, gen_loss=0.255, disc_loss=0.253]\n", "Epoch 15: 100%|████████| 125/125 [00:24<00:00,  5.19it/s, recons_loss=0.0207, gen_loss=0.257, disc_loss=0.252]\n", "Epoch 16: 100%|████████| 125/125 [00:24<00:00,  5.08it/s, recons_loss=0.0204, gen_loss=0.286, disc_loss=0.261]\n", "Epoch 17: 100%|████████| 125/125 [00:24<00:00,  5.20it/s, recons_loss=0.0201, gen_loss=0.297, disc_loss=0.252]\n", "Epoch 18: 100%|████████| 125/125 [00:23<00:00,  5.22it/s, recons_loss=0.0196, gen_loss=0.296, disc_loss=0.252]\n", "Epoch 19: 100%|████████| 125/125 [00:23<00:00,  5.28it/s, recons_loss=0.0195, gen_loss=0.283, disc_loss=0.248]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["epoch 20 val loss: 0.0220\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Epoch 20: 100%|█████████| 125/125 [00:23<00:00,  5.23it/s, recons_loss=0.019, gen_loss=0.286, disc_loss=0.248]\n", "Epoch 21: 100%|████████| 125/125 [00:23<00:00,  5.25it/s, recons_loss=0.0189, gen_loss=0.287, disc_loss=0.247]\n", "Epoch 22: 100%|████████| 125/125 [00:23<00:00,  5.27it/s, recons_loss=0.0184, gen_loss=0.283, disc_loss=0.245]\n", "Epoch 23: 100%|████████| 125/125 [00:23<00:00,  5.31it/s, recons_loss=0.0183, gen_loss=0.287, disc_loss=0.247]\n", "Epoch 24: 100%|█████████| 125/125 [00:23<00:00,  5.23it/s, recons_loss=0.018, gen_loss=0.275, disc_loss=0.247]\n", "Epoch 25: 100%|████████| 125/125 [00:24<00:00,  5.03it/s, recons_loss=0.0179, gen_loss=0.281, disc_loss=0.248]\n", "Epoch 26: 100%|█████████| 125/125 [00:24<00:00,  5.14it/s, recons_loss=0.017, gen_loss=0.317, disc_loss=0.267]\n", "Epoch 27: 100%|████████| 125/125 [00:25<00:00,  4.92it/s, recons_loss=0.0178, gen_loss=0.274, disc_loss=0.245]\n", "Epoch 28: 100%|█████████| 125/125 [00:24<00:00,  5.10it/s, recons_loss=0.017, gen_loss=0.281, disc_loss=0.248]\n", "Epoch 29: 100%|████████| 125/125 [00:23<00:00,  5.24it/s, recons_loss=0.0172, gen_loss=0.277, disc_loss=0.247]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["epoch 30 val loss: 0.0178\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Epoch 30: 100%|████████| 125/125 [00:24<00:00,  5.19it/s, recons_loss=0.0167, gen_loss=0.283, disc_loss=0.246]\n", "Epoch 31: 100%|████████| 125/125 [00:23<00:00,  5.23it/s, recons_loss=0.0164, gen_loss=0.283, disc_loss=0.245]\n", "Epoch 32: 100%|████████| 125/125 [00:23<00:00,  5.25it/s, recons_loss=0.0165, gen_loss=0.293, disc_loss=0.244]\n", "Epoch 33: 100%|████████| 125/125 [00:23<00:00,  5.23it/s, recons_loss=0.0163, gen_loss=0.283, disc_loss=0.247]\n", "Epoch 34: 100%|████████| 125/125 [00:23<00:00,  5.23it/s, recons_loss=0.0162, gen_loss=0.306, disc_loss=0.242]\n", "Epoch 35: 100%|████████| 125/125 [00:23<00:00,  5.23it/s, recons_loss=0.0176, gen_loss=0.298, disc_loss=0.245]\n", "Epoch 36: 100%|██████████| 125/125 [00:23<00:00,  5.23it/s, recons_loss=0.0161, gen_loss=0.3, disc_loss=0.242]\n", "Epoch 37: 100%|██████████| 125/125 [00:23<00:00,  5.23it/s, recons_loss=0.0159, gen_loss=0.3, disc_loss=0.242]\n", "Epoch 38: 100%|██████████| 125/125 [00:23<00:00,  5.23it/s, recons_loss=0.0162, gen_loss=0.3, disc_loss=0.243]\n", "Epoch 39: 100%|████████| 125/125 [00:23<00:00,  5.22it/s, recons_loss=0.0158, gen_loss=0.307, disc_loss=0.238]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["epoch 40 val loss: 0.0166\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Epoch 40: 100%|████████| 125/125 [00:23<00:00,  5.23it/s, recons_loss=0.0157, gen_loss=0.306, disc_loss=0.245]\n", "Epoch 41: 100%|████████| 125/125 [00:23<00:00,  5.22it/s, recons_loss=0.0156, gen_loss=0.301, disc_loss=0.242]\n", "Epoch 42: 100%|████████| 125/125 [00:23<00:00,  5.23it/s, recons_loss=0.0158, gen_loss=0.301, disc_loss=0.241]\n", "Epoch 43: 100%|████████| 125/125 [00:23<00:00,  5.23it/s, recons_loss=0.0159, gen_loss=0.304, disc_loss=0.241]\n", "Epoch 44: 100%|█████████| 125/125 [00:24<00:00,  5.19it/s, recons_loss=0.0158, gen_loss=0.311, disc_loss=0.24]\n", "Epoch 45: 100%|█████████| 125/125 [00:23<00:00,  5.23it/s, recons_loss=0.016, gen_loss=0.314, disc_loss=0.235]\n", "Epoch 46: 100%|████████| 125/125 [00:23<00:00,  5.22it/s, recons_loss=0.0154, gen_loss=0.303, disc_loss=0.241]\n", "Epoch 47: 100%|████████| 125/125 [00:23<00:00,  5.23it/s, recons_loss=0.0157, gen_loss=0.316, disc_loss=0.234]\n", "Epoch 48: 100%|████████| 125/125 [00:23<00:00,  5.22it/s, recons_loss=0.0156, gen_loss=0.317, disc_loss=0.236]\n", "Epoch 49: 100%|████████| 125/125 [00:23<00:00,  5.21it/s, recons_loss=0.0155, gen_loss=0.323, disc_loss=0.232]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["epoch 50 val loss: 0.0184\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Epoch 50: 100%|█████████| 125/125 [00:23<00:00,  5.23it/s, recons_loss=0.0155, gen_loss=0.32, disc_loss=0.238]\n", "Epoch 51: 100%|████████| 125/125 [00:23<00:00,  5.23it/s, recons_loss=0.0155, gen_loss=0.319, disc_loss=0.233]\n", "Epoch 52: 100%|████████| 125/125 [00:23<00:00,  5.23it/s, recons_loss=0.0156, gen_loss=0.333, disc_loss=0.231]\n", "Epoch 53: 100%|████████| 125/125 [00:23<00:00,  5.23it/s, recons_loss=0.0155, gen_loss=0.327, disc_loss=0.236]\n", "Epoch 54: 100%|████████| 125/125 [00:23<00:00,  5.24it/s, recons_loss=0.0158, gen_loss=0.327, disc_loss=0.242]\n", "Epoch 55: 100%|████████| 125/125 [00:23<00:00,  5.23it/s, recons_loss=0.0156, gen_loss=0.341, disc_loss=0.221]\n", "Epoch 56: 100%|█████████| 125/125 [00:23<00:00,  5.23it/s, recons_loss=0.016, gen_loss=0.355, disc_loss=0.226]\n", "Epoch 57: 100%|████████| 125/125 [00:24<00:00,  5.20it/s, recons_loss=0.0159, gen_loss=0.347, disc_loss=0.227]\n", "Epoch 58: 100%|████████| 125/125 [00:23<00:00,  5.24it/s, recons_loss=0.0159, gen_loss=0.357, disc_loss=0.221]\n", "Epoch 59: 100%|████████| 125/125 [00:23<00:00,  5.23it/s, recons_loss=0.0161, gen_loss=0.358, disc_loss=0.226]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["epoch 60 val loss: 0.0176\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Epoch 60: 100%|█████████| 125/125 [00:23<00:00,  5.23it/s, recons_loss=0.016, gen_loss=0.389, disc_loss=0.215]\n", "Epoch 61: 100%|█████████| 125/125 [00:23<00:00,  5.21it/s, recons_loss=0.0166, gen_loss=0.372, disc_loss=0.22]\n", "Epoch 62: 100%|█████████| 125/125 [00:23<00:00,  5.24it/s, recons_loss=0.017, gen_loss=0.355, disc_loss=0.225]\n", "Epoch 63: 100%|████████| 125/125 [00:23<00:00,  5.22it/s, recons_loss=0.0162, gen_loss=0.375, disc_loss=0.219]\n", "Epoch 64: 100%|████████| 125/125 [00:23<00:00,  5.22it/s, recons_loss=0.0162, gen_loss=0.393, disc_loss=0.204]\n", "Epoch 65: 100%|████████| 125/125 [00:23<00:00,  5.22it/s, recons_loss=0.0163, gen_loss=0.386, disc_loss=0.224]\n", "Epoch 66: 100%|████████| 125/125 [00:23<00:00,  5.22it/s, recons_loss=0.0162, gen_loss=0.384, disc_loss=0.226]\n", "Epoch 67: 100%|████████| 125/125 [00:23<00:00,  5.22it/s, recons_loss=0.0157, gen_loss=0.351, disc_loss=0.228]\n", "Epoch 68: 100%|██████████| 125/125 [00:23<00:00,  5.23it/s, recons_loss=0.016, gen_loss=0.382, disc_loss=0.22]\n", "Epoch 69: 100%|█████████| 125/125 [00:23<00:00,  5.22it/s, recons_loss=0.0163, gen_loss=0.385, disc_loss=0.21]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["epoch 70 val loss: 0.0181\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Epoch 70: 100%|████████| 125/125 [00:23<00:00,  5.22it/s, recons_loss=0.0165, gen_loss=0.392, disc_loss=0.205]\n", "Epoch 71: 100%|████████| 125/125 [00:24<00:00,  5.20it/s, recons_loss=0.0165, gen_loss=0.386, disc_loss=0.215]\n", "Epoch 72: 100%|████████| 125/125 [00:23<00:00,  5.23it/s, recons_loss=0.0162, gen_loss=0.383, disc_loss=0.227]\n", "Epoch 73: 100%|████████| 125/125 [00:23<00:00,  5.22it/s, recons_loss=0.0163, gen_loss=0.396, disc_loss=0.209]\n", "Epoch 74: 100%|████████| 125/125 [00:23<00:00,  5.22it/s, recons_loss=0.0162, gen_loss=0.412, disc_loss=0.208]\n", "Epoch 75: 100%|████████| 125/125 [00:23<00:00,  5.22it/s, recons_loss=0.0162, gen_loss=0.444, disc_loss=0.191]\n", "Epoch 76: 100%|████████| 125/125 [00:23<00:00,  5.21it/s, recons_loss=0.0168, gen_loss=0.409, disc_loss=0.207]\n", "Epoch 77: 100%|█████████| 125/125 [00:23<00:00,  5.23it/s, recons_loss=0.0163, gen_loss=0.402, disc_loss=0.21]\n", "Epoch 78: 100%|██████████| 125/125 [00:23<00:00,  5.23it/s, recons_loss=0.0167, gen_loss=0.427, disc_loss=0.2]\n", "Epoch 79: 100%|████████| 125/125 [00:23<00:00,  5.21it/s, recons_loss=0.0163, gen_loss=0.458, disc_loss=0.195]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["epoch 80 val loss: 0.0181\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Epoch 80: 100%|████████| 125/125 [00:23<00:00,  5.23it/s, recons_loss=0.0168, gen_loss=0.442, disc_loss=0.197]\n", "Epoch 81: 100%|████████| 125/125 [00:23<00:00,  5.23it/s, recons_loss=0.0164, gen_loss=0.428, disc_loss=0.195]\n", "Epoch 82: 100%|████████| 125/125 [00:23<00:00,  5.24it/s, recons_loss=0.0164, gen_loss=0.423, disc_loss=0.203]\n", "Epoch 83: 100%|████████| 125/125 [00:23<00:00,  5.22it/s, recons_loss=0.0162, gen_loss=0.399, disc_loss=0.215]\n", "Epoch 84: 100%|████████| 125/125 [00:23<00:00,  5.21it/s, recons_loss=0.0163, gen_loss=0.393, disc_loss=0.217]\n", "Epoch 85: 100%|█████████| 125/125 [00:24<00:00,  5.20it/s, recons_loss=0.0166, gen_loss=0.45, disc_loss=0.185]\n", "Epoch 86: 100%|████████| 125/125 [00:23<00:00,  5.24it/s, recons_loss=0.0164, gen_loss=0.433, disc_loss=0.197]\n", "Epoch 87: 100%|████████| 125/125 [00:23<00:00,  5.23it/s, recons_loss=0.0164, gen_loss=0.412, disc_loss=0.218]\n", "Epoch 88: 100%|█████████| 125/125 [00:23<00:00,  5.22it/s, recons_loss=0.0159, gen_loss=0.395, disc_loss=0.21]\n", "Epoch 89: 100%|████████| 125/125 [00:24<00:00,  5.21it/s, recons_loss=0.0166, gen_loss=0.426, disc_loss=0.198]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["epoch 90 val loss: 0.0182\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Epoch 90: 100%|████████| 125/125 [00:23<00:00,  5.23it/s, recons_loss=0.0162, gen_loss=0.409, disc_loss=0.215]\n", "Epoch 91: 100%|████████| 125/125 [00:23<00:00,  5.23it/s, recons_loss=0.0158, gen_loss=0.419, disc_loss=0.214]\n", "Epoch 92: 100%|██████████| 125/125 [00:23<00:00,  5.23it/s, recons_loss=0.0157, gen_loss=0.411, disc_loss=0.2]\n", "Epoch 93: 100%|████████| 125/125 [00:23<00:00,  5.23it/s, recons_loss=0.0161, gen_loss=0.407, disc_loss=0.214]\n", "Epoch 94: 100%|████████| 125/125 [00:23<00:00,  5.23it/s, recons_loss=0.0168, gen_loss=0.426, disc_loss=0.194]\n", "Epoch 95: 100%|██████████| 125/125 [00:23<00:00,  5.23it/s, recons_loss=0.016, gen_loss=0.418, disc_loss=0.21]\n", "Epoch 96: 100%|█████████| 125/125 [00:23<00:00,  5.23it/s, recons_loss=0.016, gen_loss=0.432, disc_loss=0.198]\n", "Epoch 97: 100%|███████████| 125/125 [00:23<00:00,  5.24it/s, recons_loss=0.0161, gen_loss=0.43, disc_loss=0.2]\n", "Epoch 98: 100%|████████| 125/125 [00:24<00:00,  5.20it/s, recons_loss=0.0162, gen_loss=0.441, disc_loss=0.209]\n", "Epoch 99: 100%|████████| 125/125 [00:23<00:00,  5.23it/s, recons_loss=0.0162, gen_loss=0.409, disc_loss=0.204]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["epoch 100 val loss: 0.0182\n"]}], "source": ["kl_weight = 1e-6\n", "n_epochs = 100\n", "val_interval = 10\n", "autoencoder_warm_up_n_epochs = 10\n", "\n", "epoch_recon_losses = []\n", "epoch_gen_losses = []\n", "epoch_disc_losses = []\n", "val_recon_losses = []\n", "intermediary_images = []\n", "num_example_images = 4\n", "\n", "for epoch in range(n_epochs):\n", "    autoencoderkl.train()\n", "    discriminator.train()\n", "    epoch_loss = 0\n", "    gen_epoch_loss = 0\n", "    disc_epoch_loss = 0\n", "    progress_bar = tqdm(enumerate(train_loader), total=len(train_loader), ncols=110)\n", "    progress_bar.set_description(f\"Epoch {epoch}\")\n", "    for step, batch in progress_bar:\n", "        images = batch[\"image\"].to(device)\n", "        optimizer_g.zero_grad(set_to_none=True)\n", "\n", "        with autocast(enabled=True):\n", "            reconstruction, z_mu, z_sigma = autoencoderkl(images)\n", "\n", "            recons_loss = F.l1_loss(reconstruction.float(), images.float())\n", "            p_loss = perceptual_loss(reconstruction.float(), images.float())\n", "            kl_loss = 0.5 * torch.sum(z_mu.pow(2) + z_sigma.pow(2) - torch.log(z_sigma.pow(2)) - 1, dim=[1, 2, 3])\n", "            kl_loss = torch.sum(kl_loss) / kl_loss.shape[0]\n", "            loss_g = recons_loss + (kl_weight * kl_loss) + (perceptual_weight * p_loss)\n", "\n", "            if epoch > autoencoder_warm_up_n_epochs:\n", "                logits_fake = discriminator(reconstruction.contiguous().float())[-1]\n", "                generator_loss = adv_loss(logits_fake, target_is_real=True, for_discriminator=False)\n", "                loss_g += adv_weight * generator_loss\n", "\n", "        scaler_g.scale(loss_g).backward()\n", "        scaler_g.step(optimizer_g)\n", "        scaler_g.update()\n", "\n", "        if epoch > autoencoder_warm_up_n_epochs:\n", "            with autocast(enabled=True):\n", "                optimizer_d.zero_grad(set_to_none=True)\n", "\n", "                logits_fake = discriminator(reconstruction.contiguous().detach())[-1]\n", "                loss_d_fake = adv_loss(logits_fake, target_is_real=False, for_discriminator=True)\n", "                logits_real = discriminator(images.contiguous().detach())[-1]\n", "                loss_d_real = adv_loss(logits_real, target_is_real=True, for_discriminator=True)\n", "                discriminator_loss = (loss_d_fake + loss_d_real) * 0.5\n", "\n", "                loss_d = adv_weight * discriminator_loss\n", "\n", "            scaler_d.scale(loss_d).backward()\n", "            scaler_d.step(optimizer_d)\n", "            scaler_d.update()\n", "\n", "        epoch_loss += recons_loss.item()\n", "        if epoch > autoencoder_warm_up_n_epochs:\n", "            gen_epoch_loss += generator_loss.item()\n", "            disc_epoch_loss += discriminator_loss.item()\n", "\n", "        progress_bar.set_postfix(\n", "            {\n", "                \"recons_loss\": epoch_loss / (step + 1),\n", "                \"gen_loss\": gen_epoch_loss / (step + 1),\n", "                \"disc_loss\": disc_epoch_loss / (step + 1),\n", "            }\n", "        )\n", "    epoch_recon_losses.append(epoch_loss / (step + 1))\n", "    epoch_gen_losses.append(gen_epoch_loss / (step + 1))\n", "    epoch_disc_losses.append(disc_epoch_loss / (step + 1))\n", "\n", "    if (epoch + 1) % val_interval == 0:\n", "        autoencoderkl.eval()\n", "        val_loss = 0\n", "        with torch.no_grad():\n", "            for val_step, batch in enumerate(val_loader, start=1):\n", "                images = batch[\"image\"].to(device)\n", "\n", "                with autocast(enabled=True):\n", "                    reconstruction, z_mu, z_sigma = autoencoderkl(images)\n", "                    # Get the first reconstruction from the first validation batch for visualisation purposes\n", "                    if val_step == 1:\n", "                        intermediary_images.append(reconstruction[:num_example_images, 0])\n", "\n", "                    recons_loss = F.l1_loss(images.float(), reconstruction.float())\n", "\n", "                val_loss += recons_loss.item()\n", "\n", "        val_loss /= val_step\n", "        val_recon_losses.append(val_loss)\n", "        print(f\"epoch {epoch + 1} val loss: {val_loss:.4f}\")\n", "progress_bar.close()\n", "\n", "del discriminator\n", "del perceptual_loss\n", "torch.cuda.empty_cache()"]}, {"cell_type": "markdown", "id": "0a89d919", "metadata": {}, "source": ["### Visualise the results from the autoencoderKL"]}, {"cell_type": "code", "execution_count": 13, "id": "f53bccdc", "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 640x480 with 5 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Plot last 5 evaluations\n", "val_samples = np.linspace(n_epochs, val_interval, int(n_epochs / val_interval))\n", "fig, ax = plt.subplots(nrows=5, ncols=1, sharey=True)\n", "for image_n in range(5):\n", "    reconstructions = torch.reshape(intermediary_images[image_n], (image_size * num_example_images, image_size)).T\n", "    ax[image_n].imshow(reconstructions.cpu(), cmap=\"gray\")\n", "    ax[image_n].set_xticks([])\n", "    ax[image_n].set_yticks([])\n", "    ax[image_n].set_ylabel(f\"Epoch {val_samples[image_n]:.0f}\")"]}, {"cell_type": "markdown", "id": "c2f6f3cd", "metadata": {}, "source": ["## Diffusion Model\n", "\n", "### Define diffusion model and scheduler\n", "\n", "In this section, we will define the diffusion model that will learn data distribution of the latent representation of the autoencoder. Together with the diffusion model, we define a beta scheduler responsible for defining the amount of noise tahat is added across the diffusion's model Markov chain."]}, {"cell_type": "code", "execution_count": 14, "id": "58188b0b", "metadata": {}, "outputs": [], "source": ["unet = DiffusionModelUNet(\n", "    spatial_dims=2,\n", "    in_channels=3,\n", "    out_channels=3,\n", "    num_res_blocks=2,\n", "    num_channels=(128, 256, 512),\n", "    attention_levels=(False, True, True),\n", "    num_head_channels=(0, 256, 512),\n", ")\n", "\n", "scheduler = DDPMScheduler(num_train_timesteps=1000, schedule=\"linear_beta\", beta_start=0.0015, beta_end=0.0195)"]}, {"cell_type": "markdown", "id": "ffa7397b", "metadata": {}, "source": ["### Scaling factor\n", "\n", "As mentioned in <PERSON><PERSON><PERSON> et al. [1] Section 4.3.2 and D.1, the signal-to-noise ratio (induced by the scale of the latent space) can affect the results obtained with the LDM, if the standard deviation of the latent space distribution drifts too much from that of a Gaussian. For this reason, it is best practice to use a scaling factor to adapt this standard deviation.\n", "\n", "_Note: In case where the latent space is close to a Gaussian distribution, the scaling factor will be close to one, and the results will not differ from those obtained when it is not used._\n"]}, {"cell_type": "code", "execution_count": 15, "id": "f9bd963f", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Scaling factor set to tensor(0.8888, device='cuda:0')\n"]}], "source": ["with torch.no_grad():\n", "    with autocast(enabled=True):\n", "        z = autoencoderkl.encode_stage_2_inputs(check_data[\"image\"].to(device))\n", "\n", "print(f\"Scaling factor set to {1/torch.std(z)}\")\n", "scale_factor = 1 / torch.std(z)"]}, {"cell_type": "markdown", "id": "4d71b5ca", "metadata": {}, "source": ["We define the inferer using the scale factor:"]}, {"cell_type": "code", "execution_count": 16, "id": "25dfae5f", "metadata": {}, "outputs": [], "source": ["inferer = LatentDiffusionInferer(scheduler, scale_factor=scale_factor)"]}, {"cell_type": "markdown", "id": "286f147e", "metadata": {}, "source": ["### Train diffusion model\n", "\n", "It takes about ~80 min to train the model."]}, {"cell_type": "code", "execution_count": 17, "id": "c3e1f1a0", "metadata": {"scrolled": false}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Epoch 0: 100%|███████████| 125/125 [00:16<00:00,  7.52it/s, loss=0.39]\n", "Epoch 1: 100%|██████████| 125/125 [00:16<00:00,  7.53it/s, loss=0.159]\n", "Epoch 2: 100%|██████████| 125/125 [00:16<00:00,  7.55it/s, loss=0.144]\n", "Epoch 3: 100%|███████████| 125/125 [00:16<00:00,  7.55it/s, loss=0.14]\n", "Epoch 4: 100%|██████████| 125/125 [00:16<00:00,  7.55it/s, loss=0.137]\n", "Epoch 5: 100%|██████████| 125/125 [00:16<00:00,  7.55it/s, loss=0.132]\n", "Epoch 6: 100%|██████████| 125/125 [00:16<00:00,  7.50it/s, loss=0.133]\n", "Epoch 7: 100%|██████████| 125/125 [00:16<00:00,  7.51it/s, loss=0.129]\n", "Epoch 8: 100%|██████████| 125/125 [00:16<00:00,  7.52it/s, loss=0.128]\n", "Epoch 9: 100%|██████████| 125/125 [00:16<00:00,  7.44it/s, loss=0.129]\n", "Epoch 10: 100%|█████████| 125/125 [00:16<00:00,  7.53it/s, loss=0.125]\n", "Epoch 11: 100%|█████████| 125/125 [00:16<00:00,  7.53it/s, loss=0.127]\n", "Epoch 12: 100%|█████████| 125/125 [00:16<00:00,  7.55it/s, loss=0.127]\n", "Epoch 13: 100%|█████████| 125/125 [00:16<00:00,  7.52it/s, loss=0.126]\n", "Epoch 14: 100%|█████████| 125/125 [00:16<00:00,  7.53it/s, loss=0.123]\n", "Epoch 15: 100%|█████████| 125/125 [00:16<00:00,  7.53it/s, loss=0.126]\n", "Epoch 16: 100%|█████████| 125/125 [00:16<00:00,  7.54it/s, loss=0.121]\n", "Epoch 17: 100%|█████████| 125/125 [00:16<00:00,  7.53it/s, loss=0.122]\n", "Epoch 18: 100%|█████████| 125/125 [00:16<00:00,  7.51it/s, loss=0.123]\n", "Epoch 19: 100%|█████████| 125/125 [00:16<00:00,  7.54it/s, loss=0.121]\n", "Epoch 20: 100%|█████████| 125/125 [00:16<00:00,  7.43it/s, loss=0.124]\n", "Epoch 21: 100%|█████████| 125/125 [00:16<00:00,  7.50it/s, loss=0.119]\n", "Epoch 22: 100%|█████████| 125/125 [00:16<00:00,  7.47it/s, loss=0.123]\n", "Epoch 23: 100%|█████████| 125/125 [00:16<00:00,  7.51it/s, loss=0.125]\n", "Epoch 24: 100%|█████████| 125/125 [00:16<00:00,  7.50it/s, loss=0.121]\n", "Epoch 25: 100%|██████████| 125/125 [00:16<00:00,  7.50it/s, loss=0.12]\n", "Epoch 26: 100%|█████████| 125/125 [00:16<00:00,  7.50it/s, loss=0.121]\n", "Epoch 27: 100%|█████████| 125/125 [00:16<00:00,  7.47it/s, loss=0.118]\n", "Epoch 28: 100%|█████████| 125/125 [00:16<00:00,  7.52it/s, loss=0.118]\n", "Epoch 29: 100%|█████████| 125/125 [00:16<00:00,  7.51it/s, loss=0.122]\n", "Epoch 30: 100%|█████████| 125/125 [00:16<00:00,  7.54it/s, loss=0.115]\n", "Epoch 31: 100%|█████████| 125/125 [00:16<00:00,  7.53it/s, loss=0.116]\n", "Epoch 32: 100%|██████████| 125/125 [00:16<00:00,  7.46it/s, loss=0.12]\n", "Epoch 33: 100%|█████████| 125/125 [00:16<00:00,  7.51it/s, loss=0.115]\n", "Epoch 34: 100%|█████████| 125/125 [00:16<00:00,  7.49it/s, loss=0.117]\n", "Epoch 35: 100%|██████████| 125/125 [00:16<00:00,  7.53it/s, loss=0.12]\n", "Epoch 36: 100%|█████████| 125/125 [00:16<00:00,  7.52it/s, loss=0.113]\n", "Epoch 37: 100%|█████████| 125/125 [00:16<00:00,  7.51it/s, loss=0.116]\n", "Epoch 38: 100%|█████████| 125/125 [00:16<00:00,  7.51it/s, loss=0.118]\n", "Epoch 39: 100%|█████████| 125/125 [00:16<00:00,  7.50it/s, loss=0.117]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 39 val loss: 0.1206\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 1000/1000 [00:14<00:00, 67.90it/s]\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 200x200 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["Epoch 40: 100%|█████████| 125/125 [00:16<00:00,  7.61it/s, loss=0.114]\n", "Epoch 41: 100%|█████████| 125/125 [00:16<00:00,  7.55it/s, loss=0.115]\n", "Epoch 42: 100%|█████████| 125/125 [00:16<00:00,  7.54it/s, loss=0.117]\n", "Epoch 43: 100%|█████████| 125/125 [00:16<00:00,  7.46it/s, loss=0.116]\n", "Epoch 44: 100%|█████████| 125/125 [00:16<00:00,  7.51it/s, loss=0.116]\n", "Epoch 45: 100%|█████████| 125/125 [00:16<00:00,  7.41it/s, loss=0.113]\n", "Epoch 46: 100%|█████████| 125/125 [00:16<00:00,  7.41it/s, loss=0.114]\n", "Epoch 47: 100%|█████████| 125/125 [00:17<00:00,  7.35it/s, loss=0.112]\n", "Epoch 48: 100%|█████████| 125/125 [00:16<00:00,  7.38it/s, loss=0.114]\n", "Epoch 49: 100%|█████████| 125/125 [00:16<00:00,  7.48it/s, loss=0.117]\n", "Epoch 50: 100%|█████████| 125/125 [00:16<00:00,  7.45it/s, loss=0.119]\n", "Epoch 51: 100%|█████████| 125/125 [00:16<00:00,  7.49it/s, loss=0.115]\n", "Epoch 52: 100%|█████████| 125/125 [00:16<00:00,  7.47it/s, loss=0.117]\n", "Epoch 53: 100%|█████████| 125/125 [00:16<00:00,  7.47it/s, loss=0.116]\n", "Epoch 54: 100%|█████████| 125/125 [00:16<00:00,  7.47it/s, loss=0.111]\n", "Epoch 55: 100%|█████████| 125/125 [00:16<00:00,  7.40it/s, loss=0.116]\n", "Epoch 56: 100%|█████████| 125/125 [00:16<00:00,  7.47it/s, loss=0.116]\n", "Epoch 57: 100%|█████████| 125/125 [00:16<00:00,  7.50it/s, loss=0.117]\n", "Epoch 58: 100%|█████████| 125/125 [00:16<00:00,  7.52it/s, loss=0.111]\n", "Epoch 59: 100%|█████████| 125/125 [00:16<00:00,  7.52it/s, loss=0.115]\n", "Epoch 60: 100%|█████████| 125/125 [00:16<00:00,  7.50it/s, loss=0.115]\n", "Epoch 61: 100%|█████████| 125/125 [00:16<00:00,  7.50it/s, loss=0.113]\n", "Epoch 62: 100%|█████████| 125/125 [00:16<00:00,  7.52it/s, loss=0.114]\n", "Epoch 63: 100%|█████████| 125/125 [00:16<00:00,  7.52it/s, loss=0.113]\n", "Epoch 64: 100%|█████████| 125/125 [00:16<00:00,  7.54it/s, loss=0.113]\n", "Epoch 65: 100%|█████████| 125/125 [00:16<00:00,  7.54it/s, loss=0.114]\n", "Epoch 66: 100%|█████████| 125/125 [00:16<00:00,  7.44it/s, loss=0.115]\n", "Epoch 67: 100%|█████████| 125/125 [00:16<00:00,  7.49it/s, loss=0.114]\n", "Epoch 68: 100%|█████████| 125/125 [00:16<00:00,  7.49it/s, loss=0.115]\n", "Epoch 69: 100%|██████████| 125/125 [00:16<00:00,  7.51it/s, loss=0.11]\n", "Epoch 70: 100%|█████████| 125/125 [00:16<00:00,  7.55it/s, loss=0.114]\n", "Epoch 71: 100%|██████████| 125/125 [00:16<00:00,  7.50it/s, loss=0.11]\n", "Epoch 72: 100%|█████████| 125/125 [00:16<00:00,  7.52it/s, loss=0.112]\n", "Epoch 73: 100%|█████████| 125/125 [00:16<00:00,  7.49it/s, loss=0.112]\n", "Epoch 74: 100%|█████████| 125/125 [00:16<00:00,  7.45it/s, loss=0.112]\n", "Epoch 75: 100%|█████████| 125/125 [00:16<00:00,  7.49it/s, loss=0.113]\n", "Epoch 76: 100%|█████████| 125/125 [00:16<00:00,  7.55it/s, loss=0.113]\n", "Epoch 77: 100%|█████████| 125/125 [00:16<00:00,  7.50it/s, loss=0.114]\n", "Epoch 78: 100%|█████████| 125/125 [00:16<00:00,  7.45it/s, loss=0.117]\n", "Epoch 79: 100%|█████████| 125/125 [00:16<00:00,  7.54it/s, loss=0.111]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 79 val loss: 0.1165\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 1000/1000 [00:14<00:00, 68.62it/s]\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 200x200 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["Epoch 80: 100%|█████████| 125/125 [00:16<00:00,  7.58it/s, loss=0.114]\n", "Epoch 81: 100%|█████████| 125/125 [00:16<00:00,  7.62it/s, loss=0.112]\n", "Epoch 82: 100%|█████████| 125/125 [00:16<00:00,  7.59it/s, loss=0.112]\n", "Epoch 83: 100%|█████████| 125/125 [00:16<00:00,  7.51it/s, loss=0.118]\n", "Epoch 84: 100%|█████████| 125/125 [00:16<00:00,  7.49it/s, loss=0.115]\n", "Epoch 85: 100%|██████████| 125/125 [00:16<00:00,  7.50it/s, loss=0.11]\n", "Epoch 86: 100%|█████████| 125/125 [00:16<00:00,  7.49it/s, loss=0.112]\n", "Epoch 87: 100%|█████████| 125/125 [00:16<00:00,  7.49it/s, loss=0.113]\n", "Epoch 88: 100%|█████████| 125/125 [00:16<00:00,  7.51it/s, loss=0.113]\n", "Epoch 89: 100%|█████████| 125/125 [00:16<00:00,  7.47it/s, loss=0.108]\n", "Epoch 90: 100%|█████████| 125/125 [00:16<00:00,  7.41it/s, loss=0.112]\n", "Epoch 91: 100%|██████████| 125/125 [00:16<00:00,  7.49it/s, loss=0.11]\n", "Epoch 92: 100%|█████████| 125/125 [00:16<00:00,  7.47it/s, loss=0.108]\n", "Epoch 93: 100%|█████████| 125/125 [00:16<00:00,  7.49it/s, loss=0.111]\n", "Epoch 94: 100%|█████████| 125/125 [00:16<00:00,  7.48it/s, loss=0.107]\n", "Epoch 95: 100%|█████████| 125/125 [00:16<00:00,  7.49it/s, loss=0.115]\n", "Epoch 96: 100%|██████████| 125/125 [00:16<00:00,  7.50it/s, loss=0.11]\n", "Epoch 97: 100%|█████████| 125/125 [00:16<00:00,  7.52it/s, loss=0.111]\n", "Epoch 98: 100%|█████████| 125/125 [00:16<00:00,  7.50it/s, loss=0.111]\n", "Epoch 99: 100%|█████████| 125/125 [00:16<00:00,  7.54it/s, loss=0.109]\n", "Epoch 100: 100%|█████████| 125/125 [00:16<00:00,  7.49it/s, loss=0.11]\n", "Epoch 101: 100%|████████| 125/125 [00:16<00:00,  7.47it/s, loss=0.109]\n", "Epoch 102: 100%|████████| 125/125 [00:16<00:00,  7.51it/s, loss=0.114]\n", "Epoch 103: 100%|█████████| 125/125 [00:16<00:00,  7.55it/s, loss=0.11]\n", "Epoch 104: 100%|████████| 125/125 [00:16<00:00,  7.51it/s, loss=0.112]\n", "Epoch 105: 100%|████████| 125/125 [00:16<00:00,  7.53it/s, loss=0.116]\n", "Epoch 106: 100%|████████| 125/125 [00:16<00:00,  7.52it/s, loss=0.112]\n", "Epoch 107: 100%|█████████| 125/125 [00:16<00:00,  7.52it/s, loss=0.11]\n", "Epoch 108: 100%|█████████| 125/125 [00:16<00:00,  7.54it/s, loss=0.11]\n", "Epoch 109: 100%|████████| 125/125 [00:16<00:00,  7.52it/s, loss=0.111]\n", "Epoch 110: 100%|████████| 125/125 [00:16<00:00,  7.54it/s, loss=0.113]\n", "Epoch 111: 100%|████████| 125/125 [00:16<00:00,  7.50it/s, loss=0.113]\n", "Epoch 112: 100%|█████████| 125/125 [00:16<00:00,  7.55it/s, loss=0.11]\n", "Epoch 113: 100%|████████| 125/125 [00:16<00:00,  7.44it/s, loss=0.111]\n", "Epoch 114: 100%|████████| 125/125 [00:16<00:00,  7.58it/s, loss=0.112]\n", "Epoch 115: 100%|████████| 125/125 [00:16<00:00,  7.51it/s, loss=0.112]\n", "Epoch 116: 100%|████████| 125/125 [00:16<00:00,  7.53it/s, loss=0.106]\n", "Epoch 117: 100%|████████| 125/125 [00:16<00:00,  7.53it/s, loss=0.113]\n", "Epoch 118: 100%|████████| 125/125 [00:16<00:00,  7.55it/s, loss=0.112]\n", "Epoch 119: 100%|████████| 125/125 [00:16<00:00,  7.56it/s, loss=0.112]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 119 val loss: 0.1217\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 1000/1000 [00:14<00:00, 68.20it/s]\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 200x200 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["Epoch 120: 100%|████████| 125/125 [00:16<00:00,  7.62it/s, loss=0.108]\n", "Epoch 121: 100%|████████| 125/125 [00:16<00:00,  7.59it/s, loss=0.111]\n", "Epoch 122: 100%|█████████| 125/125 [00:16<00:00,  7.59it/s, loss=0.11]\n", "Epoch 123: 100%|████████| 125/125 [00:16<00:00,  7.52it/s, loss=0.112]\n", "Epoch 124: 100%|████████| 125/125 [00:16<00:00,  7.43it/s, loss=0.109]\n", "Epoch 125: 100%|████████| 125/125 [00:16<00:00,  7.49it/s, loss=0.112]\n", "Epoch 126: 100%|████████| 125/125 [00:16<00:00,  7.50it/s, loss=0.111]\n", "Epoch 127: 100%|████████| 125/125 [00:16<00:00,  7.48it/s, loss=0.109]\n", "Epoch 128: 100%|████████| 125/125 [00:16<00:00,  7.49it/s, loss=0.109]\n", "Epoch 129: 100%|████████| 125/125 [00:16<00:00,  7.50it/s, loss=0.112]\n", "Epoch 130: 100%|████████| 125/125 [00:16<00:00,  7.51it/s, loss=0.113]\n", "Epoch 131: 100%|████████| 125/125 [00:16<00:00,  7.49it/s, loss=0.112]\n", "Epoch 132: 100%|████████| 125/125 [00:16<00:00,  7.46it/s, loss=0.114]\n", "Epoch 133: 100%|████████| 125/125 [00:16<00:00,  7.48it/s, loss=0.109]\n", "Epoch 134: 100%|████████| 125/125 [00:16<00:00,  7.48it/s, loss=0.111]\n", "Epoch 135: 100%|████████| 125/125 [00:16<00:00,  7.52it/s, loss=0.109]\n", "Epoch 136: 100%|████████| 125/125 [00:16<00:00,  7.47it/s, loss=0.106]\n", "Epoch 137: 100%|████████| 125/125 [00:16<00:00,  7.51it/s, loss=0.112]\n", "Epoch 138: 100%|████████| 125/125 [00:16<00:00,  7.51it/s, loss=0.109]\n", "Epoch 139: 100%|████████| 125/125 [00:16<00:00,  7.54it/s, loss=0.107]\n", "Epoch 140: 100%|████████| 125/125 [00:16<00:00,  7.52it/s, loss=0.108]\n", "Epoch 141: 100%|█████████| 125/125 [00:16<00:00,  7.52it/s, loss=0.11]\n", "Epoch 142: 100%|█████████| 125/125 [00:16<00:00,  7.51it/s, loss=0.11]\n", "Epoch 143: 100%|████████| 125/125 [00:16<00:00,  7.51it/s, loss=0.111]\n", "Epoch 144: 100%|████████| 125/125 [00:16<00:00,  7.51it/s, loss=0.107]\n", "Epoch 145: 100%|████████| 125/125 [00:16<00:00,  7.51it/s, loss=0.107]\n", "Epoch 146: 100%|████████| 125/125 [00:16<00:00,  7.54it/s, loss=0.108]\n", "Epoch 147: 100%|█████████| 125/125 [00:16<00:00,  7.44it/s, loss=0.11]\n", "Epoch 148: 100%|████████| 125/125 [00:16<00:00,  7.51it/s, loss=0.107]\n", "Epoch 149: 100%|████████| 125/125 [00:16<00:00,  7.51it/s, loss=0.107]\n", "Epoch 150: 100%|████████| 125/125 [00:16<00:00,  7.50it/s, loss=0.109]\n", "Epoch 151: 100%|█████████| 125/125 [00:16<00:00,  7.53it/s, loss=0.11]\n", "Epoch 152: 100%|████████| 125/125 [00:16<00:00,  7.54it/s, loss=0.112]\n", "Epoch 153: 100%|████████| 125/125 [00:16<00:00,  7.51it/s, loss=0.109]\n", "Epoch 154: 100%|█████████| 125/125 [00:16<00:00,  7.53it/s, loss=0.11]\n", "Epoch 155: 100%|████████| 125/125 [00:16<00:00,  7.55it/s, loss=0.111]\n", "Epoch 156: 100%|█████████| 125/125 [00:16<00:00,  7.51it/s, loss=0.11]\n", "Epoch 157: 100%|████████| 125/125 [00:16<00:00,  7.51it/s, loss=0.108]\n", "Epoch 158: 100%|████████| 125/125 [00:16<00:00,  7.53it/s, loss=0.107]\n", "Epoch 159: 100%|████████| 125/125 [00:16<00:00,  7.50it/s, loss=0.104]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 159 val loss: 0.1100\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 1000/1000 [00:14<00:00, 69.06it/s]\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 200x200 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["Epoch 160: 100%|████████| 125/125 [00:16<00:00,  7.63it/s, loss=0.106]\n", "Epoch 161: 100%|████████| 125/125 [00:16<00:00,  7.62it/s, loss=0.106]\n", "Epoch 162: 100%|████████| 125/125 [00:16<00:00,  7.58it/s, loss=0.108]\n", "Epoch 163: 100%|████████| 125/125 [00:16<00:00,  7.54it/s, loss=0.107]\n", "Epoch 164: 100%|████████| 125/125 [00:16<00:00,  7.53it/s, loss=0.109]\n", "Epoch 165: 100%|████████| 125/125 [00:16<00:00,  7.50it/s, loss=0.109]\n", "Epoch 166: 100%|████████| 125/125 [00:16<00:00,  7.49it/s, loss=0.111]\n", "Epoch 167: 100%|████████| 125/125 [00:16<00:00,  7.51it/s, loss=0.107]\n", "Epoch 168: 100%|████████| 125/125 [00:16<00:00,  7.49it/s, loss=0.109]\n", "Epoch 169: 100%|████████| 125/125 [00:16<00:00,  7.51it/s, loss=0.109]\n", "Epoch 170: 100%|████████| 125/125 [00:16<00:00,  7.48it/s, loss=0.107]\n", "Epoch 171: 100%|████████| 125/125 [00:16<00:00,  7.43it/s, loss=0.108]\n", "Epoch 172: 100%|█████████| 125/125 [00:16<00:00,  7.48it/s, loss=0.11]\n", "Epoch 173: 100%|█████████| 125/125 [00:16<00:00,  7.48it/s, loss=0.11]\n", "Epoch 174: 100%|████████| 125/125 [00:16<00:00,  7.48it/s, loss=0.108]\n", "Epoch 175: 100%|█████████| 125/125 [00:16<00:00,  7.49it/s, loss=0.11]\n", "Epoch 176: 100%|████████| 125/125 [00:16<00:00,  7.51it/s, loss=0.107]\n", "Epoch 177: 100%|████████| 125/125 [00:16<00:00,  7.50it/s, loss=0.106]\n", "Epoch 178: 100%|████████| 125/125 [00:16<00:00,  7.50it/s, loss=0.108]\n", "Epoch 179: 100%|█████████| 125/125 [00:16<00:00,  7.54it/s, loss=0.11]\n", "Epoch 180: 100%|████████| 125/125 [00:16<00:00,  7.49it/s, loss=0.108]\n", "Epoch 181: 100%|████████| 125/125 [00:16<00:00,  7.49it/s, loss=0.108]\n", "Epoch 182: 100%|████████| 125/125 [00:16<00:00,  7.43it/s, loss=0.106]\n", "Epoch 183: 100%|████████| 125/125 [00:16<00:00,  7.53it/s, loss=0.111]\n", "Epoch 184: 100%|████████| 125/125 [00:16<00:00,  7.52it/s, loss=0.107]\n", "Epoch 185: 100%|████████| 125/125 [00:16<00:00,  7.49it/s, loss=0.108]\n", "Epoch 186: 100%|█████████| 125/125 [00:16<00:00,  7.51it/s, loss=0.11]\n", "Epoch 187: 100%|█████████| 125/125 [00:16<00:00,  7.52it/s, loss=0.11]\n", "Epoch 188: 100%|████████| 125/125 [00:16<00:00,  7.55it/s, loss=0.104]\n", "Epoch 189: 100%|████████| 125/125 [00:16<00:00,  7.52it/s, loss=0.108]\n", "Epoch 190: 100%|████████| 125/125 [00:16<00:00,  7.52it/s, loss=0.108]\n", "Epoch 191: 100%|████████| 125/125 [00:16<00:00,  7.51it/s, loss=0.108]\n", "Epoch 192: 100%|████████| 125/125 [00:16<00:00,  7.50it/s, loss=0.108]\n", "Epoch 193: 100%|████████| 125/125 [00:16<00:00,  7.49it/s, loss=0.105]\n", "Epoch 194: 100%|█████████| 125/125 [00:16<00:00,  7.45it/s, loss=0.11]\n", "Epoch 195: 100%|████████| 125/125 [00:16<00:00,  7.53it/s, loss=0.111]\n", "Epoch 196: 100%|████████| 125/125 [00:16<00:00,  7.53it/s, loss=0.108]\n", "Epoch 197: 100%|████████| 125/125 [00:16<00:00,  7.53it/s, loss=0.109]\n", "Epoch 198: 100%|█████████| 125/125 [00:16<00:00,  7.54it/s, loss=0.11]\n", "Epoch 199: 100%|████████| 125/125 [00:16<00:00,  7.53it/s, loss=0.104]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 199 val loss: 0.1028\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 1000/1000 [00:14<00:00, 68.91it/s]\n"]}, {"data": {"image/png": "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*********************************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\n", "text/plain": ["<Figure size 200x200 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["optimizer = torch.optim.Adam(unet.parameters(), lr=1e-4)\n", "\n", "unet = unet.to(device)\n", "n_epochs = 200\n", "val_interval = 40\n", "epoch_losses = []\n", "val_losses = []\n", "scaler = GradScaler()\n", "\n", "for epoch in range(n_epochs):\n", "    unet.train()\n", "    autoencoderkl.eval()\n", "    epoch_loss = 0\n", "    progress_bar = tqdm(enumerate(train_loader), total=len(train_loader), ncols=70)\n", "    progress_bar.set_description(f\"Epoch {epoch}\")\n", "    for step, batch in progress_bar:\n", "        images = batch[\"image\"].to(device)\n", "        optimizer.zero_grad(set_to_none=True)\n", "        with autocast(enabled=True):\n", "            z_mu, z_sigma = autoencoderkl.encode(images)\n", "            z = autoencoderkl.sampling(z_mu, z_sigma)\n", "            noise = torch.randn_like(z).to(device)\n", "            timesteps = torch.randint(0, inferer.scheduler.num_train_timesteps, (z.shape[0],), device=z.device).long()\n", "            noise_pred = inferer(\n", "                inputs=images, diffusion_model=unet, noise=noise, timesteps=timesteps, autoencoder_model=autoencoderkl\n", "            )\n", "            loss = F.mse_loss(noise_pred.float(), noise.float())\n", "\n", "        scaler.scale(loss).backward()\n", "        scaler.step(optimizer)\n", "        scaler.update()\n", "\n", "        epoch_loss += loss.item()\n", "\n", "        progress_bar.set_postfix({\"loss\": epoch_loss / (step + 1)})\n", "    epoch_losses.append(epoch_loss / (step + 1))\n", "\n", "    if (epoch + 1) % val_interval == 0:\n", "        unet.eval()\n", "        val_loss = 0\n", "        with torch.no_grad():\n", "            for val_step, batch in enumerate(val_loader, start=1):\n", "                images = batch[\"image\"].to(device)\n", "\n", "                with autocast(enabled=True):\n", "                    z_mu, z_sigma = autoencoderkl.encode(images)\n", "                    z = autoencoderkl.sampling(z_mu, z_sigma)\n", "\n", "                    noise = torch.randn_like(z).to(device)\n", "                    timesteps = torch.randint(\n", "                        0, inferer.scheduler.num_train_timesteps, (z.shape[0],), device=z.device\n", "                    ).long()\n", "                    noise_pred = inferer(\n", "                        inputs=images,\n", "                        diffusion_model=unet,\n", "                        noise=noise,\n", "                        timesteps=timesteps,\n", "                        autoencoder_model=autoencoderkl,\n", "                    )\n", "\n", "                    loss = F.mse_loss(noise_pred.float(), noise.float())\n", "\n", "                val_loss += loss.item()\n", "        val_loss /= val_step\n", "        val_losses.append(val_loss)\n", "        print(f\"Epoch {epoch} val loss: {val_loss:.4f}\")\n", "\n", "        # Sampling image during training\n", "        z = torch.randn((1, 3, 16, 16))\n", "        z = z.to(device)\n", "        scheduler.set_timesteps(num_inference_steps=1000)\n", "        with autocast(enabled=True):\n", "            decoded = inferer.sample(\n", "                input_noise=z, diffusion_model=unet, scheduler=scheduler, autoencoder_model=autoencoderkl\n", "            )\n", "\n", "        plt.figure(figsize=(2, 2))\n", "        plt.style.use(\"default\")\n", "        plt.imshow(decoded[0, 0].detach().cpu(), vmin=0, vmax=1, cmap=\"gray\")\n", "        plt.tight_layout()\n", "        plt.axis(\"off\")\n", "        plt.show()\n", "progress_bar.close()\n"]}, {"cell_type": "markdown", "id": "35ac6265", "metadata": {}, "source": ["### Plot learning curves"]}, {"cell_type": "code", "execution_count": 20, "id": "a29c1b9c", "metadata": {"lines_to_next_cell": 2}, "outputs": [{"data": {"text/plain": ["<matplotlib.legend.Legend at 0x7fcc4d609000>"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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**************************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\n", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure()\n", "plt.title(\"Learning Curves\", fontsize=20)\n", "plt.plot(np.linspace(1, n_epochs, n_epochs), epoch_losses, linewidth=2.0, label=\"Train\")\n", "plt.plot(\n", "    np.linspace(val_interval, n_epochs, int(n_epochs / val_interval)), val_losses, linewidth=2.0, label=\"Validation\"\n", ")\n", "plt.yticks(fontsize=12)\n", "plt.xticks(fontsize=12)\n", "plt.xlabel(\"Epochs\", fontsize=16)\n", "plt.ylabel(\"Loss\", fontsize=16)\n", "plt.legend(prop={\"size\": 14})"]}, {"cell_type": "markdown", "id": "8fb02dd3", "metadata": {}, "source": ["### Plotting sampling example\n", "\n", "Finally, we generate an image with our LDM. For that, we will initialize a latent representation with just noise. Then, we will use the `unet` to perform 1000 denoising steps. For every 100 steps, we store the noisy intermediary samples. In the last step, we decode all latent representations and plot how the image looks like across the sampling process."]}, {"cell_type": "code", "execution_count": 30, "id": "d3b478d2", "metadata": {"lines_to_end_of_cell_marker": 2}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 1000/1000 [00:13<00:00, 76.31it/s]\n"]}], "source": ["unet.eval()\n", "scheduler.set_timesteps(num_inference_steps=1000)\n", "noise = torch.randn((1, 3, 16, 16))\n", "noise = noise.to(device)\n", "\n", "with torch.no_grad():\n", "    image, intermediates = inferer.sample(\n", "        input_noise=noise,\n", "        diffusion_model=unet,\n", "        scheduler=scheduler,\n", "        save_intermediates=True,\n", "        intermediate_steps=100,\n", "        autoencoder_model=autoencoderkl,\n", "    )"]}, {"cell_type": "code", "execution_count": 31, "id": "1eeea976", "metadata": {"lines_to_next_cell": 2}, "outputs": [{"data": {"text/plain": ["(-0.5, 639.5, 63.5, -0.5)"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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***********************************************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\n", "text/plain": ["<Figure size 1000x1200 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Decode latent representation of the intermediary images\n", "decoded_images = []\n", "for image in intermediates:\n", "    with torch.no_grad():\n", "        decoded_images.append(image)\n", "plt.figure(figsize=(10, 12))\n", "chain = torch.cat(decoded_images, dim=-1)\n", "plt.style.use(\"default\")\n", "plt.imshow(chain[0, 0].cpu(), vmin=0, vmax=1, cmap=\"gray\")\n", "plt.tight_layout()\n", "plt.axis(\"off\")"]}, {"cell_type": "markdown", "id": "8e5e2262", "metadata": {}, "source": ["### Clean-up data directory"]}, {"cell_type": "code", "execution_count": 22, "id": "ad55465b", "metadata": {}, "outputs": [], "source": ["if directory is None:\n", "    shutil.rmtree(root_dir)"]}], "metadata": {"jupytext": {"cell_metadata_filter": "-all", "formats": "ipynb,py"}, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.6"}}, "nbformat": 4, "nbformat_minor": 5}