{"cells": [{"cell_type": "code", "execution_count": null, "id": "8efe4285", "metadata": {}, "outputs": [], "source": ["# Copyright (c) MONAI Consortium\n", "# Licensed under the Apache License, Version 2.0 (the \"License\");\n", "# you may not use this file except in compliance with the License.\n", "# You may obtain a copy of the License at\n", "#     http://www.apache.org/licenses/LICENSE-2.0\n", "# Unless required by applicable law or agreed to in writing, software\n", "# distributed under the License is distributed on an \"AS IS\" BASIS,\n", "# WITHOUT WAR<PERSON><PERSON>IES OR CONDITIONS OF ANY KIND, either express or implied.\n", "# See the License for the specific language governing permissions and\n", "# limitations under the License."]}, {"cell_type": "markdown", "id": "e0a3f076", "metadata": {}, "source": ["# 3D Latent Diffusion Model\n", "In this tutorial, we will walk through the process of using the MONAI Generative Models package to generate synthetic data using Latent Diffusion Models (LDM)  [1, 2]. Specifically, we will focus on training an LDM to create synthetic brain images from the Brats dataset.\n", "\n", "[1] - <PERSON><PERSON><PERSON> et al. \"High-Resolution Image Synthesis with Latent Diffusion Models\" https://arxiv.org/abs/2112.10752\n", "\n", "[2] - <PERSON><PERSON><PERSON> et al. \"Brain imaging generation with latent diffusion models\" https://arxiv.org/abs/2209.07162"]}, {"cell_type": "markdown", "id": "da9e6b23", "metadata": {}, "source": ["### Set up imports"]}, {"cell_type": "code", "execution_count": 1, "id": "b44c4689", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/media/walter/Storage/Projects/GenerativeModels/venv/lib/python3.10/site-packages/tqdm/auto.py:22: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}, {"name": "stdout", "output_type": "stream", "text": ["MONAI version: 1.2.dev2304\n", "Numpy version: 1.23.5\n", "Pytorch version: 1.13.1+cu117\n", "MONAI flags: HAS_EXT = False, USE_COMPILED = False, USE_META_DICT = False\n", "MONAI rev id: 9a57be5aab9f2c2a134768c0c146399150e247a0\n", "MONAI __file__: /media/walter/Storage/Projects/GenerativeModels/venv/lib/python3.10/site-packages/monai/__init__.py\n", "\n", "Optional dependencies:\n", "Pytorch Ignite version: 0.4.10\n", "ITK version: 5.3.0\n", "Nibabel version: 4.0.2\n", "scikit-image version: 0.19.3\n", "Pillow version: 9.3.0\n", "Tensorboard version: 2.11.0\n", "gdown version: 4.6.0\n", "TorchVision version: 0.14.1+cu117\n", "tqdm version: 4.64.1\n", "lmdb version: 1.4.0\n", "psutil version: 5.9.4\n", "pandas version: 1.5.3\n", "einops version: 0.6.0\n", "transformers version: 4.21.3\n", "mlflow version: 2.1.1\n", "pynrrd version: 1.0.0\n", "\n", "For details about installing the optional dependencies, please visit:\n", "    https://docs.monai.io/en/latest/installation.html#installing-the-recommended-dependencies\n", "\n"]}], "source": ["import os\n", "import shutil\n", "import tempfile\n", "\n", "import matplotlib.pyplot as plt\n", "import torch\n", "import torch.nn.functional as F\n", "from monai import transforms\n", "from monai.apps import DecathlonDataset\n", "from monai.config import print_config\n", "from monai.data import DataLoader\n", "from monai.utils import first, set_determinism\n", "from torch.cuda.amp import GradScaler, autocast\n", "from torch.nn import L1Loss\n", "from tqdm import tqdm\n", "\n", "from generative.inferers import LatentDiffusionInferer\n", "from generative.losses import PatchAdversarialLoss, PerceptualLoss\n", "from generative.networks.nets import AutoencoderKL, DiffusionModelUNet, PatchDiscriminator\n", "from generative.networks.schedulers import DDPMScheduler\n", "\n", "print_config()"]}, {"cell_type": "code", "execution_count": 2, "id": "a21c1f6a", "metadata": {}, "outputs": [], "source": ["# for reproducibility purposes set a seed\n", "set_determinism(42)"]}, {"cell_type": "markdown", "id": "2b02aa6c", "metadata": {}, "source": ["### Setup a data directory and download dataset\n", "Specify a MONAI_DATA_DIRECTORY variable, where the data will be downloaded. If not specified a temporary directory will be used."]}, {"cell_type": "code", "execution_count": 3, "id": "5d450e1d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/tmp/tmp5nw3g3c4\n"]}], "source": ["directory = os.environ.get(\"MONAI_DATA_DIRECTORY\")\n", "root_dir = tempfile.mkdtemp() if directory is None else directory\n", "print(root_dir)"]}, {"cell_type": "markdown", "id": "74302407", "metadata": {}, "source": ["### Prepare data loader for the training set\n", "Here we will download the <PERSON><PERSON><PERSON> dataset using MONAI's `DecathlonDataset` class, and we prepare the data loader for the training set."]}, {"cell_type": "code", "execution_count": 4, "id": "c34a9ba3", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["<class 'monai.transforms.utility.array.AddChannel'>: Class `AddChannel` has been deprecated since version 0.8. please use MetaTensor data type and monai.transforms.EnsureChannelFirst instead.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["2023-02-19 09:07:46,210 - INFO - Verified 'Task01_BrainTumour.tar', md5: 240a19d752f0d9e9101544901065d872.\n", "2023-02-19 09:07:46,210 - INFO - File exists: /tmp/tmp5nw3g3c4/Task01_BrainTumour.tar, skipped downloading.\n", "2023-02-19 09:07:46,211 - INFO - Non-empty folder exists in /tmp/tmp5nw3g3c4/Task01_BrainTumour, skipped extracting.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Loading dataset: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 388/388 [01:32<00:00,  4.21it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Image shape torch.Size([1, 96, 96, 64])\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["batch_size = 2\n", "channel = 0  # 0 = Flair\n", "assert channel in [0, 1, 2, 3], \"Choose a valid channel\"\n", "\n", "train_transforms = transforms.Compose(\n", "    [\n", "        transforms.LoadImaged(keys=[\"image\"]),\n", "        transforms.EnsureChannelFirstd(keys=[\"image\"]),\n", "        transforms.Lambdad(keys=\"image\", func=lambda x: x[channel, :, :, :]),\n", "        transforms.EnsureChannelFirstd(keys=[\"image\"], channel_dim=\"no_channel\"),\n", "        transforms.EnsureTyped(keys=[\"image\"]),\n", "        transforms.Orientationd(keys=[\"image\"], axcodes=\"RAS\"),\n", "        transforms.Spacingd(keys=[\"image\"], pixdim=(2.4, 2.4, 2.2), mode=(\"bilinear\")),\n", "        transforms.CenterSpatialCropd(keys=[\"image\"], roi_size=(96, 96, 64)),\n", "        transforms.ScaleIntensityRangePercentilesd(keys=\"image\", lower=0, upper=99.5, b_min=0, b_max=1),\n", "    ]\n", ")\n", "train_ds = DecathlonDataset(\n", "    root_dir=root_dir,\n", "    task=\"Task01_BrainTumour\",\n", "    section=\"training\",  # validation\n", "    cache_rate=1.0,  # you may need a few Gb of RAM... Set to 0 otherwise\n", "    num_workers=8,\n", "    download=True,  # Set download to True if the dataset hasnt been downloaded yet\n", "    seed=0,\n", "    transform=train_transforms,\n", ")\n", "train_loader = DataLoader(train_ds, batch_size=batch_size, shuffle=True, num_workers=8, persistent_workers=True)\n", "print(f'Image shape {train_ds[0][\"image\"].shape}')"]}, {"cell_type": "markdown", "id": "1d36e0c4", "metadata": {}, "source": ["### Visualise examples from the training set"]}, {"cell_type": "code", "execution_count": 5, "id": "723c2dad", "metadata": {}, "outputs": [{"data": {"text/plain": ["<matplotlib.image.AxesImage at 0x7f5030f90af0>"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Plot axial, coronal and sagittal slices of a training sample\n", "check_data = first(train_loader)\n", "idx = 0\n", "\n", "img = check_data[\"image\"][idx, 0]\n", "fig, axs = plt.subplots(nrows=1, ncols=3)\n", "for ax in axs:\n", "    ax.axis(\"off\")\n", "ax = axs[0]\n", "ax.imshow(img[..., img.shape[2] // 2], cmap=\"gray\")\n", "ax = axs[1]\n", "ax.imshow(img[:, img.shape[1] // 2, ...], cmap=\"gray\")\n", "ax = axs[2]\n", "ax.imshow(img[img.shape[0] // 2, ...], cmap=\"gray\")\n", "# plt.savefig(\"training_examples.png\")"]}, {"cell_type": "markdown", "id": "513d7eee", "metadata": {}, "source": ["## Autoencoder KL\n", "\n", "### Define Autoencoder KL network\n", "\n", "In this section, we will define an autoencoder with KL-regularization for the LDM. The autoencoder's primary purpose is to transform input images into a latent representation that the diffusion model will subsequently learn. By doing so, we can decrease the computational resources required to train the diffusion component, making this approach suitable for learning high-resolution medical images.\n"]}, {"cell_type": "code", "execution_count": 6, "id": "1042ebac", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Using cuda\n"]}], "source": ["device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "print(f\"Using {device}\")"]}, {"cell_type": "code", "execution_count": 7, "id": "383a2043", "metadata": {}, "outputs": [], "source": ["autoencoder = AutoencoderKL(\n", "    spatial_dims=3,\n", "    in_channels=1,\n", "    out_channels=1,\n", "    num_channels=(32, 64, 64),\n", "    latent_channels=3,\n", "    num_res_blocks=1,\n", "    norm_num_groups=16,\n", "    attention_levels=(False, False, True),\n", ")\n", "autoencoder.to(device)\n", "\n", "\n", "discriminator = PatchDiscriminator(spatial_dims=3, num_layers_d=3, num_channels=32, in_channels=1, out_channels=1)\n", "discriminator.to(device)"]}, {"cell_type": "markdown", "id": "67f94d1b", "metadata": {}, "source": ["### Defining Losses\n", "\n", "We will also specify the perceptual and adversarial losses, including the involved networks, and the optimizers to use during the training process."]}, {"cell_type": "code", "execution_count": 8, "id": "7594daa3", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["The parameter 'pretrained' is deprecated since 0.13 and may be removed in the future, please use 'weights' instead.\n", "Arguments other than a weight enum or `None` for 'weights' are deprecated since 0.13 and may be removed in the future. The current behavior is equivalent to passing `weights=SqueezeNet1_1_Weights.IMAGENET1K_V1`. You can also use `weights=SqueezeNet1_1_Weights.DEFAULT` to get the most up-to-date weights.\n"]}], "source": ["l1_loss = L1Loss()\n", "adv_loss = PatchAdversarialLoss(criterion=\"least_squares\")\n", "loss_perceptual = PerceptualLoss(spatial_dims=3, network_type=\"squeeze\", is_fake_3d=True, fake_3d_ratio=0.2)\n", "loss_perceptual.to(device)\n", "\n", "\n", "def KL_loss(z_mu, z_sigma):\n", "    kl_loss = 0.5 * torch.sum(z_mu.pow(2) + z_sigma.pow(2) - torch.log(z_sigma.pow(2)) - 1, dim=[1, 2, 3, 4])\n", "    return torch.sum(kl_loss) / kl_loss.shape[0]\n", "\n", "\n", "adv_weight = 0.01\n", "perceptual_weight = 0.001\n", "kl_weight = 1e-6"]}, {"cell_type": "code", "execution_count": 9, "id": "354a3057", "metadata": {}, "outputs": [], "source": ["optimizer_g = torch.optim.Adam(params=autoencoder.parameters(), lr=1e-4)\n", "optimizer_d = torch.optim.Adam(params=discriminator.parameters(), lr=1e-4)"]}, {"cell_type": "markdown", "id": "be4fe2d4", "metadata": {}, "source": ["### Train model"]}, {"cell_type": "code", "execution_count": 10, "id": "047c1bc4", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Epoch 0: 100%|█████████████████| 194/194 [02:27<00:00,  1.31it/s, recons_loss=0.0642, gen_loss=0, disc_loss=0]\n", "Epoch 1: 100%|█████████████████| 194/194 [02:29<00:00,  1.30it/s, recons_loss=0.0421, gen_loss=0, disc_loss=0]\n", "Epoch 2: 100%|█████████████████| 194/194 [02:30<00:00,  1.29it/s, recons_loss=0.0337, gen_loss=0, disc_loss=0]\n", "Epoch 3: 100%|█████████████████| 194/194 [02:31<00:00,  1.28it/s, recons_loss=0.0325, gen_loss=0, disc_loss=0]\n", "Epoch 4: 100%|█████████████████| 194/194 [02:31<00:00,  1.28it/s, recons_loss=0.0307, gen_loss=0, disc_loss=0]\n", "Epoch 5: 100%|█████████████████| 194/194 [02:31<00:00,  1.28it/s, recons_loss=0.0277, gen_loss=0, disc_loss=0]\n", "Epoch 6: 100%|██████████| 194/194 [02:51<00:00,  1.13it/s, recons_loss=0.027, gen_loss=0.528, disc_loss=0.342]\n", "Epoch 7: 100%|█████████| 194/194 [02:50<00:00,  1.14it/s, recons_loss=0.0282, gen_loss=0.594, disc_loss=0.228]\n", "Epoch 8: 100%|█████████| 194/194 [02:51<00:00,  1.13it/s, recons_loss=0.0291, gen_loss=0.572, disc_loss=0.238]\n", "Epoch 9: 100%|█████████| 194/194 [02:51<00:00,  1.13it/s, recons_loss=0.0284, gen_loss=0.511, disc_loss=0.246]\n", "Epoch 10: 100%|████████| 194/194 [02:51<00:00,  1.13it/s, recons_loss=0.0287, gen_loss=0.389, disc_loss=0.223]\n", "Epoch 11: 100%|████████| 194/194 [02:51<00:00,  1.13it/s, recons_loss=0.0279, gen_loss=0.425, disc_loss=0.218]\n", "Epoch 12: 100%|█████████| 194/194 [02:51<00:00,  1.13it/s, recons_loss=0.0277, gen_loss=0.406, disc_loss=0.23]\n", "Epoch 13: 100%|████████| 194/194 [02:50<00:00,  1.14it/s, recons_loss=0.0269, gen_loss=0.384, disc_loss=0.221]\n", "Epoch 14: 100%|████████| 194/194 [02:50<00:00,  1.14it/s, recons_loss=0.0259, gen_loss=0.432, disc_loss=0.231]\n", "Epoch 15: 100%|████████| 194/194 [02:50<00:00,  1.14it/s, recons_loss=0.0259, gen_loss=0.375, disc_loss=0.225]\n", "Epoch 16: 100%|█████████| 194/194 [02:50<00:00,  1.14it/s, recons_loss=0.0257, gen_loss=0.41, disc_loss=0.226]\n", "Epoch 17: 100%|████████| 194/194 [02:50<00:00,  1.14it/s, recons_loss=0.0255, gen_loss=0.394, disc_loss=0.218]\n", "Epoch 18: 100%|████████| 194/194 [02:50<00:00,  1.14it/s, recons_loss=0.0254, gen_loss=0.403, disc_loss=0.221]\n", "Epoch 19: 100%|████████| 194/194 [02:50<00:00,  1.14it/s, recons_loss=0.0256, gen_loss=0.389, disc_loss=0.224]\n", "Epoch 20: 100%|████████| 194/194 [02:51<00:00,  1.13it/s, recons_loss=0.0257, gen_loss=0.403, disc_loss=0.221]\n", "Epoch 21: 100%|█████████| 194/194 [02:51<00:00,  1.13it/s, recons_loss=0.0252, gen_loss=0.406, disc_loss=0.22]\n", "Epoch 22: 100%|████████| 194/194 [02:51<00:00,  1.13it/s, recons_loss=0.0253, gen_loss=0.388, disc_loss=0.214]\n", "Epoch 23: 100%|████████| 194/194 [02:51<00:00,  1.13it/s, recons_loss=0.0246, gen_loss=0.387, disc_loss=0.215]\n", "Epoch 24: 100%|████████| 194/194 [02:50<00:00,  1.13it/s, recons_loss=0.0239, gen_loss=0.411, disc_loss=0.214]\n", "Epoch 25: 100%|████████| 194/194 [02:50<00:00,  1.13it/s, recons_loss=0.0243, gen_loss=0.415, disc_loss=0.211]\n", "Epoch 26: 100%|█████████| 194/194 [02:50<00:00,  1.14it/s, recons_loss=0.0243, gen_loss=0.41, disc_loss=0.209]\n", "Epoch 27: 100%|████████| 194/194 [02:50<00:00,  1.13it/s, recons_loss=0.0234, gen_loss=0.461, disc_loss=0.227]\n", "Epoch 28: 100%|████████| 194/194 [02:50<00:00,  1.14it/s, recons_loss=0.0237, gen_loss=0.426, disc_loss=0.207]\n", "Epoch 29: 100%|██████████| 194/194 [02:50<00:00,  1.14it/s, recons_loss=0.024, gen_loss=0.421, disc_loss=0.21]\n", "Epoch 30: 100%|████████| 194/194 [02:50<00:00,  1.14it/s, recons_loss=0.0236, gen_loss=0.447, disc_loss=0.209]\n", "Epoch 31: 100%|████████| 194/194 [02:50<00:00,  1.14it/s, recons_loss=0.0236, gen_loss=0.414, disc_loss=0.208]\n", "Epoch 32: 100%|████████| 194/194 [02:50<00:00,  1.14it/s, recons_loss=0.0231, gen_loss=0.418, disc_loss=0.206]\n", "Epoch 33: 100%|████████| 194/194 [02:50<00:00,  1.14it/s, recons_loss=0.0236, gen_loss=0.412, disc_loss=0.212]\n", "Epoch 34: 100%|████████| 194/194 [02:50<00:00,  1.14it/s, recons_loss=0.0234, gen_loss=0.435, disc_loss=0.206]\n", "Epoch 35: 100%|████████| 194/194 [02:50<00:00,  1.14it/s, recons_loss=0.0231, gen_loss=0.423, disc_loss=0.207]\n", "Epoch 36: 100%|████████| 194/194 [02:51<00:00,  1.13it/s, recons_loss=0.0231, gen_loss=0.424, disc_loss=0.205]\n", "Epoch 37: 100%|████████| 194/194 [02:50<00:00,  1.14it/s, recons_loss=0.0232, gen_loss=0.427, disc_loss=0.214]\n", "Epoch 38: 100%|████████| 194/194 [02:51<00:00,  1.13it/s, recons_loss=0.0222, gen_loss=0.476, disc_loss=0.217]\n", "Epoch 39: 100%|████████| 194/194 [02:50<00:00,  1.14it/s, recons_loss=0.0225, gen_loss=0.446, disc_loss=0.206]\n", "Epoch 40: 100%|████████| 194/194 [02:50<00:00,  1.14it/s, recons_loss=0.0226, gen_loss=0.437, disc_loss=0.207]\n", "Epoch 41: 100%|████████| 194/194 [02:51<00:00,  1.13it/s, recons_loss=0.0229, gen_loss=0.426, disc_loss=0.207]\n", "Epoch 42: 100%|████████| 194/194 [02:51<00:00,  1.13it/s, recons_loss=0.0221, gen_loss=0.468, disc_loss=0.198]\n", "Epoch 43: 100%|█████████| 194/194 [02:51<00:00,  1.13it/s, recons_loss=0.023, gen_loss=0.455, disc_loss=0.201]\n", "Epoch 44: 100%|████████| 194/194 [02:51<00:00,  1.13it/s, recons_loss=0.0225, gen_loss=0.456, disc_loss=0.198]\n", "Epoch 45: 100%|████████| 194/194 [02:51<00:00,  1.13it/s, recons_loss=0.0221, gen_loss=0.501, disc_loss=0.196]\n", "Epoch 46: 100%|█████████| 194/194 [02:50<00:00,  1.13it/s, recons_loss=0.022, gen_loss=0.476, disc_loss=0.194]\n", "Epoch 47: 100%|████████| 194/194 [02:51<00:00,  1.13it/s, recons_loss=0.0226, gen_loss=0.487, disc_loss=0.197]\n", "Epoch 48: 100%|████████| 194/194 [02:51<00:00,  1.13it/s, recons_loss=0.0225, gen_loss=0.486, disc_loss=0.186]\n", "Epoch 49: 100%|████████| 194/194 [02:50<00:00,  1.14it/s, recons_loss=0.0226, gen_loss=0.508, disc_loss=0.187]\n", "Epoch 50: 100%|████████| 194/194 [02:50<00:00,  1.14it/s, recons_loss=0.0226, gen_loss=0.511, disc_loss=0.189]\n", "Epoch 51: 100%|████████| 194/194 [02:50<00:00,  1.14it/s, recons_loss=0.0224, gen_loss=0.564, disc_loss=0.182]\n", "Epoch 52: 100%|████████| 194/194 [02:50<00:00,  1.14it/s, recons_loss=0.0224, gen_loss=0.508, disc_loss=0.183]\n", "Epoch 53: 100%|████████| 194/194 [02:50<00:00,  1.14it/s, recons_loss=0.0221, gen_loss=0.526, disc_loss=0.175]\n", "Epoch 54: 100%|████████| 194/194 [02:51<00:00,  1.13it/s, recons_loss=0.0227, gen_loss=0.521, disc_loss=0.181]\n", "Epoch 55: 100%|█████████| 194/194 [02:50<00:00,  1.14it/s, recons_loss=0.0224, gen_loss=0.56, disc_loss=0.182]\n", "Epoch 56: 100%|████████| 194/194 [02:50<00:00,  1.14it/s, recons_loss=0.0232, gen_loss=0.543, disc_loss=0.182]\n", "Epoch 57: 100%|████████| 194/194 [02:50<00:00,  1.14it/s, recons_loss=0.0228, gen_loss=0.525, disc_loss=0.168]\n", "Epoch 58: 100%|█████████| 194/194 [02:50<00:00,  1.14it/s, recons_loss=0.023, gen_loss=0.539, disc_loss=0.165]\n", "Epoch 59: 100%|████████| 194/194 [02:51<00:00,  1.13it/s, recons_loss=0.0228, gen_loss=0.572, disc_loss=0.178]\n", "Epoch 60: 100%|████████| 194/194 [02:50<00:00,  1.14it/s, recons_loss=0.0231, gen_loss=0.536, disc_loss=0.165]\n", "Epoch 61: 100%|████████| 194/194 [02:50<00:00,  1.14it/s, recons_loss=0.0231, gen_loss=0.579, disc_loss=0.158]\n", "Epoch 62: 100%|█████████| 194/194 [02:50<00:00,  1.14it/s, recons_loss=0.024, gen_loss=0.549, disc_loss=0.162]\n", "Epoch 63: 100%|████████| 194/194 [02:50<00:00,  1.14it/s, recons_loss=0.0235, gen_loss=0.565, disc_loss=0.153]\n", "Epoch 64: 100%|████████| 194/194 [02:50<00:00,  1.14it/s, recons_loss=0.0234, gen_loss=0.598, disc_loss=0.152]\n", "Epoch 65: 100%|████████| 194/194 [02:50<00:00,  1.14it/s, recons_loss=0.0235, gen_loss=0.591, disc_loss=0.163]\n", "Epoch 66: 100%|████████| 194/194 [02:50<00:00,  1.14it/s, recons_loss=0.0232, gen_loss=0.604, disc_loss=0.156]\n", "Epoch 67: 100%|████████| 194/194 [02:50<00:00,  1.14it/s, recons_loss=0.0229, gen_loss=0.625, disc_loss=0.152]\n", "Epoch 68: 100%|█████████| 194/194 [02:50<00:00,  1.14it/s, recons_loss=0.023, gen_loss=0.589, disc_loss=0.152]\n", "Epoch 69: 100%|████████| 194/194 [02:50<00:00,  1.13it/s, recons_loss=0.0234, gen_loss=0.617, disc_loss=0.148]\n", "Epoch 70: 100%|████████| 194/194 [02:50<00:00,  1.14it/s, recons_loss=0.0235, gen_loss=0.635, disc_loss=0.156]\n", "Epoch 71: 100%|█████████| 194/194 [02:50<00:00,  1.14it/s, recons_loss=0.0234, gen_loss=0.61, disc_loss=0.161]\n", "Epoch 72: 100%|████████| 194/194 [02:50<00:00,  1.14it/s, recons_loss=0.0235, gen_loss=0.587, disc_loss=0.142]\n", "Epoch 73: 100%|████████| 194/194 [02:50<00:00,  1.14it/s, recons_loss=0.0236, gen_loss=0.637, disc_loss=0.149]\n", "Epoch 74: 100%|████████| 194/194 [02:50<00:00,  1.14it/s, recons_loss=0.0238, gen_loss=0.615, disc_loss=0.149]\n", "Epoch 75: 100%|████████| 194/194 [02:51<00:00,  1.13it/s, recons_loss=0.0242, gen_loss=0.609, disc_loss=0.142]\n", "Epoch 76: 100%|████████| 194/194 [02:50<00:00,  1.14it/s, recons_loss=0.0235, gen_loss=0.643, disc_loss=0.143]\n", "Epoch 77: 100%|█████████| 194/194 [02:50<00:00,  1.14it/s, recons_loss=0.0237, gen_loss=0.65, disc_loss=0.145]\n", "Epoch 78: 100%|████████| 194/194 [02:51<00:00,  1.13it/s, recons_loss=0.0231, gen_loss=0.704, disc_loss=0.121]\n", "Epoch 79: 100%|████████| 194/194 [02:51<00:00,  1.13it/s, recons_loss=0.0233, gen_loss=0.649, disc_loss=0.125]\n", "Epoch 80: 100%|████████| 194/194 [02:51<00:00,  1.13it/s, recons_loss=0.0237, gen_loss=0.656, disc_loss=0.132]\n", "Epoch 81: 100%|████████| 194/194 [02:50<00:00,  1.14it/s, recons_loss=0.0238, gen_loss=0.651, disc_loss=0.142]\n", "Epoch 82: 100%|█████████| 194/194 [02:50<00:00,  1.14it/s, recons_loss=0.0235, gen_loss=0.669, disc_loss=0.13]\n", "Epoch 83: 100%|█████████| 194/194 [02:50<00:00,  1.13it/s, recons_loss=0.0238, gen_loss=0.653, disc_loss=0.13]\n", "Epoch 84: 100%|████████| 194/194 [02:50<00:00,  1.14it/s, recons_loss=0.0232, gen_loss=0.688, disc_loss=0.126]\n", "Epoch 85: 100%|██████████| 194/194 [02:50<00:00,  1.14it/s, recons_loss=0.0223, gen_loss=0.763, disc_loss=0.1]\n", "Epoch 86: 100%|████████| 194/194 [02:50<00:00,  1.14it/s, recons_loss=0.0234, gen_loss=0.655, disc_loss=0.136]\n", "Epoch 87: 100%|████████| 194/194 [02:50<00:00,  1.14it/s, recons_loss=0.0236, gen_loss=0.664, disc_loss=0.121]\n", "Epoch 88: 100%|████████| 194/194 [02:50<00:00,  1.14it/s, recons_loss=0.0234, gen_loss=0.697, disc_loss=0.117]\n", "Epoch 89: 100%|████████| 194/194 [02:50<00:00,  1.14it/s, recons_loss=0.0228, gen_loss=0.721, disc_loss=0.101]\n", "Epoch 90: 100%|████████| 194/194 [02:50<00:00,  1.14it/s, recons_loss=0.0235, gen_loss=0.704, disc_loss=0.113]\n", "Epoch 91: 100%|████████| 194/194 [02:50<00:00,  1.14it/s, recons_loss=0.0243, gen_loss=0.674, disc_loss=0.127]\n", "Epoch 92: 100%|███████| 194/194 [02:50<00:00,  1.14it/s, recons_loss=0.0215, gen_loss=0.833, disc_loss=0.0804]\n", "Epoch 93: 100%|████████| 194/194 [02:50<00:00,  1.14it/s, recons_loss=0.0221, gen_loss=0.742, disc_loss=0.106]\n", "Epoch 94: 100%|████████| 194/194 [02:50<00:00,  1.14it/s, recons_loss=0.0233, gen_loss=0.707, disc_loss=0.107]\n", "Epoch 95: 100%|████████| 194/194 [02:50<00:00,  1.14it/s, recons_loss=0.0231, gen_loss=0.736, disc_loss=0.106]\n", "Epoch 96: 100%|████████| 194/194 [02:50<00:00,  1.14it/s, recons_loss=0.0231, gen_loss=0.729, disc_loss=0.113]\n", "Epoch 97: 100%|████████| 194/194 [02:50<00:00,  1.14it/s, recons_loss=0.0237, gen_loss=0.702, disc_loss=0.112]\n", "Epoch 98: 100%|████████| 194/194 [02:50<00:00,  1.14it/s, recons_loss=0.0226, gen_loss=0.735, disc_loss=0.105]\n", "Epoch 99: 100%|████████| 194/194 [02:49<00:00,  1.14it/s, recons_loss=0.0225, gen_loss=0.736, disc_loss=0.108]\n"]}], "source": ["n_epochs = 100\n", "autoencoder_warm_up_n_epochs = 5\n", "val_interval = 10\n", "epoch_recon_loss_list = []\n", "epoch_gen_loss_list = []\n", "epoch_disc_loss_list = []\n", "val_recon_epoch_loss_list = []\n", "intermediary_images = []\n", "n_example_images = 4\n", "\n", "for epoch in range(n_epochs):\n", "    autoencoder.train()\n", "    discriminator.train()\n", "    epoch_loss = 0\n", "    gen_epoch_loss = 0\n", "    disc_epoch_loss = 0\n", "    progress_bar = tqdm(enumerate(train_loader), total=len(train_loader), ncols=110)\n", "    progress_bar.set_description(f\"Epoch {epoch}\")\n", "    for step, batch in progress_bar:\n", "        images = batch[\"image\"].to(device)  # choose only one of Brats channels\n", "\n", "        # Generator part\n", "        optimizer_g.zero_grad(set_to_none=True)\n", "        reconstruction, z_mu, z_sigma = autoencoder(images)\n", "        kl_loss = KL_loss(z_mu, z_sigma)\n", "\n", "        recons_loss = l1_loss(reconstruction.float(), images.float())\n", "        p_loss = loss_perceptual(reconstruction.float(), images.float())\n", "        loss_g = recons_loss + kl_weight * kl_loss + perceptual_weight * p_loss\n", "\n", "        if epoch > autoencoder_warm_up_n_epochs:\n", "            logits_fake = discriminator(reconstruction.contiguous().float())[-1]\n", "            generator_loss = adv_loss(logits_fake, target_is_real=True, for_discriminator=False)\n", "            loss_g += adv_weight * generator_loss\n", "\n", "        loss_g.backward()\n", "        optimizer_g.step()\n", "\n", "        if epoch > autoencoder_warm_up_n_epochs:\n", "            # Discriminator part\n", "            optimizer_d.zero_grad(set_to_none=True)\n", "            logits_fake = discriminator(reconstruction.contiguous().detach())[-1]\n", "            loss_d_fake = adv_loss(logits_fake, target_is_real=False, for_discriminator=True)\n", "            logits_real = discriminator(images.contiguous().detach())[-1]\n", "            loss_d_real = adv_loss(logits_real, target_is_real=True, for_discriminator=True)\n", "            discriminator_loss = (loss_d_fake + loss_d_real) * 0.5\n", "\n", "            loss_d = adv_weight * discriminator_loss\n", "\n", "            loss_d.backward()\n", "            optimizer_d.step()\n", "\n", "        epoch_loss += recons_loss.item()\n", "        if epoch > autoencoder_warm_up_n_epochs:\n", "            gen_epoch_loss += generator_loss.item()\n", "            disc_epoch_loss += discriminator_loss.item()\n", "\n", "        progress_bar.set_postfix(\n", "            {\n", "                \"recons_loss\": epoch_loss / (step + 1),\n", "                \"gen_loss\": gen_epoch_loss / (step + 1),\n", "                \"disc_loss\": disc_epoch_loss / (step + 1),\n", "            }\n", "        )\n", "    epoch_recon_loss_list.append(epoch_loss / (step + 1))\n", "    epoch_gen_loss_list.append(gen_epoch_loss / (step + 1))\n", "    epoch_disc_loss_list.append(disc_epoch_loss / (step + 1))\n", "\n", "del discriminator\n", "del loss_perceptual\n", "torch.cuda.empty_cache()"]}, {"cell_type": "code", "execution_count": 11, "id": "a27064b6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2023-02-19 13:52:44,991 - No artists with labels found to put in legend.  Note that artists whose label start with an underscore are ignored when legend() is called with no argument.\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.style.use(\"ggplot\")\n", "plt.title(\"Learning Curves\", fontsize=20)\n", "plt.plot(epoch_recon_loss_list)\n", "plt.yticks(fontsize=12)\n", "plt.xticks(fontsize=12)\n", "plt.xlabel(\"Epochs\", fontsize=16)\n", "plt.ylabel(\"Loss\", fontsize=16)\n", "plt.legend(prop={\"size\": 14})\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 12, "id": "fd710efe", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.title(\"Adversarial Training Curves\", fontsize=20)\n", "plt.plot(epoch_gen_loss_list, color=\"C0\", linewidth=2.0, label=\"Generator\")\n", "plt.plot(epoch_disc_loss_list, color=\"C1\", linewidth=2.0, label=\"Discriminator\")\n", "plt.yticks(fontsize=12)\n", "plt.xticks(fontsize=12)\n", "plt.xlabel(\"Epochs\", fontsize=16)\n", "plt.ylabel(\"Loss\", fontsize=16)\n", "plt.legend(prop={\"size\": 14})\n", "plt.show()"]}, {"cell_type": "markdown", "id": "92e38b28", "metadata": {}, "source": ["### Visualise reconstructions"]}, {"cell_type": "code", "execution_count": 13, "id": "ec9685bb", "metadata": {}, "outputs": [{"data": {"text/plain": ["<matplotlib.image.AxesImage at 0x7f506a8ce0b0>"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Plot axial, coronal and sagittal slices of a training sample\n", "idx = 0\n", "img = reconstruction[idx, channel].detach().cpu().numpy()\n", "fig, axs = plt.subplots(nrows=1, ncols=3)\n", "for ax in axs:\n", "    ax.axis(\"off\")\n", "ax = axs[0]\n", "ax.imshow(img[..., img.shape[2] // 2], cmap=\"gray\")\n", "ax = axs[1]\n", "ax.imshow(img[:, img.shape[1] // 2, ...], cmap=\"gray\")\n", "ax = axs[2]\n", "ax.imshow(img[img.shape[0] // 2, ...], cmap=\"gray\")"]}, {"cell_type": "markdown", "id": "fe436141", "metadata": {}, "source": ["## Diffusion Model\n", "\n", "### Define diffusion model and scheduler\n", "\n", "In this section, we will define the diffusion model that will learn data distribution of the latent representation of the autoencoder. Together with the diffusion model, we define a beta scheduler responsible for defining the amount of noise that is added across the diffusion's model Markov chain."]}, {"cell_type": "code", "execution_count": 42, "id": "88cbe609", "metadata": {}, "outputs": [], "source": ["unet = DiffusionModelUNet(\n", "    spatial_dims=3,\n", "    in_channels=3,\n", "    out_channels=3,\n", "    num_res_blocks=1,\n", "    num_channels=(32, 64, 64),\n", "    attention_levels=(False, True, True),\n", "    num_head_channels=(0, 64, 64),\n", ")\n", "unet.to(device)\n", "\n", "\n", "scheduler = DDPMScheduler(num_train_timesteps=1000, schedule=\"scaled_linear_beta\", beta_start=0.0015, beta_end=0.0195)"]}, {"cell_type": "markdown", "id": "243ddf9e", "metadata": {}, "source": ["### Scaling factor\n", "\n", "As mentioned in <PERSON><PERSON><PERSON> et al. [1] Section 4.3.2 and D.1, the signal-to-noise ratio (induced by the scale of the latent space) can affect the results obtained with the LDM, if the standard deviation of the latent space distribution drifts too much from that of a Gaussian. For this reason, it is best practice to use a scaling factor to adapt this standard deviation.\n", "\n", "_Note: In case where the latent space is close to a Gaussian distribution, the scaling factor will be close to one, and the results will not differ from those obtained when it is not used._\n"]}, {"cell_type": "code", "execution_count": 14, "id": "c5fedcea", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Scaling factor set to tensor(1.0026, device='cuda:0')\n"]}], "source": ["with torch.no_grad():\n", "    with autocast(enabled=True):\n", "        z = autoencoder.encode_stage_2_inputs(check_data[\"image\"].to(device))\n", "\n", "print(f\"Scaling factor set to {1/torch.std(z)}\")\n", "scale_factor = 1 / torch.std(z)"]}, {"cell_type": "markdown", "id": "439ff2d8", "metadata": {}, "source": ["We define the inferer using the scale factor:"]}, {"cell_type": "code", "execution_count": null, "id": "7de37f3a", "metadata": {}, "outputs": [], "source": ["inferer = LatentDiffusionInferer(scheduler, scale_factor=scale_factor)"]}, {"cell_type": "code", "execution_count": 43, "id": "5eef3ec7", "metadata": {}, "outputs": [], "source": ["optimizer_diff = torch.optim.Adam(params=unet.parameters(), lr=1e-4)"]}, {"cell_type": "markdown", "id": "4705c795", "metadata": {}, "source": ["### Train diffusion model"]}, {"cell_type": "code", "execution_count": 44, "id": "0a7f6459", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Epoch 0: 100%|███████████| 194/194 [00:23<00:00,  8.24it/s, loss=0.58]\n", "Epoch 1: 100%|██████████| 194/194 [00:24<00:00,  7.85it/s, loss=0.356]\n", "Epoch 2: 100%|██████████| 194/194 [00:23<00:00,  8.30it/s, loss=0.315]\n", "Epoch 3: 100%|███████████| 194/194 [00:24<00:00,  8.05it/s, loss=0.29]\n", "Epoch 4: 100%|██████████| 194/194 [00:23<00:00,  8.41it/s, loss=0.277]\n", "Epoch 5: 100%|██████████| 194/194 [00:23<00:00,  8.43it/s, loss=0.253]\n", "Epoch 6: 100%|██████████| 194/194 [00:22<00:00,  8.45it/s, loss=0.276]\n", "Epoch 7: 100%|██████████| 194/194 [00:22<00:00,  8.47it/s, loss=0.302]\n", "Epoch 8: 100%|██████████| 194/194 [00:24<00:00,  8.02it/s, loss=0.258]\n", "Epoch 9: 100%|███████████| 194/194 [00:23<00:00,  8.28it/s, loss=0.26]\n", "Epoch 10: 100%|█████████| 194/194 [00:23<00:00,  8.18it/s, loss=0.247]\n", "Epoch 11: 100%|██████████| 194/194 [00:22<00:00,  8.47it/s, loss=0.25]\n", "Epoch 12: 100%|█████████| 194/194 [00:23<00:00,  8.36it/s, loss=0.286]\n", "Epoch 13: 100%|██████████| 194/194 [00:23<00:00,  8.40it/s, loss=0.26]\n", "Epoch 14: 100%|█████████| 194/194 [00:23<00:00,  8.38it/s, loss=0.307]\n", "Epoch 15: 100%|██████████| 194/194 [00:23<00:00,  8.41it/s, loss=0.28]\n", "Epoch 16: 100%|█████████| 194/194 [00:23<00:00,  8.43it/s, loss=0.291]\n", "Epoch 17: 100%|█████████| 194/194 [00:23<00:00,  8.40it/s, loss=0.274]\n", "Epoch 18: 100%|█████████| 194/194 [00:23<00:00,  8.43it/s, loss=0.257]\n", "Epoch 19: 100%|█████████| 194/194 [00:24<00:00,  8.02it/s, loss=0.276]\n", "Epoch 20: 100%|█████████| 194/194 [00:23<00:00,  8.21it/s, loss=0.255]\n", "Epoch 21: 100%|█████████| 194/194 [00:23<00:00,  8.28it/s, loss=0.245]\n", "Epoch 22: 100%|█████████| 194/194 [00:23<00:00,  8.34it/s, loss=0.276]\n", "Epoch 23: 100%|█████████| 194/194 [00:23<00:00,  8.17it/s, loss=0.232]\n", "Epoch 24: 100%|█████████| 194/194 [00:23<00:00,  8.43it/s, loss=0.256]\n", "Epoch 25: 100%|█████████| 194/194 [00:23<00:00,  8.21it/s, loss=0.243]\n", "Epoch 26: 100%|█████████| 194/194 [00:24<00:00,  8.06it/s, loss=0.249]\n", "Epoch 27: 100%|█████████| 194/194 [00:23<00:00,  8.26it/s, loss=0.283]\n", "Epoch 28: 100%|█████████| 194/194 [00:23<00:00,  8.28it/s, loss=0.277]\n", "Epoch 29: 100%|█████████| 194/194 [00:23<00:00,  8.30it/s, loss=0.279]\n", "Epoch 30: 100%|█████████| 194/194 [00:23<00:00,  8.09it/s, loss=0.251]\n", "Epoch 31: 100%|█████████| 194/194 [00:23<00:00,  8.17it/s, loss=0.257]\n", "Epoch 32: 100%|█████████| 194/194 [00:23<00:00,  8.20it/s, loss=0.278]\n", "Epoch 33: 100%|█████████| 194/194 [00:23<00:00,  8.33it/s, loss=0.264]\n", "Epoch 34: 100%|█████████| 194/194 [00:23<00:00,  8.20it/s, loss=0.256]\n", "Epoch 35: 100%|█████████| 194/194 [00:24<00:00,  7.92it/s, loss=0.266]\n", "Epoch 36: 100%|█████████| 194/194 [00:23<00:00,  8.33it/s, loss=0.253]\n", "Epoch 37: 100%|█████████| 194/194 [00:23<00:00,  8.13it/s, loss=0.268]\n", "Epoch 38: 100%|█████████| 194/194 [00:23<00:00,  8.13it/s, loss=0.257]\n", "Epoch 39: 100%|█████████| 194/194 [00:23<00:00,  8.25it/s, loss=0.246]\n", "Epoch 40: 100%|█████████| 194/194 [00:23<00:00,  8.37it/s, loss=0.254]\n", "Epoch 41: 100%|█████████| 194/194 [00:23<00:00,  8.15it/s, loss=0.277]\n", "Epoch 42: 100%|█████████| 194/194 [00:24<00:00,  7.79it/s, loss=0.277]\n", "Epoch 43: 100%|█████████| 194/194 [00:23<00:00,  8.11it/s, loss=0.259]\n", "Epoch 44: 100%|█████████| 194/194 [00:24<00:00,  7.82it/s, loss=0.258]\n", "Epoch 45: 100%|█████████| 194/194 [00:23<00:00,  8.30it/s, loss=0.249]\n", "Epoch 46: 100%|██████████| 194/194 [00:23<00:00,  8.31it/s, loss=0.26]\n", "Epoch 47: 100%|█████████| 194/194 [00:23<00:00,  8.19it/s, loss=0.261]\n", "Epoch 48: 100%|█████████| 194/194 [00:23<00:00,  8.16it/s, loss=0.275]\n", "Epoch 49: 100%|█████████| 194/194 [00:23<00:00,  8.38it/s, loss=0.259]\n", "Epoch 50: 100%|█████████| 194/194 [00:23<00:00,  8.28it/s, loss=0.291]\n", "Epoch 51: 100%|█████████| 194/194 [00:23<00:00,  8.23it/s, loss=0.268]\n", "Epoch 52: 100%|█████████| 194/194 [00:23<00:00,  8.29it/s, loss=0.272]\n", "Epoch 53: 100%|█████████| 194/194 [00:23<00:00,  8.15it/s, loss=0.251]\n", "Epoch 54: 100%|█████████| 194/194 [00:23<00:00,  8.13it/s, loss=0.289]\n", "Epoch 55: 100%|█████████| 194/194 [00:24<00:00,  8.03it/s, loss=0.261]\n", "Epoch 56: 100%|██████████| 194/194 [00:23<00:00,  8.18it/s, loss=0.28]\n", "Epoch 57: 100%|█████████| 194/194 [00:23<00:00,  8.22it/s, loss=0.259]\n", "Epoch 58: 100%|█████████| 194/194 [00:24<00:00,  7.83it/s, loss=0.248]\n", "Epoch 59: 100%|██████████| 194/194 [00:23<00:00,  8.33it/s, loss=0.25]\n", "Epoch 60: 100%|█████████| 194/194 [00:24<00:00,  8.00it/s, loss=0.273]\n", "Epoch 61: 100%|█████████| 194/194 [00:24<00:00,  7.79it/s, loss=0.259]\n", "Epoch 62: 100%|█████████| 194/194 [00:24<00:00,  8.08it/s, loss=0.257]\n", "Epoch 63: 100%|█████████| 194/194 [00:25<00:00,  7.75it/s, loss=0.263]\n", "Epoch 64: 100%|█████████| 194/194 [00:23<00:00,  8.31it/s, loss=0.251]\n", "Epoch 65: 100%|█████████| 194/194 [00:24<00:00,  7.98it/s, loss=0.278]\n", "Epoch 66: 100%|█████████| 194/194 [00:24<00:00,  7.81it/s, loss=0.258]\n", "Epoch 67: 100%|█████████| 194/194 [00:23<00:00,  8.14it/s, loss=0.288]\n", "Epoch 68: 100%|█████████| 194/194 [00:24<00:00,  8.05it/s, loss=0.248]\n", "Epoch 69: 100%|█████████| 194/194 [00:24<00:00,  8.03it/s, loss=0.257]\n", "Epoch 70: 100%|█████████| 194/194 [00:23<00:00,  8.39it/s, loss=0.246]\n", "Epoch 71: 100%|█████████| 194/194 [00:24<00:00,  7.88it/s, loss=0.288]\n", "Epoch 72: 100%|█████████| 194/194 [00:23<00:00,  8.38it/s, loss=0.282]\n", "Epoch 73: 100%|█████████| 194/194 [00:22<00:00,  8.44it/s, loss=0.287]\n", "Epoch 74: 100%|█████████| 194/194 [00:23<00:00,  8.29it/s, loss=0.282]\n", "Epoch 75: 100%|█████████| 194/194 [00:23<00:00,  8.42it/s, loss=0.263]\n", "Epoch 76: 100%|█████████| 194/194 [00:23<00:00,  8.11it/s, loss=0.286]\n", "Epoch 77: 100%|█████████| 194/194 [00:25<00:00,  7.68it/s, loss=0.252]\n", "Epoch 78: 100%|█████████| 194/194 [00:23<00:00,  8.18it/s, loss=0.273]\n", "Epoch 79: 100%|█████████| 194/194 [00:23<00:00,  8.33it/s, loss=0.235]\n", "Epoch 80: 100%|█████████| 194/194 [00:23<00:00,  8.34it/s, loss=0.265]\n", "Epoch 81: 100%|█████████| 194/194 [00:23<00:00,  8.21it/s, loss=0.258]\n", "Epoch 82: 100%|█████████| 194/194 [00:23<00:00,  8.36it/s, loss=0.243]\n", "Epoch 83: 100%|█████████| 194/194 [00:23<00:00,  8.11it/s, loss=0.251]\n", "Epoch 84: 100%|█████████| 194/194 [00:24<00:00,  8.01it/s, loss=0.306]\n", "Epoch 85: 100%|█████████| 194/194 [00:24<00:00,  8.07it/s, loss=0.265]\n", "Epoch 86: 100%|█████████| 194/194 [00:23<00:00,  8.12it/s, loss=0.243]\n", "Epoch 87: 100%|█████████| 194/194 [00:23<00:00,  8.17it/s, loss=0.257]\n", "Epoch 88: 100%|█████████| 194/194 [00:23<00:00,  8.32it/s, loss=0.268]\n", "Epoch 89: 100%|█████████| 194/194 [00:23<00:00,  8.16it/s, loss=0.263]\n", "Epoch 90: 100%|█████████| 194/194 [00:23<00:00,  8.19it/s, loss=0.244]\n", "Epoch 91: 100%|█████████| 194/194 [00:23<00:00,  8.39it/s, loss=0.278]\n", "Epoch 92: 100%|█████████| 194/194 [00:23<00:00,  8.28it/s, loss=0.274]\n", "Epoch 93: 100%|██████████| 194/194 [00:23<00:00,  8.15it/s, loss=0.24]\n", "Epoch 94: 100%|█████████| 194/194 [00:24<00:00,  8.01it/s, loss=0.275]\n", "Epoch 95: 100%|█████████| 194/194 [00:24<00:00,  8.06it/s, loss=0.259]\n", "Epoch 96: 100%|█████████| 194/194 [00:24<00:00,  8.00it/s, loss=0.247]\n", "Epoch 97: 100%|█████████| 194/194 [00:23<00:00,  8.32it/s, loss=0.273]\n", "Epoch 98: 100%|█████████| 194/194 [00:25<00:00,  7.75it/s, loss=0.262]\n", "Epoch 99: 100%|█████████| 194/194 [00:25<00:00,  7.71it/s, loss=0.281]\n", "Epoch 100: 100%|████████| 194/194 [00:24<00:00,  7.82it/s, loss=0.301]\n", "Epoch 101: 100%|████████| 194/194 [00:23<00:00,  8.39it/s, loss=0.276]\n", "Epoch 102: 100%|████████| 194/194 [00:24<00:00,  7.83it/s, loss=0.279]\n", "Epoch 103: 100%|████████| 194/194 [00:23<00:00,  8.34it/s, loss=0.289]\n", "Epoch 104: 100%|████████| 194/194 [00:23<00:00,  8.35it/s, loss=0.277]\n", "Epoch 105: 100%|████████| 194/194 [00:23<00:00,  8.42it/s, loss=0.251]\n", "Epoch 106: 100%|████████| 194/194 [00:23<00:00,  8.36it/s, loss=0.262]\n", "Epoch 107: 100%|████████| 194/194 [00:23<00:00,  8.41it/s, loss=0.273]\n", "Epoch 108: 100%|████████| 194/194 [00:23<00:00,  8.41it/s, loss=0.283]\n", "Epoch 109: 100%|█████████| 194/194 [00:23<00:00,  8.38it/s, loss=0.31]\n", "Epoch 110: 100%|████████| 194/194 [00:23<00:00,  8.43it/s, loss=0.278]\n", "Epoch 111: 100%|████████| 194/194 [00:23<00:00,  8.35it/s, loss=0.256]\n", "Epoch 112: 100%|█████████| 194/194 [00:23<00:00,  8.43it/s, loss=0.26]\n", "Epoch 113: 100%|████████| 194/194 [00:24<00:00,  7.92it/s, loss=0.251]\n", "Epoch 114: 100%|████████| 194/194 [00:25<00:00,  7.74it/s, loss=0.274]\n", "Epoch 115: 100%|████████| 194/194 [00:23<00:00,  8.12it/s, loss=0.289]\n", "Epoch 116: 100%|████████| 194/194 [00:24<00:00,  7.88it/s, loss=0.262]\n", "Epoch 117: 100%|████████| 194/194 [00:24<00:00,  8.04it/s, loss=0.247]\n", "Epoch 118: 100%|█████████| 194/194 [00:23<00:00,  8.41it/s, loss=0.25]\n", "Epoch 119: 100%|████████| 194/194 [00:23<00:00,  8.43it/s, loss=0.263]\n", "Epoch 120: 100%|████████| 194/194 [00:23<00:00,  8.40it/s, loss=0.259]\n", "Epoch 121: 100%|████████| 194/194 [00:23<00:00,  8.42it/s, loss=0.258]\n", "Epoch 122: 100%|████████| 194/194 [00:23<00:00,  8.37it/s, loss=0.272]\n", "Epoch 123: 100%|████████| 194/194 [00:23<00:00,  8.42it/s, loss=0.248]\n", "Epoch 124: 100%|████████| 194/194 [00:22<00:00,  8.44it/s, loss=0.286]\n", "Epoch 125: 100%|████████| 194/194 [00:23<00:00,  8.36it/s, loss=0.288]\n", "Epoch 126: 100%|████████| 194/194 [00:23<00:00,  8.43it/s, loss=0.283]\n", "Epoch 127: 100%|████████| 194/194 [00:23<00:00,  8.39it/s, loss=0.283]\n", "Epoch 128: 100%|████████| 194/194 [00:24<00:00,  7.86it/s, loss=0.256]\n", "Epoch 129: 100%|████████| 194/194 [00:24<00:00,  7.91it/s, loss=0.268]\n", "Epoch 130: 100%|████████| 194/194 [00:24<00:00,  8.05it/s, loss=0.266]\n", "Epoch 131: 100%|████████| 194/194 [00:22<00:00,  8.47it/s, loss=0.276]\n", "Epoch 132: 100%|█████████| 194/194 [00:23<00:00,  8.24it/s, loss=0.25]\n", "Epoch 133: 100%|████████| 194/194 [00:23<00:00,  8.24it/s, loss=0.295]\n", "Epoch 134: 100%|████████| 194/194 [00:23<00:00,  8.22it/s, loss=0.263]\n", "Epoch 135: 100%|████████| 194/194 [00:23<00:00,  8.28it/s, loss=0.248]\n", "Epoch 136: 100%|████████| 194/194 [00:22<00:00,  8.44it/s, loss=0.234]\n", "Epoch 137: 100%|████████| 194/194 [00:23<00:00,  8.41it/s, loss=0.265]\n", "Epoch 138: 100%|████████| 194/194 [00:25<00:00,  7.67it/s, loss=0.288]\n", "Epoch 139: 100%|████████| 194/194 [00:23<00:00,  8.35it/s, loss=0.232]\n", "Epoch 140: 100%|████████| 194/194 [00:23<00:00,  8.37it/s, loss=0.275]\n", "Epoch 141: 100%|████████| 194/194 [00:23<00:00,  8.38it/s, loss=0.267]\n", "Epoch 142: 100%|████████| 194/194 [00:23<00:00,  8.31it/s, loss=0.247]\n", "Epoch 143: 100%|████████| 194/194 [00:23<00:00,  8.39it/s, loss=0.261]\n", "Epoch 144: 100%|████████| 194/194 [00:23<00:00,  8.23it/s, loss=0.263]\n", "Epoch 145: 100%|████████| 194/194 [00:24<00:00,  7.80it/s, loss=0.265]\n", "Epoch 146: 100%|████████| 194/194 [00:23<00:00,  8.29it/s, loss=0.276]\n", "Epoch 147: 100%|████████| 194/194 [00:23<00:00,  8.27it/s, loss=0.263]\n", "Epoch 148: 100%|████████| 194/194 [00:23<00:00,  8.14it/s, loss=0.275]\n", "Epoch 149: 100%|████████| 194/194 [00:23<00:00,  8.36it/s, loss=0.266]\n"]}], "source": ["n_epochs = 150\n", "epoch_loss_list = []\n", "autoencoder.eval()\n", "scaler = GradScaler()\n", "\n", "first_batch = first(train_loader)\n", "z = autoencoder.encode_stage_2_inputs(first_batch[\"image\"].to(device))\n", "\n", "for epoch in range(n_epochs):\n", "    unet.train()\n", "    epoch_loss = 0\n", "    progress_bar = tqdm(enumerate(train_loader), total=len(train_loader), ncols=70)\n", "    progress_bar.set_description(f\"Epoch {epoch}\")\n", "    for step, batch in progress_bar:\n", "        images = batch[\"image\"].to(device)\n", "        optimizer_diff.zero_grad(set_to_none=True)\n", "\n", "        with autocast(enabled=True):\n", "            # Generate random noise\n", "            noise = torch.randn_like(z).to(device)\n", "\n", "            # Create timesteps\n", "            timesteps = torch.randint(\n", "                0, inferer.scheduler.num_train_timesteps, (images.shape[0],), device=images.device\n", "            ).long()\n", "\n", "            # Get model prediction\n", "            noise_pred = inferer(\n", "                inputs=images, autoencoder_model=autoencoder, diffusion_model=unet, noise=noise, timesteps=timesteps\n", "            )\n", "\n", "            loss = F.mse_loss(noise_pred.float(), noise.float())\n", "\n", "        scaler.scale(loss).backward()\n", "        scaler.step(optimizer_diff)\n", "        scaler.update()\n", "\n", "        epoch_loss += loss.item()\n", "\n", "        progress_bar.set_postfix({\"loss\": epoch_loss / (step + 1)})\n", "    epoch_loss_list.append(epoch_loss / (step + 1))"]}, {"cell_type": "code", "execution_count": 18, "id": "93b93696", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2023-02-19 14:12:22,536 - No artists with labels found to put in legend.  Note that artists whose label start with an underscore are ignored when legend() is called with no argument.\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.plot(epoch_loss_list)\n", "plt.title(\"Learning Curves\", fontsize=20)\n", "plt.plot(epoch_loss_list)\n", "plt.yticks(fontsize=12)\n", "plt.xticks(fontsize=12)\n", "plt.xlabel(\"Epochs\", fontsize=16)\n", "plt.ylabel(\"Loss\", fontsize=16)\n", "plt.legend(prop={\"size\": 14})\n", "plt.show()"]}, {"cell_type": "markdown", "id": "c9de4288", "metadata": {}, "source": ["### Plotting sampling example\n", "\n", "Finally, we generate an image with our LDM. For that, we will initialize a latent representation with just noise. Then, we will use the `unet` to perform 1000 denoising steps. In the last step, we decode the latent representation and plot the sampled image."]}, {"cell_type": "code", "execution_count": 51, "id": "bc946d70", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 1000/1000 [00:15<00:00, 64.03it/s]\n"]}], "source": ["autoencoder.eval()\n", "unet.eval()\n", "\n", "noise = torch.randn((1, 3, 24, 24, 16))\n", "noise = noise.to(device)\n", "scheduler.set_timesteps(num_inference_steps=1000)\n", "synthetic_images = inferer.sample(\n", "    input_noise=noise, autoencoder_model=autoencoder, diffusion_model=unet, scheduler=scheduler\n", ")"]}, {"cell_type": "markdown", "id": "fed68b96", "metadata": {}, "source": ["### Visualise synthetic data"]}, {"cell_type": "code", "execution_count": 52, "id": "0763caa1", "metadata": {}, "outputs": [{"data": {"text/plain": ["<matplotlib.image.AxesImage at 0x7f510c1672e0>"]}, "execution_count": 52, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["idx = 0\n", "img = synthetic_images[idx, channel].detach().cpu().numpy()  # images\n", "fig, axs = plt.subplots(nrows=1, ncols=3)\n", "for ax in axs:\n", "    ax.axis(\"off\")\n", "ax = axs[0]\n", "ax.imshow(img[..., img.shape[2] // 2], cmap=\"gray\")\n", "ax = axs[1]\n", "ax.imshow(img[:, img.shape[1] // 2, ...], cmap=\"gray\")\n", "ax = axs[2]\n", "ax.imshow(img[img.shape[0] // 2, ...], cmap=\"gray\")"]}, {"cell_type": "markdown", "id": "d3ab7b79", "metadata": {}, "source": ["## Clean-up data"]}, {"cell_type": "code", "execution_count": 21, "id": "52b71f99", "metadata": {}, "outputs": [], "source": ["if directory is None:\n", "    shutil.rmtree(root_dir)"]}], "metadata": {"jupytext": {"cell_metadata_filter": "-all", "formats": "ipynb,py"}, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.13"}, "vscode": {"interpreter": {"hash": "a7e6f8385898884a13cbe220eefefb32cba5012927a94186742ddc14746e4dba"}}}, "nbformat": 4, "nbformat_minor": 5}