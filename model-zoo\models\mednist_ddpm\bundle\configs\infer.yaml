# This defines an inference script for generating a random image to a Pytorch file

batch_size: 1
num_workers: 0

noise: $torch.rand(1,1,@image_dim,@image_dim)  # create a random image every time this program is run

out_file: ""  # where to save the tensor to

# using a lambda this defines a simple sampling function used below
sample: '$lambda x: @inferer.sample(input_noise=x, diffusion_model=@network, scheduler=@scheduler)'

load_state: '$@network.load_state_dict(torch.load(@ckpt_path))'  # command to load the saved model weights

save_trans:
  _target_: Compose
  transforms:
  - _target_: ScaleIntensity
    minv: 0.0
    maxv: 255.0
  - _target_: ToTensor
    track_meta: false
  - _target_: SaveImage
    output_ext: "jpg"
    resample: false
    output_dtype: '$torch.uint8'
    separate_folder: false
    output_postfix: '@out_file'

# program to load the model weights, run `sample`, and store results to `out_file`
testing:
- '@load_state'
- '$torch.save(@sample(@noise.to(@device)), @out_file)'

#alternative version which saves to a jpg file
testing_jpg:
- '@load_state'
- '$@save_trans(@sample(@noise.to(@device))[0])'
