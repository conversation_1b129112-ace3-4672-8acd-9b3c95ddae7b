"""
BUS到CEUS图像转换的DDIM模型测试脚本
支持加载训练好的模型进行推理和评估
"""

import os
import argparse
import json
import time
from typing import Dict, List, Tuple

import torch
import torch.nn as nn
from torch.utils.data import DataLoader
import numpy as np
from tqdm import tqdm
import matplotlib.pyplot as plt

from bus_ceus_dataset import BUSCEUSDataset
from conditional_ddim_model import create_conditional_ddim_model
from metrics import MetricsCalculator, normalize_for_metrics
from visualization import create_comparison_grid, save_individual_images


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='Test BUS to CEUS DDIM model')
    
    # 模型和数据参数
    parser.add_argument('--checkpoint', type=str, required=True,
                       help='Path to model checkpoint')
    parser.add_argument('--bus_dir', type=str, required=True,
                       help='BUS images directory for testing')
    parser.add_argument('--ceus_dir', type=str, default=None,
                       help='CEUS images directory for evaluation (optional)')
    
    # 推理参数
    parser.add_argument('--batch_size', type=int, default=8,
                       help='Batch size for inference')
    parser.add_argument('--num_inference_steps', type=int, default=50,
                       help='Number of DDIM inference steps')
    parser.add_argument('--image_size', type=int, nargs=2, default=[256, 256],
                       help='Image size (height width)')
    
    # 输出参数
    parser.add_argument('--output_dir', type=str, default='test_results',
                       help='Output directory for results')
    parser.add_argument('--save_images', action='store_true',
                       help='Save generated images')
    parser.add_argument('--save_comparisons', action='store_true',
                       help='Save comparison grids')
    parser.add_argument('--num_samples', type=int, default=16,
                       help='Number of samples to visualize')
    
    # 其他参数
    parser.add_argument('--device', type=str, default='cuda',
                       help='Device to use (cuda/cpu)')
    parser.add_argument('--num_workers', type=int, default=4,
                       help='Number of data loading workers')
    parser.add_argument('--seed', type=int, default=42,
                       help='Random seed for reproducible results')
    
    return parser.parse_args()


def load_model_from_checkpoint(checkpoint_path: str, device: str):
    """从检查点加载模型"""
    print(f"Loading model from {checkpoint_path}")
    
    # 加载检查点
    checkpoint = torch.load(checkpoint_path, map_location=device)
    
    # 从检查点中获取模型配置（如果有的话）
    # 这里使用默认配置，实际使用时可能需要从训练时保存的配置中读取
    model, scheduler, inferer = create_conditional_ddim_model(
        image_size=(256, 256),  # 可以从checkpoint或配置文件中读取
        in_channels=1,
        out_channels=1,
        num_train_timesteps=1000,
        device=device
    )
    
    # 加载模型权重
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()
    
    print(f"Model loaded successfully from epoch {checkpoint['epoch']}")
    print(f"Model validation loss: {checkpoint.get('val_loss', 'N/A')}")
    
    return model, scheduler, inferer, checkpoint


def generate_images(
    model: nn.Module,
    inferer,
    bus_images: torch.Tensor,
    num_inference_steps: int = 50,
    device: str = 'cuda'
) -> torch.Tensor:
    """生成CEUS图像"""
    model.eval()
    
    with torch.no_grad():
        # 设置推理步数
        inferer.scheduler.set_timesteps(num_inference_steps)
        
        # 创建初始噪声
        noise = torch.randn_like(bus_images).to(device)
        
        # 生成图像
        generated_images, _ = inferer.sample(
            input_noise=noise,
            diffusion_model=model,
            condition=bus_images,
            verbose=True
        )
    
    return generated_images


def evaluate_model(
    model: nn.Module,
    inferer,
    test_loader: DataLoader,
    device: str,
    num_inference_steps: int = 50
) -> Dict[str, float]:
    """评估模型性能"""
    print("Evaluating model...")
    
    metrics_calc = MetricsCalculator(data_range=1.0)
    
    all_bus_images = []
    all_target_images = []
    all_generated_images = []
    
    model.eval()
    
    with torch.no_grad():
        for batch_idx, batch in enumerate(tqdm(test_loader, desc="Generating images")):
            bus_images = batch['bus'].to(device)
            
            # 生成图像
            generated_images = generate_images(
                model, inferer, bus_images, num_inference_steps, device
            )
            
            # 收集图像
            all_bus_images.append(bus_images.cpu())
            all_generated_images.append(generated_images.cpu())
            
            # 如果有目标图像，也收集起来
            if 'ceus' in batch:
                target_images = batch['ceus']
                all_target_images.append(target_images.cpu())
    
    # 合并所有图像
    all_bus_images = torch.cat(all_bus_images, dim=0)
    all_generated_images = torch.cat(all_generated_images, dim=0)
    
    results = {
        'bus_images': all_bus_images,
        'generated_images': all_generated_images,
        'num_samples': len(all_generated_images)
    }
    
    # 如果有目标图像，计算指标
    if all_target_images:
        all_target_images = torch.cat(all_target_images, dim=0)
        results['target_images'] = all_target_images
        
        # 归一化图像用于指标计算
        gen_norm, target_norm = normalize_for_metrics(
            all_generated_images, all_target_images
        )
        
        # 计算指标
        metrics_calc.update(gen_norm, target_norm)
        metrics = metrics_calc.compute()
        results['metrics'] = metrics
        
        print("\nEvaluation Results:")
        print("-" * 30)
        for key, value in metrics.items():
            print(f"{key.upper()}: {value:.4f}")
    
    return results


def save_results(
    results: Dict,
    output_dir: str,
    args,
    save_images: bool = True,
    save_comparisons: bool = True,
    num_samples: int = 16
):
    """保存测试结果"""
    os.makedirs(output_dir, exist_ok=True)
    
    bus_images = results['bus_images']
    generated_images = results['generated_images']
    target_images = results.get('target_images', None)
    
    # 限制样本数量
    num_samples = min(num_samples, len(bus_images))
    
    # 保存指标结果
    if 'metrics' in results:
        metrics_file = os.path.join(output_dir, 'metrics.json')
        with open(metrics_file, 'w') as f:
            json.dump(results['metrics'], f, indent=2)
        print(f"Metrics saved to {metrics_file}")
    
    # 保存测试配置
    config_file = os.path.join(output_dir, 'test_config.json')
    with open(config_file, 'w') as f:
        json.dump(vars(args), f, indent=2)
    
    # 保存单独的图像
    if save_images:
        # 保存BUS图像
        bus_dir = os.path.join(output_dir, 'bus_images')
        save_individual_images(
            bus_images[:num_samples], bus_dir, prefix='bus'
        )
        
        # 保存生成的图像
        generated_dir = os.path.join(output_dir, 'generated_images')
        save_individual_images(
            generated_images[:num_samples], generated_dir, prefix='generated'
        )
        
        # 保存目标图像（如果有）
        if target_images is not None:
            target_dir = os.path.join(output_dir, 'target_images')
            save_individual_images(
                target_images[:num_samples], target_dir, prefix='target'
            )
        
        print(f"Individual images saved to {output_dir}")
    
    # 保存对比图像
    if save_comparisons:
        if target_images is not None:
            # 有目标图像时的三列对比
            fig = create_comparison_grid(
                bus_images[:num_samples],
                target_images[:num_samples],
                generated_images[:num_samples],
                num_samples=min(8, num_samples),
                figsize=(15, min(8, num_samples) * 3)
            )
        else:
            # 只有BUS和生成图像的两列对比
            fig, axes = plt.subplots(min(8, num_samples), 2, figsize=(10, min(8, num_samples) * 3))
            if min(8, num_samples) == 1:
                axes = axes.reshape(1, -1)
            
            for i in range(min(8, num_samples)):
                # BUS图像
                axes[i, 0].imshow(bus_images[i, 0].numpy(), cmap='gray', vmin=0, vmax=1)
                axes[i, 0].set_title(f'BUS Image {i+1}')
                axes[i, 0].axis('off')
                
                # 生成的图像
                axes[i, 1].imshow(generated_images[i, 0].numpy(), cmap='gray', vmin=0, vmax=1)
                axes[i, 1].set_title(f'Generated CEUS {i+1}')
                axes[i, 1].axis('off')
            
            plt.tight_layout()
        
        comparison_file = os.path.join(output_dir, 'comparison_grid.png')
        fig.savefig(comparison_file, dpi=150, bbox_inches='tight')
        plt.close(fig)
        
        print(f"Comparison grid saved to {comparison_file}")


def main():
    """主测试函数"""
    args = parse_args()
    
    # 设置随机种子
    torch.manual_seed(args.seed)
    np.random.seed(args.seed)
    
    # 设置设备
    device = torch.device(args.device if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # 加载模型
    model, scheduler, inferer, checkpoint = load_model_from_checkpoint(
        args.checkpoint, device
    )
    
    # 创建测试数据集
    print("Creating test dataset...")
    test_dataset = BUSCEUSDataset(
        bus_dir=args.bus_dir,
        ceus_dir=args.ceus_dir,
        image_size=tuple(args.image_size),
        augmentation=False,  # 测试时不使用数据增强
        normalize=True
    )
    
    test_loader = DataLoader(
        test_dataset,
        batch_size=args.batch_size,
        shuffle=False,
        num_workers=args.num_workers,
        pin_memory=True
    )
    
    print(f"Test dataset size: {len(test_dataset)}")
    print(f"Test batches: {len(test_loader)}")
    
    # 评估模型
    start_time = time.time()
    results = evaluate_model(
        model, inferer, test_loader, device, args.num_inference_steps
    )
    end_time = time.time()
    
    print(f"\nInference completed in {end_time - start_time:.2f} seconds")
    print(f"Average time per image: {(end_time - start_time) / results['num_samples']:.3f} seconds")
    
    # 保存结果
    print("\nSaving results...")
    save_results(
        results, args.output_dir, args,
        save_images=args.save_images,
        save_comparisons=args.save_comparisons,
        num_samples=args.num_samples
    )
    
    print(f"\nTesting completed! Results saved to {args.output_dir}")


if __name__ == "__main__":
    main()
