"""
快速测试脚本 - 验证模型前向传播是否正常
"""

import os
# 解决OpenMP库冲突问题
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

import torch
import numpy as np

# 导入我们的模块
from conditional_ddim_model import create_conditional_ddim_model

def test_model_forward():
    """测试模型前向传播"""
    print("Testing model forward pass...")
    
    try:
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"Using device: {device}")
        
        # 创建模型
        model, scheduler, inferer = create_conditional_ddim_model(
            image_size=(256, 256),
            in_channels=1,
            out_channels=1,
            num_train_timesteps=1000,
            device=device
        )
        
        print(f"Model parameters: {sum(p.numel() for p in model.parameters()):,}")
        
        # 测试前向传播
        batch_size = 2
        bus_images = torch.randn(batch_size, 1, 256, 256).to(device)
        ceus_images = torch.randn(batch_size, 1, 256, 256).to(device)
        
        # 测试训练模式的前向传播
        model.train()
        noise = torch.randn_like(ceus_images)
        timesteps = torch.randint(0, scheduler.num_train_timesteps, 
                                (batch_size,), device=device)
        noisy_images = scheduler.add_noise(ceus_images, noise, timesteps)
        
        # 模型预测 - 使用正确的接口
        noise_pred = model(
            x=noisy_images,
            timesteps=timesteps,
            condition=bus_images
        )
        
        print(f"Input noisy images shape: {noisy_images.shape}")
        print(f"Input condition shape: {bus_images.shape}")
        print(f"Noise prediction shape: {noise_pred.shape}")
        print(f"Expected shape: {noise.shape}")
        
        # 计算损失
        loss = torch.nn.functional.mse_loss(noise_pred, noise)
        print(f"Training loss: {loss.item():.6f}")
        
        # 测试推理模式
        model.eval()
        with torch.no_grad():
            # 设置推理步数
            inferer.scheduler.set_timesteps(50)
            
            # 生成图像
            noise = torch.randn_like(ceus_images)
            generated_images, _ = inferer.sample(
                input_noise=noise,
                diffusion_model=model,
                condition=bus_images,
                verbose=False
            )
            
            print(f"Generated images shape: {generated_images.shape}")
        
        print("✓ Model forward pass test passed!")
        return True
        
    except Exception as e:
        print(f"✗ Model forward pass test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("Quick Model Test")
    print("=" * 30)
    
    success = test_model_forward()
    
    print("\n" + "=" * 30)
    if success:
        print("🎉 Test passed! Model is working correctly.")
        print("You can now run the full training script.")
    else:
        print("❌ Test failed. Please check the error messages above.")
    
    return success


if __name__ == "__main__":
    main()
