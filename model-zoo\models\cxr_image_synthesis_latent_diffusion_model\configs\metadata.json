{"schema": "https://github.com/Project-MONAI/MONAI-extra-test-data/releases/download/0.8.1/meta_schema_20220324.json", "version": "1.0.0", "changelog": {"0.2": "Flipped images fixed"}, "monai_version": "1.1.0", "pytorch_version": "1.13.0", "numpy_version": "1.22.4", "optional_packages_version": {"nibabel": "4.0.1", "generative": "0.1.0", "transformers": "4.26.1"}, "task": "Chest X-ray image synthesis", "description": "A generative model for creating high-resolution chest X-ray based on MIMIC dataset", "copyright": "Copyright (c) MONAI Consortium", "data_source": "https://physionet.org/content/mimic-cxr-jpg/2.0.0/", "data_type": "image", "image_classes": "Radiography (X-ray) with 512 x 512 pixels", "intended_use": "This is a research tool/prototype and not to be used clinically", "network_data_format": {"inputs": {"latent_representation": {"type": "image", "format": "magnitude", "modality": "CXR", "num_channels": 3, "spatial_shape": [64, 64], "dtype": "float32", "value_range": [], "is_patch_data": false}, "timesteps": {"type": "vector", "value_range": [0, 1000], "dtype": "long"}, "context": {"type": "vector", "value_range": [], "dtype": "float32"}}, "outputs": {"pred": {"type": "image", "format": "magnitude", "modality": "CXR", "num_channels": 1, "spatial_shape": [512, 512], "dtype": "float32", "value_range": [0, 1], "is_patch_data": false, "channel_def": {"0": "X-ray"}}}}}