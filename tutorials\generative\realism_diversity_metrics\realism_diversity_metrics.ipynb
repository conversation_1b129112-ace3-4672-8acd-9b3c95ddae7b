{"cells": [{"cell_type": "code", "execution_count": 1, "id": "c6161aec", "metadata": {}, "outputs": [], "source": ["# Copyright (c) MONAI Consortium\n", "# Licensed under the Apache License, Version 2.0 (the \"License\");\n", "# you may not use this file except in compliance with the License.\n", "# You may obtain a copy of the License at\n", "#     http://www.apache.org/licenses/LICENSE-2.0\n", "# Unless required by applicable law or agreed to in writing, software\n", "# distributed under the License is distributed on an \"AS IS\" BASIS,\n", "# WITHOUT WAR<PERSON><PERSON>IES OR CONDITIONS OF ANY KIND, either express or implied.\n", "# See the License for the specific language governing permissions and\n", "# limitations under the License."]}, {"cell_type": "markdown", "id": "0e837e16", "metadata": {}, "source": ["# Evaluate Realism and Diversity of the generated images"]}, {"cell_type": "markdown", "id": "7dcfe817", "metadata": {}, "source": ["This notebook illustrates how to use the generative model package to compute the most common metrics to evaluate the performance of a generative model. The metrics that we will analyse on this tutorial are:\n", "\n", "- Frechet Inception Distance (FID) [1] and Maximum Mean Discrepancy (MMD) [2], two metrics commonly used to assess the realism of generated image\n", "\n", "- the MS-SSIM [3] and SSIM [4] used to evaluate the image diversity\n", "\n", "Note: We are using the RadImageNet [5] to compute the feature space necessary to compute the FID. So we need to transform the images in the same way they were transformed when the network was trained before computing the FID.\n", "\n", "[1] - <PERSON><PERSON><PERSON> et al., \"Gans trained by a two time-scale update rule converge to a local nash equilibrium\", https://arxiv.org/pdf/1706.08500.pdf\n", "\n", "[2] - <PERSON><PERSON><PERSON> et al., \"A Kernel Two-Sample Test\", https://www.jmlr.org/papers/volume13/gretton12a/gretton12a.pdf\n", "\n", "[3] - <PERSON> et al., \"Multiscale structural similarity for image quality assessment\", https://ieeexplore.ieee.org/document/1292216\n", "\n", "[4] - <PERSON> et al., \"Image quality assessment: from error visibility to structural similarity\", https://ieeexplore.ieee.org/document/1284395\n", "\n", "[5] - <PERSON> et al., \"RadImageNet: An Open Radiologic Deep Learning Research Dataset for Effective Transfer Learning, https://pubs.rsna.org/doi/10.1148/ryai.210315"]}, {"cell_type": "markdown", "id": "80769612", "metadata": {}, "source": ["## Setup environment"]}, {"cell_type": "code", "execution_count": 3, "id": "629c60fc", "metadata": {"lines_to_end_of_cell_marker": 2}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" missing cuda symbols while dynamic loading\n", " cuFile initialization failed\n", "MONAI version: 1.2.dev2304\n", "Numpy version: 1.23.4\n", "Pytorch version: 1.13.0\n", "MONAI flags: HAS_EXT = False, USE_COMPILED = False, USE_META_DICT = False\n", "MONAI rev id: 9a57be5aab9f2c2a134768c0c146399150e247a0\n", "MONAI __file__: /home/<USER>/miniconda3/envs/genmodels/lib/python3.9/site-packages/monai/__init__.py\n", "\n", "Optional dependencies:\n", "Pytorch Ignite version: 0.4.10\n", "ITK version: 5.3.0\n", "Nibabel version: 4.0.2\n", "scikit-image version: 0.19.3\n", "Pillow version: 9.2.0\n", "Tensorboard version: 2.11.2\n", "gdown version: 4.6.0\n", "TorchVision version: 0.14.0\n", "tqdm version: 4.64.1\n", "lmdb version: 1.4.0\n", "psutil version: 5.9.4\n", "pandas version: 1.5.3\n", "einops version: 0.6.0\n", "transformers version: 4.21.3\n", "mlflow version: 2.1.1\n", "pynrrd version: 1.0.0\n", "\n", "For details about installing the optional dependencies, please visit:\n", "    https://docs.monai.io/en/latest/installation.html#installing-the-recommended-dependencies\n", "\n"]}], "source": ["import os\n", "import tempfile\n", "import shutil\n", "from itertools import combinations\n", "from pathlib import Path\n", "\n", "import matplotlib.pyplot as plt\n", "import torch\n", "from monai import transforms\n", "from monai.apps import MedNISTDataset\n", "from monai.config import print_config\n", "from monai.data import DataLoader, Dataset\n", "from monai.utils import set_determinism\n", "\n", "from generative.inferers import DiffusionInferer\n", "from generative.metrics import FIDMetric, MMDMetric, MultiScaleSSIMMetric, SSIMMetric\n", "from generative.networks.nets import AutoencoderKL, DiffusionModelUNet\n", "from generative.networks.schedulers import DDIMScheduler\n", "\n", "print_config()"]}, {"cell_type": "markdown", "id": "620df5c6", "metadata": {"lines_to_next_cell": 2}, "source": ["The transformations defined below are necessary in order to transform the input images in the same way that the images were\n", "processed for the RadImageNet train."]}, {"cell_type": "code", "execution_count": 4, "id": "f0e0b019", "metadata": {}, "outputs": [], "source": ["def subtract_mean(x: torch.Tensor) -> torch.Tensor:\n", "    mean = [0.406, 0.456, 0.485]\n", "    x[:, 0, :, :] -= mean[0]\n", "    x[:, 1, :, :] -= mean[1]\n", "    x[:, 2, :, :] -= mean[2]\n", "    return x\n", "\n", "\n", "def spatial_average(x: torch.Tensor, keepdim: bool = True) -> torch.Tensor:\n", "    return x.mean([2, 3], keepdim=keepdim)\n", "\n", "\n", "def get_features(image):\n", "    # If input has just 1 channel, repeat channel to have 3 channels\n", "    if image.shape[1]:\n", "        image = image.repeat(1, 3, 1, 1)\n", "\n", "    # Change order from 'RGB' to 'BGR'\n", "    image = image[:, [2, 1, 0], ...]\n", "\n", "    # Subtract mean used during training\n", "    image = subtract_mean(image)\n", "\n", "    # Get model outputs\n", "    with torch.no_grad():\n", "        feature_image = radnet.forward(image)\n", "        # flattens the image spatially\n", "        feature_image = spatial_average(feature_image, keepdim=False)\n", "\n", "    return feature_image"]}, {"cell_type": "markdown", "id": "52dbd59a", "metadata": {}, "source": ["## Setup data directory\n", "\n", "You can specify a directory with the MONAI_DATA_DIRECTORY environment variable.\n", "This allows you to save results and reuse downloads.\n", "\n", "If not specified a temporary directory will be used."]}, {"cell_type": "code", "execution_count": 5, "id": "e0b189f4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/tmp/tmpfa_a4r00\n"]}], "source": ["directory = os.environ.get(\"MONAI_DATA_DIRECTORY\")\n", "root_dir = tempfile.mkdtemp() if directory is None else directory\n", "print(root_dir)"]}, {"cell_type": "markdown", "id": "9d79c501", "metadata": {}, "source": ["## Set deterministic training for reproducibility"]}, {"cell_type": "code", "execution_count": 6, "id": "39c4b986", "metadata": {}, "outputs": [], "source": ["set_determinism(5)"]}, {"cell_type": "markdown", "id": "38e5a5d1", "metadata": {}, "source": ["## Define the models"]}, {"cell_type": "code", "execution_count": 7, "id": "b2bdf536", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Using cuda\n"]}], "source": ["device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "print(f\"Using {device}\")"]}, {"cell_type": "code", "execution_count": 8, "id": "195db858", "metadata": {}, "outputs": [{"data": {"text/plain": ["AutoencoderKL(\n", "  (encoder): Encoder(\n", "    (blocks): ModuleList(\n", "      (0): Convolution(\n", "        (conv): Conv2d(1, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "      )\n", "      (1): <PERSON><PERSON><PERSON><PERSON>(\n", "        (norm1): GroupNorm(32, 64, eps=1e-06, affine=True)\n", "        (conv1): Convolution(\n", "          (conv): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "        )\n", "        (norm2): GroupNorm(32, 64, eps=1e-06, affine=True)\n", "        (conv2): Convolution(\n", "          (conv): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "        )\n", "        (nin_shortcut): Identity()\n", "      )\n", "      (2): <PERSON><PERSON>(\n", "        (conv): Convolution(\n", "          (conv): Conv2d(64, 64, kernel_size=(3, 3), stride=(2, 2))\n", "        )\n", "      )\n", "      (3): <PERSON><PERSON><PERSON><PERSON>(\n", "        (norm1): GroupNorm(32, 64, eps=1e-06, affine=True)\n", "        (conv1): Convolution(\n", "          (conv): Conv2d(64, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "        )\n", "        (norm2): GroupNorm(32, 128, eps=1e-06, affine=True)\n", "        (conv2): Convolution(\n", "          (conv): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "        )\n", "        (nin_shortcut): Convolution(\n", "          (conv): Conv2d(64, 128, kernel_size=(1, 1), stride=(1, 1))\n", "        )\n", "      )\n", "      (4): <PERSON><PERSON>(\n", "        (conv): Convolution(\n", "          (conv): Conv2d(128, 128, kernel_size=(3, 3), stride=(2, 2))\n", "        )\n", "      )\n", "      (5): <PERSON><PERSON><PERSON><PERSON>(\n", "        (norm1): GroupNorm(32, 128, eps=1e-06, affine=True)\n", "        (conv1): Convolution(\n", "          (conv): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "        )\n", "        (norm2): GroupNorm(32, 128, eps=1e-06, affine=True)\n", "        (conv2): Convolution(\n", "          (conv): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "        )\n", "        (nin_shortcut): Identity()\n", "      )\n", "      (6): AttentionBlock(\n", "        (norm): GroupNorm(32, 128, eps=1e-06, affine=True)\n", "        (to_q): Linear(in_features=128, out_features=128, bias=True)\n", "        (to_k): Linear(in_features=128, out_features=128, bias=True)\n", "        (to_v): Linear(in_features=128, out_features=128, bias=True)\n", "        (proj_attn): Linear(in_features=128, out_features=128, bias=True)\n", "      )\n", "      (7): <PERSON><PERSON><PERSON><PERSON>(\n", "        (norm1): GroupNorm(32, 128, eps=1e-06, affine=True)\n", "        (conv1): Convolution(\n", "          (conv): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "        )\n", "        (norm2): GroupNorm(32, 128, eps=1e-06, affine=True)\n", "        (conv2): Convolution(\n", "          (conv): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "        )\n", "        (nin_shortcut): Identity()\n", "      )\n", "      (8): AttentionBlock(\n", "        (norm): GroupNorm(32, 128, eps=1e-06, affine=True)\n", "        (to_q): Linear(in_features=128, out_features=128, bias=True)\n", "        (to_k): Linear(in_features=128, out_features=128, bias=True)\n", "        (to_v): Linear(in_features=128, out_features=128, bias=True)\n", "        (proj_attn): Linear(in_features=128, out_features=128, bias=True)\n", "      )\n", "      (9): <PERSON><PERSON><PERSON><PERSON>(\n", "        (norm1): GroupNorm(32, 128, eps=1e-06, affine=True)\n", "        (conv1): Convolution(\n", "          (conv): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "        )\n", "        (norm2): GroupNorm(32, 128, eps=1e-06, affine=True)\n", "        (conv2): Convolution(\n", "          (conv): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "        )\n", "        (nin_shortcut): Identity()\n", "      )\n", "      (10): GroupNorm(32, 128, eps=1e-06, affine=True)\n", "      (11): Convolution(\n", "        (conv): Conv2d(128, 3, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "      )\n", "    )\n", "  )\n", "  (decoder): Decoder(\n", "    (blocks): ModuleList(\n", "      (0): Convolution(\n", "        (conv): Conv2d(3, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "      )\n", "      (1): <PERSON><PERSON><PERSON><PERSON>(\n", "        (norm1): GroupNorm(32, 128, eps=1e-06, affine=True)\n", "        (conv1): Convolution(\n", "          (conv): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "        )\n", "        (norm2): GroupNorm(32, 128, eps=1e-06, affine=True)\n", "        (conv2): Convolution(\n", "          (conv): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "        )\n", "        (nin_shortcut): Identity()\n", "      )\n", "      (2): AttentionBlock(\n", "        (norm): GroupNorm(32, 128, eps=1e-06, affine=True)\n", "        (to_q): Linear(in_features=128, out_features=128, bias=True)\n", "        (to_k): Linear(in_features=128, out_features=128, bias=True)\n", "        (to_v): Linear(in_features=128, out_features=128, bias=True)\n", "        (proj_attn): Linear(in_features=128, out_features=128, bias=True)\n", "      )\n", "      (3): <PERSON><PERSON><PERSON><PERSON>(\n", "        (norm1): GroupNorm(32, 128, eps=1e-06, affine=True)\n", "        (conv1): Convolution(\n", "          (conv): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "        )\n", "        (norm2): GroupNorm(32, 128, eps=1e-06, affine=True)\n", "        (conv2): Convolution(\n", "          (conv): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "        )\n", "        (nin_shortcut): Identity()\n", "      )\n", "      (4): <PERSON><PERSON><PERSON><PERSON>(\n", "        (norm1): GroupNorm(32, 128, eps=1e-06, affine=True)\n", "        (conv1): Convolution(\n", "          (conv): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "        )\n", "        (norm2): GroupNorm(32, 128, eps=1e-06, affine=True)\n", "        (conv2): Convolution(\n", "          (conv): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "        )\n", "        (nin_shortcut): Identity()\n", "      )\n", "      (5): AttentionBlock(\n", "        (norm): GroupNorm(32, 128, eps=1e-06, affine=True)\n", "        (to_q): Linear(in_features=128, out_features=128, bias=True)\n", "        (to_k): Linear(in_features=128, out_features=128, bias=True)\n", "        (to_v): Linear(in_features=128, out_features=128, bias=True)\n", "        (proj_attn): Linear(in_features=128, out_features=128, bias=True)\n", "      )\n", "      (6): <PERSON><PERSON><PERSON>(\n", "        (conv): Convolution(\n", "          (conv): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "        )\n", "      )\n", "      (7): <PERSON><PERSON><PERSON><PERSON>(\n", "        (norm1): GroupNorm(32, 128, eps=1e-06, affine=True)\n", "        (conv1): Convolution(\n", "          (conv): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "        )\n", "        (norm2): GroupNorm(32, 128, eps=1e-06, affine=True)\n", "        (conv2): Convolution(\n", "          (conv): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "        )\n", "        (nin_shortcut): Identity()\n", "      )\n", "      (8): <PERSON><PERSON><PERSON>(\n", "        (conv): Convolution(\n", "          (conv): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "        )\n", "      )\n", "      (9): <PERSON><PERSON><PERSON><PERSON>(\n", "        (norm1): GroupNorm(32, 128, eps=1e-06, affine=True)\n", "        (conv1): Convolution(\n", "          (conv): Conv2d(128, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "        )\n", "        (norm2): GroupNorm(32, 64, eps=1e-06, affine=True)\n", "        (conv2): Convolution(\n", "          (conv): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "        )\n", "        (nin_shortcut): Convolution(\n", "          (conv): Conv2d(128, 64, kernel_size=(1, 1), stride=(1, 1))\n", "        )\n", "      )\n", "      (10): GroupNorm(32, 64, eps=1e-06, affine=True)\n", "      (11): Convolution(\n", "        (conv): Conv2d(64, 1, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "      )\n", "    )\n", "  )\n", "  (quant_conv_mu): Convolution(\n", "    (conv): Conv2d(3, 3, kernel_size=(1, 1), stride=(1, 1))\n", "  )\n", "  (quant_conv_log_sigma): Convolution(\n", "    (conv): Conv2d(3, 3, kernel_size=(1, 1), stride=(1, 1))\n", "  )\n", "  (post_quant_conv): Convolution(\n", "    (conv): Conv2d(3, 3, kernel_size=(1, 1), stride=(1, 1))\n", "  )\n", ")"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["autoencoderkl = AutoencoderKL(\n", "    spatial_dims=2,\n", "    in_channels=1,\n", "    out_channels=1,\n", "    latent_channels=3,\n", "    num_channels=[64, 128, 128],\n", "    num_res_blocks=1,\n", "    norm_num_groups=32,\n", "    attention_levels=(False, False, True),\n", ")\n", "autoencoderkl = autoencoderkl.to(device)\n", "autoencoderkl.eval()"]}, {"cell_type": "code", "execution_count": 9, "id": "c2424564", "metadata": {}, "outputs": [], "source": ["unet = DiffusionModelUNet(\n", "    spatial_dims=2,\n", "    in_channels=1,\n", "    out_channels=1,\n", "    num_res_blocks=(1, 1, 1),\n", "    num_channels=(64, 128, 128),\n", "    attention_levels=(False, True, True),\n", "    num_head_channels=128,\n", ")\n", "unet = unet.to(device)\n", "unet.eval()\n", "\n", "scheduler = DDIMScheduler(num_train_timesteps=1000, beta_schedule=\"linear\", beta_start=0.0015, beta_end=0.0195)\n", "\n", "inferer = DiffusionInferer(scheduler)"]}, {"cell_type": "markdown", "id": "f05d9e13", "metadata": {}, "source": ["## Load pre-trained model"]}, {"cell_type": "markdown", "id": "250b1304", "metadata": {}, "source": ["Here we will use a pre-trained version of the DDPM downloaded from torch. However, users can also use a local model and evaluate its metrics if they want."]}, {"cell_type": "code", "execution_count": 10, "id": "ddc61384", "metadata": {}, "outputs": [], "source": ["use_pre_trained = True"]}, {"cell_type": "code", "execution_count": 11, "id": "0e81539b", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Using cache found in /home/<USER>/.cache/torch/hub/marksgraham_pretrained_generative_models_v0.2\n"]}], "source": ["if use_pre_trained:\n", "    unet = torch.hub.load(\"marksgraham/pretrained_generative_models:v0.2\", model=\"ddpm_2d\", verbose=True)\n", "    unet = unet.to(device)\n", "else:\n", "    model_path = Path.cwd() / Path(\"tutorials/generative/2d_ldm/best_aeutoencoderkl.pth\")\n", "    autoencoderkl.load_state_dict(torch.load(str(model_path)))\n", "    model_path = Path.cwd() / Path(\"tutorials/generative/2d_ldm/best_unet.pth\")\n", "    unet.load_state_dict(torch.load(str(model_path)))"]}, {"cell_type": "markdown", "id": "9c187146", "metadata": {}, "source": ["## Get the real images"]}, {"cell_type": "markdown", "id": "b2b42415", "metadata": {}, "source": ["Similar to the 2D LDM tutorial, we will use the MedNISTDataset, which contains images from different body parts. For easiness, here we will use only the `Hand` class. The first part of the code will get the real images from the MedNISTDataset and apply some transformations to scale the intensity of the image. Because we are evaluating the performance of the trained network, we will only use the validation split."]}, {"cell_type": "code", "execution_count": 12, "id": "bd4c90f9", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["MedNIST.tar.gz: 59.0MB [00:00, 130MB/s]                                                                                                                                                                                                             "]}, {"name": "stdout", "output_type": "stream", "text": ["2023-05-02 16:24:48,981 - INFO - Downloaded: /tmp/tmpfa_a4r00/MedNIST.tar.gz\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["2023-05-02 16:24:49,097 - INFO - Verified 'MedNIST.tar.gz', md5: 0bc7306e7427e00ad1c5526a6677552d.\n", "2023-05-02 16:24:49,098 - INFO - Writing into directory: /tmp/tmpfa_a4r00.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Loading dataset: 100%|████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 5895/5895 [00:02<00:00, 2657.67it/s]\n"]}], "source": ["val_data = MedNISTDataset(root_dir=root_dir, section=\"validation\", download=True, seed=0)\n", "val_datalist = [{\"image\": item[\"image\"]} for item in val_data.data if item[\"class_name\"] == \"Hand\"]\n", "val_transforms = transforms.Compose(\n", "    [\n", "        transforms.LoadImaged(keys=[\"image\"]),\n", "        transforms.EnsureChannelFirstd(keys=[\"image\"]),\n", "        transforms.ScaleIntensityRanged(keys=[\"image\"], a_min=0.0, a_max=255.0, b_min=0.0, b_max=1.0, clip=True),\n", "    ]\n", ")\n", "val_ds = Dataset(data=val_datalist, transform=val_transforms)\n", "val_loader = DataLoader(val_ds, batch_size=180, shuffle=True, num_workers=4)"]}, {"cell_type": "code", "execution_count": 13, "id": "0e6facbe", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 50/50 [00:01<00:00, 26.71it/s]\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAgMAAAClCAYAAADBAf6NAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjYuMiwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8o6BhiAAAACXBIWXMAAA9hAAAPYQGoP6dpAADDLElEQVR4nO396W5jS5beDy9S1EhSc05nqMGNrkYDNmzAV+B7sG/SN+DP/dEGDLTRVf5XdVedU3Uy8+Sg1ERRokby/ZDvL/Tw0YpNKuucVBqlBQgUN/eOHcOKtZ41RERrMplM4pEe6ZEe6ZEe6ZH+Zqn90BV4pEd6pEd6pEd6pIelRzDwSI/0SI/0SI/0N06PYOCRHumRHumRHulvnB7BwCM90iM90iM90t84PYKBR3qkR3qkR3qkv3F6BAOP9EiP9EiP9Eh/4/QIBh7pkR7pkR7pkf7G6REMPNIjPdIjPdIj/Y1TZ94bV1ZWIiKCPYp2d3fjyZMnsbq6Gpubm9FqteL8/Dyurq7mfnm73Y7xeFy+TyaTmEwmcX19HTc3N3FzcxOTySRarVa02+3yDPfe3NyU7xFRyhqPxzEej8s9nU6n1P/6+rpc1/uzumm5/ufPTSaT8p0+og78pp8REa1Wa6q8Vqs1dU+r1YpWq1V+U/LrtImyea+Wk11T0mfpA39On+f93OfjyDvG43Hc3NxERNyLP34qWlpailarFSsrK7G4uDjFHzoeWn/lOb93YWEhFhYWyr1K2q86zhFxp5+4ps9qHahHp9OZqsfi4mIsLi6Wd1Ef7uV/Hxutq14bj8dxdXVV+IdryptaL69jRJTxVV72tmbl6FzR/tD5BOl8ysZs3ndl8yrj8YiIhYWF0td7e3vVd/xc9J//83+OTqcTv/rVr2JnZydGo1Gcnp5GxC0/Od8o70Lcc3NzE9fX11O/6Xxvt9t3+AQZoOW02+2pOaDzXfsWXuR7Vme91mq1otvtRr/fn+IB5WO9d2FhIZaXl2NhYaHw4NLSUiwvL0+NpfI25aIDrq6uYjwex/n5eVxfX8fl5WX5Ozs7i+vr6xiNRnF1dRWj0SguLy/j/Pw8zs7O4ubmJkajUdzc3MTl5WXRW8wd9M319fWUfqAOtEGvZ3JC57T2gcoy72ftv9Fo1MRmEXEPMJBRTVFF3FX0GTUJmqb3fQppZzXVoenddDBtqyno7LsPIlSbtD64WX2ysmt193JnPVNrU1N7srrr/V/CZpf3rcPPVecaPzZRJgwdTMwa1/F4PKVwf2qaZ5xnKe6sjL+mrpTnfdPU/16Hh+JdV7DzUgau5mmTAicHkQ4I3PDQMrhH66HKTtunAKbVasXNzU0xGhQsusxDDi8sLEwBRQU1mTFGuRm41HmloL/T6RTlq+0HWGF0AgT4BAxcXV3dqY+PkRuBWb1qY1Ubs/vQ3GCACivC6XQ6xWphADqdzhTSrCnbbLLrgGHlOAP68xna0u/cc3FxUf6fBwAgNNXaUoTnA5XVKauvo26vc0RMeS1qz7tF4/fq9ew7n8pk7rXIrNzs+azv/PvV1dVfBeb+Gmqy/pomjY9LZtXMAwD5P+MBLTtieuwVgLZarWKhLi8vx+Li4tR7EVrMQy1T+RIrSd/v3hEdv6Y5rG3yd7mw9Xtm8a1buxn/Z2PngMnnG5ZYu92Oq6uruLy8vPO8j1sG/D8Xdbvd4p1QKzsi7vSV8jljxli7Isoo60+Xp9o/qsS0DK8jpApO68F4qIcrMywcRDAfmRe8C72UGSyLi4vlN+aQzhe8btlcd/J+1/nF38XFRdE/yMGrq6u4vr4unojz8/O4ubmJs7OzuLq6iouLi+KZ4LfLy8sCLgAb6o3LDIR5dR00NxjQgVT3BsJHkZoLiUyxKTNwT1Z5UF8TudveBZC6qfm9puhoq/4PyIF0gmXMUgMwTvST1i3iNpSRAQWfXCrIHWFqX2QCXxG51kOZvGZF1oR5rd0PJUwzmgc1ZwrbhXGNmu5pQvMKDh3A6HxbXFyMpaWliLiruFWw6Tx0xZyFtOalbN64YPSQg97n/KltBwyhHDLenWdeZRa1g3sHXvpsU1s/Jy0tLZVxdz6ZxUNKtDWzOjOlrs9RHlZ4rf89/MDzSshiDKuIKEp5aWkpOp1OXF9fTxmdEbdgF1IwoHKKe7mf+vK5srISS0tLsbi4GMvLy0W2t9vtEm4AECwsLMTKykqZc61Wq4xHp9OJpaWl8py+nzC3Kvrz8/MSiri8vIyLi4s4OzuLy8vLGAwGcXV1FUdHR3F5eRmnp6dxdnYWFxcXcXx8HNfX19Fqtab0An2pPJDJ6lkGj9LcYMAZx+M8GQqPmFacXlEnBs0ZaJaHIQMBEXcRq17T2Ji7a3i/tlEBxyxkXatDjZrAjH93hZu9K2MAHyO1OPk9m3D6bBP5vfouYmkPJVAhB4XertrEyXIiEKwe5uH37B3ZOGYKU5WgkgPwyWRyZ8y0rS7kMwCeWdj+Wy23J+PTWh84+XuVrx3IKGUKUN+RWbD+Xo3pqhzw++8jSH8u0rnkykAtUtrixk82RgoKuKa/+71KLpuVMqPPZSv/j8fjKe8W4wAIyN47a+yVsvAZ3gDAAh4C5hSgAM8C//Oc3kMZCwsLJScJvlUPRxb/xzuxuroa4/E4nj59OjWfNNRAPoMCDP7cw3B6elq8CXgXTk9Pf3owoJ2sFkjTfU3KmHuy+/X/mlCgTGWQjFFrFrbWY5bnotVqlRiWJ8TAuPpMzULXshVkeN0gFQRuwWTkCsutNf0/q48LYRU6/l69xv8oJhAzZYNy72N9/pRE+5S/qLuOlVtMEdNJUBrGInnV+yFTLK6otM8h6oagYMLznNaFOs8CaNn8m9VPWd0dEChf1+aOPp8p6ppxkAG0WtlaV32HK7usnYyn8qmXo+U9FMFjqhAgNbiop3o8VCbq3KbdeDt1LLP+zsJ+GbB1crml8049rRrSqIFEnQsuz7VNDvaa+jSr12QyKQpfCevf5zyeBAUK3W431tbWYnl5Ofr9fklgx5uA54FwH/f0+/2S/Khei1arVT4xrAAD5+fncXh4GOfn5/H69esYDoexv78fR0dHMRwO48OHD3Pz7709AzpQymDOEM5YXGv6Tvl63T0LEdOuRmfM+7bHr2XgQ5nNwYwyndYpa0sNGGTKt6meTmrdePmuqGpWwH3eV7sPIewCQBXYl0DaL7X2NvWbAgQvt+kdTUJT/9e5o1bNffqwyfVbu671rXnjqEem2PWerP2z+hzS+zMg31Rvvrt3apYHSMu47/z7OakJqNTkp97r/OuyR69l5UTcNexq/ZN5wLK2eB0zmZrVrQmc1OpdoyZQ6oZiu92Oi4uLO/kM6D/c+IAFrPXFxcUYDofR6XSKcicUAiDodDqxtrYWnU4nut1uLC0tlT8FGjzvPI5Rsry8HLu7u9Hv92NjY6OsOjk8PGz05ih9MhhQtOIWDIOmrp0mCyITErMGvBaT1LKb3H6Zh6JWnr6z9ltTPfyat7kmdF2Z+2+Tye0yN1ci+j5l4OyeWl2zya195RaYIvt2u13QsC53ewjK2lFTkm4xRNxaXur60zwLV3x8d6vTlVrWh8pn6nFxQQS5O1SpSak7uTdOy+a6WnNeT3/vvGPd5DWhf9S65RlX+PC191GmuDJwp/dkXriHAgWZnKwJd8bE+8blEwoOuZ15t9TzpeXDj4yL9zXGAM+r90Hr4dY4ZaunNQtNZaEzNco0jB1x68mlD7MQdOZdqumubC7NAukK5tXap34YS8vLy0VWAhzIY+j1etHpdKLX68Xy8nL0er1YX1+P1dXV2N3djbW1tXjx4kUsLS3F2tparKysxMXFxc8bJsiopnj9f7+WxZOaXJA8N2/jXEjPQ01o28vU+tSU/qzvTeg8Q641N1qtjOyeJhCm170MByiz3hVxN7fk/zWqjYsLEBWEfG8aW39HNhZNfdYEAnScanyT3Z8BghqgUMAwbzuzd9bqPS/NAtv6/33q6crxoSkzCqCsXTWPgAOCpvJUuf4U5IBk1ruz601l+nNZ+zwcDWXGllIToM4MVwf+lO8hCMBVq3Wbz6CgAU/CaDSKTqcTZ2dnsby8HGdnZ3F+fh5ra2sFSLRaHxMk8Sjwvnlp7jtBgXSwNkhRWw3F+j0ZytNkKJ7PMlQpz1cIzOsOyQbWGcrbkVkwStonmVuzJpC8PH/GXbLO3OPxuOQzNHlCXDjoPbU6qnWR1d1JeUPHstZnn4syxa396gpIvSh8z7wLETE1kdvt9p3kH56ZVR8+mReaoORWlObs1CxcVawIQC8HUsE8LyDAcmGukOiUAU0XrhmQqM2zWjJhDaTp3HPQpmVnbm+tF3+6hOuhSC1Ll70eLo2o8wRUs/qVPASq96k3TOeDesxcFqrnBnLPhJaFt7OWeDyPPGnKG+C9+ul5FVrXpvw2batez8IXWqZ7QGpJn8q7nqS4tLQUvV4vFhcXY2NjI1ZWVuLp06exs7MTy8vL0e12o9VqxX/9r/+1sa8i7ukZqE3c2n3ZtUyJ6/0ZmmsqL7t+Xws0c0dlQszbAc3qj1l1b1K2Wh8Vjv57BhS8bz9FGddATGaN1sDKp77756Qmr4i2tYnHdYLqcqaIPHm0iT+y3zKvSqa0Z5Wt92fWt8dps3rVAE2n05kCpNnzNUCQ9W+TJT4LyNbA6qy+0fo5YJw3+ernIq9PzZquGVhu2TswnyVrsz6eJY9Vuev8d8PEQ2QZNcmN2tg0PZPxm/+fGXZZiErvzYBDzWvWat0mOer46O64Op6qMx3gsPxxYWEh1tfXY3l5OY6OjuLJkyfR7XZje3t7bu/OvTcdokJ0gk4YZ8paIp0LHzpFvQZcp/NqzJ4xZ2btOeBoihmzplS37swmj6PjrB9qdaxN6to1t5iyejRRDWg03evjMYt8cvOZJYE+NKk1o99dMWTCgvv0mcvLy0Y+9XcA7FzIKO9cX18XN1+r1Zpa8jSPsqopYq1L1j6ssogcKDM39Lpb3rX6eN/V+jMrp0lxZW7Zpjo1gY5ZCupzk/NdNp/U+5ORylKPo7v3RGW382RNHsATmrOgddb3+PfMYJjllm+yurUNKFhV2vpegHz2rNbH92BRch1Yq5vLGl8WzFzHxa9jzXf3LOh4sizz4OAgLi8vo9vtxnA4/HnAgDMDjclCAdqhPrmzCZrFfWqT0hWaW0iaTOIItalj+E3jOu7qbXKvZUzQJNgyayZTQrW2+7UsDlcTevMoZkXv/tysfkDZzXLVfQ7SSaS8pwrD+bQGAvjdkb7zbGa1ZwJV66DWO6EGVfxMfBKQXDBE3E36U0Awj6Wr/aRjmL0nyyPI+qbWh1mfNgH5DEzMml9ehs8v95bcZ959DnJFzZzUPlAll/VdLd9DZaS/x+/Xd2ZAMmJ6fxYApY6n8wffVeE1eam8Pdk9KnPG43HZuQ9Fq0nAlOWyTGVWzdOi9/sGSXof1z127wDC5zj18xAVc1/1LGVguA4Gg5I8eH5+PjewvdcOhKA/HeB5BSikg6mWSMStm6T23KxrjpSze5osNvdeKNNk97uidUWT3aP9lSkFf09tItfaULteE5r+W60eXKuBqUxQZ1blQ1ANtPCbj2dN2M1SOBF3Y6RN9VFUn80lPAFkGStI9T52IZXF/v26/obw1nFUT0825zNAgCBT5VGbA/q9aS5n1zLg4fe60ZHd77yh/aqK96FI26D7B/hvWvf75Dgo2K/xe03eNfVLBh64XpPBDjqyMvV+2skcItlOvQK18mYZhxkYQP/xf9YGfsvaOI9RRLtq3tTr6+up+aUeBwXwkB/m10SfdDaBb+Pp6E9dVo6aMmZmoww6orYuXZ91RanM4SENvUcpU5DaBk2I8cHJrItsUqkwrAEBngE96iYiunynBjD0XToOtQmp9c/AgnpUHNioMlJm9PZ5vb4Eoer9U7OIHNgqeT9lyrnWVrfePNGScrBkAAIsK8rug7L54teUT7PNaxzwZXOHtmUKR3mYTVHuQ86nTSA8G09/xv93sJWVTR9pjPYh+RaCr3Q5p3pms21/I+4um66B/cnkdjdLlRmZDHAgquQAsbYRnH42KUlfNt3EH8vLy+V0WrwCjKXugqpzh2WSyDD3EGQJ7OPxeCpHyAG0ehpcR+g17xO18l2XZXOS67qTIm3yOs1Dn7y0MMvynUeI+jVHW01AYF5iQLL3z2OVAEj8nU3WStZ2V7h6X5OAaXrGLdlZ9fspKQMUtfc2IeeHoCbedKp5RlyQ+v8O7mrtzbwA/jtzIfMcROT7ZLg3wMdHwxAOQrKd6pr6RQF/Fp7y++9D9+GTrO/mKU/nUCYTPrXuPzUBGl1JqXxrGqua3FPScfRwcGYsNMniJuNLKQOzNY9BpgCzWPrKykr0er0yb66uruL09DRubj4eM4whi7Ll8LRs5YP+zeq3Wvt8zKj7LPJ5rkDFZUz2Lq65MTmLPnk1AQLF4zeZBc3g6TXv6Ha7XZZnQZz6pFSbvMq4NSWcKf5ssnDcpCLJ7PmaMsZiVKvZvRpONWVSiwVrv80z8bN77oP49R5XjI56PfalXoqHJK17lpntCsLHGzck43t9fV34U2Ov80y+rO/0N7wC7FK2sLBwZzta3aHMvWH85nycAVz9reYxol6q/NVrph4RnQdNc05lgHosamBXv/tcr/Vvbd57OTVQxudD8a7OIbf2msIBeqyvU3YNpeEyz93lLj8i6mE4BRn8ZfPOZZj+ni0tzORPu92OnZ2dePbsWWxubsbu7m7Zr//q6qrE0dmz/+zsLA4PD8spgerxur6+vnOapesz6uk6zEENnrIm4DMvQHB5rX2EAcsYMp732eztkzwDtQnogsZR4iyUhaC978TzskGGWdynaXKokGoCAo7SahZyk1uo6XqtjbXrtck4zzs+1QrKFKkrxM/tufhraBZfaHsQCBpG0t+aELxT5m1QsNJkeUTcPWehpiT1nojpBENt0yxSQKDfGX/18jXxpQP1mkU/izd9js2j/GqAvgZCHpKa5tK888n7mnKdVOG7Us/AoRoqbrz4Pfepo1+fpThVUS4sLMTy8nI5BAgQHRFlL/+rq6ty/ofu9a/h2aY66Xvn5RWdL1kfZWGKWntr9fBPANhPDgZ84D3e6e4W9QjUKq+oMSIKGtNyOp3OHetG66ODR5n9fj9WVlbKYF9fX8fZ2VkKDrKJxiExmaCoWd/OtNRVPQPatiZwxLN6QI5ve+v31urkz2RKq6akauVl9/KpHp7aiXoPRW5ZubWV3a9AkBgkCX0cJazWMUKllgSkwALBpQKfPckjbi3n8/PzYukvLS1NIX/6Gb7SuK+vhNExd4Wu+So+P7Iy1IpstVrFC6Fzmv+zA2goUy1Q/T4LTHj9/N5sTjQpwux91Os+Icqfmnwb6kwGQtp3s5bzuvEUEVNLqR00ZnPfQxU1o0t/yyz9rB2ZwnRQQD4B+SmvXr2K4+Pj2N7ejvfv38fy8nJsbGzEwsJC9Pv9Ml86nU45RngwGMTvfve7OD09jdPT0+Lp0/Gnj9QToe3361n/1MIg2j4FBJ6PkJF7jOgjlQG1Tfsy+uQwgSdJzIuSMlSuStqtG89MborjY6mRdHV+fj5Vjlv4Xg9Ihdms2BzXXeHru/Sd6javKSJHuhHNKy1mgQoXgjWLfd7fM6GqSoIJ9yXTfYATvwMQWQcM8Im4PabZ0biWmQlTkn/gC0IDhCAAGOPxuIAs5U1dKhVxK7RYUuXt0PmkpPdQviZv1TwCekiL9x2ZzPOEiJqssRrYdWCb3avWkj+bySJ/z6x6/9xU85jop153Ps7mqoIJrhNjnxVjzqz0TP5kgGWWDHd57+912e0u+MFgEKPRqBzti1GIp2BxcTFWVlZiZWWlzNODg4P44YcfisFYM3hq5DJDv/ucuS/dBwhkddLQwTw0t8SuHWoxj7JU0kmY3e/Mlgnp2rMIsJWVlej3+9Hr9SIi4uLiIg4ODgoSvLi4mMrKzZRAk3Cp1cOFU4Zmif/qpIDR+fTDPTwm7+92S6oGevR+9VD47xmTqbLX+2ueimySPBQtLS1FxLRVkvVnrQ8V5DDJzs7OppSx920G9lTwqqWvFobWK4s3Rtzug6HvVUucMrIxVkGrgkq9OH4gEUBU28bzeiY99dZlitovmqHvdB8h7P2kZWpfz1Kafk3Lz971EKShKI29UzelecCBE7/Xtv3Ve7SPkW/+7qYyZl1TGehggnZTV3f/8wz/n5+fx8rKShwdHUWn04nV1dWyS1+32y3JhicnJ3f2R6h5s13Rezsy+ZiBJy3HwwBeBvNZvYWzSOf0feTuvcAAApEGOOrJmKMJlTdZ+coQPgA15uSe1dXVcjb06upqOeRhNBrF5eVlQcDu7snir01gwK/5wMK42mcsGdP+Q9heXl7GZHIbHsBtlymLbCIpUm8SdtyT9atSJkB8DPwaY6uodJag+LlpeXm59K+6zTKlosolO4+j3W6X2KNew2p34BAxLUiY2BxTSphAiX5S9632I2eij8e32wCroNHnfZ44uatT68M48v7MParj7UCo0+kUweobk2l/6LXaXFLKFHSm9DLemwXqvT8eGgyoZafnXsxbN/VyQj7fFaRlsqAGMvxeV3zOd15OthTcy3JZj0zU9fbIT66dnZ3F/v7+1AZdgO6dnZ3Y2NiI7e3t+Prrr+Ps7GwqNOLeYO1Hresso8hJ+wpXvodRszJ4r5502AQINEyX6d4mutemQxHTaEcH2a0GbZh2cNYQt2j8+XlJGUeRbrt9e6jK6upq3NzcxPn5eYqc51H6Tl5XVcZMWE6gev78eaytrU0dM4lr6+TkZCrmfH5+PnNgm0BXUx2b7nMl723zd/n/Hu5xQf+5qen9CmD8uv+vvK8AwcGt/q+oPgMKfmiMKl2usXaa92iZKrhqMVvIx7Z2jytoL6/p2Yhpi0T5vwbEank02ZysgdAm/poFjGvP3FeY/tw0i0/1u/PkPO79WXM6e5+Xo8pcxyQLF2TPe3187NUzwv+AAeSOA3r+b7fbMRwOI+Ijj25ubsbFxcWUh0/rXqvXXwMOmbsZuM7mWA0UK8BzT4LSfeTuvTcdooP9UAWNM1JBFIdWPGMqBQs1JsxiL1k+AcsRLy8vS/LhwsJCdLvdWFxcjMvLy1hdXY2jo6NS/4uLizvormnSqVDOCARPSGBxcbF4K/7Tf/pP8ezZs/j222/j2bNncXR0FO/evYvT09N49epVjEajODw8jNFoFB8+fCjMqsq1Vr9sWafWOevfWff4dRfyCqZqLjJ3b35uyjbYibirZJT0N9qpoTKP1avlojF2gKj2oe4fgDXjk57fOcNclxaSF+NLDb0tDhyyeLDfS719t7saQOS6hjQUMGr+iNZDeULrjNdOFXwTMOG7e8Myns3qraT8fN/w589FjInOL51XOh61pYbaf5m3JHtunrnq/VKb4w4KMjCXlatl6f4A6iEZj8d3PKp4BBQM4Lk7Pz+P/f39uLi4iJWVlak8sxqwrpGDD22X59BAmfypvaumF9X6j7h7xoH2WZOHzelengEfRBqdKQ9lXrcCmjq6hsZnDY4yOKsIyLyGUZaWloqVNRqNSrxTB3Me1DePcPBy6AMFBpubm2UJZLfbjaurqxiNRtFqteLk5KQcMqGK91Povh6XTOBnDD8vfQnW1X2swabrqjRrijFi2guge41H1JN/5nGN67HGXG96ZhY1tcHv82OpI/LNVDzE5i5fDAQFAroyoQmweB1Vxuj9TffW2jjv/P+clAFsTczMQpNNVANSEc0JgU41b0DT+7Kwbq2+TVa5Hu8NcNNy2JsDr0FEFMOMEJuG6EieXFlZiaurq5J34O+t1dPnxbw8lN3r/dUUQpmnzPvU514p3yoQdB9oV/pqMTHxfVc/vYcGNrk0MivCiYE/Pj6OiCjegU6nE91uNyKiZGoTg8FFr+W7pZYJl6YO9u0gHahcXFxEq9Uq503/4he/iJubm3KwxO9///t49+5dtFqteP/+fdX9W1NcfKpg5bvHlt2yV4vWk4rcSlFLKmJ6vbHGA5vG7HNQzQrU32pKIxNaau0qTyrvAAI6nU6sra3dcUNipesmRpQNUTZLC8mIZmkj9yD0mIs1AePCPrtPtw/3OlFX5hSgW/kz85jQN142/19fX8dwOJzyNip/eR9nFlhtK28lX6an7a5Zpw/Nu2pwYRWrN8k9IhF3PXhci2hWJPf13PGeDOzV5o2GEJ0PvaxsnFgdwIoxBZZsMLSyshJra2tTYAC+XV9fj16vF7u7u/HLX/6y6LTr6+v405/+FB8+fIgff/wxhsPhVH309FCvl+/eqfXN+kzbOgvMaW6Ektaj5hHSw6LmoXuv/8oUwjz3Z3kBSuqazEIK83gGaDQW9urqalxeXk5ZZxEfO48lJtfX11OZmjqJXEG4os3qoAIqE2RXV1clkW08/rhcbHNzMyIi1tfX4+LiIvb29uLi4iJWV1fLu7N+rSm5pnrXntU26djqmKHsZpGWlQmrh6Z5kXLtPhXQ2W9uvWCVqBWi5ehnBlJcoCnQnuVNyOo4yzvn7kfqxDzC8mq1bsMcvEfBjc4BdW172Itym1zM2R9lODjN4qY6lrOsUn3mocGAu6FVATcBWahJIWX9cB/PiPf1rH7ycVFgiKzRcrJxY5lgt9uNra2tqTFfWFiIs7Oz6PV60ev1powS5P/W1lb0+/3Y3t6Ofr9f5uj19XX0er0YjUaxvLxc6qd8cJ++UdCj9fc+0lCD/qbXs3kx7x4u9+HducFA9vJsOQqdppNf449Z5zgTOINlKD1jQso7Pz8vrrTFxcXodrvlsJdut1uE2MLCQgwGgxiPx3FxcRHHx8dTSU4IYgc9TRay15d+Ihnwt7/9bXz//ffx+vXrePLkSXzzzTfxj//4j7G6uhobGxtl2RobYKjHwimzYCOmrXGtv7ajyUKqCVXtdy+L51XwzwMWH5LuKwRRviSD0r/ab7ghXVHrGC0uLpZ7Iu4KfAAYOS8ARzY90v8zvmxK5IV0pYErFn2GbZH9PTzDqgbNp1DvSAaQPWHw5uam9AfgAtCcgQBvl/YZ76W88Xg85fZVRerJZtqH3qcPTU0Ws7bB48TeT025W3rdKeuHJvkAZaEH1wMZCHCgsLKyEp1OJ/7+7/8+fvWrX8Xm5mZ89dVXERFxfn4e5+fn8dvf/jZ+/PHHePHiRXzzzTexsrISGxsbU+MMKLi8vIzj4+Mp/XR1dTW16VfE3RUPmvA7iy8yN/+sPtO5y9zMcop8LtSM08z7UKN7rybIXqj/K4qqKfLMQq0hmMyyqL2fMlk+2Ol0SpIIz+mmLgiP09PT6HQ6cXJyMlU3d+c6elOrTlGotpf6U6c///nP0W634927d7GxsRG/+c1votvtxvr6+lSSC8sgte2zxsOFmCaz6dKUTJgoo/JM05j4OzPhrJOwqbzPSTXLaZ7JrS5plKBPStyYWpaPH96CjPRedaO32+0CYDlkhR0MtW3Kkw4IPM8gSz50gEJ4AkWtClzL1DqoUnaQoUoYEKHLbVHibByj9W0i9TJ4KEPr7PJGAYu2uwY8Pjc5WNF6+z387nMvuy/zDtWUlb5vnjms4DirX82joIae/g+fr6ysxC9+8Yv4D//hP8Tu7m786le/islkEicnJ3F6ehofPnyI4+PjePr0afz617+OjY2N+Oabb8rumJPJpJxP8Pbt23j//n1cXl6WDcMApRmId0s9619vz7x842E5LYPfXT45gOB92bjOG/75pKWFmTcgQyEoMhUeHl9qciOrVeENy0CETmKQ3tnZWXQ6nTg9PY3xeFxyHTST9PLyMk5PT2MwGJR3ZAlNDnxq6Nr7LHueOPDZ2Vmcn5/H8vJyySUYDAZxcHAQw+GwCEWfYFn53pcorloda0CuqS0Z+Mr6qIlfPjdlgNSJNtSUQCbQ2u12UcYIB8Cm3quxd3f9uVDJhDzWDOEGfhuPxyXhVN32mfWo5WkfULZvv6310aVQzvMO9jILnj8VavzRHkJ2k8ltKA3vRdPWuj6mKHfN62C/A19Vkim/WcbJQ5LK04i72eKuRCPu8pdSzULnN3939lz2fBMQnkUZf6kcPj8/j6Ojo2i327G6uhrtdrssz97f3y+5VhcXF9Hr9eLdu3exuLhYwgGj0SguLi7iw4cP8Ze//GUKIC4vL5cwXq2NtblF3R0c10JfWR+pXIBqc7IWCnMeuA+QvXfOQDZJNLaiTAjyV+veLWgvM0uKQWg4o9cmLsp8NBqV74QAer1ejMcfs/c3Nzej1+vF8vJyHB8fx/7+fkwmkzg7O7uzmUvWD75ZR23gnLEmk4+hjMvLyxgMBsUzcXp6GhERe3t78eOPP8bh4WEJLzQpYfpM3aH0GbFYxgGl4e58LVP7VN/jY6ICnTH2+5vCHJ+LHIBCtTaqyzki39BnMpmU3S4VdGU7TCopf9eAnXqaFLiq+9LB7vb2diwuLpYNkfBeqNWgY0vddF4pTytvAW5UCal1lLlC9ZrWQQW7vkNzBnTvePadrwlhF3yamwFQg+cJO/jzNWH/kB4BSHkBucb/vluey1Xl32xuZxa6y9iMMkveqWYcaN383e4e13tpw3A4jHfv3pVQMPNtNBrF69ev4/vvv483b97EH/7wh1hbW4snT56UZENk7Gg0ipOTk9jb24tWq1VWFzx58iS63e7UKYbeV5pflsn8LEyi99E26u1n7rj3zvvcZX6t/6FacmFGc4MBV2bZIOtEUgWQCWD/n2f0HU5NrqVMWSIwz8/P4+zsLCKiCEo8BOxXfXl5GWtra8WdT5zWLeaaUlMhp3Xg09vtilX7jH0S9DyCzGqfZfHOGisXsE0Ct6n92fudeb9EyoR+phj41LX/JNGhaFQJqSXspAAsiwVq3TRZkGcduOmyRQXiWrbzJN8dKHCd0JiWnQFG7S/yGxByTe1SAKl8jbAlLPIpvEMfkXXv1rQDEr2mfeWfDwlmFaRm8zaibqShNNyV7AdSUW5NscwCALX5n5Uzj7fAdQl/p6encXh4GBEfE651uThgCdCH239xcTFGo1EJweIJPj8/nwK72ZzM5H9NXtR4NTNsa32mIF1Jwfc8ffgpMnduMKBWsKJ/t2SU3HrW57MGOSLUcjIwUgMETHSsiuvr63jz5k2srq6WzVqI7RK/XVlZiW+++Sb6/X788MMPUwylZWqd9H8XLhFxRygykAhNXJm4sdhw5ezsLAaDQfEKaJkqDCivtq84/ekMSN97MkwNcCBc/bqOZ8R0vDXrj4ci5x2vmwtbf4YxJDlwbW0ter1eqszVpa4nbippjJz3zLpHl+UpGMH61/CAnxrpik5dxzzD+Q3wvb4jAzC6jrvVahUPCdu+Kp8pH2o8H2LjL/pPl2FqvTMed68NGzExBigHbUOThyGb518CEKAdfgpdNmcjpt3GCkzVc+VyODPUaqEDny/UBfChwBSqube9DpknEVn46tWrePfuXXz99ddlJ1d4l2fZYrjdbsfBwUGpl76Pv5WVldjc3Cw5CYT51INH/6tVHpFvp6wyXscBcs+h/o4cR254nyuoyzx+2s+Zh30W/VVHy80r5P+aydTEsPo9AwQIg+vr63KCIZmnanXjJlpdXY2rq6vCEJPJx/ilC6MMFOhv2UC68M3ap/tuq/DP2uvP1wSCUpOyz9rm92SUPVf7/6Goxn86kebhUT1cSA8L0udV4TbF7PR3fzeTuSlmjsL1Q4WoQ2aZ1+qi96slCZjNcnb0GfUiqCDSempfZa5Tv+5/3v4a+XzynRRn8fd93/e5qKYk+a3pmZqXJuJunlEGArjuisnv0b7NwFcNWNQ8aFn8/fz8PK6uroq7HyNK2wFIVa+Wgz01AjAINRzh1CQDZ/WxP6uAoYkv/R1a/1r/cK3W10107+2I1erVSrCpUETciYNopTJ0qOU4ZQoNS9WVrj6vKO7m5iaOjo6KVXd2dlYYABCwvLwcz549i/X19eI+Ojo6mlreRLk+gDrYfq+6XLVv6M/T09PY29sraJbNh2DwjHF88tb6Wv93MOV11rap0Pach3mohsTncQ/+3OQKpkmgaT90Op3Y2NiIXq9XQkvj8bhsZa0KjbH2mKC6/XU81DKHXNEifFV4kXk/mUxKfF0tD+W9VqtVtlxFSer4EpdHqeNeZZOjVqtVPFcRHz0IrPlGSKvXUEMM2Znq9InLE1y2msuQjY2Wk/2m7mL9PZs72kdYhQokHpJvPXbNtZq3TmWhW7Dc422atR2xK5mm+ax1yMByRN3T49+1njpWyMiXL1/G2tpaye9Stz/vdne7ewVZfshJhvCtggVvl/eRynlX0NRZ+wJDT5/Rumrf+jzQ+2sgS73ZuopsFn3y0sIMQc/zUnUlZRZHhsBqXgG9lik57Sys7ZOTk4iIGA6HcXp6GpPJJNbW1soeBIuLi9Hv90uWv7tbMpSXkSpBZSydjOPxbRIYygIFU0Ps8/RBRt6H7p3I2sFYKYNl92TX3OK9L6D4KSkTRE3k44vS1HPRSdbLLHsFERG3O2MCFLKwjscptRy1BtRdiacCZUyMlHdksWJd6aCJe54wq5slqRuWeyir2+1O7USo/O6CrAZmtX6qmLPxyBRINqYqIOFhyvL3q+Gg29w2eXY+F9UUb1Y/vy+zFFFcTctKs/ep/PDQb2aN1tqSeQ+y9s4ikq9JCidU5spSQ5e0HwLkswGd8l3GczUDi7JqFjuk9dAwre7a2Wq10lCQvl/b1gRW592YCLrXEcZaMa0UpMxJZ6lSzCxUnou4e+yl3+8ordYJNeE/mXxck3pxcVG2pby6uipnXXOS4PPnz8smFyScDIfDIohriLYJxDgTMqlOTk7i9evXsbKyEoPBICaTSZyenk4pELUK9Z21SdMEzpriW95X2scZGNT/XXE13fO5yft/lsBxzwi5AisrK7G6ulrGTvsm25+cPz2oR+eHTuqagoq4tcZ0O1qNV2o8nuf1GQSUgnCOdVYFj0WPhUxsX+vNd0JptJ1VOsS1FfhAKjDpOw87ZDsROj818bWG/zwMk4E2dwsD1Mg34r6HArIQdXJ3cRNQiriblK3LlP03tbxrfebueKUMmDjIbZIBNZmuvAzRF51OJ7755ptYWlqK3/3ud+UwOoC6biPs/QYI5mA4NhzS02M1MVhDZlm/ZKDI269t9BBf9pwbFhF3cxX8XvVG3odv751AqFZfDZVqBXhO0WQNPSnaqbnAtENrzJUxMnUeDofRarXi4OCgnFewtbUVS0tLZXvK58+fx/r6eozH4xgMBsWL4GXW3qvv8/0KVMCPx+MYDofx448/xtLSUtkdkX2xI6Z3isv6vMkzoe9zVOn9qvXOABi/ZVQTzi64muJqPzdlfRcx7T3iu/5pngCZy2xUUgNdbh1nhOLVMwb0N0IKqjRR7Hi5eI/XQ+eIh85ubm6KSxSLnt/UG+CfKgg5AEzBDjsysnGXhksAtg6YlLcU1NR41oGm/q4uceXfmttW+ySbl/RxBhg+J2m9fAXAvKRySJW5GgM1z4N+zww4/1/vz35zF7e/r6ZIFdhxH0sLSSZcX1+PTqdTksaVV1Rh6h87EbK8u91ux2AwmAITgGOva83b4B4yN5j5zfnKZa2X6+NAOQrWNcTFfJ+X7n1QUTZZ+U0pY4IaAzlDZoxZQzpuUWX/K9FJo9EohsNhLC8vl41bqCeego2Njdjc3IxWqxXHx8cFcdbCFjVgAvP6ALdaHzfHGA6HxRXLNWLAngA1D9UEaq1f/P4a4GgCX5kicq/DQ1tXkNfLP1WQqbdLlw1CHvLSZ+7rRtV7sjXjHL6ysrJSwgEKnvVZ9QbQHspUt74KJxVShESWl5djY2NjSuBoAq6CVkjzHyaT2yRHVhooqNH55GMDuVVXsyCpRxba08+MMrkyz3Ofg1z21azTjJwHI2avPXdZ7PJu3vdm4B8Q7LI68zgoQHeAe3l5Wby2KG14Vvdb0flLWfQHfHJ6elr4ZmFhIc7Pz++0UQ8MqrXf5V4m81ReZICIOma/Zd4DB8X8ft8QQcQnJBDSiTpArmiadgzLGCtjPH7T+7IJmr2jJlBAyOPxOI6OjiLi4yDv7OzEeDwuG7ewTOrm5uOZAuwGeH5+HoPBYEqQZgPHNYQ0AjC7l8TBTqcTm5ub0W63YzgcxsXFRcmcrVnoNQsfwa3bEWfKijJcuPB8pjRnWcO8I6OHtq6UMgXImLngBX1rfI9yM0tHY7JuVcxr2aEoCVMsLCxEv98vJxay1a6DALWK1EU6mUzK8axqhXs/6SmfHOby1VdfFc9Iq9Uq3jJ1pbMcam1trWynTaIufbe6ulp2AxyPPy4Bc8+hWrE1z4CvtFHriGRHgPs8fZ7Jkpp37HOTy8lMbjY9yzOEfXSb56a5zDjovaqQVG54XVX++W+Zksra4Ru66YmWyNSjo6NycuHi4mL0er3Y3NxMk76dX8gXuLy8jNFoFJPJXS+S5o84+OV5BRfUz8N12mb1sHl/017vI32/KnsHxsgiDxvMS/dOIKwJ1uxaNrn8Hp10GfNkZWUo/j51U0V/cXFRdnG7vLwsFhdIs9frxeXlZfR6vYiIkj2uAj9Df1lsnnvdbYTQ4oRF3SzlU4SRv2Pee1xB/jXvaBqvh6R5rEMfr1ljkClVpXk8AypUuNcFhO6Jsby8PLX23HefVNBOO7iOVd4UA+U3FTAo9eXl5RiPx8V7hTKOiHJcc0TcyW0g5KC7Yqp7f9bYZJaWX6/JIu1jN2CavFbzzocvmTLFHTHdD2r1Ku8pP9b6yecK78sMRQehGahx2ZHNR3gecIMS9rMFvB+y8t3lr+DeQURWFmAjU/T+vuy3mj7zd2Ztqb2r5n1oonufWphZi9luVqooHVG5GyhTHIpCs+sZZcjUr7F/APsNtFqt6PV6cXJyEisrK9Hr9cq2lOvr6/Hv/t2/i62treLOf/v2bfESuCCG0VdWVmJpaakodI5UxjtAP4B43ZLUSamuMm1HNvE8LlhTyD4pskk7LyDQOnEtm/Beh4cgfb/H4Bzsah8TV3f3KkpQ+ZTns90JVTFqBnHE7VzIdh5TwLiwsBC7u7vx7NmzuL6+LmD2+++/LwcZYeHjkVCe4JO9/1mNwPv1+NaIjwpdt+dmBcHGxkZ8+PAh9vf3Y2Fhoayy2NzcjKWlpXj//n3s7e2V5FvKBmQvLi7G0dFRCYnpHNL+xbvGGPn4UFfGSD08Wo7KpCz+mvF/dt9DkIaBfJVIE6l3iDFW0EV/qmxXCxxZpqAQeaV7oTh/adkeq58n7yEbF20D3grOk2FjofF4HKurq8WDRfszOaZ8xDxx8Mw7I263tKa/1OuqvKlyw8cQDzHkc19DF7X6Mp5NdWX8fAfTWfRJBxX5Na2IU6bQ/Xd/T5OLrqZYMuVVYwKYms2IiBmdnZ1Fu90uWxN3Op3o9/txfX0d/X4/IqLEbbU8dWvCLLr7XBPo0f/xDOh1V1TzIL0mxZ/dW7N+fgqLyMHgQ5D2gU7cWWBJrQO3IPT3iGmQqwJQrQW3mHRCu9BUUgu61fq4JwErYTSjXzPxARza9wh9DRU5MM9APW5XkqqWlpbK7p0eciLPYG1traxY8BAjzwMKxuNxcdVm5HVyQahtbOLZJlD6pQDWJvqUurmnyWUK5ICJZzU3ha2iOTGTclHOqhzdIFC+n6cdmYHC/wpW4E/AYLbraxMY0P+b3PcKBPikT7M2Ze5+15FNoCjTi/MYZ2p0NOU3ZHTvswncsvWOcPSD0HPho7/TCJCMbh+q784Esv6e3e8M5R16cXER+/v7cXFxUbaZ5Xqv14v19fXodrtlQyIOuzg9PY2zs7OyzzVMqUyg79TJ45OFfvHdDr0f3drWdmaDniFVrlEPJjl183dlQtIVmf+uMcUan3xO0t0kM/6LyNuHgl1eXi5JcXpuRcTdZC61+NUFzm/0tVtOuiQu4m72+Hg8LolNetLlxsZGrK+vx2g0KhtYwY/suonrVK1+9QbULJmLi4tiBS4sLMTx8XF0Op148uRJbG1txfX1dSwuLsbNzU2xzi4vL2N1dbWcEqq7f2KBra6uxubmZgHMw+EwRqNR6VfNhXDvmPJRNr+d17Pr8xgleu2vBcR/Dam3T/tHY9Y+DzMjzXlW+dMBA8bM6upqPH/+vKy0WlpaKidKwl94ZdgIiHwRlmL7IVNqQc8y3lT5YlnrPMMDy/kzyjNurdM3mu+mckr1kPKc14v3q5fXf/PPTObMw1NNoNY9LB6iYM7+LJ4BnRieNOVo0zvDAUG1Mv9/MJC5khTVzossZ92Hh4B1/f1+v3gFcKGiDNi/+ubmpuxHsLq6GhcXF7G4uBjX19clFKCxUghGVjDjfdbERE0uqKZ2qnWr3/X9Ht/jvpq15c/N088PSVjIHld30nZqjJvkK52EmijkFlCWAMRv9Ju6L11hwSc+BwADJMetrq7G+vp6XF1dxbNnz+Lk5KSEvwCX+i5drkgbNEHVlYh6zyKizIeVlZVYW1ubSo5lSSHWo+7MSIiBRFk29oqIUkdPiNK/Jn6vCWT9ns2rpiQrBcsPzbvaH/CfXtN21ADNZHLr/nfr1tuHe5lE6p2dnVhbW4utra1ykBvhUTZLQxEfHh6WkNDZ2dkdQBsxbURmRpPPJW8Ln/Czeiu0byCVmwrAs/ml/VWTE03WfM3yd/n6qQl+Wj99F5vVeVgLwDIvIPikBMKa+yaz/F2xueCDKdrtdjlmUhEwLkoULNZdDTBo2Vn99TtMgrBjR0KWHC4vL0e/34/xeFyWRV1dXZXs7sXFxZJ3AEhA6WhcDYHKuzT5RQe2lsxF/9UsHf2eoXC/z/skE6A6XjqZFEVnngv/a2rT56bMItH+gRd1Xb0CMLU8uD9ierI7+OJ3JZQpW/3CF+xGCT9CbCNMLPTy8jJOTk5K0mtExMbGRqysrJTTOdmdTRWsh6x07tEe/9/H8Obm40FaR0dHxbWvipt8Gnhcd9OkrrphzPr6etzc3MTS0lJRKnjY9Bx65fdMEaJYNL9Ax8jHCfL5QH+ocnlI0nldWxKo897bpnNZx1R/j7iruFze4i2AD8fjj8fA6/iTW3V8fBwfPnyI8/PzODo6KoaS5htE5Dk4GTgBZFI/9mTZ2dmJfr8fNzc3xRvBEnDtA/9UcF77zftLwb/+Pg/Rf5TtuWNaV5U3GSDJeABPkZ7HoHzeBGCU7g0GMq9ARs6I/h3hBJMtLS3FxsZGcccSLiBjmSV4CM1aKKGpTt4OJhgKm4QmNh9iuQoZ3Dx3fX1dTrk6Pz8vcU8GlgnEWlh1pZ2cnBRhziTCcvL+o74ZY9QsHu7xv1lCLQMW+n7eq6EcTXpEiLoSUffVQwlWnRwZStb+wZLO6g7Qm0xu3Y0IKY3XO/B1sIfFtbq6GltbWzGZTMpGJ2/evCm5I/AUSVxYcpeXl3F0dBSdTicuLi5iaWkpdnZ2IiKmwCrWmQNFVZSEUNxlrGOKV4S5CNDQ/gW8Hx4eFsHZak2fZ4CyZ36wLnwymZTkQhRHRNxZkgmIoEzlb7Uya+G1TDH4fQAQ3velkOclKW/CX+qR8jCQ8pMnvbprXb1CjJfmD+gulfrJ+BwfH8f+/n6cnp7G69evYzQaFXBASEhBQc1ipr3u3WKZ7dOnT0vuzHg8jsPDw/JeJ+27DAzQHzq33fWehb9rVPNYuceplivEeLncVyDMNepF33hy47x0rzBBhrbdG0Aj5iUGWPeIZhA4DAX3Ip8sAVRXpH76n7dD3801LJd2ux1nZ2dlZQHvYP0yE4Ed3CLijoWP2wbLTwUYApxJSbwLcJRZ/N7nTX2pzzV5C7z9+lw2ziooaorVLe/MG/MQlCF+J52cng/hSieLF/JsFmt0aww3LGv3I6IINRJWUeRaf/qdtdGnp6cxHA6njuMmdMBGVuqyd8tQx8/nAkogy8bWnQsjbvdVUB72/QOUNPSyvLwcl5eXsbm5GZPJJPb29sq5D27l6P+6AZQL+GweZOPugth5N7NUPze5UldZV6N5DSN9hxPjj2IdDAYRcXvCK4CJ+cKYqveFUNL5+XksLS3FaDSKk5OTGA6HZZUJsjGzzjOLmGvIzXb79phiDEYNR8/yqHof1bxGWT8qyNd3uBJ2HellaC6EW/O18VPjkzo03T8PfVLOgAsVrVzmqoJ4ThEqpwaurq7G7u5uOZ9az0tXNxNnC5ydnZXBR9Gqe96Fk8ceESja6aDWhYWFMgn6/X5JLETYRnxU+GtrayXxEK8FLiFFsbyXJJvLy8vY39+P4+PjODk5Kacj6p7Y3t+ZyycThvqZIcvM9VvzLOhz/M6OXwgFR6r+rLrc7wMSf0rSjVO8z1yJaJIfxP0ocdzIlMk9GWHVREyfV97tdqf4CmW4uroaOzs7cXBwUJbmcYolCYAnJyclWavVapXthXGd4m1gm1U291F+p26ZNYarfjQaFYWtfYBnjueYc6qosmQu7gewrK2txcbGRmxsbMT5+XkcHh7G8fFxCS0QIlSAAa/Rpwpe1LJnXHTpr/OmWpxqZWMd4pl4SECge45ACswdvGbKILOE/XcAnfb3cDiMV69exeLiYoxGo+j1etHtdqPb7RZv7tLSUsmn2traim63G1tbW+WMAGTbYDCIi4uLODw8jKOjozg8PIwffvihbOqmu7u6F4u2YTwpz3Y6nXj79m1ERLx7967oCdrlW2FnMhFynZb1VWYgQQ4OuGeWjGgKE8+To+BeMfolq2MT3Ws74lqjMga8j4tCAQYMputjdenKZDKZcldpdituTBJcsiUWTXXifpICSZZRpUfb9ZAJQAh1xc2MxwPS+uMynUwmBeFiDTpwuQ85Qm1qc8aws8r2yfSlWFBN1NT+mkXppGEDReWO0Hlf5onR311IoHg07+T09DTa7XbJIVCLEIsf8IoXTZNe8V418YHytFowKPjJ5PZYY/pBcw/oQ5+jzscKDFEk5OVcX1/HxsZGiUMTMnAZ4n2WebH0c5ZHTI2azID5EnhalaP2rdatZnzx3d3KlMvvNb5Apt7c3JTEQAhPLWGp8XhclpPi9YmIYty1Wq07J2MOBoMYjUZxcXFRDCgAgStBb796BpgfKlPdk6S8kMnHmk5r4oWa58DlAzRPzoEbZhnN48lo4okazQ0GXGjoixS5qdWl1nfG0KBRtYbH43GxulX49vv9InT0WdxQeA1AoyRisVIAJlP0qW55rAHqwCqDg4ODuL7+eEwmS8wAIouLi2WZ1NXVVRwcHJTEQEXt9NnCwkLZp2BhYaG4c1dXV2M0GsV4/HH5GN4Pntc6al2duTPB6fc4czYBARUkxJa57vEz5xG97lbZl0SaWa3JQqoodHmTbhGM9eoTtzYR6e/T09N4+/ZtrK+vl1DSzs5OUZCTySR2dnZie3s7Tk5O4rvvvovz8/OylBU6Ozsry2LfvXsXZ2dn8eLFi7Jxlm+l7UsrVXC68kURACzco6RtVZCi7VTAsLCwUDwYm5ubsbm5WcKCi4uL8Zvf/Kac4Mn8Pjg4mGsMFUwhH3R+uBfB4+M6B1wmfCoo/6nIQ6AeStR5pXNc2xoxvaGNZ93XjA+SRRnPw8PDEpKivE6nExsbG7G8vBy7u7uxsbERvV4vtra2pkIH6+vr5ROv069//es4OzuLV69exdnZWbx7964kpmoIgbZpOAo5SR9ACmgh7S+ViZm3Re/3cmvKG3mhfeihRJWZTV5EpXk9qRlgycDILLp3mKCJasJCrQmfZMq06nLWU9Owpr3ciJgCAysrK3FxcRER0zv8qZvWJz91cNQMgCDehXtck2f0vHfu5f0oDQdQqlR1hQJLtjTM4P3v9VYGrKHU2rWasHMGdqHj8Uu3rByN18r+Uki9Uh6j9vuUP7n3U8IfxEsXFhamrCLCEHiUAGHv3r2LyWRSEq+oj1pHbJwFmF5dXY2NjY2yMRHhNresEVq+IRFKA5e7Aga3tpVPHPyr5wjgQ2hQ59D29nZZwru5uRmrq6uN3kXle0KOEbdeOgXMyCQV2v7X5EV8SECQ1ZH6NCWw6b0ugxQI1NqoclmNPeQh5cHDgGNkYKvVKvsTEFJkuSJKnv0xWq1WnJyclDrj8fVllGqoTSa3q8CoI8fON/GMewm8DzJgpXzu5XnZTeVl9+i4ZHIkMzS8DO037vmUkOy9PQOqEDOrRyvtSJXr+smyECwB3FKXl5dT8UomPIedsOGEhgdIwur1emVjoOPj4+Jyvb6+juFwWJY8wXB68pW6WBG45Cjc3NxMuWE5chjSJEP1XGh7I24Zo91uF88CiVRnZ2elLhqbbVLwTYhWx8P/d8XAn2+ME3Ebd/fnNXPVY14ktenysIck758aOMlAEm3VA0PcIs2ErIaXKFeX2y0uLpZ9Lbrdbuzu7sbq6mpsb2/H9vZ2HB8fF759+fJlnJycFK8X3jDmEMmpJP6RO7C7uxvD4TDev39fwGpEFLDtc1VjkePxuAh3EmzViwLpEjDvV/ip3+/H+vp6SarVc+cB7l9//XV0Op04OTmJN2/elMPB6F8Hse6ZUAXp/a71YeyQIbopj/PAQ/KtJ2RCOk7q0crCjJPJ9EE76hHQsmoyBp6NiDtzudX6eOR6u92O4+PjAkK3t7djZWUldnd3Y2lpKZ4+fVp2pySUgJeU81+eP38eR0dHcXR0FHt7e1NLE31JNuFgeB+Q4GNIHd1oyWSAZvB7Xygv8VymxLXcTB54rlZm0Llh6uVmupfx/2vor9p0SCvjDObPKbkQIdbD0j6uc348yhdLgsSrXq9XwACo9ObmJnq9XpydncVwOJxKzru6uipuKP5IUkI4YI0R59L934lp8jtrpElQ0vXgCFFdM+5CEsZQrwD1mkwm5fTCWt9Rb3fL6n0Zg/l1F5haro4Tdc2sKweJyrwokIcGA0pqsWZo3ZVORNwBA7pPgFtuWVlqcQNgIz7uyLeyslKy6lnSur6+XviWBCwSa+F1klZHo1EBlADpzc3NGI8/nsbZ6XRif39/ysKKuAWw1F+T9Ki7Ln1lfjDWzH3d1CbiVmngFSTBkdAI/UBOAxYdO33+8MMPsb6+HhFRlisqf6pxomOH10YVgwNh9ejRZ9RHM8O/BDCQKXb9Ta1AV4ZOupQvAwXZO/ieHTXtzx0cHJStsre3t2NtbS2Gw2Gsra1FxEcwuLGxUYy57e3tWFhYiBcvXsRkMonnz5/HyclJfPjwId68eROnp6fx7t27uLi4KEtvCXchk/V8mGy5Iv/7fhr6G3/wfQYGvF+yEK6Xl4E4ns3eoWE7lekui11Ou5GjBkiT98jpXkcYU7jHwnVSUklvqAoOLVMFJAgPgcdE5dhTFb6OnrC0W62P2cooIJYpdbvduLq6KnsDsMTl/Py8hBFwzbLDoOYHMDBYR+z0pkJK91rX8vA+UEd+U4Habt9uurS1tTXFEAo4miZ7E+rNQEGGTrnuwlbHmrHT8jQz2/txOByW7Wm/NMp4V6/rHwKFMeF/bbMn2GlZjD+/ocwnk4/xWFam3NzcFAV+c3NT9r3Y3d2NdrtdPEh4jcbjcTkueDgcliS8brdbNmi5ubkpq19Q+oBtjc16P0TEHSCXeYO8H/X3xcXFMndXVlbKqhqEugLJhYWF6PV68fTp0/j222/j/fv3sb+/f0cANsmbTJlRx4jbQ6QiooAylymZBfYQlC03hlR5ReQKuokc0KsVm4H9zJjRunk/4k2FxwAJm5ub0e/3iwcLeT2ZTEpoAU8vHlL4hYRD9ixgx1i8TJpTxjx1YOftckWbGU7OQ9rPWe6Bg4R5xgg95p46fd55VGW1XuN/TWCfRfc6tZDJqm5CR0LeCYrOtSOViREux8fHRdEeHx+XA09YvoKiXVlZKRa0JjgRg0eI8i61xA4PD2M0GpUlLiQJ3tzcnvJEUpOubJhMJlObHxGfUrdpr9eb6heWP5LIpczHM2ohkZsQEbG1tRXv37+PTqdTwItaYJmAyJJGQLwaG86AFPdmY+aCQAGh8gOeFUI5vV6vHPh0eHg4L6v9bNSk9GsEP8EHxDM1sanVahXew1LxPo+4K0AQYLp50OnpaWxsbMQvfvGLkkPw5MmTosC3t7fLBlkRUcICBwcHcXZ2FhsbG9Fut2N3dzc2NzdjbW0tdnZ2Ynl5OT58+DAV111dXS07uB0fH9+xYlxYasKv5k7g2YLXFOzo/iHr6+tF0B8cHJR15xG3ew/88pe/jI2Njfi7v/u7uLq6ij/+8Y/xl7/8pQg9F6r6rqzurszwACJDdD8HB7vanocCBcx3NTrcAuR/KKtrZqEqCMgAgJajfOAKi7rhoR0Oh/Hhw4fCF4uLi/HmzZvodrvx/PnzePLkSfT7/Xjy5EksLy+XkEK/349+v1/CZOzIiTFFuOzs7CxGo1FZhgqQZtk2yba63BvDTb1fnihMu3R5sYMI12P6mcmUDIhk9/g7siRc/dQ6e4hM5TP5HfPQvXYgzBRDRp9iAaJs2+12cY2r+ws0zzIqOm5paSkmk9vlTwwinyhyAAJgggEguUoRIW5QVQDqSiJeRfKX7pqo9+pa/NXV1cJ8+segkx/RbrfLNp/kPlB3QhaaCNmE2PUdjKGCAReWykx8qlWs1rEuE9NkNwQAFt7q6moR+A9pZdWsK79H+8YT0JxUoNSEKvfpOOk+BvA9gvTs7Cw6nU7xpnAGBuGx8XhcYu/cE3GbsMpmRL1er7QHa5z9PPRALA19eLsza8Rzg7RvM4sKniGBjBwLjW8rqGDOkJG+vr5e6g/vN42fkgvkzDp8SEU/D6nCz8aDeyDnc7f2eV6ViMfJa+Ago6xOer8uTcTgIekQw6vVahWPL8nU8CHXdZdXDeOyoRYb0XGAFxtuORjwreIzS909B97+zHpX3tIwuo9dkxHi7/D7XG94bpcap3q+xLyhgnuDgSbSWCOUJWXUkg2xivXwEra6xBLH9bm5uRkbGxslAY9ras3zvyrsZ8+exWTycUOWwWAQp6en8eHDhynGccuHstrtdrEKyW9gAxU91hVmINTQ7/dja2tralIQ21VXDu60drsdm5ubJcbKdp6Xl5dlW0+YOSJnNB0z/a6brKhyp53qaXFFr/do1jvtpq8pm3G4ubmJ//t//2+67fLnIBeIPlkdMKnHQyccpIAT1yTEOzR+iXCjPzSuTWiMxNGIKAp+cXGxWPfLy8vx9OnT2N3djclkEmtra/Hjjz/G2dlZcaOORqN48+ZN8Zg9ffo0Wq1W7O7uRr/fj+Pj47KtNt4BPesjYnojIkIhbjkroESYamhE+ZHchfX19bJpDe8G+HJGAasJer1efPPNN7G2thatVit++9vfxsHBQbx69aqEFSjfLSbqwNipm5jxUDlTi5e7wJ8l+34u0hwPtQD1u97rwCDzmDgQUq+D3kOZTg4m6BsML/1DXr579y4WFhbi4OAgXr58Gd1ut3gESKB+9uxZbGxsRL/fL0tt4Q/dBh2PGrzPeQTsW0D4SdvG8vWLi4viPSAfR5PMSUrMcg+8D+gHXfWAYUqo25dx6vzSclzRqyHi8lrlLod+AaBZ7s7ZJ2ySNw/da9OhrHP+2kmiwsaROgKTz4WFhYIueS9uVlyRaoXp/ygzdf+hHFGsuJZ03/2ImFJwEbdLD0noUutKEaUujcRDoOWrW18VDzE0UCxbLxMnU6CSIdvMunX0qH+q4LHsHQjwG/2IYuM3YsLKDwAGPDFfYs4ApJ6UDEQpufWctU35WBPTFHzBgyq0UXZnZ2cxGAxKzFStofX19dja2ipbDgOksYDIhWHDLJ7jgCRAG+7XrG3uAlWwFHGbR+DL3ZzgFfeyURbtYv06PENWOqEFwEOTFa8gge9usdKmTJk6fQkegxp/8VtEPTnY5VFNVmeWb80joDyfvV/HVo1A+Jr8FLxTWPBsNAU/kXTY6/WK3NLN3FQftFqtYlhpEq3WE7DKvMAD5xvVKXDI+rTWd5n3KaNMrqhs4FP/1081dJkzGMEYpexuimdgXvqkMIEqEu0QBlvjLU0JMM5YaqFFTLthEVpsgjEYDAoAAEU+efKkxCbX1tai2+2Wda4RURQTSg0XVavVmlqVoEsO2fKVwYDBseavrq7i5OSkKG/cuZTPemkIZtNT2rCYuQ+Bvbi4GN1uN0ajUdmsY3NzsyQvklRYy1ilLxHCTKSI2zPus5i/ejjUQ4NgVzAAIOCeiNs4J2344Ycfyng+BPnkc17OJl2GyNUC0nkQEcWrBLnrMCKmEvZubm6mVpHgjUIovX//vsTy19bWot/vx7Nnz6Lb7ZYDvTqdTgwGgxgOh/Hjjz/GxcVFsYjYzpjliu12u2Tns0rl7Owsjo+P7ygLwABAHO8cY48iJf6qXj0Fm7Rve3u7JIMhH1hiBq9j1aAQ8Kq9ePEi/uEf/iE2NjbKUsNa1rx6CpjHek3llWZuZ+X4/w8FDHRuuxVfA6mMXcQtr7o3h0+9F7mn4+rAQi1irZfXRz1NWo4qXJLDAb0nJyextrYWu7u7cXBwEOvr6/HrX/+65BNo/Btv0ng8LgmybGuN3Mbbq2CKdunKBJITj4+Pp5LL+Y15qfkjrqsi7ubV0X6VNchKNcJ4RueZG2iECkkOxhONjlC9g1xuAoAZfXLOQIZGVdi6iy6i7nLy747gM/QFsgMJIlSJiYL4UG6rq6sxHo+nVghoh/GsCubs9CuN97bb7bIKYmHh9uCOiI/WPRaZrkgg/olViMDSyaXxVZZk0S68BDCw9rF6VFRZqTLXmJIDBOpLH9E33A8I8DCBIvaIKJ4PmLbb7RYeeQiqebNqANf525MuNWeCcY+IKp/DFyB6+gfLWD0IWE2aMPr06dMCBHEBdrvduLy8jGfPnsXKykpJgoUnhsNhOVxmZ2enxGcRnghQ6u3gB4HNmFJ/BQOuVLhPlS/Cih1Ftf8I8XEPigNvGGGSZ8+eFYCANeguauUt6u/hDRXemcXLdTVSsns/N7nsw9MEXzKOyqPqFVWlknkZ/FwJN9KyvvT6RUwntbl3RkMS8Ck5V3hYWe0FID4/P4+dnZ3CDxG34R8ApY4buoAEwqurq+JJhagj3lxNLj86OipnZJCQ22q1CljS5EP6VcvUMKl6XRVQ4wFRTyp6CCMM3tdQAGCZ0DjPq4ELiFBA7CGgJrrXpkMqMLiWoaTMhVRzu2STWL+7C4qBUETLgSzsz87yqn6/H4PBIJaXl+Po6KjE73XQtGwdSK2nDry2L/N4TCaTgkojbrOkiX8i8GBu7UvdCz4iyoDjEiM+zaE1epCLCmG1YJk4MKMnAiJUeJ+iV1WS+pt6A1Twq2JUMMC774NSf0pyxaHtd49HlrClCTkK1HBnR0TZLhtljlDifYS6yI7O8icQHsyD0WgUi4uL8eHDh7i4uIjt7e0SZ19bW4vNzc34u7/7uzg+Pi5WPlY/eQgLCwtlt0ME1traWtkjA2tdhTV1pm9UuDD3dO5rH6NYeA/7JbA/h4MnVXDqzlVLCm8Y+y7UvGE6xmrJ6hhQV6+/e+/8+0ORAy8FbLWcAcAdhwcByEnGo38hd6Hr9u26msGJPnZPDfyt3hkFHA6a1YNDoiAHdL1//z5WV1eLwcfGRXiOdFyRM4BLlidisCkAUDBFX+Ld5bAl6sDW9Aq2IVXm6oVRXalufzXCkDnMM8KzyBnND8D6x8hUgKer6NwTcx+6Fxig8e4yB3HCaPqp8ccmLwEC0JG+DlbEtPJE8BI6ODw8jIWFhdjY2Ihutxu9Xq/EU3q93tQJWySqkGShCl8tXQYTxqNeKGZlMCYVHgX6hlitCh4dbDwMuJo9ro9VRGIlikbjva5IaIe68HXiuCKHUT10oJYFDOtWInE7jelSZz3Pwd1qn4vcqlFvDXXDDacbUanFowmp/JH8pF6mk5OTktHPckp+03wRBIqDMwUO8MOPP/5YTiRE2G1ubpblg4Dh/f39ePnyZQEjrOIYDAbFasDlSp7M8vJysdLcU6A8A1/pKoAMCCP0WSa2vr4e29vbsbq6WtoKv+hzlKteE/iOpZWbm5tlbTnhkJrLnHIZPxXWJE6qkIY3VdCr7HooyoBAxG1fR8SUPMZwYMvn58+fx+rqajx58qQcgqVgS2U1vA+fAhD0Hp3fjBtJsBoy4g/wizWuK6H4VN4jFMCST6zj0WhU8khYKdPv96f0EZYy85Oyr6+vS+I1ibYqAzgNtNfrlb6lbch2Er816ZZ+1JCryhgdGx0/BQnqbWUZtub5qBdN+VW9XR4SaArNN9G9wgR83sfCyyaTggN1S7lnIHs/z3k91LrQU95QvFjjMA0TR88DUOuQd4LMdGOMiCjC1OO9TArKIPFJE2hUSKknAlTsVnnE7WErmjypKNC9F5SnqymUcdwdrmV6TFzr4bE32qtjC1CCeR9aoEIKBNTqVQDqk1kBDAJAgSmeAh0XAJCWof2C5eVEf7oLHncqu2oSNkJQYgG2Wh8TDyNiKjRDmQpsCZ2RnBeRL/fT/tDVBQ4GdC6qwtLf4UEsMF99o8pOwQfCF1BN/yqoqrn9XVapJ7Nm/ev1h/JmQWo8ZS5+vS/ztmqimSaTMc8jbgEQG17puzQviXerDBiPx+XYYJ4DjBLuQonC9yo3UaadTqccdKQeOAVplMtpnScnJ8XgUaJdChD6/f4UWKAuWUhDDVvkBcfULy4uTs0T3pcper3moF89s8wL3P0aVnaPm+aX6fvciM489rPoXmDAwwSzkIe70hEo6nbKwgK1JBkfNO0AVUwsG8TFShJTp9OJg4ODEoMhdsrmF7u7u7G8vFyQbKvVKkmKW1tb1XiMToqbm48nfYF22RhDgYUyA4pJQwcefuFe6oXVqOAj62sEgMaVADXqUuKdTA5lYO7xZVkwMpNCXWgqHCLiToLk5yZ192MJ4N5XcJeBNBWYmpTFZ6/XK9Y2e1YgaEgQxdrlNEo2zAJAqkXNsxp2GQwGcXl5Ge/evStW/HA4jM3Nzfj222+j2+3Gb37zmzg/P49+vx97e3tTW2gDzAg79Xq9IujwSLEXPOE2bScgE0HuXkC9R69hVcHLCGSu+3yIuD3Ii7qRH3N+fh6bm5ulP+k7tu52JYlXjjoBxJlTvEfBdEQ9B+EhKfOmKvjPZClWNQmn/X6/JJhpPpIaP+pF1DNhImLqPA39u7m5Ka506qVLpw8PD8vOgSTksWoLuYhFvL29XfZYUeBBmbQPjwPhL0AlLvSIKIqfeU6yIZvAsVqH0N14PJ4CH8xJVoOxIsr1kYJtqGbZA0R0mXbEtHeQttKHw+FwymuMnMiMYeqApzI7P6eJPmlpoSthrs/rlnAEW/ucpy7Z+zMXJvHXiNvYEs9pxqgCDQQV96m1hAWvk4JPJqcuUUSRwwDal5m3hfIV9eE+pg0OqngHRH01YZAJr4reLXwFfU2uUu9jBQZqYdzXXfVzkbvWFORE3HUrQyogdPJr+yhbcyo0/snY6HI/DQW4e5z3qEJTIHF2dlaSSokPt1qt6PV6RYFq4pWShj0Qllg8nhtDf1Ef+kvnhZarHj4A4vn5eSlfvViZ9a7zgXcCfNX9TdgNYHBfPsuUvv/+JfDtfWVixDT4UcWtRoaOAWOpXi6W9iGv8KBqOYzxwsLCVN4SMgBeJ+bOJ8nPrNTCBc6eGjq/VN5pOI45w1xReUaStSp4TzqcTCYlJIK8oj9om3o2VQa7rkFu0i/qKaQMjDL62ENUrgsAw8qn9IfLeQeLeBp0rOehT84ZwB2UZcNrBalMLcaozKVKR1HYrImuiJ93UabGwHDFsCyl3+/HyclJ2eltNBpNrXdG6Ewmt3Etteg1OW8ymRRGXltbKxYcDK/WtrqI+HNgBSOrMOcaaJalMcT1vK8UDGAF6NIWFD0TmdgZE4i+02swuQsatRprXpuHEq6qgJioJM6pV0Xr6J4cwAPWKvfidbm4uJha1oorPpuMuFA5kQ93KtYU48uKAtz2eBYQXlguJMYuLy/H8+fPY3t7OwaDQRwdHU1Z2ZxngMs94iNIXV1dLctVdY31xcVFsRJ1vusKAwcFCl5OT09jb28vfv/73xeQ0u/3p7b7ph6qoPCCjccft7b94YcfYjgcljn27bffxvPnz+Pdu3fx8uXLkh9BPynoU57zxC/a8CWTJnQyX3WuIzOVx+GfVqtVYuVskEZYCa+W5s2QQxJxq1B8eamDYQUeyE08b8hfgCz1IjeBkBZ8RP3UmMCrqJu+qecN71BElCWKmmDI2ntyxXTJN65/gDPyVPcpUB6PmD610cNlyBK8yfQbshX5qd4w1Q30hYIRxoa64DVwY0uNV/p/ZWWlbJE/D33SqYVUvLZsQTtIGVjL0HL9r4bam5QJnZVZFghOhKJmaWonc5ALyYZq6akFidChfShsbZfGpNWNpBY339XDoMoUJaSACsWNYMZ6dLSoY4WA1UxWj5erpcB9WHYKKgATunubMq8L5AzBPhRpGz2/ool0vNQSwFInWXIymZQlcrpcTpcXIXSvrz/uPsghK5rIxxJSzUWJuN0xbzQaFVfnyclJWX7KO7H24XtyaABrOpYoY+rPvNAYsM5H+M/jlXofvIp7eH9/P87Pz2N7e7uUoZanziX1QtAf7BTK/Sims7OzqYPLZvGXzkE+M1njZT2kl0AVfuY95B6IusJjp6enZU4zn4l9R8TU3g8qJwAByEi1vLUu6u1EAbNzKvI24tZLihFDpr7WHUNNwYAbI4RgmQO0VV3nJFgrsCE8ioEF76IXVMHqeHufozu4D5mnAE37Uj0TLnddRvpvOh/QEbyDdispyNBw0E8OBrQSTGh3lzoI4D4VFIADmEyfy8qseQXcPan1c8SkEwoPAVY7SgEUGxF3jg0mc1njUyQxLS0tFYWpA6lxenevat1UmGYeEnfRcw8ok7qjNBCiDj50DavWFQXAva3WbaKX7ujI74ATf4cm17Csh+VM7CL2UKR9rSGTVqtVlBITD8vcwYKCHM2HYBwmk0nZQ2AwGJTle7pGmLwTBWaUR59p9vKTJ0+K0FSPRKvVKjkHWGQXFxdTOSI3NzdTu0RGTIfHaAtrvBFg9AeJWgjXiFs3ssf6lXd94xrm2dLSUtkOWXdRw0qlPH5jjvz444/x+vXrwlfcu7DwcdXQt99+W4ACmy5pXoMrSv3MftPret9D8S985nFpbVvNyCJmr7tZ8ofnkzHnO5a9W7ZY2KpMVdYhg7Gssfq1fhExlbjnoU4NA6jLv9VqTW2sA2+yV4bm5sB/utPgwsLH5bXs1scpoLy/FgZV0uWD1FOB62Ryu3JBwYR6EOifWduyIwd09ZGGB6iLroJTGY/3B90zT/siPsEzoJ2nL1IlDmP4Egd3JXp5WBQaB3T066hekZXeq0iR3xBWCDhdM8pvEbcuKa4zyCSCYFEh2FQ5u/sfFKnHzboQUhc0iM/bRN8okMCNRF/T/5r5G3HrvUAQuEdAvRTUWf9cqAIgdMXAzc1NcfGpK1rjj1+CZ8DHh7ZrLoeT8rR6m+Ax3OudTqck/ayurpawE33BWe6Uw5iMx+OyXIoxHI1GZc+Aw8PDuLy8LMmoETEVItItXbe2tkoSlo61Cg0FzQoElJ8iogBY3TrWPUwaMgSIKgiIiDg8PIx2ux3v3r2bslIIE6j3hPlFnY+Pj+P9+/dlfiE7FhYWipubXRhJXEXZZJ4Ap5qiV2/cQ5LKV52H6qHyUA2/c4YKfavzHmOFJENkmi951kx/NrtiAynu0d0l8QZBGWCAXLao61zlN++AXwAcq6urxfN1fX1dlvUSDlPFe3JyEouLi3F2dlY272JpooeTlNQDRv08PKy6T8fBAZsbzg5aM6NYdYL2G4BODVRdwu0rReahe+9AqC9y9I3AcbSlblliJ5pARFl0gh8D6/XQd7srOiJH0WqZt1qtqYNTEEC6O2E2oBHToRGEHt4G2oa1o2ECrUvNawHx/pq1osyJovLYoVrB/E4GrzKmu6UUeWpfMH7qLkfh6KRWcKHK9qHJvVq+RpvvDqLUxae/q6dpYWEhzs/PS//pPhBLS0vFah+Px2XTK8JQGn+PmN44BcFHDsr6+noJIwAYFRQyDr7/hCoLBQWAlIiPmyYx7noOAODOjQD6lDnqy2pVTvBuV9A8h0cmIqbO3oiIEgqBr+g7XdkTEeWgI/gby6rmWXRyGaLf1dJ7KHIgoOTjgtsfvia0EzFtkBEKwEOqG4nRv1zDq8Ofej5ZDeC8pqTyg/J9xz4Nt6lsIucp4jbUpXWD7/HMAQjYg0ZlE6tPFMBjYWsyoQMtHwv19qITXNErqAHEq+KvAU2VVYy5em8j8mXj2pcq6+alucGADqIqGLV0GXA6SVE3lWRDIBCnKg/KU6sdZOeDwmRXxuU+R1W+LAwGXltbKxabdqrWTQdNBS2JgZBmjG5sbBSUTUyT5/1P3fAR0zE4f7d6BJSRmOx+D8k0Gv/npC5AjFuO1IFPJh1AxzfYwCWF8FdlozGyhyQHXrjfuOYWVUTcqb8CLlV66h1ot9tluZOulSZM8Pbt23I0L5nT29vbUzFGkutUyDEXSDIcDodlyRHtoO8BqAoY3SpTNyLhBHib3Qz5HQ+EznX4QnepY0WOKgW8DOrlcmCMsALgHB4e3tnohnv29/enQglsKraw8PGERtq+sLBQ+BvBPo87OPMgNHkVPgdpnd1LqtfpJ1zJrGTR5GJIx1D7GdI2w5csjVO5sr6+XpSpGlAKniHmC2Wwidbi4mIJQezs7Ext/KNyCfChhgteJffkkk9DvgnhEhJ29/b2ymmznJaIVw/dA1CiPT4Gaqiq4uVP288c4RlV0Cpn6DeVxYyDgjMMTpXJWg/+Rz7MS/cOE9CATIA2TTaEEq4mzZT3sAKM7I1TUsZXVy8CUQFBZgni7sKNrR2NQFZPCIzoiE0nqK7F90HNgIB6GGp9XgMSjkJ1QquCpw+IN4OIYXx1H3oSol7HWtM18KB5twR9kniiy0OQ8g8Txb0vqvCVtI/cynb3a8T07mxY1QBM3XPh+vp6Kq4Pb2LlaJwSvsQdS/mavJopMfW4MW4KeHRddqvVKpv54NW4ubm5E0JR/muymGuKVPlT+1jbr22kzxgz6qSJUjyD4iC0kSlxvZZ5F90T8KV4BmrfdRzgSfX+uBxRcManl6/8o94DFBeWrv6mcyIz3iJulwjCu3jIdNdAHQ/agtWvZatRqjoJ0BER5ch7XQXDsyp/kZWZfFaZWxsL5VnK0r5FRrucdoMZneFggP7VeeLjqHqEd9aS/DO6FxiAMai0M2RGuGJwLz19+jS2t7dL/Ec7UbPjcSN1u90SQ808DWoNo8yxpDwRQ91na2trUxvwaGeur69Pbf+I0Im4zYpVpOe7dHGErK4DV2s/IlcmLqS035WptG0sCVNGBDFGROk36sa9mTXQVBf1YKiCYhLDpNrG1dXVWF1djaOjo3sx5U9NIHW18HGFYwFoaEMtHI2Nu8CD52jzZDIpbnwsU6jT6cTe3l4sLS3F9vZ2ie0/ffq0LIFiNQCbhWB5AU77/X5EREmiGo9vN4rST+V7gGBElC14VVmsr68XD5sCF/qEVQ66ZbECccCJZlg7KIX4H0Wu/U3yKePCO/TYbvqYup+cnMTh4eHUyo3JZBKrq6tTVpHODa2H/q+Wcnb9oai2U2XE9EotBeTIzslkcicfCcKIUK+ZgzN9n286ROhA50IGmp1Y8ra2tlaO4MYoOzg4KImCvmPq27dvixUMsNZ7+A2vb7fbLZvFTSaT2NnZKcm97HnAfGDOojccFMB72t8Klpo8NmoYKNDRtvFJ2MU9kv7J/5TpW3Pz/zzJikr33nQo4m4CoP+fWVeaoMRSD8AAjWIygxhJ8hiPx1PudkVPOlC6FhWFqdmlSoABFQJ0HGtQNeZOLkTErXLRuJG2QxkoYxSflD5xdFD5VEuJTwCJWt7aN2qZwzDZvhAuEFwAKlPzDu0DntNwT8QtYLsPQ/7c5BZAxHQ+ho6H9oX+abhEwYB6o7D8KV+T8XiWpEvK0LX/KGJ1raripK4oYawt6k2dVKjBL7pdtC6/4hOPBbsFUj9NuOWdqpjm7X/vWwUnWaIZ/K/ufvgVnobvUQj+/Kw6ab2y+x8KEDg4cdnBNeQnXiZfcqn7R/Cs9qEbBrX2uic0G8/sea4BVqjP5eVlASyEXgEy8BdAFd5nPhAmAKQTQ+d//Q2vGgYnYBpZpvPXlaq23Y0oBQNZu7O+VX53b4Bfz/rUZZfKGa0TOuIn9wxAHgvRCuhEyuKDDAKITBFdxK3rBkuo2+3G5uZmRERsbm7eAQ6AB6w7lsioMOQPZMunZtartRgRU8dEwpiUQwjDB0k9G0w63qHU5BHgE6HLoON9QJBj4YNocZ+RyEPmrYYCmHzqJnYmcWvAx9YFuHqI9P4MDWeuw89NqqzV06FCQ91x6hHQmCj5Jhx4Rbsmk0nZzpftUrPsdtZaHxwcxIcPH4q3gE2Ler1erK2tlaQ+4rXkHpCLEDGdtIsyUP68uLiI/f39kkxFfU5PT0vctN1ul9UM7fZtRj/KFR5DmLI9MEBUAQrklpV+1giFwG6hukEMgFYVGsqEhM2FhYUp3meuZAKavsvqoP+jwB6KqKPOJUiBeKfzcSO1jY2NcqIlgDHidm97NSLUo6kGRsTtenm8p+6J5Xnq4cBEvb0Rty79ra2tsjHX1tZWkf+tVmtq0yKUN2CVFV3wnHt+Iqa9TGxNzBzSFQ8O4FGYeH21nz3kmcnrmgtfFTXkFr6Os+stlRkKwpVU7jo/+4Z0s+jeqwn0pU2xJh0gvqtbHbegCmDKwnuAINB3oyg1Zg0Y4JALT2CBcW5ubkqWqbqFYXLACF4LrCNVwgAHXZJFzFL3TNeYNBNN+1GZIRNWygyara2eDibI5eVlmVB4X8hxiIiCqvWd2bjVkKfek7kEFQiqcPH42ENZV0rqYWEsldTDkQEgrA68R5ubm1MJrGtrayVEhAXEOmgdL1YhsOSJpYH8zqYqugTs+vq6bDCkYCXi7oYlWIi6k6HXZTyeXv6pYRPlObxkmn2ugko9JZDyfJOl7b/B47oUlrgyc9AVDu9CEUTcKr+aIFS+dCXrvz8kiFW56HV2i5S9Lfr9fjx58mQq5KVhJDcsUIZ4jLhG+Epd8vQFvKTPqyUK4MbAYjx3dnZKiAwwoKAcK575wwZajL+GOXmHWr+AGOTgyclJARS6Lh8Zjj7ST/UEO5D1eL3KBQ2voVN8fuq4uq5UGcp1HRvtV+eR7B0s8/zJwYBPDv48aQFyQUs88ujoKK6ursqyPhVENIQ8A1yRKFwtU2OXCFZQqA5CxDTScmuXumIVNSlJFIFe12QP6gfjquXsLlttr7s1MyXLRMM60g1WAB+EXShfl57gRlZBqpPK3U01Ae5eAc2nUEExmUymNpTxpJiHIHcFuvWqllTmqtYEUXYNxBrXNdsIQT2MaDy+PXAIq1XfCxAl32RlZSVOTk7K/KCP2TqYzVQ0jEVddFVIu90ucVgEPNY3Y8w46Y5lCEcVmO12u/AfygThjKte+5pQhIME5X3lJwA5cwzlpAJQwazPq4iY2oAmAwM1wegCVu/NfvtcpO+mj7zNOi+xirGIAVIQMlMtXe7hGp4gPC3KZ7xH5YaCf64zZsh9+ECXc29sbNzxLE4mk5JToLkP5MlkYAZgq/wCb7LKh+W9Oj+os3pKAAnKX9xLf80T1lWgMi+pvtBy4AOXy26c6zOab1NLUHf65IOKvCLaCK8gA3Z5eRn7+/vlsArWXLO0D4ZheRAdSSKhLmv05MCFhYXqMgplVsiBi4KBzIKlzQpKFIWqu9YtZ/oAhaL1YKJpX6llCgPQVgBA5hlA+dAWFewABdquE4DJhAB1i17b7+3SMaEf6G+Wv+CteUgwkIEgve6/Z3VV8Mle7yhglikhbDRcxP8fPnwoG6KwlwanubFhzmAwKJnW9B1LryaTW6+VHscKCNEVMBG34LXX6025W9+/fx+np6dlYyBCEhFRlhTSFwAelL2CAcId8A7Uat2evLa4uFjO91ByfuIaYRBkhiZ1edvc+p9MJmVezLKGfJ43eRC+BDBAP2mbvc4AOhQtYw9PqjyBb1kKrWv+Ob2SkJHOeZV3jIVbqx6K0N/wEOjOpHrvzc3HszJ0K28F0roXAEYQK6QA0qPRqOib4+PjiIiy+RLnHyjoVO/SZHJ7eqwqYyjTffAm19T76N4sytVP90Bq2RmYVd7VceBe3QVyMBjMzb+flDOgWeNNqENjTLhfdKKiMFCquukPCp/B8Y0xVKhjgeh+zIruUH6gXhUsdFSGDCNiSjBGRHGVulJRr4IPsg6ePsNzTlpOzZLPGAzPCFYj4RZ1Q6kwxbUaEXcQqNaD/xEGWo6DAa5FxJTSekjSSeZCVMdfJ6RmzMM7+slEZ02/bmGNpatxfRQmIQH+ONvAtxz2nAXq6Nn8jBtKWhMD1X2qghWlQdsuLi7KufMRMeXlINs64vZkQ8JngEznSfoSXqGP1dun88ETkpnTuirDQSrPOmi9D8HTmWKlzNpvD0GzvBrkTpydnZWMeXWZ+0oSDcUQ6sI4wZBgbmdeCOa1WtqMH3JTgTF8kVm48Lt6gzO3egZE9D2aYKgb/ehcypQu/7vsy3K+tB0qM3Q/Dtqsm3EpqZeFOtHX6nnN6qn9oQSoAiSpoTcPfdLSQoSCx9G1E91joEkaKG6YkJgk8SkaSuxbmRlBpUISJtENX7KYD4OgqFjjQ7i02OKVUwgpP+I23KGeAZ04Kvx8ENWFo5NBkaj2tbvc6HNdNsaYjMcflzTSvsFgUOKH6ubTbG2UAcxD2xTEKGMrw7p7Wq086oAVe58ztX8O0vYof+o46pjglSJmD78xodUdzxbB7Xa7bMTSarWKqxbFHPFxzTPgAeGhMXzGAEWoLm8Uv+4NoSBPlw/e3NyUpYm+OczS0lJJAMR6uLq6ijdv3sTS0lJ89dVXERFlMy49bnY0GkWr1SoWHYm+bGaleRGa/AXv6HbCEXf3K2Ac6HtOt+NAGwUX+owKVP4cdNfIPUTKM9n1hyI3IBQcUb/BYFB+w8Oyubk5tXyPXBc8COpdgq+yZXtugCATNY8Aoj5q7ZO8yjXmkytjBS8YODo3qc94PC6GDnldvV5vKlzApkM6lxRUUG8HM/yvIVCuediK8WAOIlOR27ocFyDOXKDfkSXMT8ZO+VrrqkBBdYp6GQGGzMt56JO2I3bBPmvS1YCBTlg6RDufzSLU5Q0DO5pFAGjnavn6HsrT5WFaL+rb6XSKoHckqmAAQOTMXXOL66Tieb2u6HIet72Wq6ABd7QieA1lUI4yGsJVrU7K1kmky90UXOg9CkIiZi/xeghSwcWnxu70j/vhFc3WxUJWVz0JS5ACR53oPIdFjBABdLMsVg+b0nK0LYw/IA8PmwoVchwiorj3yWHgtDedWwhdNiFiXnKNMIJ63XiX8q0KcgXo/O79q0JUx8pBXW1M9Xvtvi/N+p+X1DJUryUgioN5NO6uYP/m5vb0Vh0blJluLqaWvco0NwIyrxvKXMdSjRwFd770Va+p18u9pV4f/o+49XIxtxx4Kl+oBT+ZTKZknANN7lH9455UynPQ69a+61b3DGibatcjoowVnh2VvfPQvcGAo3JvpE5qKgp6m0xuD/0Buelg6LaS7jqhLFAtf6pwfK9rZRT1DIDCQGIkMNLRIDYmlRLt1QGhftSfT9zkrVZrapc0mFxRpjK+x8Y0z0Az1x2YqfK+vr4uy9yw0hYWbvcR50+Bhk90nXBMRJ5jkul46yRBqd6XIX8ucuHhygfhp25O7lMBEXF7cI/ma/je7RsbG/H06dNimTP+Ebc8iKJ1Ycw7EaQIcdz4Onbu2SAM0W63pzbIYnnV4uJibG9vR6/Xi9FoFMfHx1Pbtb569SrevXsXz58/j6+++iparVbs7u4WsMCRuDqHbm5uYnNzc4rnWA1BexVA+tkjyiPq/dJ9DZy/mIM1RcTYKYDgHcrz/qnlOa88JDkojbg7R9mCdzgclhg5eVksuWR/FTaxUvmp44QMxGurXgOMC7VM3VPhOkLrrAB1MpmUJDfAZK/XKzkLyHHyAjLQpsCQP7W6x+NxOedjOBzG2dnZVFIswPjk5KS41cldYIM8QmaEuyOi6DW2xKb9o9GoWOlcw8jVHUd1nwT1Tmi4Qb0Z6jlxQAB1Op3Y2toqeRfzesgiPnHToXkLhxhQF8BuIWClqBWjHb+wsFBct5qYhkDFWnKLWd8BI/d6vbi8vCyJdWq18T5Fiu5hyCximIsEMB1on8wekwYR19C09iX1VVDi1o8yFUqENurk0Tppe7QcHTMdF43r0RYXrhnDfm7SvmmyEnXiqVXrQpi+Ve8OSg5FGBEFGKCgNf6q70EgIZgROoy9Z03zDAJNwYBauyqcAXC6TwFCmXFHmbdaH0MBFxcXRYEw51gCqatl1LMREeV/5rOGlpwfsFAzD5cqbfpdxyPjU/6fBUB9DnzJ5LLDDS9Id9GD3/gkm/7s7KwYVL7FMP8vLy+XMVcPAnyuho3yp8pznzNq3MEPvtxbjR5/F25+L9ctZN7Bd7xa8DlzU6/xTrypgHxkM3VXUKR11GPDmYvMS+Q0pLsnAhCU/xXwUo62U5NBaaO2u9VqFY/Q1dVVOeZ5Hrp3zoCSInN1n3C/Ng4CASniV4TLbwogeA+Mo+WqUlZ3uNZN6wMjbm5ulg06GDQGlfIzVOUT0JWiKmHqAANwf0RMuXm1//zPXUgILxW+1NcFho6Fxu6IY9PP3l98MkGJLVNfTSTLBLJaqUz6hyQfH3XPeQgD4RYx7XnRyaYJqijtk5OTKYF6cnISg8EgVlZWYnd3t2wxzIYquo5a4+s+DhExFXt3kEp7mFdqJZJVjcWIC1EFMhts4WIeDAYxGo1ib2+vnBn//PnzwjOqMPCcES6YTD6udsCDwOFFni1OKAX+w0qC39VYUOu8BiprgMGpNu5abha6uK/x81OTtj0DfbTHPYztdruEOY+OjopS9+18GVOsVgAg3h8FctyrSkvDvnzCn9QbWQBAZjVBREzlnrVarZIAqfk26uVyT516jiOmtxFX72XE7am58CPtn0wmd+SUHlOP8Qmf004FSiofANPkM1BnylGDA3lDLoXqA5VFtE3bo/qBurJxWb/fj5WVlc8DBqiMJuvVlCikk1CVnT7vLn6dDGRfg+K8bBemHrdHCGpMSvfX5/1eR1XGKlRUENNHMKwyiFp01MMFnPaBDqAKr1qyi05GRagqIBRN8pxuVqQIGKKv2SoUa8PBH21T4XBxcVEQKu14CHJQqJaVglPtR8CnhgtUkeqqAJQv5UdEnJ6exsnJSaysrBShsLOzUxK4rq6uijdAgbAKCUf8EB4I9yyoNTwYDOLg4CBarVY5GwLFgJBjkxoAH0vKrq6u4ujoKI6PjwtgViGu/IIgV2G3sLBQwAi8c3V1VZZ5uWeEZZSAA+dntd7dkneZ5NazexG0f7NneWdT+OFzkns4VLa6l0kVsSvoiFvDS/9QXgA83fANPnfXtNYl++T92gZCuGtrayVkQWKxgl7CsxgtukU33ibdTyYi7sg1nTecMQOoQbHzvNad8IGCJpS3etTwAiAHPamaOpPUq6FeQjQ6TpogSZnIV/Xq4qmg7gqG9P1ra2vx9OnTkjT6k4cJNK6hHemufh0cvZeKU45btdynn/o8DKBuc41dOflE0PsWFham4vUOYFxwuPXvFr0zpSZDIshVgWpfaH2dFDz4fdrnGVLPvAvcqxYPzIViQ9C3Wq07R3pqUqbep4Kdd0wm0/sMNFl2PzfVhLnypXuQ+E3DOT6xtA+ycsktOD4+LrHL5eXlEmvHOlpcXCw7EWriD0IJgabuVVycvtIBWlhYKGu119fXC3gmIZH1+LQNob+1tVVWHLBu+/3792VrV0CQhgToIwVSuscFYQUAInlD9C/tVgHo2etKtfk+65qGV2qGjc43542HIPe2otAjpvO4Iu7mO2TyVPnc5S75KShN+CvirkXqdXJ5rsl5Cgb6/X4xwNhPBgBJeerpUBmbGUDaLnhPvXzwqm4QpoaYAiiOnlfwA89wD94TbTPzXEN99JvWT+UlngvVqxpadsDqusJlFyBgcXGx7DlCvRWYNdHcYEBdIVgEvumHW4qZosUtqgkjrrS10Xyq5QbD+CYrmbvKLQXN2tYkPnc9qWfB8wl8Urj3QBG2ZvLqs/SjW0BatioAdQN7PRW88C7tCxUSjkJJ1GLMlGnZVEbL0xivJh65h6TVak3t4Z8Bm89FLkCU1xi7iHwZKF4k+FknJUovA54Rt/1LiOXo6KhYG4AkNtNiAsMXLANbXl4uChqrQpcp6WFG+n618Mbjj/kGBwcHMRqN4uTkJE5PT2N5eTmOj49jdXU1vv7661Kvy8vLePXqVRwdHcVgMChA5cmTJ8U74Acb4bHThC14n7ne6XTKOng9OIz5xZps3WxIqTZOtd997nv+jj9PXeCLh/QIQDpftR18j7gF3yg59Qw46RxQr6F7XhwIuVJWuZP1pcsnwMDm5mZZ7ggfATLX19fvWLzwQObBy0CienYBN4BlnVfIVlzx4/G4LCVH3l5dXcVgMIiI6RwDBcAeutb8tZqhpGDALXo8EpTjHhkFyN5+AD0yV43eeegn2w2mNnHULRcxnWjWhLwzAe4d4CAkQ0vOzLpfgSptFcTqHsL9yuD6ZNLyVUnqYOvE0zp73dQ97GEJByJqJSiCVYGhdczAQOYG1WcUWev2uQpeNC6nihL3mSe8PDQ5n6pw0+9+T+YRiLj1TikwzvoTwYQi9wQvPukvlLIuJ7y+vp5KAEWYaV2UX9S6cgtMLRDqr7zLHhHkEozH4xJCAOQgXNUTgHJRbwXv11CGhwLpg9oYwKvOR9n3GljIvteeU49f9p7PRVkdfP7qb35vVm9vm4+FknpL5wUD2dhlhpq683WFmK6zV9mCTPL3qAx0eakK0pfbURYgEZmpspj5pveoEcQ9EbeJgMwBrat6SfSPua2eZL5nnirvQ67TN/T12dlZfPjwIfb29n56z0BNIfObW6w0nE7nE0tUk+xUiTjyycIQXh+Pr2tCDAIQ6xRXztbWVomHbm1tlWzrdvvjyVpYtf1+f4o5dHcnFCRtVaChsTZVoHr2NBMC5mIdt8YtPUwBw6PUlZn8QBf1eDgY4E/j3u5RQfDjVkZJKGOzeYnWkXrqgU+qeD43ZULLedkVAUQf8T+8BIpnNQqu72w5HJnQbJ4DqULWcySw+Fn6urW1VZaC6XkP3MeyQc34RmjBA+12O77++uuSw3F19fEUw+Pj45hMJnFychILCwvx5MmT6Pf7ZeyPj4/j5cuXJZ8Aj8/y8nI8efIkfvnLXxari1wDcgS0Te12O3Z2dqLdbsdwOJzK/dFYsAJQxkLHz8NeTvBYJqcyr5p7tRzo6v0PQa7U1QWfhRDdIud65jVRUKnzXsvL5Lr+HnH3MDqfP1oO78AbxsmfLNHjvBrktO+EmIE2TcbWUIA+x/iqp5Nl171eb+oeZLDKWfQIMg0P1mQyKTkC8DurcnS/BAUQ7mXn/AfdrRXljo5hebFuz625E8gg5MzZ2Vns7e3Fd999Fzc3N/Hf/tt/m8lrP4lnoIYqlWDebLlchsibhHVEPcYO0kQIkQgDc/GduC3rbrkfBgRIcPqfomlXsChu3fdAlR/P64RwoaZKVD0CWT9yn7q0FEWrVa91QCjUELp6LTJhWhtT/YuIqQno3pmHopr3qWZ5KXDwZZMRMSV0Im5XwPAbRIYw9+hSVR1jDUloKID4quYG4MmiLK4RLkBhwGfwAXMDATqZTErGP0IPxUhWOXMC4YOHI+L2QDGAgFpw3o+tVqsIVPoTD4Lmq/BcRtn1eSzfbLxrlr/Pky+JavNH61rzGtTArl9TEJZZ/LPmcFO/qbyAv5CxrLAhNOYeWV99BQGklfcURPgOgrqDLH/q2aKOzFvmroJsNdC0fzU5XUPjKrNdvqsBpXKZ/3XvGT5x/au3BaODRN2Tk5P48OFDvH///qf3DOiAqgBzJKkKRUnRoSK2LGdAy1G3JuXClHSwLltB8cNoWEyAAT0bnhO0OO6TzgWxwpzaHphLlZ9PGm+3MocKefUAaBsh7nV3L4yI0NbEEx0fhLgzP2VOJpMCYFRJoQA1Loxnhfge/QXwUPcWCkdBwkMCgYh63oALT52YKD/ch2ySpV4YkvTUu0V5EdNHvZJdr94YJip9BI+gXCeTj5uYqAfGVxdQVxXWxN3ZKph4Zrv9cTlht9uNiIjNzc0iSKgvljurIP7hH/6hWEJYPWzZrV4pcgZoL+3ymCjLnfCkkKxI3ygwaRpPHzMdXx2DmnGhYJj5lQGBhwQFzFOtu8sd5R2oBnaarjFna94Ffz57l9ZVjRu8pf1+PzY3N2Nrayt+8YtfxOrqajx79izW1taKpxbZ6zpD26pyRXMl3DOBvMOje3p6GqPRqMgpgAd8DAEc+N8VOhs5+fuRnYAJ3XROvbjq4QWQ65bklIEHTU+rJRlYwTmhAbbvZk7t7e3NZaxHfKJngMGvJSbM83I6VTOR/S9DUChsRZiq4DudztQ+3BkYIHGL/eN3d3cj4nbZoi4nUcsv4jarFqspYjp73K0y9wZwL23WyUff1oSYCgFFqI44NfblXgi1wGgPZanHQ5Weut1QAOp9oE5aP/d4fClUA52ufFQQ0UYmOMpcQQ+CxIXKeDyeWjanx7DWELtaBYAuxsfjkKrMFDwiSC4uLuLo6GgKRE8mkwLeSJrinQgcBNLi4mJsbm5Gu90uu8C9f/++JGapF4EyNMGKeiII4Z2I2wQv2pMlh7lF22R16pjWFFlWrl7PxuKhSYGLtsstT2+H3uvtrfVldp+T929GDjDwtuIB6PV6JSzw5MmTssxVwYBaxC5PFBQjk3WVivKzKkxWSKnXS932apBRpudZqWxUsMuc8dBTxPRKATVwFQQMBoMy9zgZdTgcFoOClUC6FJilu4B4wn8k6Z6cnMzNw/cCA8qU2pgmZlDS+90lkgmBzOrVWKm6/AEBCwsLxbLvdrvlXG/c/YomSRSEAXgH1rzuZeDoUBkNIZy5oBThqTsWEKBMo7F6ylKBSh09JKFueQVQbpUrWlfwQMwQaxFyrwIgqN1uT+1fT/8xVkwyEjTdav7c5AolE2Laby5EdcMmJjseouFwWBJNmei65aj2H0v8FAzoyWoRMQXyGBtWGqyvr0+5URWIaL4A/E4dEC6Hh4dlad/79++Lt4G8BMBtq9Uqx7+y9JF9EtSiWVpaKnyKBQRRbsS00gJs6yEzunOjbuGqY5BZ+z62XNfQlz/jHgDGyPOOXDY9FGUrKmZ5Lpzf9f/MM5ABA1Wo2bv0mocZNRmOxD3ysL766qt48eJF7OzsxLffflu27lYjQ8dL80d4FwCZjbTgGZSjrlJTwD2ZTErOQKfTKUsbyYVBphK20BwgzQ/DoEIP8A4AhycPRkzvOaB1U6DCoWf6x2mi6iHQ7bo1fKefuhvlTw4G3PXCNQcEPlmz5xXFusJRpatJfQg69tre2NiYUvSAAjJHFX1iuSjS4116fjsJV+rq0VPmFEyoogcRwpy0VZfunZ2d3YnP01ZNBAEpqstUPQyqJDRkwDv1O0vFABwax9c4tCfpeLKOW1uzXP/8pidzPTRlSl5/0/+97QBCLG5f0gfgUdcjSYbqNfDkUxVoCH0EknqD6EMSrXQrU+7R9/OHgGMHyfPz8zg+Po63b98WzwbC+D/+x/84ZbVfXFzE8fFx4Y9utxtff/11rK6ulmRCBwPqGVpdXY2tra0pYc0xzbwXom6ca0BOQTZGNc9B7Xd93r1AmmRJO7QNX4JXIJOtLnMzBQ9lnkftuxoIqBlp2fv8nZoPsLm5GcvLy/HixYvo9Xrx7bffxtdff13CBJrh7+Uo7zhYViXKyYSnp6fFskYhw0uEqHyPDu1j5ODW1lZsbW2V3wjxqdGH57nVuvVWKxhQo5nfMCioIxY9IPj4+Lgka/OnYAB5oW1zUhD9s4IBdZHUFEGNMb1COsCakKFxFY1DwzQcy8p2iyA5rFVCARoLohwsEwaQwQW16bnpqnzVJeUuMs2IVtdoxC0Y8GQPhLT3m+554LHlmodALXN+11CGK3r1LKjioI8VOOi4qVdB78kULH3GXvYPvR2xUibgMoEYMb0FaERMjbmOE9d07wpdLki/Yg1rgqAu2Yy43X8CAvgRc1cQ5yCNelBn3PJY9zs7O7G0tFQ2HFJAfnBwEBcXF8WCa7dvT108PDws+8ezIRH5CxyXCl/rkcysnkAg+ZzX/sYtqsLM3dlNSsj7IrOeuaZ8m4GIzNvwUKRzy9vl9czkbY23/X8F/TXKytJ64a0i/LqyshI7OzuxsrISz58/j263Gzs7O9Hv94tyBtiozKVcZAdzSwGQWsIcj+yru7Qsrb/PFZXZuPvZ4VMNP43dq5KFrzUUwX3oBsAEhyG5m5/fMEAxMmkbcyzzOqjcpZ0ZH8+ie4EBVdi8WJWRrhLIJqsONp0YcXt4A5tR9Pv96PV6pVHqbtJTuDR5SxUVCYS6wQRbUFJPBkCXf6iAxp2JJajbFav1rugTJajLP2puPgUElEmdQI/q8vJ1utnE97wGdTkDmJiAHmZQDwH1U3Cj1hTl+CoGfW/Ex8Og1tbWysYd92XOn5IUiGV1UZ7UtqgFwfhTFh4fwgTwKsT/Kvg8RKNzJ+LuLmTUzUEaQgoXpVoDFxcXxU2/srISvV4vbm5uotvtFguczYf29vbi6uoq/vjHP8bi4mL8wz/8Qzx9+rS4di8uLuJPf/rT1CqCX/3qV/HixYs4PT2Nw8PDKeFETs5gMIizs7M7niStP3RzcxPD4bBYQbTNgQ6kY4jlq2CZsFcTyMtCaBqTV0XwkGDAE6gjpj2zECCH/tB6zwIGWR80kQIH9UwRxtre3o5nz55Ft9uNFy9exNraWjx58qSs3tIDvNRqRplywiBJcdfX13FycjK1G6rKRNrhoTKVv85/CjBQ7Mx58hsoZzKZTHnyGBfNk9F2DIfDGA6HcXl5WXh6MBjE9fV1HB0dldg+ibaeOOjech0rzWtQ41x/UwP0PvRJYQKdjI7em/IIMtKJifDqdrvlACFcsezUpsmBGjeNyBNbKEOTwNS9r/c5GKCNGt9noFDWisKUAd297/WC0fz96h3wmJWGUryPlSlVkTFR1TUGw+vEof9VELqnQ5WkJt1QJu1RhgVNfwkuV6d5ELT/TrsYC7L+r66uilVBvyPs4D2NyXsCqAJjDenoWDsPqaXIPQhI+MhBsoJBteRpRwZUcE0yX7RceF6FlSbdupVP3ylfafx0lnWajY9SBpKzOmhd9L7s+S+N3Ovx1z5Xs/ize7Sv4FFdKdDr9WJrayt2dnai2+3G9vZ2kekAVACvxs5RtB7vx6LGla6/1/SM8jHkYAB5q9c0TJp5nVRB65zLrH+SAbH2AQDHx8elLbpckDmruSre395G5VdkOe2ZB9Q5zQ0GFEln7iVVBiAdRVYgSCqpFj1xVpY8bW1tRa/XKx1P8hSxSxKfWKKEwtTMT3XZRty6dolLwlhqAaDcUeSawJgl6WkIIeI269xDDNpXlNfv90tCmS4b4ZQ5XETEinC5as6DW+06VjAIY4bw5zedHNk9Wm/q7s8h+HV/BhSdJtK9fPmyKKmHIlcCmRLRyaXWJtYqFv5kMinLk5aWlkqoYHFxMYbD4ZRFjncJjxcxf/pQ10N7SIr5onFT/VPrxkNc/Ib1srCwUPJnFMj4PgP6bgWlk8mknOnebn9cWYAVqHMfpQ4v67yifwFFtJfz37ECudd5MFNaLiwVyNaAAmVrGA7gpWDvU92tPyXBW1mmu5Ja6/r7PIo9MzBU+fm1yeRjjgihgG+//TZ6vV68ePGiAIEXL16U3C4NK8NTGtdng56Tk5OiUJFxHh7QHAP4CMJAUTCgIT5V5ugnBebMVzx9zCvtP+qkntvBYBCXl5exv79fNvIiGfD4+LhY/8wF5In/QQ5k1AjTe5QfVDfxXVd6zUP3zhmYdQ8VmfdZDT3oZj8M9M3NTRkcFI9atRG3E0aFl5Ja+7hvFCjAKBq/RYD6ZirqPvLEuCYrhbYi7GlrRExt4qJ/AAVNNuFTEaoLTx0HZfgaw7kXQd3ROp46jjohCaH41rRMTt9573OTA4FZ9ym56xSeU6AQcZscqF4cfl9bW4ubm5uy4oDxyPJmNGcAj5TWRflTvVAamlLFO5lMypwh1MbYUyctkzimgn5V4p57wwoe5oLyJ4JUlQh9ggeF3Ar1pLmHr8Y7NatJf1djJQMTutR2Fvh4CFLF7eMKuRXrwCiTS15uE9DK+pkk0F6vF9vb29Hv9+Pp06exs7MT29vbJdREbomCVfUKYfUTEsAQwpvIPFKZlIV5uO5AQHO9NAwUcRsSRKbrBnTwJbyszwAIyL0ZDAZxfn4e+/v7cXp6GkdHR3F0dFTCBJ4zBvhUL60DAwd+Kr8BrS7v9dlPAbH3Xlrog5EhR0WxOpiUoUyBYFFrRhOOUOSDwSDa7Xacnp5OJb5hIaHMx+PxVEdm8XjdtMEZrtVqFXToCr/T6ZTtiWmLM1w2gVTI6Xab6rKinbiUzs/P4+DgIPb396eWmDgYYDwUBXt7lHG4lgk6VXiZgFSm1DwD3UKXBE6s5sXFxbLO/aHIJ0s2SVRwOgBQa5NjmdXlT18uLCyU9ffn5+cxGo1Kxv3S0lIMh8MSL8W1qvtfKBBTIe2xUY2XImThbxU2unJkYeF2JY4n5vJ+5VWeY0nhwsJCCdWR6YywU++Y5jyQ98O8xlU6GAzKaoS1tbWyHwLuUgcBNYWejaPGWH2sGVP14LkirfHFQxH9mrVd57saBCobtD9cQThIzlzgPAO/slnb5uZmfPvtt2WVSa/Xi6dPn8b6+npZ0q0eKzyyJycnxQNE+AmZBhCAX1CUOj/gXZ2DkBs+ajDe3HxcEqyrtG5uble2sLwWno24TSiHZ9Q4Ozk5Kcr/5cuXMRqN4uDgoMwLnkU3qfzlExCgOUk6Htk193Yo+CEHzQ2Dn9wzkFVOmU/dqgwYyCpze0CqPDOL17O2a+5uZ2BVrhp70s0b6DRACcqMzS8UDOApYE8DZTaYTy09bRu/tdvtco68okGdgCSW4G7a29srS8K0HSq0PXGP8vhNx0vBgIMXBxcuCHke618z1snrQGngMVhaWiobX3wJ3oFZvze5hjVBEyGhfA+vR0RZx7y4uBhXV1flU7ddxXLCKtEkQxUkmr8CKNEkKuXrzD0OOGBVAcJvZWWlAARNuuJdCEpWIbCH+uvXr4vFg1cLcIirtdvtluWDAHWWFiLwWbWgS2l9/qgQnMfayTwLWp56VDLgoZ9N4PFzkS5/83F1g0utQp/fCsabPCkej6dMvLW7u7vx7Nmz2N7ejr//+7+PtbW1ePHiRVlKyiZvLGllbAl5Hh0dlYz6wWBQXOiezE2YVueJei81V0m9a26geY6Ahh5oo86Ffr9f5hG8yX3UEePsw4cPcXx8HH/5y1/i7OystMstfP1UOazAmb5W2axGnMp092Bpv7Raralctp8FDMwjTBWVqhBzyoQVCDHio7DV5CN9P8dFqvLyMvX9MJi72RnUiOkT29rtdpycnBTm9/AEApFYbOYN0P/VatQYPoyJlcUSLXIFiDsdHx+XDVmUkVWwuTvU3chaD59AWd3pbwdtjrp1Yi4sLMRwOCwKREMh+/v7d2J/n5N0cqkF5b/XyD0EtXuY1BHTSXKM7WQyKRv4ADzpK74rWMustIjpJavqPeOdCuq0z7Hu9LROEnJZyaMbG+3s7MR4PI7hcBitVitGo1GJRXa73Tg7O4vhcBiTycfwQqvVKp4Td7lSNwQX9WDOKcDRtmbjWPPs+Bhl81Hvz+Zu0zMPQW7ZR0xb8pBfg9ddBmdtcgNM5zTKeHd3N7rdbtk0iG2F8aKqBQ4AuLi4iL29vfJ5dnZWjsXODDRAAe3R1WPj8ccjhgGpmXdTla2De+07NeTY7RBPbeaB0x0B2fd/b28vfvzxx7JHhnoytD5ZjhbfqZ96s32Ms/ZBamRjnLjhfR8e/qQwgQtPhE7mpvJGqnCDgRACICsS5hgs3o2FoasBmhheY6q6wYMK00wgILA4tXBlZSW2trZKjAyk7JPIO562wsAIPtxOJDSenJwUQHB+fh6Hh4ext7cX79+/j729vdLerHwVkEwSFyAaktEESL2nJkRdaLrQzf582SKT6aGEK++lDzKQqROypvRrgoa+d+WrWdLtdjsGg8GU5QKo1DwLjw3qn64U8KWmatUp4NH5Rvv0JElAwebmZqyursY//MM/xPPnz6Pf78ezZ8/i+Pg4vvvuu6lk283NzdjY2JhKpPINtwDS6lHQRFMANtfIi8nCjjXrXccPQqZkc7Jm6bth4b89JCjwuazyFAUEz6i3g4RINxa0TC3HwQCAdXd3N1ZXV+Obb76JjY2N+OUvfxm/+MUvpsZTN25jGd2HDx/i9PQ0fvzxxxiNRsWbhIGjoWI8Rp54vrS0VFYjjMfjO3zkBM/zh27I9BV8iEcAMKD3E+bQZG4O/3n79m189913U/sbUG+V++oh4N1u2euYqAdBSb0Hfo/OkSzU8JN7BjJXf6Y89LsynYYTakQogIEkiYMyFQzoPtSZB8IFpK/lRBlrfRUMqJWDy4q4Z6s1fTaBKljtI43feo6DrgUFdZJRDePpyoEm8snuY9Kk6PXTKWPK2jNarroYIyKdkA9BXnf/rFmdes885WflZGOoAkCXOelcUT5WUOBCS8sE2NTGnOuqMM7OzoqFf3FxUVz48BV8OplMYm1trTzP8l72UFcLCADB9q++qVVN8M3b59nvPhdq99QAvN4zbz0+BzXNO2RM9ruXkYUyfTwIZa2trcXOzk5ZNbKxsRG9Xq+shuF5eFFDAcTTDw4OyrXhcFjyRag3gFI9Q8rTbAFOqHFlZaXsqqqhg4i7+WnUT/sJ3sQbh3dKvbbqTcZAQz4DaMiBUFmnvOy87b9n4+SK3HUupONdo1m6NqO5wYB3GB2qStUrA2Xo0+P74/G4eANGo9GUYFQBqHF+VzKZixHBoO+OiDtKNlN6eBI6nU4MBoOyo16v1ytokCxtBKMOoMaecJl6/U9PT8sEefv2bZyensabN2/KMhUyst3DooOtbXTApq7xjDlcadSYN+uj2v28S2PeDwkEfOJof2p9sbyaXHZ6L3zrE1yXLSlRnubSEHN3bwCCUp/Tctza9/Y1KTO1AK+ubg9fWllZiYODg7J7J0QY4u3bt3F+fl62MF5ZWYkXL17E8fFxvH79Ok5PT6esRKzV8Xg8lTSrZ4I0GRneb97WTJm7ovfyuK4hlloffwlEPZyfnAdcwatHwEllYavVmkr8XVlZifX19djZ2Ylerxe/+MUvyqZB5IH4km6MMpTkhw8fikfg3bt3MRqN4vDwsBg4vlKAerjXlA2ySFplK+zLy8vY3Nyc8jC5y9z5QIECeU2Uocm7GIGDwSBOTk7i/fv3MRqNYm9vL05PT+PPf/5z/PnPfy55Xch2nb9aFwXeTvCmj22Th1L1l86HVuvW+xwRUwbDvPRXLy3MhJFS5iVouhcl6asPABO6A5W70FQ4qCDQ6y50uVYTKirwsaAibvdTBxCohyJToprZintMcxcuLi7uIFFdn+11b+pDV9Baj6Zna4pdf5vnuawfZ9X7c1KG2P+aumn7nP+a7s/mhCor+Mk9LUpN80qFrN7r1ldE3OFFzUimLOVZPBkrKyvF8q95yLTN/jvzKwPmtTFp8iRk/e5gdxY/qsz40rwCtTrPK199HAh5djqdsmPo+vp6bG5ullUDa2tr0ev1iiXOeOkhboR5B4NBWVo3Go3K5jvIS5K6HcxoffQ35sFoNIqIKMfz6rkYGKYZAMhAB55a9QpoeAlvBfJ4NBpNHQvMijQ3BFTnuKLO+OhT9KlSk0GQAeVZNDcYyDpas38zK8oHxck7Q932hAz47lYmg5opm8xTkSm6zNLQT303jLy3t1csnKurq7K+FteZMhY7JcJ0urMVjHV4eBj7+/txcnISb9++LROK5Brv+6b+45re59nF3t4m8JCBKC23ZtFlff0pzPlTUebKU8XlliTkbYKXFclj4Z+fn5fEV8rxjWJc+EXcPfcA0vXvPON1UgCq/evgwd8LLwJKaVO73Y6jo6Mp4YzngF3kqO/5+Xn0+/148uRJbG5ultUC7ONB8iieM00sZT6hVNi6GJ53ZayCWtvhfUFbZ1n5TXPJy3xoMJDtZeLJYtRTDRfvF8YZz4yeWPn8+fMSEiAUwJkCOzs7U0lxKEPdN59cL7LryRlgmTQhJl1ml8kd9zprXsHx8XEp6/j4OJ4+fVq8Gdvb2yWhW08K1fkBsRNir9eL9fX1kjsQEUXGHx0dxbt372IwGBSZ/Pvf/z6Oj4/jw4cPUyBE5WuT/IOa5GgmU50yjyN9BshxfplX7n7ycXIZovdKcJ9fyxrpwswVsqO8TFD7Z0aZEKndq0oChAoqJAsbVy+C0wWW7lfgiS268xqJhMPhcMor4H2n7cjalrVVn621e5YAzARqE4qtWYZfAqng4bvzaQZu+C3jTd0LAOXq1jeUxXizLGAX7LXEIfjJ25G1WwVHxDQAH4/H5aS0brcbx8fH5b24Y1EobL9MtvfTp0/j7OwsFhYWpvYC0fNBNMFVvWOs3VZPWMabWTs0pNMEerNxbCp7llD+XFQzrjTEotezfBH/DQXIOQGEBJ4+fVqWB3Kk9fr6egG7eGU5HRDgx/r69+/fF6XNUmgFfapAvY0ZCNfcGVZ+tdsfV7MsLi6W5a3dbjdardvNgxRQureWBETyBVwms3Lr5OSk/A0Gg9jb24vDw8Oy8iHTITXeadJHUC1/xr16NZmr764ZubPok8IEqpBJfvKX0gGaYe0D5JanI6xMIGh8rCb4agOi5ej/Xgf9dNLDKFDwm5ubcXNzU1ApridWH5A8FRFlQg2Hwzg6OoqDg4PY29sryTWgblcEWZ2bmEyVSQ3w1MrWMpr61MktFr//SwIEEXFn4mTt1T89XphlsAgTrivoo80Oblut1h2LxcFJljhay3Pwe9SVy3xrtz9ut7qyshLb29uxubk5dbIgAAUhOR5/XFKofcOGM0tLSyWf5uDgIMbjcVEuCGZVOnjSqD/AmpCDn2rp7XJPB78pj9ee9XJ1DGrXm4DI56ZMxtXyfDKe44/kzfX19djY2Cj7AqysrMTTp0+LtYwM02OlJ5NJ2XJ3MBiUrXY5UO3du3dxcXERBwcHU5u6ZUpSeTxTbJ4LpaB1MpmUco+Pj+P9+/fR7XbL8kDarvtl0C9sY7+1tVVOvsWKRibv7+/HYDCIV69exevXr+Pw8DC+//77ktdFroPG/+Fzn5veXrfo9f9ZibQ+tj72KnPoK93u+ScHA45CaZBOzIhmBV2zlGr3a3neUU2NzKyneRKt9JoyrU4qYl5kt3Y6nTg7O4vxeByrq6sRcXtKHUctq9uLQyqGw2EcHByUXQZhOBJktI8zINNEWl9PnMzKmvVbhnj1Ny2DyeL1fmihqqTtURe0x60VaTOGJDOxuQiCQLcW1WWnWMDax2qlkSTongLfgIvfZlkQep1nr6+vy6ZQ/X4/nj9/Hs+ePSthKhR7RJRVMrpyhnayFwEJkCSG6XJBVhqoZwwLjLbq/h9ZboyS8q4vu6QPvd38rmOZzQG9Tz99Hjwk72Z5SLUEV1UGvssi47O9vR0vXryIbrcbT548KdY/O1MCJJFprNpiVYCeyAcYePPmTfFuEi5zGeCg2z0EmVcHRcd1lBxhqPfv3xeAenV1VRIhNXGbuqytrcXa2lpsbW3F5uZmeb/uI7C/v1+SH1+/fh0fPnyIP/3pTyVZkHmELFfdkuUI+HxVw9g9gZB6ArIymwwAyvMNnH5yMKAFuusJyjJzPckvK0MpQ+ZNVm32WxOyz4DMp5Aymr6r1WoV16iuh9VEGBJryFglOYXBc+WbCcpZKNLvUUbS8puAgH+vCexafWpens9NmdtMvSaQ1lXvUSCLkFUh4EtHVZGrMtdxqbn7fJ64ANH7vN4OErifjWHYH2B7ezu2trZicXGxCA7qjusYVyrlqIDnXbpNOGDo+vq63KuhAfqFOaBnbrCsMbOWUALaNw54tA/8/2xs+Z4ZAl8aKe+oV7Q2XzPAw/O6ERj5HCg2PUBHvTZsg85qgNPT0+K9JHSAgYOSzyxjpXkMnJrSU2DADp8XFxdTIJW+Ii9ieXm5bKZF3gplYJjh5drf34+Dg4OS/4CFzfupm+Y3ZLLU56Te7/coaQhonv7y+9SouK8Rdi8woMo/yxfIJq4/n313YZm5VZs8DvNm0vpnJhD8Ph0EvZcwgG7lioXU6/XK0ismGxb/hw8fYjgcxo8//hivXr2Kw8PDeP/+/dTxnDUvjLbZrUXtT4S2jpErOoQ1luu8YMPr45ZUxoDep5+bcAfqcrIsqVD7FV7W9iAs1bJh3HE7YmlE3FpljIevKKFM3ocgzsCDks8llLVbKZyWyLrxX/ziF7G7uxvPnz+PJ0+elOWyenohLuRsHw2vz+npaezt7ZU6tNvtwvf80RaUBsu2iDuzFM35niQ3P6tExyyb93hq/P7Mu1IDak4PCWS1fsxXeCXL+1AvioZQyN9gtQBbYpP/gdIDrJEoyHboeC3ximGlu5x3l3UGwjJZo/kBWqaPD7wAEMXburDw8TwQQA5A4Pnz57GyshLPnj2bWkJIjgobI52cnMSf/vSnePfuXbx8+TJevnwZl5eXZV5oKAD5qUe+a1szj4D2R5NRpX2in9nSUl1txPvQRbyzZnRk9Mk7EGY0a9J4/oCXe1/KLLt5Jm4NCNTupWwdWLZ01UOT1EJkR7mI2xgOMThcbSwjdJeOMpgzjoOTn6IfP4XmYWjug74ky8s9Vg6W+D8i5/sm1O084yCJSYtQ4X7NI8jGtYlf+S3zxAFalpeXY21trXgK+K5nxGPJK6/rZkHwHfezvBZhybkbukkRFhvJsViXbORCLNbbeJ/5qf3011r988qGz0kObrimv7tirlmeavm3Wq0y/gACZBTJpIwZIR1ymtRLVrNcs++Z3HaaZwx0nmlYAuCjoFT3yImIsqJFNxIiYZDtw7P2eTua2loj7lN9OA9l3jMlzyXhXfPy8txgILNqdCmVWkE1S9MZQA/hyEIMNSHoilIRW21AvKx5OwgrEGVPPsDOzk7s7OzE5uZmbG5uxsrKStmrm+UrEVFWILCh0HfffReHh4fx8uXLsiEHblLvJ0X/jgq9bW7VOpN5ch91876hvbV3ZvfWrlGP+4Cvn4M8FwBUXVMkkLsDFTh4G3XttFsDvsRQx5UMfcJE8FnTfMDDoRYy3gj1ChE3Za5tbm7Gzs5OSabq9/tlI62Tk5NySBYrBQC2JA32+/1YXFyMd+/elfMmDg4OSvIsYJgE23a7XfZzv7y8jMPDwzv7GNzc3JTkLJQSAIJ2zgPCMt6aJTwz0K1/7ol4SNIlq+r5gdzT4fMNcMZGOsvLyyVBlK2kifmfnZ3FYDAo1rdu365EUrTPfzVUvO9qwDpiOgwQMX2+R2aU4Ynt9/vlcLnl5eXY2NgoyyWfPHlS5h983Wq14uTkpCQJ/n//3/8XR0dH8a//+q+xt7dXwIC2MQP4GV/6PHee890RFRAo2NZ+YfydVO8ByDVRvZaEXqO/6qAiGpu545rKUKZW4ZkJ7XnrVlM4Xm9ltuy3rGyEKe5gXFCgTj2hj7AB7YGhcb2RjXt6enoHgdZ2qaq1t3avAx79dOWtjP1TK+v7Aq/PRQ6alH8i4k5/eB9lEywLm6nrzt/twgOAoqetNVkNuC69XACAu8BR7rhRW63bpDIEvR69qiCOZzjoqN1ul3gr2ebkHXCYEe8myVDBgG+njNtZ+aQJDP011CRX/HptrB+C4FVNeFXwrd9rYEk9lKpc6H+OFyYscH19XRIJ4YcsJ4D3Zflf+v99LecmY1BzdZC7upwV2axJhaqnWBrJJkmHh4cxGAzKMduql7RtGcCptdmv1Ywsv/6phpMaLfdZRQDde9Mhv6aVcO9AxPQhRn6/KyaPDTYhXSdF8nzP2pB9d/ebAgDcqaynZlnK06dPY3t7O9bX12N3d7dYTnqGAe0ZDAaxv79flsMcHh6WHbp0UmY5GFofb1etjcrAGjesTS4vK7Pw/b06dt5vOoYKih6amqyUGpCsTX76VXkVUBcRJVdEwwDKZ/ocnjRNbNI6aL1qlobzkXuHsLZZGsv7lpeXp5ZtkTDG/FxdXY2NjY3odDrFZfzb3/42/vCHP8Th4WG8efNmKrwAOMbq13mt1q0S+7yrEFblpv03S6GoIKSvFHA1KSzu0ft+DpB8H8rmPd+dL3W+6rWImJJFl5eXBRxG3C6XBqixysO9YLzXk+CUJ9Ua1WczGRExPV78XvPyREwflb27uxubm5vlk90SOXhocXHxDj8dHx/HaDSKly9fxu9+97s4PDyM7777rsjo09PT8j6VfV5/vaag36/p2BHb9zH0Nmbj3eQ10ORe5V/1rMxDn7TpUE2ZKONxze9VcMA9fOr1eSb+vHWbdb2mBNg1TUMA6+vrZa329vb21E5WxKaIpTIguN3wChwdHZWYaVbHGoN4/Wpt9AmXledKUd+TTYQaIND7VblqUkutnz83Zf3W5ElqAgNuxdbiuZkC8mvO6/QdQEKBtQNpntNNjmp9TayYd3c6H8+LRxFQnsaVI6IcWrS3txeDwSB++OGH+P3vfx/D4TA+fPhQytc6EZMm0UqXH+oe6hG3O7+p4MrAgI9Tdt2VkY5JBkhrYECt7C8BDNR+cwWilqF/RxaxRwbP4w1SRaLJs9mc0Lp5wrODAb3Xy2wCiU6TyW3iHqcNrq+vT/2x14XuCaJt4xjl9+/fx1/+8pc4OjqaWhrJCaM868An4y0HSjX5HDEdYndg39QP2pcKKryftb0/GxhAAGUT1JUNlXOLUJln3gnmwtUVkwpdrStCLUOcyrhcI1GK5ECAQK/XK54B/SRMgMtVJ53uwY179ODgoLihOEZZ25i1I2NAnXB+v45BNjZerv5eY94MJPhvtTpHfNrpWT81oXi0vl6nrC0Rt5a7KwgXcnpiH9fxDmR97BYoFg8u9CYlRLa0W7AqaNzV32p9POWNc+i3t7eL0Gu327G+vl5CDM+fP58CHK9fv47RaBS/+93vYn9/P/74xz/GwcFBXF5eFi+gHuPaat1umkQ7Pd9I+8HXQyso8X6rkYNOHZ8s5KDP6bt/rvDEp1ITWOF3n6PZc/qse4z8GVc0ei0r2z0wNTBWq0923UEtslkVP55ZvFGsmiGU22q1SuyfebW3txdv376NV69ela2GSWxFyWq+T6Z0XQH7vM/6J5PfqtTnie/XwKwmR7rcvY/s/SQw4JuouAXD/TXXMJ1SQy3esZn1porJG66D6RmhOiAqbAABi4uLZXkVLqeFhYU7SVf9fr9sSewol3wA1uUeHBzEmzdv4uTkpBzlmbkrXRDVkKYDAifvi2zCNzFebYJmQCD77mGKT2HMn5I0UdX51ykTrCh6vsPv2h8K7jRpz+uhc2UymZSkJrxKLGXiPQ4mWq3bGP7V1VVJPqVtuuWvv7vf78fW1lY8efIkdnd3y3axER/3bI+I+Prrr6eWnL59+zb+1//6X/H+/fv4p3/6p3jz5k1Zdra0tBTdbjcmk0lxMXuyJL/Rf7rsidCKbrJFO+clB2caG6avHQRqf7osyAydhwQDHrOGd2rKVsFfRpmidRCmYMq9UXq/95XKqmw77kxm1MCNJg4im9n2+unTp9Hr9cpJiuxC2O12y2FLKysrhb9ZOXB5eRkvX76Mv/zlL+Xv7OysbJ2sS2iz7cL5rn2ife3L/7w/Wq3WnTNHtH8ccNF/zB0fUx1/n3Mq4+aVu/deWjhLUSvppHdqiiE3WXDz1E3LyOqt14idsXsaa3H1uE4/phhhDMOAOrFwSMQZDodxenoaR0dHBYHq4U5N1n2tT2Zdy/qlqbxsHGsAbF7SMvW5hxKqmdDxOrnlr5aiZu9rmRoKgRdUIGheQSbkZoHhDDBiBehfkwUNuOUUOgQnQIXy+MSlz57s+/v78eOPP8aHDx/i5OSkCFdVDtp+TzBTZa3X3GOU/VazbmvXszHNPrO+zubAl0CZB2CWxZ2Bgyaq3V8DHLPem92bGRM1cKDjT2iJrbT7/X5R+ih+TYoFYKL8yYEh32Vvby/29vbKlsouj9XK1z5QfZG13697COA+eszLza5nYdisnPt44e+9HTFJdSxr0QmfIRee83igkltjNTRZAx+OzDR5ye/RTmq1WlOu/ydPnpTzvLHUyFTd2NgoGdXEWnVZFujz6uqqnELI+tXj4+P485//XFYOgHbVMnFr3gWqK2+dMM4w3K/vyNyfPm6ZcHZknIV9tBxVWFCW2PQ5yU/AVFLFm1mJ/BZxy18ZoFxdXS1zg0RAX+6nFpcL9oiPVq1as3gkEHS6wRXvYBdBDcsx1xYWFmJ9fT3+8R//MXZ3d+OXv/xlWXKlZTGvAcbtdjtevnwZv/3tb+P777+Pf/qnfyrH0l5cXBT+1/wfltIqf2tbsO7UQtJ+4F6S2bx/HUgpZfNDx9TH2xVeRNx5h/PGQ5Em4jYln0E+T1UZuCzJ3qUySEn7y72NLlP8GSWXdyo7yBvhOgnbKysr8e233xbP1sbGRmxsbMSLFy/KUkLmCPKYFVtv376N8/PzePv2bYxGo/jjH/8YL1++jNPT0zg+Pp4K02meAfX3Dbe8vzS8nI2NyxTvC+9bHSMtRxOFlX+1bJ/796VPSiDMGFOZwL0BdOasGGBN0bu1lpFPjkzY+v0IPw5nIQ+ATwaarVlZWaBCdDy+3b+brGi8AAAC3bDDUWdtctYm+7wWSwbMPhWdNr1znjF5aCurBj7nrZeHkzLFo+T876DCCT5SUJcJEY3jZ2UowaNra2vFM6DHaetzKljYRIhjXD98+FByXXwrZn2nJlxloY0s3JXVXwWh13HeZCjndVeGDgj+X6H7zOFMBje1+a/xini9avzpcrwGIuAVlnCvrq6WcAB7uHDWAAq83W5Phd7YSplk7cPDw+KlJUcgA52a++XtaDKI/pqxqfXpPNeUmgzleehengFfOuGbBqkg4H8XaGohZW5QZxSEjMdaZjF3Bg5UwBKD6na7sbOzE6urq7G7u1uSrPAAABZYNki4gDXZw+EwXr16VTZXubi4KEtUAAS6zbC2T+Pp9GmmiDPrXREs97m3Qe/XPnOPwiyE6vXhGY9dZkrP494PQW7tZXV28rbTH56U6l6P8Xh8Z+kWvykYVOWoVpx+cl3jmSyXQuCpJcUz4/E4njx5Et9880189dVX8e///b+Pra2twueUqSEQlvb94Q9/iHfv3sXvfve7+Od//ucCcEmoVe+Dg4FW6zZpsok0vJEpeO139vagzQqa3Jum1zLgoaBO+VvHVcdEZdlDEfVh+RheT/3dwan2ibcZcus8ey/38en9wj2ZR8HlRyZTKA9epJ1s5762thYvXryI1dXV2NnZibW1tdjd3Y2tra3o9/vx5MmTaLVaxZt0cHBQtrs+PDyMk5OT+Mtf/hLn5+dlMyFCuNoO3duD+nu+CZTpLLxpHqfXPvK57eDWjWsdJ+51DzCeRM1xUJ17X5l7rx0IXRm7S8WVfJN1m1lrfn+mnJxJ7wMG9JOlTisrKyXhxD/J2sYturCwUNzBCFG27GRzFT3Kk7wBrXcNGc/qr4xBahOO/zM3qfdt1pe1svXTLThH+d7mh6TMI5W1v0beZv7PFIVOQge8ukQQ0h0DJ5PJnXnGcw48NBTmgHs8/niC5pMnT8ofSbGE+HiGckh4ffnyZXz//ffxr//6r/Fv//Zv0Wq1yqmDKHrWomdzFoGqvys5wKr9nlmK2Ri4F6IJvOq4uaDPeKA2Tz8nOXiEmupUkzEum91S93c6ZQBZr7v8mJdQzCg2jK9+vx87OztToVuOYCb/RUEiyv7Dhw/loKE3b97EaDSK/f39qZ0vXVZmXjcF6k61MAr94SDA50gtcVDlioYrs7rovNfy3WCZl+61A6EmGrkbVIWS5gl4FqVvgavlZ98zhVR7TtGpT3RXTLqHAMkphAcICywvLxdQ0Ov1otVqlS1b37x5E+/fv4+Dg4N49epV8RKw4RC7C2bWZdMA6ST22Fx2H0zdJABmCWCuZxaX9llNeCsDq7JSy+EhAUENlABoI6Zje5kbMCsTQEm/oVjJqqe/OP2PcJJaVhnIxtpA+fIcp8Mx2XWTLpQvoYCvvvoq/v7v/z6ePHkS29vb0e12y8qXyWRSDg7i5Mx/+7d/i6Ojo/jtb38br169ir29vantWyOm91NwS4e6s2JiYWGheEK8D0lU1PXtvt2tvlM3wMl2XdTvLhSdd5v41+eNe98ekmquaf3UuT4rJMv93KMKxxWlvjfzvPgc8rplYEO9MfDL1tZWrK2txc7OTjx//rx4a0mCZZ8X9nxZWlqK8/Pz2N/fj+FwGN999118+PChnDrIb7qqgHarvOLTE1e1jap4AQ7q+cu8jzoO2teag1SjecdPz8XROmYr/GbRJ60mcOSSNUAnLh2goQQaoxNWlbkmRVAGVBuwbMJQvitPvALEn3T7Sv0kLrW2thaTyaTsHPj+/fv485//XDat0N3dyBHIJk4TyHGrxUEWfaP3e/5GTZHr/W4lcZ8qtRra549+1XHNXLX+noegmhLQ0Bd1xmqOyJf66Hh68hB9oUlJevofCYW8X/tcr6nLHfDJGRe1hCaSJNkx8Pnz5/HrX/+67Myma69RvqPRqFhR//t//+/Y29uLf/u3f4v3798XUJPxFvkIGdHmiNwTEnGbz6ChiizplzJUBuAqd+DrvJpZR86fmWWlwpQQ15cACFRReXgvYrqPa1ZnjdyY4Jp+1p7RezzUknle9JM/5iH7Xzx58iS++uqrqfMwOB9jc3Mz1tfXS8Lf+fl52cflu+++i9evX8fR0VE5M4ON3ZQH9C8zgrxtOvd5RucAPJzlxWTgrNa3GZ/V8gu8Hs6/CrjmpXsnEKr1xGRX5eHbENcs26bylbIOy5CxK5x536sJhOxvrUhXBdb19XW8e/cuTk9Pi1eAGJTu7qbCyidjrU7OJG6tZO4mp8wb0uSZyMBI0zu0TP2sWbc+8R+Ssn5wwVWzHjN+43/dajdiWtggLHyDKRRNJsDVva5AC3CAtyCzONrtdvR6vdja2ipCs9vtTq2yiYiyH/uHDx/ij3/8YxweHsa7d+/i6Oio5Dto2W7B0DZtt/JEtr2yAib2yHel7oq3xpeZbMnkRpMioxzn2wwoPCRl86amTLL6ZtcyIKr5EZkS13crGNZ3cM0NDB8nLZM9XFixtbGxUb6rZxYwCy9yfgKhgP39/djf3y8Jg+qB83rfxzDJgI23I3PfZ8/MA8xcjyEv4McsrwxSA3reXR2V7gUGFMGD2DUBhM95K5F1ol/PynH05vc1bSqj9yPU2FMALwBMNx5PH+s5Go3KqYNv376NDx8+lOM96QsVJj65/Ddn1CwZTRNrtP0OirTsrK21PvAytT7O8BnoUqWmbVZlxm8PRdlEjribne8uUicfp1brdokcYw8P6fI/MvSxLFihos+p61/BQ8RtXgGCkRCUChfet7u7G99++218/fXX5Sz3paWlaLfbJdzw/v37ePXqVXz33XfxP//n/4yTk5N48+ZNybDWEIf2i441CVMR08aALi1k6a3u09Bqtcqc0vlCHoIrEgSbK5psQyH9VMve5YmW5eUov87jqv1c5DJlHiBwH3JgUCvP+07lnl7j3mxJnMoZNhLqdrvx9ddfl1AB3lqWDZLvwql8g8EgXr58Gfv7+/Ev//Iv8eHDh3jz5k0cHR2lsqzpz/sgAzxu3KgxrP2W5QOoweR1U6Jc51Htd8rUMdK5gh5GPmQ5SDW610FFNCZjFrWIXFnVULZ3XIam9P16Xya0531Wkf94PC57qOuJbaooWKYyGo3KBkIIZI3hZkzo7aqhen8ms5D83gx0eR82va+mJGvPzPvdQcaXRN4/mcXSxIfcmwkH/U0nqL47q0cN1Gbjq7yr1oJunEUOjIJaclnOz8/j4OAg9vb24uDgoBzcku3LAZiBVBgpr7o16X3rdYfUoPB3a9jJQxV+X40yq1Z/c+Ffs+gekprmbZOhdN8yfyqaB5QocNBTBgHRhNb8LAu26z47O4uTk5PY398v27yfnZ1NHcLlhlYTCPB79X/nr6a+07BDFhbge42/ZvEz76g9C9Xk+iy6FxiIiILgIY8fTiaTqbPJlTlUiNGAJoHJdxW2WVxGy/CJ4soZZoGpjo+P4927d7G8vBwnJydT1g91vbi4KC7U169flwRBLD5tS5NQ1MGuCUDq5h4BtdK0PSrEavfoOxypez0dwPBdQydZ+3ySKej6EoRszRJQvs68NhF5mKPJapxMJlNL8dxzFnEbT/fkK97N/dyLIByPPybUkX8wHo9jcXExdnZ2otvtxvPnz+NXv/pVPH36NFZWVmIymZSdA3//+9/HwcFB/OEPf4jvv/++nDrIe5eWlkruAeEzrHbtw8wLpfzLPW79u/WNLNFEW0AAcgWljdfELaelpaU7cqVmiHhfa31VznyqMP05yMODWketu4ZOuL8p3pxRJof1vQpEvS5812f0Oa7zXY+B39nZKQe+sbvg1tZW2Q0WgNBut+Pw8DCOj4/j9evX8S//8i8xGAzKtsK+pbC2xfWGfqohS/9w730s6wxwZDlBKqcB3RG3q4Z0jLKcFq+nehDc0PlZEgjH49vjWDUGCGmFECg/tYs4YzbtEN9aMnuvTnq8AnqUsAoM7jk/Py9gQFcKZB2dDUpmBWb1a0KtGWXAqansecr0Z5SBZ41jVv8vgWbVx4Vc9jufPpZNfaJChomvc0fDQi6kvBze78oVtz67ZbJJC+5Ulg0Oh8PY39+Pvb29eP/+fezt7ZU9BCKibLJF0qDvKdJECjI9RJR5YfS7Jghq+1WZKRjwvnKX6Tw8Ost6bbKyHoJ0HLJYsCsDNwr+mnc2US3MO0//4c0iFwAvACAQr5by4WQyidFoFMfHx3F4eFhWEXASo4M7l3mZweLKNAMFDnId1GZ95v9nfYNO5bryu/dxU65A7fp9efeTdiCMmM4chXTjA1XQXKOCWskMWfK/DprHzP1aRjr4jrDIpib2gyWnCBvLhXs551vjokpNFoWjwizWDjUN/H3colp2xsDuydD61TwsNfJJFRFlM45WqzXlTfrcpBMK8ondRNlYuCWPEMEa1t0sd3d3o9PplP3Rz87OCu/VTq9EweH6b7fbJYzVan1cDaMeJFbDYGWwFetwOIy//OUvMRgM4v/8n/8T79+/j8PDwzg6OorxeFxyF+gbBQUsDfRESAUlgBvyDBREKw9pf7tA5JpbUeqR0HKyPx3jzKrKhLPmIyife70fkjRfwr2FmaXOp9fbZa6Th3e9PzLrP1Ou/q5MnsC/nC2QAQLG9PT0NCKihGS///77+OGHH2J/fz/evn1b5LHOwaxeCip0zvK95hnw/vLEX/0963f/rmFo9dJm5enYZMCFPqVtGrLWds3Lw/fagVDJXRCzXE/KTDVE2UTOnPOgIRVCXhYWiS6hwhWlbl02qdCzGGplOiKuIUJFofSHgwFvT6aca+Ap65NZE1Un0LwIvwZi5nFVfk6a5cbUz/uW6RaFJhahyFkmdXh4GKPRqCx7urq6SkGS1qXdbhcrCTCAIEFhIlzh34iPwvP4+DgODg7KEtjvvvuu7B/AHgK+RJAyCEe48KspFb3HhZt6FLW/KCObSzovHMRlioi66Thkzzhp2MHb54rxIcjrV6ur07xzWBUefe4KpwaaGadsnje9n/sVwOIF0FMvJ5NJkb+DwSAuLi7izZs38fLly7KSQENvqmC1r1wpck2tcr2nBv65l+WtmR5Q8n708IrvUOpjQhl+zevkngTv53lpbjDAGmU9iMWRE4JNLWqs6KaDirJr3tHZM7Pc9DWE78CEI1YRrIq4fSc1/XSLxpH5PEgxu5YhbZ+UvDdT7rNif7X3ZwInQ/b6nH53S0wtyIcUqFDmuckEWBPV+DITmK3Wxw2Hnj9/Huvr6/HLX/4yJpNJcdMPh8N49+5dOVzFN0dBMPr8USGHACXWSiLW2dlZDAaDODg4KOe2ExJgHjeNiZ+/QRt9znmow/kx48+Iu7FQFciZ4KZOPKfhA71PQYfO5ckkP955Fsh+aFJl0NSffL/vRkku22pygn7JLO+Iekg4M/zwJrFkkFNiOYmQPShI3L64uIj379/HcDgsKwbwrEXE1HbCGT9lAFI9AV6/Wt9q/XUuZHkB3n6XyQ5wnWZ5gPkdj3ZETIX9avuANNG9wAADyP7oPtlwgbB0iAHN4oc0KlOm7tJRpaIxyWzA+K2Gtlxo6c5nTRZELSSgE7DJAs6s7xp6doZVa5N7VAhnFoz2TyYYXdlnbfU+8/tdgKoSow78fQlgIOKuJ8An6zxgifv8ezb+q6ur8atf/SqePHlSzr54/fp1vH79Ovb29uIPf/hDEXLsMMiyILXSIb5rSI7NWTiMaHFxsay/Pjg4iJcvX5a4akRMWSQu0LSti4uLU/M6SzrVuauWiir7zHqsgSf3RCjo1ZCTK3Sdg3ynHAUHXt95DJOHpMwyRJlmCkPDnNpel3G1Nqq3Ut+r8zhTNKoDdMx81z+VZYTROJZ4Y2Mj+v1+XF1dlSXbb968ibOzs/juu+/i+Pi4rOZCbvs8yeSn9h26RdvhMkGBj/e/y2HXU/w/SyfwnNbP9ypxWe3zgpyLXq8XERF7e3slGReD/T68PDcY+Pbbb6Pdbsf29nasra1NCQc8Arh0zs/PCxgg4/78/Lwk4ylYcCsrU2zeEfNQBhRqlno24TLl5QOeIdEm5K73zVvvWfdk99cYwOtaQ7+z3uEToAlE1er4Oak2DjXKrEV/zi0NfxYgDFJHaLFDIEcBI/Q6nU6cnp6m+0l4rF6JZCs2zuLdHKTFH3PTyecVAlaBXNY3Tljq84aEanybCTBPmpxVlwz4ulHhysvH+ksCBBHTiqcpDuxKHKrJQy+75hFwQyazfmuyPGsLYTL4k/0zWq1WXFxclKRXThvkJFhCZfRDLUegyb2u9cj6T/t3ngQ+yAFbjWqG5awy/TqAV3nbgfJ9aG4w8F/+y3+JTqcTv/nNb+Lp06dTgoKB121TWV5I5v3BwUGMRqN49epVQXckMYFm6ED1JDg4cADhHZR9KukGKVgQnoiYvdcnT43ZMmtar7tF7+/lXre6m4ASlDGB35/1Ua3MWvuz5/1/TXbKPDWfkzJgmbU5U0LZ8woEa4rm8vKyrH+mLzhOeGVlJZ49exYXFxfxj//4j8XyOTo6in/+53+O77//Pk5PT2MwGJSclkywEmvtdruxubkZGxsbERFTh2Txx/bYquhVaFJvPQkxS2xqEjRZvFI9iNr/3q/8rssHfWxUgdMfmadCQZRad94eBVe1dt3XuvqpKfOe+O6P2tf0S2aZapjFKZO33te+cY6Oh9dRk/G8Pcj8k5OTuLm5KbH/y8vLWFpaiuFwGIPBoCwbZMvh8/PzqTlHqIu56CGVWUaTbiFMH3BvJrccPPn80X7LZLnyoitxnd8ebtUx4jnORdETE0lOxhvfpCsymhsMbG5uRqfTiZ2dnXjy5EnaUexgBgC4vLwsnoCVlZXiBtW8g6urqzg7O4uIuzkH2slKNesBagIDTdeyiecMVVPYNXJwoAxzX7rP4DYp+fuWWWur982scr4Ea6uJNyLuX8eMPxSlZ7F+Ypwge/Zcj/gYVlhaWoqLi4tStrtam+qi7/edQuehn8IyrlmE8zxXe2/Na8BvEdOeqqwdet//K5TJpIxUmfOcj7lb8JmnJfvu/Vv7Xes3q97kanU6nbIs8ObmJhYXF+Pk5CQGg8HUskG8yWq8+XzIjJJ5qInX4ScUsVIt54C+199r997XUFK5rkAoIu7M9fvOw7nBwO7ubiwsLMT6+nqJUWBpsKaZSlAp0CsDf3V1VTwDP/74Y7x8+bIc/csmQJplrQl8WCvqvtTr2hEawvAJoQk2mWXexMS0Vz0DitA1Q5rvfq0JRGSKSlG53tc0SbNyVFA6qTVVI7WuZtVbJ9BDW1bUJyKPi/NbE5hs8iK41YQbE0tHc2YAx8qbzJ/t7e1YWlqK7e3t6PV6ZcdAB6DKT4TdOp1ODIfDWFlZic3NzTsni9YAsI6XW83zALtsftHPKujcK6Bz1/cZcOtU+zobC37THAr3LHg/0I8ZQNd28ft9Q5Q/JZHnQc6W97n2ryqWmmzTttf2Vmm323dyMCLy0Jj3lfeXW72TyaScaLm/v1+8AyS+ttvtOD09LbtlHh8fl3ryO+/wcbmvYeOyWfsqS3j3Zagq573P3fuozyhQqNXZwxU1b5rzNuOsXqN56V4JhJ7cw4BoUkaNUNDj8Ti63W7JLTg9PY3J5KNbFSYBDPA3Ho/LaXLaWPYHADH6Tm8oJKXMMq9Z6plS1jKy8rN3aVn3seybvn/K/bW281lT2k1jO4+l/dBgIKNMoSjI0/vmifG5u9FB62QymcqT0X5ptz/u439zc1Ni/8w3nqNuKhx81Q5zpKa8MuGRtQuFXcsBcIXpz2b3RUSaD5FZo9nYKLngc/7MLFj/PROyOp9r8+RzkxsaOr7Kb96fPs7c42EUSME73zPg4UDKx1CNDn3e+xIPMitpALXtdruEuNjXZTKZlN+yHAHtF7+mYz8PqFOjx3l5Fg84OHCi3X5vrVz3LszLg58qc+cGA//9v//3iIhyNjrK3TMua1YizHZ0dFR29Ds8PCx5BSh4Pn39dW09tqJid5Fk5MLLmXlWJzpKbrrfrcfsee0rZVr6qxb7cXDiddeJqqQTuenZ2u+ZdeBtyJDyQ1LWd95e4o+49nV8Pe6t48Rz/g5+I66PYtWNQfhOvO/y8jJarVY5QdPzSzS+TR1Zq83Og0tLS+V9TUpDv0dMb4+svKdWRjYHfdz9vZlC4JoCdx8n+lS3O9ezQ5rGWpVRBizcWtXfahbbQ/Gyh0uz+eeWeeZ61jGg/z2EpeXUQCC/6fxAbivYc4Ci5M8dHx9P8Q4h5kwuO8jzMrPfdNxVubKUnH7MtlyfR87XvGPaZxnQdh3p9c14WAkAhMdEgR7vuI9Xa24w8D/+x/+IiNsdmDSxKVMUXmGEDDtGqcLnGRoFI+n+5O5613f6Tk6uVJsmtU8Kv88tD594+j7/1HL0eSizQrXemWWSMUr23qytel9Tn9TAR1MZTf1Qq9/nouy93h5NxEHYZZasltlqTS+z8t85YAWeVQ8XfMd8QAAuLCzE8vJyedYtCQALyVYrKyvlqFc2cFELypOrHFTMilnqPOUzm1fMb90b3oW+v0sBfMZL9CFnPLDxl1p8+h7lsxrg9XFU17vW38HgQxHjny1/VuGPvITPMtc3nw4GHACowq95xXT8sy2S9V1K3q+uB/Q5H1+tq9ZF3+cA3suMiLJpEPfrTqlOnyK7sn5QIOLnHWh7vS3zvEs3T3Ie/1nAwMXFRURMWw6amU9l9DOrMJaRZw0jNNz1qIKLxmn5rda068UFsltHjhj1HU3KtMaAmTJuAkiOcDOFqZPTrVKvWyb0vH1ebu25WXGsrOxsEnmdM6Hw0JSBNJ1ATWBJr+uxuzyrW6pGRFk3fXV1NQUeHAB3Op149uxZTCaT6Pf7sb6+PpV1DT8gQFGU6+vr8eLFi9jc3Cw5B+Qr9Pv9klvg8XnagIUecTu/dT4C+lHGCm40p0etQeo4Hk+vFuJTy6Ee2dwdj8fFVewWvc+LbN7NoizmqmX5fH0oyt6vypzvNc8o391DoP+7Ep41b33ssnwifq+NxSyjZJanxmWueyRqiXtqoWu/ZDyV1XVeajJEmt5R0zdK7nVUA70p/FAtb94byfjPBqTGAHo/g5lNaD5xk/I9m4z6+6zYmb9b69YEYhS9+kDpILkrJhMo3OP7iWuZzrDuQtKya8ztoMMRspOCA/5UEXh7vN7ax3omvE7ILG7+JZADJFXqWAp6X00gqHXBuBD/190DR6NRnJ6eRr/fv2O5szcA1u/CwkJ8/fXXZZ01SxQJp2lCIqe5dbvd+Lu/+7uyg9vKykr0er3Y3d2N09PT+Oqrr9JTGVWpD4fDKX7W39grAa8GAkiXMukOiiQDc6zs6enpVEhEtwDvdDqph4L66V4lyt/qHdR2ZYBOAVDNIvP4sFrXD+kZUHJ+rM3TJiCUWY/uKfK+rb3DZZEq1ExOZTLKn3eZlBl/Ktv1/yy/wccOntbwk4IprVfmxXXgP4tqfTTPvdyvuTv0BweTkVtE4v3FxUVp37yAGLr3noU11DTrN64pk+nA+qffP+/7M8VVq0d2zzzvzqzempXi78rqn024rM581gCZ96Pecx+m+BQE/P8i1fol88Q0laFlqbKqgdQMXCAI2ESIZMKImDozg/IUDCAQ1FIgzKBnFqggQliQgLu0tBQ3NzdT+QZqPemnLu/CW8CKIpS7lpEBeO/vpn7PwH3TWGi5tXHMfnP5k5X5JVPGWzUgy/3Z9azceZ/P5I/WZV7Zch8ZNEsmfkoZ96ljE4/Mc89fU4+me+/dnsn/S9z+SI/0SI/0SI/0SD85PdwC2kd6pEd6pEd6pEf6IugRDDzSIz3SIz3SI/2N0yMYeKRHeqRHeqRH+hunRzDwSI/0SI/0SI/0N06PYOCRHumRHumRHulvnB7BwCM90iM90iM90t84PYKBR3qkR3qkR3qkv3F6BAOP9EiP9EiP9Eh/4/QIBh7pkR7pkR7pkf7G6f8Hm5bJSzBx2t0AAAAASUVORK5CYII=\n", "text/plain": ["<Figure size 640x480 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Create some synthetic data for visualisation\n", "n_synthetic_images = 3\n", "noise = torch.randn((n_synthetic_images, 1, 64, 64))\n", "noise = noise.to(device)\n", "scheduler.set_timesteps(num_inference_steps=50)\n", "\n", "with torch.no_grad():\n", "    syn_images = inferer.sample(input_noise=noise, diffusion_model=unet, scheduler=scheduler)\n", "\n", "# Plot 3 examples from the synthetic data\n", "fig, ax = plt.subplots(nrows=1, ncols=3)\n", "for image_n in range(3):\n", "    ax[image_n].imshow(syn_images[image_n, 0, :, :].cpu(), cmap=\"gray\")\n", "    ax[image_n].axis(\"off\")"]}, {"cell_type": "markdown", "id": "5676aa62", "metadata": {}, "source": ["## Compute FID"]}, {"cell_type": "markdown", "id": "98452f3f", "metadata": {}, "source": ["The FID measures the distance between the feature vectors from the real images and those obtained from generated images. In order to compute the FID the images need to be passed into a pre-trained network to get the desired feature vectors. Although the FID is commonly computed using the Inception network, here, we used a pre-trained version of the RadImageNet to calculate the feature space. Lower FID scores indicate that the images are more similar, with a perfect score being 0 indicating that the two groups of images are identical."]}, {"cell_type": "code", "execution_count": 14, "id": "a42c4e9c", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Using cache found in /home/<USER>/.cache/torch/hub/Warvito_radimagenet-models_main\n"]}, {"data": {"text/plain": ["ResNet50(\n", "  (conv1): Conv2d(3, 64, kernel_size=(7, 7), stride=(2, 2), padding=(3, 3))\n", "  (bn1): BatchNorm2d(64, eps=1.001e-05, momentum=0.01, affine=True, track_running_stats=True)\n", "  (relu): ReLU(inplace=True)\n", "  (maxpool): MaxPool2d(kernel_size=3, stride=2, padding=1, dilation=1, ceil_mode=False)\n", "  (layer1): Sequential(\n", "    (0): Bottleneck(\n", "      (conv1): Conv2d(64, 64, kernel_size=(1, 1), stride=(1, 1))\n", "      (bn1): BatchNorm2d(64, eps=1.001e-05, momentum=0.99, affine=True, track_running_stats=True)\n", "      (conv2): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "      (bn2): BatchNorm2d(64, eps=1.001e-05, momentum=0.99, affine=True, track_running_stats=True)\n", "      (conv3): Conv2d(64, 256, kernel_size=(1, 1), stride=(1, 1))\n", "      (bn3): BatchNorm2d(256, eps=1.001e-05, momentum=0.99, affine=True, track_running_stats=True)\n", "      (relu): ReLU(inplace=True)\n", "      (downsample): Sequential(\n", "        (0): Conv2d(64, 256, kernel_size=(1, 1), stride=(1, 1))\n", "        (1): BatchNorm2d(256, eps=1.001e-05, momentum=0.99, affine=True, track_running_stats=True)\n", "      )\n", "    )\n", "    (1): Bottleneck(\n", "      (conv1): Conv2d(256, 64, kernel_size=(1, 1), stride=(1, 1))\n", "      (bn1): BatchNorm2d(64, eps=1.001e-05, momentum=0.99, affine=True, track_running_stats=True)\n", "      (conv2): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "      (bn2): BatchNorm2d(64, eps=1.001e-05, momentum=0.99, affine=True, track_running_stats=True)\n", "      (conv3): Conv2d(64, 256, kernel_size=(1, 1), stride=(1, 1))\n", "      (bn3): BatchNorm2d(256, eps=1.001e-05, momentum=0.99, affine=True, track_running_stats=True)\n", "      (relu): ReLU(inplace=True)\n", "    )\n", "    (2): Bottleneck(\n", "      (conv1): Conv2d(256, 64, kernel_size=(1, 1), stride=(1, 1))\n", "      (bn1): BatchNorm2d(64, eps=1.001e-05, momentum=0.99, affine=True, track_running_stats=True)\n", "      (conv2): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "      (bn2): BatchNorm2d(64, eps=1.001e-05, momentum=0.99, affine=True, track_running_stats=True)\n", "      (conv3): Conv2d(64, 256, kernel_size=(1, 1), stride=(1, 1))\n", "      (bn3): BatchNorm2d(256, eps=1.001e-05, momentum=0.99, affine=True, track_running_stats=True)\n", "      (relu): ReLU(inplace=True)\n", "    )\n", "  )\n", "  (layer2): Sequential(\n", "    (0): Bottleneck(\n", "      (conv1): Conv2d(256, 128, kernel_size=(1, 1), stride=(2, 2))\n", "      (bn1): BatchNorm2d(128, eps=1.001e-05, momentum=0.99, affine=True, track_running_stats=True)\n", "      (conv2): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "      (bn2): BatchNorm2d(128, eps=1.001e-05, momentum=0.99, affine=True, track_running_stats=True)\n", "      (conv3): Conv2d(128, 512, kernel_size=(1, 1), stride=(1, 1))\n", "      (bn3): BatchNorm2d(512, eps=1.001e-05, momentum=0.99, affine=True, track_running_stats=True)\n", "      (relu): ReLU(inplace=True)\n", "      (downsample): Sequential(\n", "        (0): Conv2d(256, 512, kernel_size=(1, 1), stride=(2, 2))\n", "        (1): BatchNorm2d(512, eps=1.001e-05, momentum=0.99, affine=True, track_running_stats=True)\n", "      )\n", "    )\n", "    (1): Bottleneck(\n", "      (conv1): Conv2d(512, 128, kernel_size=(1, 1), stride=(1, 1))\n", "      (bn1): BatchNorm2d(128, eps=1.001e-05, momentum=0.99, affine=True, track_running_stats=True)\n", "      (conv2): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "      (bn2): BatchNorm2d(128, eps=1.001e-05, momentum=0.99, affine=True, track_running_stats=True)\n", "      (conv3): Conv2d(128, 512, kernel_size=(1, 1), stride=(1, 1))\n", "      (bn3): BatchNorm2d(512, eps=1.001e-05, momentum=0.99, affine=True, track_running_stats=True)\n", "      (relu): ReLU(inplace=True)\n", "    )\n", "    (2): Bottleneck(\n", "      (conv1): Conv2d(512, 128, kernel_size=(1, 1), stride=(1, 1))\n", "      (bn1): BatchNorm2d(128, eps=1.001e-05, momentum=0.99, affine=True, track_running_stats=True)\n", "      (conv2): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "      (bn2): BatchNorm2d(128, eps=1.001e-05, momentum=0.99, affine=True, track_running_stats=True)\n", "      (conv3): Conv2d(128, 512, kernel_size=(1, 1), stride=(1, 1))\n", "      (bn3): BatchNorm2d(512, eps=1.001e-05, momentum=0.99, affine=True, track_running_stats=True)\n", "      (relu): ReLU(inplace=True)\n", "    )\n", "    (3): Bottleneck(\n", "      (conv1): Conv2d(512, 128, kernel_size=(1, 1), stride=(1, 1))\n", "      (bn1): BatchNorm2d(128, eps=1.001e-05, momentum=0.99, affine=True, track_running_stats=True)\n", "      (conv2): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "      (bn2): BatchNorm2d(128, eps=1.001e-05, momentum=0.99, affine=True, track_running_stats=True)\n", "      (conv3): Conv2d(128, 512, kernel_size=(1, 1), stride=(1, 1))\n", "      (bn3): BatchNorm2d(512, eps=1.001e-05, momentum=0.99, affine=True, track_running_stats=True)\n", "      (relu): ReLU(inplace=True)\n", "    )\n", "  )\n", "  (layer3): Sequential(\n", "    (0): Bottleneck(\n", "      (conv1): Conv2d(512, 256, kernel_size=(1, 1), stride=(2, 2))\n", "      (bn1): BatchNorm2d(256, eps=1.001e-05, momentum=0.99, affine=True, track_running_stats=True)\n", "      (conv2): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "      (bn2): BatchNorm2d(256, eps=1.001e-05, momentum=0.99, affine=True, track_running_stats=True)\n", "      (conv3): Conv2d(256, 1024, kernel_size=(1, 1), stride=(1, 1))\n", "      (bn3): BatchNorm2d(1024, eps=1.001e-05, momentum=0.99, affine=True, track_running_stats=True)\n", "      (relu): ReLU(inplace=True)\n", "      (downsample): Sequential(\n", "        (0): Conv2d(512, 1024, kernel_size=(1, 1), stride=(2, 2))\n", "        (1): BatchNorm2d(1024, eps=1.001e-05, momentum=0.99, affine=True, track_running_stats=True)\n", "      )\n", "    )\n", "    (1): Bottleneck(\n", "      (conv1): Conv2d(1024, 256, kernel_size=(1, 1), stride=(1, 1))\n", "      (bn1): BatchNorm2d(256, eps=1.001e-05, momentum=0.99, affine=True, track_running_stats=True)\n", "      (conv2): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "      (bn2): BatchNorm2d(256, eps=1.001e-05, momentum=0.99, affine=True, track_running_stats=True)\n", "      (conv3): Conv2d(256, 1024, kernel_size=(1, 1), stride=(1, 1))\n", "      (bn3): BatchNorm2d(1024, eps=1.001e-05, momentum=0.99, affine=True, track_running_stats=True)\n", "      (relu): ReLU(inplace=True)\n", "    )\n", "    (2): Bottleneck(\n", "      (conv1): Conv2d(1024, 256, kernel_size=(1, 1), stride=(1, 1))\n", "      (bn1): BatchNorm2d(256, eps=1.001e-05, momentum=0.99, affine=True, track_running_stats=True)\n", "      (conv2): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "      (bn2): BatchNorm2d(256, eps=1.001e-05, momentum=0.99, affine=True, track_running_stats=True)\n", "      (conv3): Conv2d(256, 1024, kernel_size=(1, 1), stride=(1, 1))\n", "      (bn3): BatchNorm2d(1024, eps=1.001e-05, momentum=0.99, affine=True, track_running_stats=True)\n", "      (relu): ReLU(inplace=True)\n", "    )\n", "    (3): Bottleneck(\n", "      (conv1): Conv2d(1024, 256, kernel_size=(1, 1), stride=(1, 1))\n", "      (bn1): BatchNorm2d(256, eps=1.001e-05, momentum=0.99, affine=True, track_running_stats=True)\n", "      (conv2): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "      (bn2): BatchNorm2d(256, eps=1.001e-05, momentum=0.99, affine=True, track_running_stats=True)\n", "      (conv3): Conv2d(256, 1024, kernel_size=(1, 1), stride=(1, 1))\n", "      (bn3): BatchNorm2d(1024, eps=1.001e-05, momentum=0.99, affine=True, track_running_stats=True)\n", "      (relu): ReLU(inplace=True)\n", "    )\n", "    (4): <PERSON>ttle<PERSON>(\n", "      (conv1): Conv2d(1024, 256, kernel_size=(1, 1), stride=(1, 1))\n", "      (bn1): BatchNorm2d(256, eps=1.001e-05, momentum=0.99, affine=True, track_running_stats=True)\n", "      (conv2): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "      (bn2): BatchNorm2d(256, eps=1.001e-05, momentum=0.99, affine=True, track_running_stats=True)\n", "      (conv3): Conv2d(256, 1024, kernel_size=(1, 1), stride=(1, 1))\n", "      (bn3): BatchNorm2d(1024, eps=1.001e-05, momentum=0.99, affine=True, track_running_stats=True)\n", "      (relu): ReLU(inplace=True)\n", "    )\n", "    (5): <PERSON>ttle<PERSON>(\n", "      (conv1): Conv2d(1024, 256, kernel_size=(1, 1), stride=(1, 1))\n", "      (bn1): BatchNorm2d(256, eps=1.001e-05, momentum=0.99, affine=True, track_running_stats=True)\n", "      (conv2): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "      (bn2): BatchNorm2d(256, eps=1.001e-05, momentum=0.99, affine=True, track_running_stats=True)\n", "      (conv3): Conv2d(256, 1024, kernel_size=(1, 1), stride=(1, 1))\n", "      (bn3): BatchNorm2d(1024, eps=1.001e-05, momentum=0.99, affine=True, track_running_stats=True)\n", "      (relu): ReLU(inplace=True)\n", "    )\n", "  )\n", "  (layer4): Sequential(\n", "    (0): Bottleneck(\n", "      (conv1): Conv2d(1024, 512, kernel_size=(1, 1), stride=(2, 2))\n", "      (bn1): BatchNorm2d(512, eps=1.001e-05, momentum=0.99, affine=True, track_running_stats=True)\n", "      (conv2): Conv2d(512, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "      (bn2): BatchNorm2d(512, eps=1.001e-05, momentum=0.99, affine=True, track_running_stats=True)\n", "      (conv3): Conv2d(512, 2048, kernel_size=(1, 1), stride=(1, 1))\n", "      (bn3): BatchNorm2d(2048, eps=1.001e-05, momentum=0.99, affine=True, track_running_stats=True)\n", "      (relu): ReLU(inplace=True)\n", "      (downsample): Sequential(\n", "        (0): Conv2d(1024, 2048, kernel_size=(1, 1), stride=(2, 2))\n", "        (1): BatchNorm2d(2048, eps=1.001e-05, momentum=0.99, affine=True, track_running_stats=True)\n", "      )\n", "    )\n", "    (1): Bottleneck(\n", "      (conv1): Conv2d(2048, 512, kernel_size=(1, 1), stride=(1, 1))\n", "      (bn1): BatchNorm2d(512, eps=1.001e-05, momentum=0.99, affine=True, track_running_stats=True)\n", "      (conv2): Conv2d(512, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "      (bn2): BatchNorm2d(512, eps=1.001e-05, momentum=0.99, affine=True, track_running_stats=True)\n", "      (conv3): Conv2d(512, 2048, kernel_size=(1, 1), stride=(1, 1))\n", "      (bn3): BatchNorm2d(2048, eps=1.001e-05, momentum=0.99, affine=True, track_running_stats=True)\n", "      (relu): ReLU(inplace=True)\n", "    )\n", "    (2): Bottleneck(\n", "      (conv1): Conv2d(2048, 512, kernel_size=(1, 1), stride=(1, 1))\n", "      (bn1): BatchNorm2d(512, eps=1.001e-05, momentum=0.99, affine=True, track_running_stats=True)\n", "      (conv2): Conv2d(512, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))\n", "      (bn2): BatchNorm2d(512, eps=1.001e-05, momentum=0.99, affine=True, track_running_stats=True)\n", "      (conv3): Conv2d(512, 2048, kernel_size=(1, 1), stride=(1, 1))\n", "      (bn3): BatchNorm2d(2048, eps=1.001e-05, momentum=0.99, affine=True, track_running_stats=True)\n", "      (relu): ReLU(inplace=True)\n", "    )\n", "  )\n", ")"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["radnet = torch.hub.load(\"Warvito/radimagenet-models\", model=\"radimagenet_resnet50\", verbose=True)\n", "radnet.to(device)\n", "radnet.eval()"]}, {"cell_type": "markdown", "id": "b9faca46", "metadata": {}, "source": ["Here, we will load the real and generate synthetic images from noise and compute the FID of these two groups of images. Because we are generating the synthetic images on this code snippet the entire cell will take about 6 mins run and most of this time is spent in generating the images. The loading bars show how long it will take to complete the image generation for each mini-batch."]}, {"cell_type": "code", "execution_count": 15, "id": "1b48d18c", "metadata": {"lines_to_next_cell": 2}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 25/25 [00:23<00:00,  1.07it/s]\n", "100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 25/25 [00:23<00:00,  1.07it/s]\n", "100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 25/25 [00:23<00:00,  1.07it/s]\n", "100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 25/25 [00:23<00:00,  1.06it/s]\n", "100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 25/25 [00:23<00:00,  1.06it/s]\n", "100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 25/25 [00:13<00:00,  1.84it/s]\n"]}], "source": ["synth_features = []\n", "real_features = []\n", "\n", "for step, x in enumerate(val_loader):\n", "    # Get the real images\n", "    real_images = x[\"image\"].to(device)\n", "\n", "    # Generate some synthetic images using the defined model\n", "    n_synthetic_images = len(x[\"image\"])\n", "    noise = torch.randn((n_synthetic_images, 1, 64, 64))\n", "    noise = noise.to(device)\n", "    scheduler.set_timesteps(num_inference_steps=25)\n", "\n", "    with torch.no_grad():\n", "        syn_images = inferer.sample(input_noise=noise, diffusion_model=unet, scheduler=scheduler)\n", "\n", "        # Get the features for the real data\n", "        real_eval_feats = get_features(real_images)\n", "        real_features.append(real_eval_feats)\n", "\n", "        # Get the features for the synthetic data\n", "        synth_eval_feats = get_features(syn_images)\n", "        synth_features.append(synth_eval_feats)"]}, {"cell_type": "code", "execution_count": 16, "id": "1bcc49bd", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["FID Score: 12.0831\n"]}], "source": ["synth_features = torch.vstack(synth_features)\n", "real_features = torch.vstack(real_features)\n", "\n", "fid = FIDMetric()\n", "fid_res = fid(synth_features, real_features)\n", "\n", "print(f\"FID Score: {fid_res.item():.4f}\")"]}, {"cell_type": "code", "execution_count": 17, "id": "2b50e92f", "metadata": {}, "outputs": [{"data": {"image/png": "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**********************************/iZgIE9OC2FEtNbQ545geQdxRuj9zDa44qbxKVloietb+dJhUJz20GkHnjUKlC08jcocIya5jt9sd+lscS8RAQjMZoujXvPg4kWfnZ21+sxZ41BY1P3g4KB47WSM0368JYwHCYKrq6slgebg4KBFI5sRoF653w0OoHfJW/B99DHAyXHKlwQDBkifYwTsbZGQxD2e1I5d0/c1qpakzaZpyvJWttEmtk+fdrvdkqtiZfvTTz+16g7Y9f4Pb968aeW/mIaNiDLv8LT39/db48oeH6xEQKHMZrPY2dkpCph14sQ+OeZ6OBwWb4V95ZumKZnlHgvAS9M0ZZWM57UVpfM8mJeMgfUQwBygy1p4yxzvNGjHMbA3CXNnh+KlikM05E5Q6LMclqPYmDnBl36zjs6Olt/h3zbCzBU7Y+PxuJV8d3Z2Fr1eL05PT2NzczP29vZif3+/eOWwYeRg2VjbK4bRIynUOTSm75ETH94Dc4XNIZEX8PKf//N/jsFgEGtra3F8fBxnZ2flWGDmXTbGlpUMDuzo0WdmvHwf/c948b8TD13oV48nMkK9YGdImn9qeTIYcCcsQ+umNQwKHPtjQpOM5E7ISXp4Rvwf0T77mffYm2Jig4K9BNGeD53JhMfzQphQ9IQNZrNZK551cXFRFDf02P7+fhEGe9UkB7p9TKqTk5PWskf6wVQrxQKEEqaO7Lo2m82KgfCmQdCoJNkweZxwYxSO8reScP/hXWbgByuSPYeXLHlCPOYBud6Me/ZCI9p7uZuxyXkX5HA0zX2I6vLyMjY3N0vWP2NvGURODJA956BgAaKwAevr6yWZ1lvGsoMgsoA8eI5B/Xc6nZZCp26Tyf166c3NzZLYe319XTaTcd6Ok6QImTFvmF/MEwClw102vpkSt2L2s8xGogzJHjezhV6yY2MZ9hzMjsRLFHv6jE9EGzA5Bu/2AQbcnzga2WDlkkEAJev9iIcb0rnuo9GosAYstyNR9urqquwxQCiTcJd1up1EM0d8bmNK/XyYErobhsC5DCsrK/HNN9/E3t5eOfWRsAHv4NmUzA66bgZa/i47sfSl+5f+M9jySjw/ywweOsdhROuhvzkzkIsVo42cJ1g2Jvx2I2qo28/I9/B9phNrHYTX6+fwDOgmkgYj2omJEVGAgOPq3obXx3TmpZIoIZgBDAUeOnVeW1trxWt5rkGUPTzu84TmPiaQV0oAbBDmLDARC0qMBJq81p13mq7j3SgpxgUBpp9z8thLlscmBfW0XGdvKxvlDDLtcTpPxntEbGxsFEYIep7xdBgK4+tipYdhGAwGZSMgYvBeksp3vV4vrq6uWrFEs1l4ZVY+EYtVQZ1Op+y/QVz29va2rBVnue7GxkZhO9giNoNjihkv5AmjbePvvkfBM2ZWfAbPnsd4mw6/8RyAK2CGeeLE4TwOX7IQTsH7o9C2Go3s39axnsMeEzMLlPycxxg16wKuMWPAuMAoDQaDsvpgfX09zs/PY21tLXZ3d4vc7uzsFOfFY+yYO7JiRtrMmuXHuQMrKytlCeN0Oi3M7vv370tIi2XcHLTEPHfCt9uNPFm+PY/47dwPL2F3ySGJiAUoRKez4oEVa2YkADpmfJ9SnsUMUOhkflAoVvzZ+7IiMJ2RJxv3Za/YYICBxwiixOnonPAVsViyxGBeXl7G5eVlMfbz+bwkjTDAnU6neF4+9wCkC7MBnYwRJR5L2zudTpydnbXWWUdEEcSNjY2iiEg8YwkPu21tbGxERJSllQASngOF1jRN8TwBMQYM3jM7Myns5X55eflg2QvjZ2FmHMhd8ISleFevlwIDyyYEdYLNsLLJkzqindiTJ7cZJ9iZzc3NmEwmcXp6GvP5vBycNZvNSvKVk/wss3hQnjvkDHjvB0Jbp6en0ev1yiYtOzs7sb+/H71er7yLkBEbIrH5lkNNsGUwZzAeEVGyr5FBwmF3d3dlc62dnZ2SYwBD4NPxcv/bi41YrGDI3hfjYIXrMF5moOxAGHAZ0Fk/oNNoGzFuQP5LyS5ArKb/ItorAmrJYjg63vufvmGMc5ghO1n8TVkWSsjjhG6JiLKPBvrXwJXEbU5CfffuXXzzzTdl8zjuyx4wxo+EW/QmoSL3D4YR5xA9PZ1Oyy6Hs9msnHExm83KJkjuLyene6UBz84Jf5kVxd5RfzsA6CCz3QbAsH+wvoApmEeeR4hsOBz+NmAgG3dP5BpirVUiC1qmlZikGDUMkoWNdfK1+AoG03E/JkPEIl7JAODtY1h5LkKX9+Z3sh3vwbjjdWQAQ18xKS4vLyPiIVL19dQVBZ1DBZ5U7le8CG/sYsqe50YsUC3C6a1lPX5W1jaeViqWCcAh9TeVV2OBvnQxUrcR+hxYsedpA0W83142E5JlTDwbY+yz2b08tdPptMYOpYTyA6w4POHQHEoOqt/5NI6vsj9Gp9Mp1zNP8MI8xrWEJINFK1ieBxC18svgFdaM/s3jg9xSH9qAR0X7mMN2Shxmy96ZnZLsQVteWVf+kkD21777MTYuMzX5GrMn1r2e4/5sWb0joqWj3N+AxNlsVpww9DAMAmyCnR8zb171RZsBi9nBdPIuMg0A4Lk4cfv7+/H27duSBH59fV0YAmQKgFkDY/5N/bBpxPsjotUWyzeynIErdWZPGHIt2AafOU1o0LbvKeXJYIAGmtZDwdtLpWSjmItpVQSGzkCRErtumqbEHYl50zmj0Sh++umn6HQ68fbt23ISFwraBhdDiVKMaCO1fLBJv98v1KgP5UA4GWxishkhZwru5uYmvv/+++LBsfYfZsKTEw9/e3u7rEOnbgy4QyGAAagsGyTAA+1F8PE+oawxbBn9krzEe5umKYrfIQEABkgbmhrj4bjblyyPeS7ZQNXobAMBe6Jk2kdEYVoODg5ae+oTLlhZWSke0MnJSRwfH5fnoSy63W6RNzbMur29LYwNyX3ICEZ3Op2WJVEsH4TixONiPKlnr9cr3hHhKytY5gVe6Hy+ONPDqwHo38PDw5hMJsUjseEwPT2bzUoiJePgvArqz3zo9XoF5OPJwcAZFDMuyDtUqVcR2cDlkCDJht6t0ZuEvVTJ9H/EwzBqLgaL6Dbk1+3KzlyNsvb/Boi5Tr7G+tenVzo8OZ/f510ZBCO33e79RlcwlCzHxRDCbO7s7LSejdc8n89jMBi0DDJLWNHza2tr8dVXX5XxhaW9vr6Ofr8fX331VVxeXsbHjx9jPB7Hx48fy7LYy8vLVuI4DmvtyGbAAHLpA7UALoAD2yqHwMzkkp+2u7tbksHZOh+9QT0ITz7VCXsWM1ATFn/vCUd5rCI1AXcCkEEC21O6Ux2TbZom9vb2yppmG3UrXT83oo1QHeOChXAMtmmaknVtjwQElpEzg40xZHIiOOQrZGrJ1BCGnnbwtylSJoC/z8+yUkf4AWT8bbbF45M9KT/ToQTqDJiwp/zSSrVWaMdjys0lU37ck2UXJcE4AWDZQwB63dRgBgOwKuRjkEGN4kOxkg9ClrSpYo+vs5INxGmDE8qoex7bmrfuPplOp2WpYKaVMbTZichggDZjrBwiwxhRb4dtTCdbidbGLI8fbfbc+T3Ka57bjxU7JPzOIT+DZOtvPjMr+dh7re98nVmmLGO+z3XOsgsLhL5EXiIezkfk1TLi8IrbYTaMuYVTRigPpphwFzYA+8T/6HknzlquDAZgtpFP2x2DBH7TZphpHz1PyBqbxxJi7zXynPLsI4wjFh58plmN/rge1ONBttG0gYHW39/fj52dneK1rq2txbt376LX65V37O3txd7eXnz99dfxL//yLxGxoKS8i5o9KNMtKDyQmX/n2A+JLBFRNk3ifpQ/fUQbLYR4kHjdnc795kvn5+fR7/fj8PAwer37A5Ict0bwPZnsvWN0qKu9f/p3PB4XjwD6LNOECKFXCszn8yKwHjeWL3KqIRvJoLRB9Uyura2tuLy8fJBc8yVLpjwtdxEP6WNTy/bAiMmhqGCsbDhZPkX/MFmZ0Izz119/XZSlwwXIHglOZq6coBhx7ymzpG93d7fIjQE4lCKJquSnsNkRc86UO0oWStMZ6dSZNpninM/nJTvc4BLZpa7kKvCdw3fk39jjQvZYAeENt8wmuh/39/fL8k2KQbZlY1l5quH9LUtmGT2PsqF2X/E/xQ6A6WnrRpZXZyYsoq3zmTvOOYiI1tJAMwPoHxhGEkvxbFkW++bNmwKGmYPoPDa46vf7rXbgGNrxcMKfnUKYYVP8hCnQqfQLJyAeHBzEZDKJP/7xj2UuZJvm0GuNETPY5zMMO2OL8wm4n8/nZX4BJnLYi2cz92BsOZPkl19+Kc9/SnlWAiEvzwwBCjPT3AhwRBs1LkPdeLskWkVES3n0er1CaZM4wa5WEQsaEXqbAXdnOknDg2O6BiqKwcfQcQ1LEI0STY/xbhSP90fn3VD0nc79fgD0q9kEK0THkPH07J0Z3XsieCww5v4+C1hEtMCIFYPBHROG61liyRg2TfPAuLykcn2q10cfZq/Xytg0aM7YxVD2+/2SjORzM/ieMUcerQCRK4+3lS7jMhqNotPpFEaAvA+DwbW1tUKvkqyK0neIx8vnslyZRYpY7HNA2xjriCjK2HOQOWGg6bahgHmOjTxr16kX45cNlgEo/ck89tia3XiK9/97AARPKabw7Wi5ZM8fHbiMwTNgyA4SRto6AUPLuxxCxcgBCBz/Jpy5s7PT0oXWZzBkTvqkXk7Cxc4YMLiPMkvKHLDu5bnYmRoQd7t5tvuZ38g3OgB2zHaD+/H+CY8bDDgROyLKuT+uN84KKzJ88u5TypPBgDM43cE2OgiJwYKTjnLHmWno9Xplacnh4WHs7++XBD4MD7TJbDYrCNLnnmPoSPyhvu5MUzFGunQqA2ZmAdQWEQWM4EmZGXAyWcRiD4TJZFJ2ucI7AgWSpEK4otPplOQP07RMEAsFCNveO+3P+wQ4PGGKOR/kAnqmHuRu5AQeb2zEdrl4i3h35BdQN0+eL1noQwMqflAyGbRAD9qAUGyg/VnT3Me8B4NBob5XVlaKDHrjLXvVyJw9CDYPsmIH7DJ2bF7E99PpNM7Pz8vSPu+b4fFznyDrxG+JSRpsepdAs1R2EPjOBgS5xBCwzJDCJkVcm2W9aRYH8lDm83mcnZ2VeUgowat9ut1u2fJ2NBq1jIpl4jGm6PcW3rJjtQyg2OFyW90GO1rIHoD24OCgAFgzPtmJyvOBd6OzzWDy49U2JMF6u+319fU4ODgojFFElNg49YmIYqTJoel271eGAbKpb64nFL4dAuoDa+H8CkANTqC3iLfnHrGQV8f8mffWL3xHG+gTbCbz2owLjpwBS8418JwCoDOPnwNmfxUYsHD5d6al7FnRcQ432AMjprq9vR37+/txcHBQzqPGCLrDWXZCXB/0xQDkODj3oWzzZO90OiVJcTAYFG/Exi8iSsJW3vgFQ4sXb4UfcT/xnJ1sw45S5J3v3r0rCS/U054TwnB3d9ei75yXAKOBUCLEPplrZWWl7B5nQ4Tnt7W1VWLcRrDuz36/X5Ir+QwvEVBAP75UAiETCQCHAqAPI+KB8kB28gSMeEjHcn1EFHqfHcCcpe5Ep42NjVaWNJ6A6XLH/GazWQyHwwLM/B2ecETEzz//XADBcDiM2WzW2oksGxQniXqOokiapnmQXMc1ZkvsqTt0ZaoZIwTQIUmSeYnRNgDKzB3KOidAEovlrAZiqlkGI9rJ0G4L39tr9ucvWegPe+f+LqJ99kZmCSjozIj2XgUAUCcmG+TZmPGdV2Z1u9346quvYm9vr7Xk1OeURERrLiH/JLgSXkOuOJOl0+k8ABPsA9A09zt7wjQQ9gIYAKhJnKQ9HnMbZcAlbUN/22HzTpBm0ND7Xknjsxyapil5Q1dXV2U+ewdS+hydkXN5LLs5XME1gAHL/lPKsxIIM51stF2jSTyJnJxWQ+n21n19/r8WdkBQDViWJUSh3DPSnk7v96ZG4IzqaJOVRA57UCeDIJ4FA9A0TTlsyMJIXwBqSFq0QszC55IVqL1ZGwFPfgTecbVMCRqdYrxMkXtszGJkJWTF8RLFtCL1ZWwxoqbZavXM3iF9bdni2Tc3N3F1dVW8bR+qwr0AOTL26Xs2D2L3QD87e1pOMMKY4lkBVr3igZyFHBeOWCifHI7IdUYJobzsieU2UrIseh7S/wAGy2lWjiRJAmqdBOkQI3kSb968ifF4HFtbWwU4eI6Y1TTw4H2Pjf/voZiNjVjs6UEfZgDufkd2PDfxJmE87WETAsTYev4zl0jyJmwTsQBaOELoTT5fWVmJvb298lzAzHw+L0l62VtGryI7yFbeUIv3WyfbmNI36Ac7bnzvEJ31px0DMw60yc826+o5YCePa9G/DklYBwBqZrNZCzhZNzBOBulPKc/eZ4CK2TOCBsxUKiV7UjU6jslsJMfn8/m8JHl4cCaTSevsa1N9EVGeZ9rS9Dhrvdm29Pvvv4/xeNxKYmHgGRyv27YAYExpFwDj7u6urAWFFjMI4ARCJgWCP51Oy6ZDVqZWWDXKCwOVJ0VeKQFdCIrMz+M3u8tBR7NrFyi4ae53bzONmBOL8NReKkyQ17KD2KEo5/P7hDoMtEumRE05WymgZGBamKRQ2HheKysrra1/uY9xYWki7IFjovbKMcZ4csRm3759Gzs7O8XjuLm5KWdfkNF8eXlZkj7ZC2E0GhXZIk+HeYMsexOku7u7UkeDAitNe0QOF2JEvA8I/cpGXugDnm3vjSVonguAJ0KN3377bQlBfP/993FxcRGnp6clOz0iWso+G3uD9JcCsRFt+avVk/Gw0UM32lDgAEVE8aIPDg5iZWWlbFXuRFIv0VxZWYn9/f2Sp9Xv91tycHJyUphI5Hx7eztWV1dLCBeZzwCs1+uVeuSdT7nf9ob+MNghYc5G1aFTAyB0OHJEX1jeRqNRCUPBIKLr6ONaqNHGF+cPx89evIEA91FsZzKYww6y8yfJjaPRqMVsmL02w/O58mQw4I0S7IVSyWWlJsD5e57LAEFroixN/TMAmaWIeBhLQ4mYYuc3VDrtub6+jrOzsxJTZwtXPK7Ly8tomqbQXwiIPXd+qJuFwIe/AHBot412LiBNBKbWn/bYa0ChBr4Q7BrtmNuUGQqHL/jfQC3Tb7luX7oYmGbvNPfXsmIgZhnM96KIDfIYQ5QfXpKfjdFDZjc2NkpIIDMENtCAUOqDTPtzNldxEhNJnygO5pfbw/gxvtTBXotlI4+1P6uNidtkOcvP4J0G51lJ8gxkk8Tig4ODcmw3+3V41UXNQcn/vyQr4Po9Nodc7xozYH1JP5nSBkyh8wADeNuEDDlrgvewz3/EYsUBIM8sgh046wk7bDgRNqhmnpBB53rxLOaT5bQGnCg1hwl5cIiZOeb3Zicwe/boVOsaG34zLo+Nu21srQ2WCf9k8PM3BwMgcdNzpmxyRSMeJr1kYaSx7BZ1enpatt91XN8JLSRyEBeCBm2aptCgFFP6JLdBn4CAT05O4i9/+Utr9z0mxd7eXhweHsbKykocHx+3Bg8vv6ak8ehI5FtZWYnRaFSW0TjTeT6/33FrNrvf/tIG23E2lrCRREaZTqetk9/sqYJajWxBwRgOjI3vpy9Ay3d3d3F6ehqdziK5MbMts9liiQ4y4W1pQe8vUcjFYHyg0804GSxmw8B4WPlQ/MyIKG0djUalz/jxpih44Cz9ZC92ZA/vyslxjmNamXS73VaiLUoYNirHDv0esqXJpQAUTCb32yg7BJG99E6nU0IO9GGm3WG6AC/uM9pkfYBuoS54qxFRroexot8Nypnb79+/j36/H2/fvo39/f04PT2N//E//kecn5/Hzz//XA6Icg5PNrYZKLxE4d0OYU0mk5ZBycYWRqimf933hCPx+mFVnFcQcT9eJPltbW2VU2cBvbBM3AuYMHDc29uLXq9XYu4RC0cInRSx2DYYOc/hiuw4IQtmRqmbjWMGSzCi3DeZTOLi4qLQ8Hj76E7mFd+hI5nXhFny3KT/vO03wAe5NesREa2tksnXyvo2IsqmTF4hBphiLLz64nPlVzEDnvCUZZTaY56XERbUbU1oMeYYQ7MI0LI8C8Vh4MH99mjJlD8/P4+PHz+WweEddD6CikJzx1pJmP5hclI/Bpsd0bK3RF0RYKhTey752WYWTEFZ8F3H2mSiDssQqoEOtDYxPE9Q19+TyxPSyZxfumQGy6sKvKQuexOeoNljfOxd2cOmj5AxCkCW/iRc1e12S3wcMGal6aRRDCChGwwq73O+CnVHKQEaUI6uL+OH0c9JtxSH7TJ7kud+jXHyvPHzAA+eK76v5jH72ezBMZ1O4+uvv4719fX4/vvvI+J+rxAO5Vrm+fv5n2ONvmRx/9oJoY9y6MNAJ4c+MC784KXDFkQsNity4qDHDJ2CbnT40PXlOzMDPMMerMNJtiXWf77etoj/MdS1seVa2mP96ZUF2SM3S202y144cyrr51xvy1YNDJhB8dJH+oy/ucYsl9/HODxVfp8MBvBiUWYgvmzYMiVMx9XYAxQRBv78/Lx4yTyLQfXhE+5QOiwi4ujoqCWcDLTXIdNxdBIoj7pxH51OogobZETce2LE82mjDS0etyce4Q+8MCM9jJMHnPaD9Il/ZgFgW2HHpq3UMRYIKdsa4xl6CZoTgjIt6/itEweNxKFgiWtNJpPCktRouy9V8mRApgxsM5Cq0W9+lhWtFSN94ng4RpXYnr17vAqOhOY5g8Egjo6OWqwWmwdxEJUThRg/wISVPHF9s0IZ5NAPeDdsJmWK09SzqV4bpewVwU7R7/P5vLULKCATxWWwiwcMuzUej0sfEUbJ5x64vijgjY2NGI1Gsbq6Gufn5/G///f/jh9++CF+/vnn+Otf/1oci2Xy+ZLMgDfCsV5jjKkf/XNxcRG9Xq+sRmJsOD7bY0V8/uLiopwHgMfsFUKdTqfoGDtUxN4vLi4K8HT4h3Hic0AuzEDEvS4F7NIWxqPT6ZQ6e+WCdafrmMGBHdYMUK3rvY07dWUeI7vD4TBGo1HrWdaDfr7/x3PH0eX9eP92SGzTmGt2Qv0bZhv7iZ43A0l48W8OBozcs0fo3wwIExkDHNFOygFN8Rk0MwlE8/m8leTnTYPsuVA33onhWllZKR2+unp/UlvTNK0tIzH0Ozs7DwSKOtzd3ZVNHCIWGbvOZKbtTqLKCBelSb/w3fb2drx582apITIgoKAoUYbe0AgB9TsxCIQUoF8xXgAN9232JBy7o42uI6AKbxdBxyi+pHdV61dPWF/j4gka0Y67ZnCQwUSnszg3ACXhpXCEZ1iWak81YgEgkCvkmX016FsUGnLm+ROxSN5k2Z3XILtQRyhi5MAemmlYA0EzB+4HKzf6nIJzYS/RRsR6gffCtDVN0/LA8hg5D6bXu9/xkRMXh8NhSfAcj8dFBxnc/Z5KTjCtfR+x0AmXl5clARUgOZvNYjAYFNkjyY/xox/IawIkODEUh4F7CEMhuxhTzw+He5HtvDTPQNa6FcCALHm7aYy2f3OfwUH2wLNOs+G3o2swipyTPGudTt9bfvxOJyBmneokTwNaOxh2hplnti1mAu1E9nq9Es59jjw/GQxQ3HnuuNzpXOti5JaVccTDrTeh+7jeCJ5OtSfgDvdeAzc3N2XFAQPgd3tfAJQ4SpOjXrkmx32pN4PDgJl9mM1mxcti+SDbcqIgEWrXEYOPYDdN06KxeAcTBGEAxHDPdDptHXYDCPLyxYjFEiWDi0zf+aAT7kHoWWlgb9H/m/Z7qVLz7PP3jFkujImVBmNgxA+9T3+iUB175z2mCA0SPeb8sA0vHiDZ1gbLFAAs7BHzCfYAmSZngjFFCTvsQKHdyIfrZqbQ/WHwBXOEt2cAgfzSV8i0xwzAybJNwFVO7LIeAkzNZrPiLb979y7G43GcnJwU4AN4dZtox0syAwaEGD8boU6nU5bmkRAYES0mMSKKQW2apjCNg8GghAQsO6y88Lzn3TyL3zZG7Hpn5gC2kmI2iTFnky575njeMF8YQ/LC0DtmOgxQDfLzfDaDhcxZng1EDc4BQdzDyjBkx9twwzbw2/KVx9f2Er1LfRyC4b3OoYMFAsygZ56zioDyq8BAXleZqRlPHqMwGmpjluOr0IJ4r2zegBHLx+zi9SKUuQ4YsPPz8wfGm7owUViKQZIMa5UjFodZsKWrPSb+xtvCM+Y97CHfNE15B5tOwIYwcfDuvIOgJ1P+Di/JS/us/OlPdqUbDocxnU6LoWB8GNeIRZjAIAfgYIQcsaC7bm9vSyKjT4bM8cGXLNlI8RklG7hccqjLCoO/CY+YycEYbW5utgAr3jf1MKuDIjbwZfkjYw+rRYKT8xHevn1b2CAAClQlgJZxtRH2gUd5eZNzD5Avh8OYI/aSqD+AlLAW+ynkzbVQZCRP0h8o1uvr67i4uIizs7MClrnOxpt3Ota9t7cXGxsb8c0330Sn04mjo6Pi3V5cXBQjBeWaWaCXKAYD3hCIcFyn04nDw8Po9/ulPzHmEe2EPLanZWnm8fFxOT+ERE/LJ31qvWWWig2FKCQkOkEVnYYeAOThvdqTx+HC0MJmGYRwH3JttjU7Hp5TyEae3zyTvyMWDk5m6pAn5505gY9l4IRe7cT6KGaH8fI8oY5meg2c7bjyHAAgdfXGer8JM5ANvgXFFXFDal6//7ZHlN9DR+LhG8WZAuI9TJhaHf18OteGlFgqShJmwHHfiMV+/CQn2UPMRhuAMp/PW3QNMXQmJiDEaC9n63OEJ+0xfWf6NScOmWblOwQExoR7TXv5FCzTcmZ1KA4P5KVBvt7A46VKBgKfKxko+HNP1IhFCMEeGorYDI5Bkj2vPGmzB0gfWoGYOnaYKwMLvBkzejbqVvw8F0Dn8TZ7gQLled6K2/PaChEZ4AAyg076EUVGzsLq6mqh9aG62Q3O1Cig1NSux87OBzLLM+gn6wX6/fda7CXCjlxdXbWOgI5oh4tcnAfizaTsXbugM7wyxGMeES1KHJCGnOOokaAICPU8sg3JISLGBUMLGKAuBvo1Q8i7aKNtSNZbtjvUJ7MigCr3GXXMzi5eu8GGQxk1fZRlkf9zIqd/qL+TjJ1b8rnyZDAAfeidpExheMItQyNU2NfYqNLJ/JAzYI+BLNdaEhMnZC2L5zfN4jAIK1G+57x5Ym6sqSX2trKyUk5+g2b3wDM5MKJ4iBZqhIwtXk0F8cNhHQATKHiQteO+rGc3MuUHStkCR//d3t7G6elpAUNQcrzXiNSThOUstAdPD/YBZYDMcG1eW/9SJU+8x7w/+jFPWHsx2atgEnrpEmwKCT05S9/KBgDlsBH1c3+ibJkXPhoVBoux3Nvbi07n/mAj73fBM2HPeM98Pi+MAzklEYtVGLwX4+D1+05shDXgOn5OT0/j6OiotYTNYNUKjbbR5xER5+fnhU2D+r64uCjyiYzTHt5PItdwOIyzs7NWsrJ1AuNgJ+QlS2Y88ITpl9PT0zg9PY3z8/MYDoctsAjdzjJTnoe8eTtuMzxmWfnMrA/PQXd2OvdJhixB55kOTcBIenkiMoU8AhIYN8KrzBO28DWLYQYTOXHYifavrKyUOcD18/liIzV0PMDSfeUlemYArfdps5kB6sdxx6PRqGxT7nwPh9fsmGVjDzPNvDNAxwGxYwKz/pTybGbAFawJrX8/pXiyZYrfdFXtvqyoUc4oIe41mIBONRhBgDCwbOXqzjWFhUJEkE0rYbARLBSqKaxMN2fvxKwAA1szVkwQC51/G5whXPw2DZaRJe80Wmd8rNxB0Px4cnI9JXtrv9fyFNbAxgrmiH5FtmCZ/FzAForMINWJVjAAOaEJ2h4mBqXhrVx9WJRlFuXAXPBOmG6zY54ocIc8Moixh4JB8Xzwj7+zggJAYrgsg15uCWh1+3kmm4TVclQ8z3iXl/xar/3eisGqHZ+IhbfoHCOzPH4GToTlgd/WMbzD4DRiwdiYyuZedKT1Qx4D6us6oUuz4fZPTReiZxg363n+/5xz6rpaDvg896N1sBk194VZ6Wwrs071fKnJYNbnudhpNjvj5zi88JTyrE2HIhZ7nueBN1XvkrPgI9r7NUcsBtOJScTFjL6gj3IHoUy3t7eLh4RiJZmLTR9QlggBcVeucbxrY2OjrCIAGTPpUCgIatM0ZdkefZL3h5/P789kp18mk0k5rAXUZ/Tt74ht4n3jZeKdO4kMRcdE5Uhd01Kmy+hvjBeZ7UxO+vru7q4kbhFbhhlgjCKi5Q1QRx8U8lJl2aSrXbfsvm73PhOb+PP+/n5hrDqd+5j77e1tbG5uxv7+fkS0zytfWVkpHpQnKwwU8o0ihSGLiGLMoYPtvRLT5SCU9fX1wj7BInz11VfF80Kp+tjv6fR+mRjj7MO6aHvT3K+AIRYMGNne3i5GmWeRZc5n5NIwl+7u7spySzwph78AQPQbfY0HDHB30hwMBYfuGJgCNIi3Z6alZsCWyciXKih1DBD1MbgGWDH+6LaIRfhvf38/9vb2ysmE6Abky8DQQJTnGAw4hGAwgCwTBgKERESRAXTK9vZ2vH//voyvQSCePUd9GwiyVbC951pSOcDZsoiTiOyRc2GgQZsB6xjknPHPONCWTqdT5BvgbycMmXKOjoEs9eZ9XsmDvZtOp0XXmhlBB0wmi2X03qb8qU7YszYdsufq+KNfRqPdqEyz8r3v4RkZkfkddAidY5SFN4QBthfF0kJ78xhj9uj2XgQ+8QrgA7WUUaHpONc3IsqzOp1OS0j4P2fmM1HoM8ACE8aUnfvFcVcjRPrFVKtjWA6zmKFxPNCCT6yVuC1/o/RzPVxHK6jfU3mKV+hxtqyxzM9Z0ySF9vv9spyVHw5yuby8bCX5oFBQEH4XshcRxSiiEEmojVhQ+Bhq4rLT6WKHSi+RJTTAnCIcZ+UHmLN3BB1pL9MGIStNK65M7fJe2jGbzR54mXj7GH6YgpzIynG2jjFj7Owd8l6MhOcACvn3xBRYD+bYeGY8luWfMGYkRb9796545DgYZhVwiCIWe3LAMlmf0/9e4reyslJWe+BkoDscg+/3++X8AvQu9WZObGxsFEcNMACQyyE6nJHMhGJUzcYCCHGiTK1TrMft+LrPDTwM2KwDqaPndQ4ReE4YCHssaZcdLOwQiZReaky/OLzxufKsTYeovI0hHi0KhM5AkGoMAM/gNwNlUJHpa5QOXqeNPstNWAbY7/eL8et0OmXToEy7z+fzMklQjngjGfGynBBGYHt7O3Z2doqyQ/CzR0cs094zRhlhspcf0V7PTV86y5d6+BCNiCgrFkDZ3oqW72AxQN5mOBBAWAgm1+3tbVEYfJbBD9f79+XlZTFaT6WqfouS5crlqQrf1zGOyC3MDQCBvSl2d3fL9fRrr9eLw8PDB5OcZEPLRabMGbvLy8uyvTOy5xAALA+JoBxEFXGfx4FRrVGUZKU76ZT+4/+rq6tCy+dzE3iOaVM+IwwHOKHvDLLoR84AMfULcICVojA3bm9v4/j4uPQFORuAWOSRvQZ88JINXB7zlwQFdoIcHmB+RyxWqxhwoTsczgLEoUNpm+UDhwWHqN/vF/2H159Pf/RyO8I4b9++belrzxVT2NzPb4AAehGwjS5mbw6zF847MbvjOQbws6whuzbsdqBs65AR5peBPMngEQvwwY8Ntuuxs7MTu7u7ZYWM9TvX2Jmkr+l/2ojuoW8AzxzvfHBw0Npa+rHyZDDg+LSNPAqIBpueWgYE+EGZ1da1ewLa0CJ4u7u7JSxweHjYii9xLjZU6fr6erx586aVkMVgMVEmk0mcnZ2V3QNdB4MBlnfhhZHAZyPMfY6le6lJ9raY1HxvjyUDK4TS3jvJihxVS0gE9Hxzc1N2zzJ9tLGxUYTU/cI1Nub8zvE1xhZlZGG+uroqVGzO7v2SxTHFzDhRlnkF+buI9nahBsRWquzjbsoPY7i9vR39fr81howrHhRyibIhtBURZTmqvVmULn2PQrfXSwgDMJDDNtSxaZrC/jCunveEqnq9XklI9Fp1A0szhZubmyVpzOOR2ZOtra24uroq8syYMJfy2fQO252dnRWP0vOE/QmQaZY55nnlOcfzc9jgSxbrSvoIOhh9w3WZOsZY4P2SK+KVIABRzw1kZ2NjI969e1f+h6b3KXkGA04ExEkhqZtCnW5ubuLi4qKl9wn5mIFCl/EMdJa9b95vR8lLcM0aOI+mFn6hjjCn3EebHEb2FvfWyxHtLeZxBp2USXI6m+rZwaL+jJntqMNpfI9+cJgYxnB3d7eVUPtYeTIYQLg8WUwrW+G6YZnWcsdDbaH0TGl4ItNQYrPExsj639vba+UDABJQqEwAG2sjK9MzFgYbbRQHcaHj4+Pi4QOCWEvKfdm759kGDrSbz8wQmPIinu8+RFl7UntcDGb4zu8wmDAQ4RlMfAyDY8LEXAFDk8mktJ96saeBabaXKFZGmWLN4KAGFnIIy88CpZNTAyi1wuJzxpt+5PNlXqk9KGSSYm/QzILDWXmFgAGZFVN+rxUsBpjxA9h57nie4C05ATGDsX6/HwcHBy3WCIOM7PhsBgAKz6K99hj93vF4XBiADFwzw1Obo6Zo3baXKA5x1Jyl3D50kse5271fdjgajWJzc7M4L+gWxtMMkBkG5ITnI3uZObG3j6Nlz5b6oEPy3MggsjZvs+62DMI+erxcf+YK/UUdfER4do4otX5HdtD/hDbQo57DtM1hrOFw2Foq6+JlyLmP/Zt5zhyi/9H5zpv5XHn2agKUjj3WTBvlTuR+/9AQvNTV1dXY3d0tNIcNB8YQEMC1u7u7JeYEpQUadba0vSxnpzqJI9fJqI8dDG9vb+Pk5KRQNUdHRy0Fz7IXEvCgOp0IQ/HmKm/evHmQPW52wQqRXAjOpPdYQAd5UtBWQjkIER4EfQt7MZstTh88Pz8vYQ4UO+uYOWESb4uQiFkE+tqK5SWKk6GsPGuKPrMCVrT+HqVLWzc2NuL6+rrswkZyJQoiYgHKoNoZ14j2ah1+W0lCGfI8wJuTbqkXhpgEwIg24wNosNdDMUDFiHC2OzLAPDJdyjv434yfQW3TNHF4eBhbW1txcnJS6oqcDIfDEgNF3vf29mI2m8Xx8XFZCgnI9x4dsGCDwaDoBJbO4syY4aSOdnTcXy/JCFAsp9TfMeeINrDLAAY9NBgMImIhg/QN3iU6zAARcGXPF+Npxohi2SHM4K2tMbYwPg53GBAa3GXQA8jwWCIrfD6bzYpHbbBHHag7oS6Wcg8Gg7I1M/Lgdke0NyYCKLOcEuPLuTWWecLW2ITJZBKfPn2KyWTSOrOEa8lVM2hClxsIOSTAd/1+v+yH0+/3H2w9vqw8ixkwEsweRQ1N5clFgyzcCAxeP8bOCBFmgLwAEgJJukKYMm1GBzlZIytd04im9B17ImPetCIAiMnkmN0yL9geoAWU9zkORZ2pLx6oaU3oZBQ8/cyP21vzdPje9CsUFdSWs81hBMghGI1GxQtz7MurGV4yV4Bidgrl+msUvWXeXkFElPFBqfFelCzybRkgVpoVlZ9thYch5n4nENKe7I1b1pxjwnNyyMN9lr2h/DwrO+4xeCHu6r6AdWDuAMgzS5fZNN5t45gZSOLVrHZh11IbGs+dGiOT5eali3VVrqcZqxrD4/5yn4xGo5a+9aZmZgibpilgNyKK04C8OPEton3qJ0xOdgZrnje60mAEOciA1WEQnl9jqGi/Q66W9zzXmE/20K3HeQfPALwCFABPBsrZm4+Ill6vyW9mO2pMtq+xnORnUZenOmFPBgMsw/CRvtnbA5WZFjLKNIVEbHBlZaW1sY+9EtP8nIdNRnZOhMGjpU4Ie7/fbw2svRUMIZ4PHj1bFyOATgTEE+EYVJaQzeftrXqpO4oX79CGHzr5+Pi4AJ1eb3EWO4lOd3d38enTp4hYLC8jm58lke5rU/N4WXjHFkxoWZ6FZ89ObzY6AKaTk5PCirBXPv3niWQk/dKgAJlzPN6UO31mDyDfy3UZ9DEGvV6v7PeO4UZpIAdv374tcfPpdFryXbySBTllox8X6mbak1UA3piHudbr9crWswaEe3t7ZQkaBtOrXWDXnK1tgwpzxMZDzEXkrNvtlvvpo4j2GQ14rF4WaNCP/OZs7IjFdtmAWNqNDNPWm5ubsu02gIv3EobwczNTZEP3UsXgM7OqzDcbN0o2huiRu7u7siyTM+9Ho1HRvTY+vV4vBoNB0b9elooutUcKuLNjZuNXKxjViCibl5GvgJzwjE6n03LamCPUl1wx3juZTMo20/TZ1dVVsWU8dzgcRqfTKfrMeWQwUcTj0YVmmX2IHs+l7sgYeh+PnbrQfxFRvHiDEPrRZz4YLBgEIANc7wTyp5RnhQkoVo6fKxmtAgi8VIhsSIMBJ0hAsxsMoNAt8O4QU2cR7ZPT+B6BNg2K8srZylCuDDgKe2VlpVybPZcapWcEa8qe344Pm+kwCjXYsqEnJpaBWC10YzTsZYIkWrGPPdfyHTtosdadtd1G9NQne+AvpVjNDFjx14r767H6WpYioigIyxvLgKBMSdqzUULGI6KEEJ76AxuDPDqnJq8Ld2KT2SVnoNvL4RrG0yFAgB/KsZb3YKPP58hFNh5ezYJHZXo365lssDNDicLmxFLq6useYwR4x0sDgYjPAxLakY2tjUVElHnOGDjLH7mk79ExjDvy6fMJDAa8r0sGARgu9EMG4tZX6Bg+x8GyAWTu4JxY3gDmzotCVh2C5X1mjHOIxOBrGTOG4c3UPX1Bv7uNWbdjJyKi9I1Xg2RWwA51Ta8hLzWW+XPl2WCAh+P1UAEnoZhy8r2+n/hKv9+Pr7/+uoACBM3eszcEYumRjbo3bcBYm943MsP7J+7vbXRRmt6DwEd9RizWM89ms0L7Eo/LlHxOcMoTzedNE+5YWVkpiXhMNpA57UC4ifllgbVyR8GbHp/P5yUREsr/5uamHP5Cf+I94fFeX1/Hp0+fWnvLM9ksuNQL4Xbm7kuUPBHn83mLMXnMI8xjhlIhl4PxBRgBJJEjM15nZ2etibm+vh5HR0dFFpwbkxUq7TDNDaDodrsll8bH1zKXkG/mKIl1ADt7lYBovMimud8eG4aBpFOHMVBcnqdmxag/v5EZ4vkoSzMu/MZj8/xGTzjeTY4ABssbQznbnHr6fu+eaYraSZovJbsUdKZ3j3SdIx4mDqOHmcv0L6AANubs7OwBaEfmM1O0vb1dNk4zWMR7rs2niPZ202Z9YU0BljBPfHd+fv7ACDKWZj/Racggy76RG+4nvMwcgo2in8iTcNa+22PWw32EfkO3sJ1xNuLMbedS8C6DBjuFvCOHwhkD9y3yjXO9s7NT6vK58iwwkOMXCI5RCr8zos3MAgKxvr5elj+QQIgR8qR0Qsrq6mrpcLxbe+QZDODZOwEOugi6yZ4NBndnZ6cMKPVGcTA5mWgMjPvE/2fB8UDn3zAUFNNOgBorQsbGfW1AlvtmPp+X5D9iiZysaMPutdnHx8cxHo/j6OiogCgAEoDDxitPmpf0tPI4YEiRQb6rXc9vsz1cY6YGjwZQABjodBZnZlxcXEREO3GKhCU8GpKHLCs5f4T6A9xQWMgD78BQm7mirl72aVnzO5ArQoMoG75HVvHgAEgoPTMl1Ml9jEEwOEOeATI8m3FgA5p8mJh32IyIAjTQG1bWlkt7cTUWs+Z1v0Shrta9BnGeY25rRHu7Z3QD/Z51NM/LLA4A3zsYUg+uYQtsF56XwQDjuLKySPymOPnWVDq/zWDMZrOyGyf0PyyZ+47xBJgzd6h3RBQda0eMeyn+jL8ZF4Mz9KjrmplxjD8yn5fm0081G2EG0vOMd3At73lKedYRxjTSySNU3EitNrko/p6GYdR97K8BA95r0zTFu8IoQwnyvIgosR8fdIRHSOY7XhGK0fkEeHxQjB4UGzcrSwMGDyDCb8XeNE0r3MEEQnCywgcMRSxyBpicCAYAIU9wlB3elZdBYgzo3/Pz88IWsCoABoF8AoCKY+LU25MiIkr813V8iWKlaANuJP45z89gJoNi06CMf9MsNopBMVDMLrDdNM8ndGZlk72TrNgZdwBFxGKJo+vjpCa8eShgKzKeS0yfo2m/+uqrEiK6vr4uh/4gL7wbuTLr5bFgJQwb/0DjY9hNb2aw3jRNMfJbW1ut8GHTNCUHgvybtbW18h1tJPRIv1g2/D/j7vp/6eJxzyHQZXkuEdHqK661sccQRizGPtPTNl7IzsXFRXz8+LFlaHhXZrXsFHlDuk7nfqMcDoZjl1iWhPv+PNey0xMRZVdNxpAN6dCjvh7QDuDA6YmI4ihSP9sq6mG2K+t49EA+ECr/xl6aVaPeHncbf0AMn3ONx5sxJtRgoPGU8mQpt+fsOKA7igF/LB5nr5VJBrVK7B7viMkM1YkBsnDTKSjSpmnip59+ipOTk2LgV1dX4/DwMDqdTlFerKF34hyJVoeHh7Gzs1OWiEVEeXaOaUKdWXnDXlA3qCsbDYQI0OFBZtKS3Ifh94RzJjaAAaPAM3gHRufu7i4Gg0FR3rSBUAnLBc/Ozlp5AU7WYaxN9WYGwN4m9XX+xksUjBzeCXVybLxWaowGcmCvAdlkvCMWXjbKJj8DKn4+X+zaiJEy2Mpr6R3fjIgyxiQMUQdAJFu5okwiFrHMfLIic5jwEBQxCgkgeX19Hb/88kscHR2V+wE+TvyiHjayo9Eozs7OYjAYlP0ZkJ/9/f2yIdbm5mZ1cxrm1/7+frx7964whvP5PI6Pj8vRx7QfY4HT4WPDH2OsTIO/VKHNyK8drgwGspePUYuI4gShL8xwwWShF/jcbc/smpfFMt+RU0AdxgyvHV06m83i8PAw/uEf/iG2trbKjp0sCbcutEft+WaWh+RbwIzPz3EiNIAHpwbZY74i+5ubm9Hv91shZBtn9Cq/zfbCwqL/c/09PowfetssAuPDvcgrxUAJR9dn4+C0mfn6XHkWGMjGt6Yk/bv2Hc+4ubkpxpfOhO4hZokyQUk55m9PjE7BYA2Hw+LtM/i8n4NY8Gy8OYozOJ0Z6/iWqdiIaCVAGvV6Jy0mT63YiPqzLCT0IX8jXBZy+tMZs9DCUPu02QmUbBx0dnYW4/G4bIaRr83eMQbRY1xTXCislwQDrlvEQ0rYSib/73g6k8sKxs/zZzyDyWpvy9S/62CWwF4615iF413Os3EMkn7HMGRKmb9z3ZEnlkvmXAfatLW1FW/evGnNR64DQAJoDBJh5CxTGG8OB+PHWwabAcyZ63hAzjNy7kJE+xx5+nKZ44JSXQYUvnTJcpZlgTH2ONuzjWhvWGPHhPuRzfwOsyqeDyQZ8x39779roUTGmW3jCeVQJ8aONiAfeY8b6re9vV0AMWDA+tFzhQJgdhuRXd4NAGZewcJxf01PIDMGURnMMLcN5gwGsm63vNZK9vwzm5Ln7rLyrJwBK7PsVVkRmEGgcqZjb29vYzgcxmw2i42NjdYaeitHMw8YRs4yd10sONPp/clr3thiOp3GDz/8EJPJpHjIpgU9OKCw7e3t1pHG7G/gvbL58f7snU6nnBzHQGCoZ7NZQb6s18eo0jceTOJqFNgPCwptnkwmcX5+HpPJpBh1X4NAc+ogRn40GsX5+XmMRqP44YcfWuuzs1fiiWOK0N8xDihv9imwd/hSJdcxIh5MsGUggHun02nZSjgr3ohFchzPzoYXRYH3D2tAf+J5IJtQm7AHnkf089raWsm3AQSyUmEymZSlpwYnVjwUe514TsPhME5PT2NlZaVs8oUCf/fuXRwcHMRkMilL10g0JVRADlBElKRTzlZwNjqhiDdv3sTOzk5J9IIZYFnr3d1dYQ69vwgK1PFoNoDy8ipAtvMf8tgzjmaRXqq4fhm0kEiYQZcNgBk99wOyZ5bV4N5GzKDUMXX0MIVxQL5hCcwW9Pv96Pf7cXh4GN9++20J2SCPMFxbW1slR6ppmtjd3S1Jd9b75Bx0u92WzGEvyI3IW2s7PEv/bG1txerqasmnAvBOJvcbBJGfg9dvyj9isfUwDB06EH1pMJT1N3WwHYHBwP7lucp9OdRpHf2b5Aw8Fltd5vHZmORr8Tzw6FG0pmxpMMIE3YQQutMw5r7GlDZhBgyhlXT2TjgMA7oJheIdu5wx7xgvCp1BZ4JRR8fgMLiegNSn1r+e6PZOPUFZcuO4Mp4ekyvnBQyHw1aeRY2CdD8Z4dZK9pqRhZdkBpaVZX2di9tE3+TQRy0UkkEH78sx3MyqmRHwczM7gydlWfOPqfA8fjXjgYJBBvwD22aQA1PG0jRWJ9T6DRbO2fuAYRSo5xpKF2CdGRB+IhZHpZu+zTKa+8D1q8mDAddLlyxXpqT9veUoy6ZlMM9RDKxZSINFxsrhsAxyMY4YYww2TozBAKwAetTjZnDH823YDKjRv+6PzApEtHdlNaNM3wBKWV3iPvBccV9npsR609fWWIDHdCH1sY7JeXg5aR4dDyNjRvBvzgyQ3Wla3Q016qIgGMRurAChstfW1gp64nnETb00kIKxQvnhidmDgt72QHki4O0TVyIL+vDwMDY2NuIf//Ef482bN7G7uxv7+/vF+3ciYQYDMB7z+WLZnjPDneRiNO4JyMDTvxhtsmCdZ0DiH33EZiJ442Y/nBdxenpatie9urqKwWBQ4r6MHZM5r5wgfuwEToOZvKSU4mS2lypZ4RtM+ntT3rV7IqIwPchAxGLnP57puB2TdmNjo8h1VjjIFe8gsdU0IewUuTRcu76+Hn/4wx9aHjMxd2K7jEFmK5BBlovRdrwjy+GHDx9iNpvFzs5ObG1tleV7vV4v3r17V+oIGCfplx3umGt8B6PBQWKcM8Kzfcoe/dE0TVndwrIpZ6TTbo+rQVVWkIxbNmwG2d5z5EsXGxYK/2enwB61kwJrINWsFHKxtbVVMvyRLwAZyXUY4kyJ84NB9cFV3W63PHdnZ6cc0sM27D5oB9lm3Pf29loUu2P2pvnpF8CANw2CDcLrJ8cKD92AAVmDWYB1sf61o4UtiIiSDEv/MXdms1lhqmljZqQjHh6oxhjhMPOZ7Sh9Mp/PC6NL/93e3hZm4ynlWdsR8xsBjGgnZrljKdkjNKXC33RORuHOK3CH+X2OTeadyTKzYAMA3clzWZPZ7/fLuQfQlRY8Z4UawWYPCNrNx4y6H2sedmZEMOymvbLnwsRnG1av9uAaKD2WCrKMkBDBYDAo6N3GwuDOFCATwu0w+s1tyt7Yly41FG7lH7F8h7R8vVG446sOcdmDBSQj39zrpUuE3exN+L4sswBZPl9bWyvHd/tUSnb2RNmbHqc+lEyFI8dNs9i5kaWo3Ns0zYPdQwHq9AlzxR6ODQmMAKEHFJnzKngeCpwQBobESbXk63g+1lhNzzX3g5U01/8emAHKY4CaPshGpsZiMQ7WZWZmYGNMiXtvDXQMY4gcovNYwcEPQBgg4KOISXD1OPI/+hed6lVUAHHGynM0hyxhD/gss9WZVXAf2tv2vEHf0c/Q+ga+tkl2nnmX9abbgeFnDgI8nN/GtdgGAJv1znOcsGeDARtiK3fHlOhgTywGyB4NVAZxLe4leTCDDLMPdI4HDNYAFEindTqL7RzZnAXvg/vW19fjT3/6U2xvb5dtLfFOMiOQqVfqyeAiNMRHqbd/Y7gtiPQn6JXPUHpN05TtNelDPC3AAAwBGwNxdsDR0VEBA95AxruS8U4E3/ScmQ2ugxFxrNkCjceWY4svXWxgM32Y6deINh1oI0acMmLBfgCWItrUtY0P74NVsneEIlxfXy/L5vB6c7Y24ACPi5Ut9rDcVj43kM/xSurG+2GV7KFMp9NycBdzh1UEtJ+tVQ3WuR6FTux4dXW1sBgGW7CCKNmIKMbGx7XSb36/vUSvumEMCTHkUI37xh74SxXrWvQDOo3ipE3CoQbyNm72KNEtZmdgn5BvZAgjBFijbuh0h5w2NzdbZ8hg8AFyGeQS8zdoRX6tT7Ixt6PiceUaQLfZoel0WhxDJ0VGRIxGoxI6JceLumJUHZri3Th8bOM+HA7j6OiolSvmZevWOS7ZQTFw8HJl2o8NZE5zjsTm5mZZWulj1T9Xnp1AaGGyB0hH58xSexw0ynFzlvlhuCmmdnin6XUmhD0zZ5M6gQNhZImhT3WiwwkPoHhBphh/FD2eV2YoDH7oE3IVWNqHULgdUFlOhOQe15EJCM2PJwUV5XgsIODs7CzOzs7i4uIifvnll2L8/X7Tfvb2EUor34go2d2eHDUwEBGl35etpPhSJTMDVkRWqpnV8L0eZ0/AnODD76ywKTY8GH1oVN9vr//NmzctetLLBvGcUN7Q7DnGiZImbFeTW+oVEcWzA8ghY71eL87OzsqyXGSRsSbRi/p5u2rkE29+Z2cndnd3y7UGUs5V8BGvpqIBA7nvAXnomey0WP5rXmJNVl6y1OTSbIWNJPO7xnbwLN8HYO/3+/HmzZvY399vUf45YRnd6vnD/1zT7/eLQ/X+/fsSNlhZWYnT09OS6ExeFztK5lAAc8z2hjbYUGaWx3lP3hfFxhN5pH/wrGFRyX1xKITwHzocG0dfD4fDsm09AIrncI8TVw1y0AdmUbNj5fs8vjhl3mCP5bkAraeUX7XPABQ4nnDNGBrFRiyoPhRoXp/Od+4wxzgZ7ExF2wPA6EClIvC9Xq8wAm/fvi1xRa8C6PXud9dyfNOsA+jS9HumDy2kBjAWBgALCh2PzXSRQxBunykhGBUvASRD+/z8PIbDYZycnJQ9A+wVZSVoofQYezysjF1Xgzh/l8frJcuyUEBWsP7cnqH/5nkZ4NCvzAu8cO7nPj+H1SmOeVv2MfIoaNcvIko+AEodg++5hcwhO4Brh6F4lj0Pe9qsrIEB2N7eLtvEGuBT7MniTSJHEQvvHrCdE23NIliX0CeERJgXPMu5N543HiPP55oMGMz+HgpGAIBTAyb2/HPeTjag1ltma6HtyS9BnwIs6TMYU7OVdn4mk0kx/IynWV6SCPPeG9kwYidGo1HLM+a5bgcOildo4bgaDEQs8qFszAmJOB/OurtpmvJcjvTOXvtsNmut4uKdtXw6O9URbeYw61Tkt2bQ7ZhERLEx2D90y1PB7LPBABXNnerKI7SgPzo8IopHkTPuSUZjwKwUIxYhhQwGHMuGksLQrq+vl/gUdCSJSo5PsmQFcMO51hjxiChKM69cMNUcca+I2OCDzH573wjn3t5eWaaIkh0MBjGdTluUaUR7TSwTjR0UR6NRQbSEB3755Zc4PT2N4+PjODk5Kf2DsjADY0ExKMsgD/bCtCHJOUwq6ugDRHjfS4YJGB8rkJyvQalRdVmxZkUIHQgrtbKyUpKxsqGZzWZF9kigW11dLb95F8YYIIsB7Xa7rRUxmXK0IqZujLfzZFBQw+EwmqYpSh+FyjIrJyDiJR4dHcXZ2VnJ6eH9bquzvGFJmNPElPkxgwgLcHZ2Fufn5yWJDMPU7XZja2urRXvDSmQHxP1Ce+mP2rgyRv6d//7ShdAQejLT5RHtzdty2+lzt9kx6263WzbZgamxg0QCH84EOVUbGxuxt7cXEYuwJvrOewIAOnHOAJWwAnZMACa2KXjaW1tbRa87LIBTZIY04uHprIwhYTTkoNvtxu7ubnS73Tg+Pm5t0W0gTRkMBuUEWxv46XRaHC+HQsx02Yni2Z7D2AaYXdhpZJ12uW3YhIiFnGLX3r59Wxi3p5RfvYA2TxAqCZLy5/a0fB2TM1NXLhnF8ru2K18WGGgSlI9jrV4JQDwcI8YAGWnTBrxsK5NcZ6/jR+AMbOiLPJntmdu74vmZJvTyKowztKx3HItYxNX8bBT455Sd64Riz3G7XD/fx3cvBQie0sZclnmIboOVLWOXKVZTfDwXCg8vyXkBFHvmGHhCCjn8FvEwjsw8pA0GRJn94W/Ar8/acAiC9mHQI+LBnh3cZ2NjByEDZy8xNA2a6xexCBFYhwDIkS97XK6XjQdjl39qQCCP+UsUzz17xrlYDvmf/qnNU/rYhpwcDL/XuhvdmvWSqW6+4zmZIeVd9sAjouQUOKfD+yTYYaQ4NIIxdbuzrHC9Qwxuo22UQwKAEoMB6k+dyRlA91Nv94HH0vkIEYuk+vF4HOPxuNgnO4VmEdwvNYYGYJ8Z0WXlyWAgC5ANIYNuCtseWMQimcr00enpaekoviPeaqrZcSkodTL+2WCE70y1bm1tFe+bOKbPBOCew8PDmE6n8fHjx+IBb2xsxNXVVYxGo9K22WxWDuxBYBDaPBGyIiZ2Q91IboGew7tEkCaTScltwBs3CmWpFUs0fZjQp0+f4uTkpJXoYrSL8nXuQ63UqEV+e52r42D5vmyMXqJ4oixT9DVDVDMclmeyriMWhg2Z29zcLPKJ9wtKJ0Zr5J+ZCsCAjTEyDIWOHDi+7pwd2oqHDrh11jPzDObr7OysLFGFwQOocKY9cglAwPuzcQGk8368Q/YWQdFfXFyUxEeDTM8n2gHbQj+63Q7DzeeLZb4GTl7+iZ5wKC73G2P/krKL0bIXyby1nDIXHZpyflU2dHx/e3sbZ2dnRRdFxIPtfJ3h75CO2YbsHOXQEbro48eP8enTpwKWaSNj1u12y8ZnrvtgMIhud7G0HaYsYqGTvHlRRBQ2y7ppPB7HaDRqLRd3aIHEc+rBwWwkXh8fH5ftsanz2dnZg91dzSBTMsg1GED2aQ/Az+EwM0QGMNzv3/TD27dvn5yz9awwQS4WMBsMD5BRGf9DO9lYZfrD6JYG8jdxEZSUla3RoONbKFQjWyv5iHZMkTaYJgLZIjyAAUIBjrVl+pbvvYxyWX/yrppCRJBcVytEjiM2VV/zsp5jIP0MK83aPfk+X/fSHtZTS61fsqwYVNEvjBfK0+EqfsMCcMqe2SK/y0o3e7D2XGwMzMp5zgHSUT6mKrkW5YWyJYmKNhCaur29fbDVtr0qt4XvzQyYyge82MP0cz1XmTv+IR7t1QaWdQMLG0d7ujWG4PdUanM44iFLGPFw1YANdq1tXAeryP4QEQsv3R5u9mgBkI7LMyYYQb5DV15eXhZjTL3t+HW73eLgWF8B4pzI6n5ApxqQ8HlmTDC0zsHI/Up7YFrx+s/PzwsYiLgH0aenp8UOmNXw/MyhHQOdHNd3v9hxy9d4jPkOfQRAt439XHn20sLMCDCArmQWYIqREo2koSgiFNNsdr+5ydu3b8vSI3sO+/v7sbe3V6gUPBiUw3Q6ja2trdjb22sp0Gyor6+v469//WuJ2XsHPyYI9UWhGqFmpcrA5B97LQgASvr09LQ1uM4PwHNkv4W8jHA2Wxz3+ssvv5TNhKzwrQhM3eX6e2LVPGHG3Eo/gwDe4VCQaduXKF5uRD084Wo0nNuYfxhnH6LjzU1YVmUAaO+KH+pm2aeeXE+9ALe9Xq/IKMuYbOBgkuydWK5cH/Jh5vN5oT4ZZ5KQSHJETr3hGP2ABx4RLaAdEWVeIs9e+gbjx/xeWVkpK2l2d3dLe31KIcCKHwC69yMxZUxbZ7NZyfZ2YiK6x4ABw2Lg/dLFLIBDl9YvXEexHNv7dGgHvUdS3GAwKJtJbW5uxnw+L0mn8/kiDMZ4AH5hL7L+4zvmFfubMGYRURw1Ti1Etjudxf4wPMceMXLtrX6tu2azWesIZK4ldwY2CoYL2RwMBnF+fh5nZ2fxww8/xOXlZfz0008xGo3i9PS05Nn4HTUmER0B48C7ACbZTqJrnTifARzzNzNYtA2QXEuW/Vx5dgJh9vRMO2XD70rUEK49BxSqG7Kycr8Glpi/Eefe3l5J/GDCs0wJg8lhJ+5Q00gRURSq10OjMLxO1QmSDLR3JuMzsqf5zKxDRBQlCIghPOBnm+5xcgmTF2Vq1I+y41TGGuXpOqHIGRMmmg2jf/Osz1GmNQ96WRjiS5UaO1Ez8nxvbzdfy/NQynhSTlwCnJotcG4Bn1vBQ3NbSfNMFAXGDSCKxwIgt0I0WGWcmavkLODhATIBblzH3OJaKFY258qg0fqAvqM/oHaZw96cJWIRFjHA9bJLJ1QacOAZ5XFifOyR4eVxPWNjxmuZon6pktmWWhjDoH3ZM9An1lm0E2+bI8ydLL2zs9PKsufdhItIQLQTZoOdY9w4MaPRKD59+hRN05Rcr5ubm7JjnpeE8+wMfpDVDEBps5ldvsNWsHLCIJD+JXwFKBiNRvHzzz+3wABzGiBvfeGkeMCAvfsaQ8O7sxOZr81jnfsaxzDPhaeUZ+UM5MF1RTCGvNihANODZhZ4jpMITeV7HbKXSzXNfWyM2A5KAhqW2E0t09nGmYFnG0f2O2Bw8cCMpmk/Qu3+od4kOdHOjNozCHIMyMyBjYgNEDTuaDSKo6Oj+PDhQ9lTgLa43YybjY9pbt7vtiwDCBbSDAxqWdyUrBS+ZMmMh70rlIWNNNeaiYqIshGK+8t0JEacTGlk0wmFvV4vrq6uiiLM9CFy6X7mZzgcFroVJT4ajco4Ns39Vr2ZfciyT0FZYQxRYhFtMO8tWXkGOwDi8fB+59A41MXyLyvflZXFskUD4Vo/YPTtfVIMdpirhDk6nfvlYDc3N/Hjjz+WOXNxcVGy4F0vAzJ//lLF+tUgsqbTbFT43ImmGfxmoE9iHbpzdXU1hsNhS96RWa/ucE6K62ZmIL/78vIyzs/PS65WRJRVCjgqBoQwR7BE6+vrMZ1OW6stkAs+81hilM3Q0SfIFAnYl5eXMRgM4vj4OL7//vty3D3zgIJcsrEPDqRDWTnmb7Y0O06eR9YLlk0vHXX+y3y+ONnXjitb+z+lPBsM1LxdC4KTVSIWx74iFI5vodic8dg0Tcn8Bww43ggooGPYLQvaamVlpeyOhmK24cIQepMeqEmON2ZAvGkENLDrnXMeMBQo/YzgXDAWrhO0rdkBCwWMAJN1OBzGL7/8Ev/n//yfuLq6KoksOdZcExiEywlWWRFSF9ffHqjrbkHN7IsR60sUT7aI9r7stAuFVovTc41zTyjZI0NmKDwX+q7X6xXlYmNKaMgAOivUDCrNTGSAznIxPzvLoPcZMJ2b5yXxW+/fMRqNiqK2nGFQUK4YC55FOwDyLPXiOcybvBLGICjLtpmTDJaR6aurq/jzn/8co9EoTk5OypJcP9Ng2czAS5ZshCMWsXXKsjBBRLQMgT3FGvNFYvRwOCzj88svvzzItQI0wrrm0JodGeSbfsZZur6+Lnr6+Pg47u7uiixQJxIAu937zbTW19fLNvEskfUSccsF+o624rixM2YOlzKXSMY+Pz+Po6Oj+Mtf/tJik3KIpte7P9OmaZo4Pz+P8Xjc2hQrg2uf8WCHOIM7A4CIhTPl/AwAE8+B4bEz8JuAgUxfYvDsHRttMfEtwFacnnSmMex1moIx7crEZXCIQdEBo9GoxPqplzdmQFHReVCkhAsYFJQlYMB1dCZ2xP0Aeuviu7u7cnSsE65Q9rVwg70git+RE1pOTk7i4uKihEU+x9zwmdvBmNIGxsZK19+ZKkSxZy/Zz7cBfmkwYOXA59QRI+SM/AyKMJDMAQMee6VXV1cFMCHb0Ia9Xq8cAmRDb9aCSZ5BlduA4fMBYNSzaZrY2dlpzZfMeCCjXgmTwb7fbS/ThyXh1TuLHXm2rNBfZveYV/S7PUiuxZOzsaa/AfMkcbHhFgdxeStwMsOvrq4KK0DfuW8zQ/Z7Ka7bYyEBvrc8+B7T27UwYnYCnAjHuDlfw3UxG+oYPc8x00OyM+NngB6xsBesaJnNZgVUjsfj2NzcjMlkUlY+kAPgnVJ5DiATeaKetA05IhcLVoBNhGAVPHeQX8swbbScmvHLQMyA33MwOyMeD57pVXT0sQGCAcHnQruUZ29HTKfN5/OSvc//bJqCAXUjsuBlutrX0oDZbFboKvYCQMnSuUzuTqcT5+fn0e124+LioqUIWHEQsdj06PLyMi4vL2M6nZYEEFPsEQswgMG0gaDjPWgI22g0iu3t7Xj37l189dVXpd5N0xRq0hQtdWTHKCf7mC24vb2N09PTuLy8jL/+9a/x3Xffld0GLQQWROqIAuBzlDIAAyGPWEzcDMAiotTHBob6GYjYq42IB4r3SxbanhNqYHNA0MjQaDRqGTj6gDbAEEREkSv6g+RNjCUAISKKd4PSYXxgbPCGqNfm5mYZH8YMOXCSKzI8nU4LSH/79m0ZK9OGjLnBC3Mq05Vuu2PDUPveSwHlyMZXJIHxDLNG1AM2bjqdxvb2dgHHtJVx4R0AeWSKXTZHo1F8//33JfyCTmCJJGDf255Pp9PCBNLmiN8fIDBDUQOyEfFA12YD79Cl8428AoPfNlQRUfRfBpY2VGYUI9p0d57/6Hyuc70jFku4HcOPWMgNsX6Sy3G4dnZ2Ym9vL/b390vyn3UybAKbaTGvyNkij2E4HMaHDx/iX//1X1tbJ7PhFnPfjiLLz3Euc99ELHK+HOYy08I4eQ76OblsbW3F/v5+RCxCaoPBoCwRhRHwyaqfK796O+LsOUU83NwlNyazADUKziABtGhl4k5FqLmGd3rDHXvw8/m8BQY4mAIlk2NCTiDMYMBCTTtRMpeXl9E09+EOdpJiOSL1dpYz/Wk6DjTnZBDyFIhtEQt1zoUpJv6nXzMizT9c70laCxd40mdAUCuZzXmpkj19JqIpQ8bWHk9mFpArJhnKj/4FeKAsMMqALoy5AQoGHyDIsxwKoL+9qZXDYfZCCCPxf8RiZ0+2sXZ/UBeyt7OcoLzyNuL2bkwho/CyPsj35fmedQqABRob+rppmjg7O4vj4+OS2AVjxtxmfnMf89ygOMtFHvP895cuNX2a60O/4rA99hzPxayb8zOzfGRdQMn9aJmxMXSGe80jNjuU6X3kycaSPWcwxk78tsGNWGxY5dg972E+wrqyvwxzqiYf7jfX2/qW9/hdtNdziPY8V85qY5bnY228lpVnLS3EKOKRsByFAXd2MINg78aC8hi6BLFFLLKMUQLn5+dVYcQIZ6Hhewz9cDgsiVfeY9olKyquMRjIE4lr7u7uyqY/l5eXcX19HTs7O8WjMV3EQCG0Ozs7Je+g0+kU4aS+Nzc3cXR0VGJaw+GwoFqe5UnsWDN7fLO0hmtQ8C4YObwIMws5c5b+oh/4zXuJf7M87SVKBq/0OcsAI6Kc+IWceTLZk8GYw0Zl8MbSPhLVaDPeGcyZ13PjnfjdV1dXJcHK2e/UB5k3EMzUOUmKyBOsAUBlfX297NUBHct1VirUiZyciMUWtLSBrZdhDEicNPuA3MGY+DwBKGO3cTqdln6EFaPviDX/9NNPBdybKQHw0EcuTlY0Y0axYl9mgL9UseHMYSMDeDskLmYxMU4OGWTnzoV5z3P4bdBhY+7nGORxnfs7s8IRi5UGOEUYaoMQxno4HMb5+XlxuJjHFxcXLWOLbLICxp4ygBpA+f3338fp6Wl899138dNPP5Vwrp0FhwLynjHZiTPwpP4+uM36xU6yxz3n5NDvbIKE/m6apoRMfLy5x+5z5VctLeR3bqxfbEEybV1LfKGhdDQKDs8nIkoyEu/PBp+Y/93dXVEydAgUEL9vb2+L50Cd3fH29HK7cxtz/6Ds5/P7hCu8Mzba8KRF2GiLE08YSK5jQxB2HSTnIQOwbPT4LCeleVL7f55jIXdba2yQv3N/IOQogt9bMUVn9qfGlvAbQ+EE0symINf26DGKpmnx/FHYOZktK+o8BqZTKVyPFwzogSLu9XoxHA7LGnLvApcZI88Hgx4bF37TdgNQx5tpF+DCiU8GmXyGoibBF+NPYiA5AD/++GPpOxsgwpkR0QItHkfrkGVz2te/RFkGvCPa+QAGrMvYV8f+83N5Xv48X5MBP9dYj2T2xX3tPq0ZQL/TOsTvZ7yRj+3t7ZhM7vdLYHdMwnfIrAGqxxPdenl5GRcXF3FxcVGSS3MbXSfPeYPxzGxwredUXuHh9hsgYO9qrAH6hXnkZM0s139zMIDR4gVketJYKyVnPqLgcjzbHjkdZdrcqKzb7T5Yi0+j8XKm02mhAzNdNZvNCmvgeFktLmpDnQ1C7X8PeAYLeDOTyaRsqPH+/fuSlU02udGcB5t6QmENh8P4/vvv4/z8PE5OTloJYNzj99uQZDRs9sPLVSKiZaj4O4/VU4SNvmHSvmQCoZUV9bq7uz/u2R5TprDdXiYdxuv6+joGg0Hr/Hco6tvb2wL+MKZQ7OxAaHqfpVIoOWhNgxSDNINxG2FoUt7lecYYsP3sxsZG2WTm22+/LYyEc2FoM/PZ3xmMANad8wILQZ1ms0UiLjJD/19eXkbEgl2EuYP6JzmQZ8GmXFxcRMSCiaTOTjp0ghXX+Noa0LJcZ6P1JYv1jA1p1hURD/MKuJbv8vV8h5xYj+Q2Z+BhHWAgYs/f9/E/S+5ySMDXIzOEzdx2A3F2Wj05OSlMWLfbjXfv3sX29nYBARnwN01T9hI4Pz+PH374IS4uLsoS7fPz88JS5QTfGhig3syTmkNBgbXyMl70L8yF2UA7e9lm2l5m3eZVNZkZW1aeDQasOJ3sAao3ujH6y/QJ9+U4EPQ4g81nzr5G6IjFk0Bnz9zZywCF7Lla4Tm+ZM/HNA11XGYMs1cBE9E0TWtzGJ/fzVJEH0hhoww7AiA4OjoqWdEsM7GyyMjbQMuGxeCsZqQ9SQFdNTDk+ta8CCP5lwQD/KYtTBjyTWqKNiN+5IE+Y00yyzlRMuSHmKq0jBGqYT+MiEU83waend88hwgrOMGqaRanWqLsSHRyiM6JRldXV2VHwJ2dnXj37l2sra0VYE0d3X7mhRWZlRaGFyCC0qfOhCJQ4gbyzp2YTCZl34zLy8s4OjoqwM2KzcCJJEOzb1kPeblijQXJXu1TParfsmSdYv3KZ08tmd30vLA+5NpldeH9mQnI4bX8bu7B4HGv8wgiovV/biuyb+BMuPf8/DzW19djZ2enxUBlhjoiyqoElpqSfzIYDAqLzP0ZDFCXrB+Yd3a28tJCy5eXPzLffaJiZrvcD2bJ+QzZ4FqHzZ5Snp0zYMo/x30sTN1ut3jfRjRcywCZkse4ueMcp+F6Iz7io3Sg0XENKbtY8GveQe1asw5uD3WjrxjgTJHikTJxOMgGwcDoRkQ5fOjq6ipOTk7KDoNeH40iy3Wz8vaksgf1mFePYrWXnMfY/ZD7iX51TPulSpY9twHj6UlaU2YRD/cTX19fL8fr2qNhT39oO5/6BwDgt4+BRk7wnmzM+M5yTV1h6TzGbhOfoxgBCBGLnBzqzJptK1En6PI/Sp1+NUNg5eO5SL0d7uLYbsAB7AOrfXIozCWvZ3fIzX1EP3luuZghYN7y3GVg+UuWGjOQAW6tf5bJse+zzrA+r/VTfnd+jqlxyy4FY45jluVzmTORv2P+AXhZ3XJwcBCHh4etXWd5hpPB5/N5WYlyfHwc5+fnrR/2pqn1QWaSMlCohQ5r10Usltt6q30Aq+2WHebsOEe0VwtlVvCprEDEM8AABixTFnmw6AQGDOUT0QYQnU6ntcGEkZ7fYfRHglXTNGWzCZSvqSWS8LJwZ6Pmd7v+j00sG3o/w/QyVLzvw/MDrTFgIFmUt999fn4eP/74YwEDl5eXcXZ2VpJkeJ/XeEcsjAEGxwIE0nWCS578tI0+9kY6mQrL9/hvAN5zBPK3KPn9nqh45paTzAxQADckIfX7/Xj37l10u91yljlGjkShTqdTdmrjaFLqgPFznQgP8Jvxy6wWc8obwHhtt4EGz2b5pJfV8h27G+7v75f1y7Q5Ly9jTFG6s9msAAPa59UymQ0cj8cl1PXDDz+UvRmcX2AjXANqUMgYbepsFoO+Msvl59qj8/wGDJBY+ZKA1jom665l12ZDtOxa96WNpg1NZknsvBmQ2nlDjl0nxpAckHwNesVtzXLudhN2AwRsb2/HH/7wh/j222/LNsN29rzMllDZTz/9FCcnJ/Hx48c4Pz+PX375pexTYQOcHb5lDpCNstvjeykAcjZSIhTuDcv8bG8Khj7w/IB19rto99+cGbChg8rLaCVi+XKH/LcrnhGv0VeOJ/mezAQ4r+ExRiCX2qDzv+tWQ6n52tpPBiVZ6VtwADUoSKho9k7ISxytpHNIIyuFbFBy+z0GFjgDlNqY57503Vw+p6B+L8UKMMucjUqm7wBmhH2QR4NdNsfKiUz8n8dl2ZgaANa8L4cSckayw2zehAWww9xGofAONs9iVUxmEWhrxEJBwQQQ82eVBIlamdWr0Zo1ecq5Q8t0UM2De6w4fGk5eEl263N19ljxm799rx2zZSXP0Rrwz/2xzNHy9bmuj41FlveIRTiMOQGjurm5WXKurE8dojMoBdyx9JZEclgoEllzO2o6wfWhjvSh9SCf5e+Z89bpuTzG0ri/bEMcuq+Nx7LyZDDgjT6Gw2HZ6jSivRbeNJGNUc4mtzGHDchoCkp9Pp+XZCp/52SiplnQLcQkawg31yGivTSuFofJ92cDy48ZEH9PO4gtr6+vx+bmZkkqhGYm3vTTTz/F6elpfPz4MQaDQZyensaf//znwoJkQz2btXekog0o7Ux1MnENTmro1xnZVrzLJjP9gnBbOZhV+tLlKZPCE8njj+GEZvazWPJJ7szKykrs7e2V/nHi3Ww2K7tF7uzslIRBxoCQAjIdEa2545gqYQdi7o53o+QYcxgMtwMvnl3bOAwmIlqJur6+1+sV1gB5B9CaccKwk+cyHo/LmRl/+ctfYjAYxNHRUUli9IZX8/m8bKrlxK9ldCveocG8adoMoJBz5oivoU0+c4Qx97h/6fKYzBrEZYeG+YducTGIzQAre/0ZRNV0iUOJmUnIYBnj/Bi4iFjE03mu7cDq6mrs7u6WZOy3b9/G1tZWbGxsFJnkZMLd3d0y3iRhX19fxy+//BLfffddnJ6elgRClp17rizT9QYCbMZF8d4vliUDAC+9ZVVbrV+wpQao2XlDH1AHr7Tw558rz2IGsgFyhfK1GdkZuTZNUxqXkapZhJpQZi/Byrk2eH6WS0ar+bnZ2GXgkEsWaBvMjCRRnnmgaC9sAHsMeG11nsQGYDWFtawdtetqfcQ4LYvr5ffkcfH3///CDEQ8pEfdx/ztRNOIhbKrUYQ8I8vrUz1WPyODK56FAnNeCMrX9bKnzw5/lkXHfM0iAAqoq+lQ6wfAACETWAAYAf6HicDT492EAyn2qGgX/1uuPI8ts8tAaE3ezeItY7heqtT0C39/LoTw2Pyt6cJfU5bJcs2brn3/mG7iOmQFY8/KLHbcQ169qqXT6ZRcFMulV6wQaq7p/8fq7nlM+Zyey86JnYLaEmwzPhkY53rZGfQ1TynPXvzthmfPDwXF3yAjJwuiPLgWJO+sZystb1jS6XQexO9QUhaY2gBRLHQZUdOJoDZ7BbQVhWulkfMSoIgRyq2trdjd3S3nsUMl47GxvAzj//Hjx/j06VMcHx+XDFd7jLSD36Bnzu62Zw9rYlp5GeDJXhJj5nGj1IQt18lsyXOE8m9dzPzkejOuNYqPzyyjjs3hidbQvEMBzAW2RIUBshzm1S6m7JE1FJoTjiLuZfLt27exvr4e29vbMR6PW+DYK3DsvcMMrK2txdu3b2NtbS02NjaKoiUPgUx9lCpemI0nHvXHjx9jOBwWo8+Srevr67JPAMA2ez4R0WIysrHPY+Rwifc3QIdYx5ghy5na1glZdj9nqH7rwnudX5Iz1Ck1kGS9hCzaIVv2vvy/DXoOodgOwJJlZ7FmXHmGV3Fl58bXdLuLk2z39vbi/fv3sbm5Gd98802R5dXV1ej3+3F4eBgRUbaaZ9XPhw8f4vj4OH788cf4y1/+UmSVJa8wUu5jdHpEmxnhN2wtBfnqdDpl23LmHoyZGTr3n+2XxzL3pYuTmh0qR+88tTwLDOTBNNVmrxfFg9dkwaCy3gjE3+MVZArMHogT/pZRJ/55bEL7XnsVzu53Pdx+3+NiZAqViQfmJYVeZ+qNhdhhi0QvUGuuQ/bErSyY7Chbh3S4f5mgZA9kWVgg93vu+xrb8hIFGTHF55Lr6GU7VkyZFeMzx+Rr3j997yxfXweoqAGN/H4n41Ga5j5ERqIitKW9a88h7gc8oEB9RPPGxkbZnRCFxvbKAHAbKPIhLi4uyp7uZ2dnMRgM4qeffoqbm5uyKZg9MOab9Yf3UjfTEdHWOZbNTIW6/R4bj99jbNoyQ/tSxc7HsmIAUMvJyte6ZHnM92XA5O+zo1Sb8zXdyf/+zg6WC3NobW2t7NPByZyEupBdQO1kMilbUl9dXZW8laOjo3IQEZ+zRNVMmMGqQ34ZtJMHRLGOtfPbNE3JtyOZPPcn+sQs+jLZyyy450X+/inl2QcVGQh4PSUGOk/UmoduIfK60YhooXnisLlRntgZmdr4506KaC8zy4i1pgAMOrjf76bO+TNQIQJrKgsQgLdHjgNHYJIsyMEZ3iN7mfeevXDHfHPfPfZ/rf3LhLGmlHO/vZRH5VILaWVPsEaj0scoAChKG9SIxdrpDJwYk9qmTl7HbAWKEiDHhOvt7bK9Ku9heRWJVG4b1GhEtLZCjYjCABCDJQyAQqVNvp7+GAwGZUvy8XgcP//8c1xdXRVmYDAYlMxsVgrQVm9e5Bi1gTh9WRu7zHJkMFGbI/4758u4jymA+HzU9UuVDCApGdTnOZvDB3bQnFj3HPCzDORnnfS559S+sw4xeIdR3dvbizdv3pTfW1tbcXh4WEIGeMcAANion3/+OS4vL+PHH3+Mjx8/llVZ+bwK25bc5secQTN9mUm1brZs17x+xiiifdruYyWPhZ/5mywt5IU2MKbRKDYOpuqy4Njg24PlWhqRs5R5hoUYoJIpPxczDXlgn4L6fb/pNZSXQx48E4+L8IDBAPVla+Tr6+ty2AqsAGAgL7fMKN19ak8LY0PdDIBqqN+f8Zxl6DKPOeOeJ9HnvJkvUTJYND1MOxzKctvcd6z/Nxigj0ji8dzw5iHcDyt2c3NTnhcRLTnm7ww4ePfW1lY54ZKVC+xeRgIV908mk7Il9u7ubskPYH4CANgvAWXqulEIx/m8DLz///t//2/JC0CGB4NBeY8BE4W54xChj5pFdvNWxV6y7Lnh0FzN03Wx4kYerBvog5qh/NLFQPExYOviuWh96nZnOeeZ2djldz3GNGT9mkN0y4yWn2MdZzCwtrYW+/v78ebNm9jd3Y2Dg4MWGLDdYQkj5w78+c9/jvPz8/jw4UOcnJyURNvMslFXzzv3T267DT1th6Hw9RkMuM9qz3PYLJd8f2aMPbZPXVYY8Uww8DmjmYUoC+9jz3EjHAbIMd2aR5xji1zHcy2Imeb1b3speC+5/rmdEQ9BER4XQlH7oV6EB2AEfPwq2yvX4m9ug/vB9bMS4PNsnC1EngDZ089KwXXJE9vgwGGgWl++RKnV2ZPWBsHKzICBSZ1l3G3Nq2zsgWC4AMvQ7jw/swsRi5PXyEHBe2W9NfsI+H488oj7I5SJU/K9Db9PdTNVigyy7I9d2obDYRwdHcVoNIpPnz4VkOCQh0GM56lBAsDZ3rpBAPXlt+vk7+h3y7yNfB432A9WZRgs21l5irPwW5XsuCwD5zUgbg80h7YiHh49XHsfny1jH9AxGVzXgEpNR9Ta63oA7thYaGtrqyS+elt3yxOrVE5PT+PDhw9FTskPYLtwmDeHk2wnavYs95WL+8T7h7jUxij3C6wC4eU8NgZaWU9HRGu+suvnU8qvYgY8sam8Kepl8S08qUzDIKwoHxoFBWhv3MrBwu56+sxoCt6Z6ZzsETuGn0/LqmWIZwGhTpubm+XsemhYPDgUEAJ4c3NTllP99NNP5TcUK4ktFAsDoAlFh9KlPrSViWIwVKO0s6HyxKQ/c+ghI1F/hudqBfR7KTXgFBGFnl9bW4utra2IiFYbZrNZa6e+LAP0rxPUaktO88EppuCtiFgu2DRNoULfvXtXzhLAmO/s7BRPFkNHvYlRkhdAG2kTOQLe+wDgAYtxd3dXdsP88OFDfPr0KU5PT+Pnn3+O6+vrODk5iclkUhiKiMUSSO9UOJ/PW0sGGYecFEeilROEDXDyKhfuZd7SBu+34NPi0FPMMfIhkAknjWZm8kuWGhhyXgm/s2ODPiWPxIyrl54tyyHyvLATYSAHkMKwmgLPzpzrb92d62+AApu0vr4e79+/j729vTg8PCx5AuhY5hHye3Z2FkdHR/Hhw4f4n//zf5Y8FrOuzEsSvGETMhvn5MZlfW370el0Wrk0BsA1AGSbyneAcoB/1uk5r856u9frlTyz9fX1cvbHU8rf5Cg5C6gb6b/p4JpRMGrNIIHfNQTLbwus61MbOP7OqLc2SLksUwhWPl7GBaqFivUqg8lkUo4oJiRweXlZTiTMiVa0yyV7uMva+lg/+LkZcdbCO7kuGQjk757Sf791qTEA+f+a55O9pNyXKBToagxVpp1zscJ8TD5zNrDXUGNokTf/HxFFiWIALJ/Uy4ovr/qg/pwJcHd3V3JZzs7O4vT0tPxv4Ow9EjJYN0CinXnu5Xmey2Pf+9nWB7VxY6zyd7kPPlefL1ky4M76sna9HYAaW7Xs+RGP6zuD4MecJf+d9UxNb/kHMLC6uhpbW1vR7/dbSwnNLEUsZJhtrtlUCJ3q3fhsoN2Py1ig2jyt9bdlBxk0o+N3OQRh5wSj7rBadjhz3eyEex4Y0HyuPHs1QRYEPMA80fjbDQGJk7lpxGol6gHOFIfzBHI8L3s1JOe57jXjyd8WFN6bhaU2ENPp/cEu+/v7ZckgmwD1+/3Y2dkpG7aQxHV+fh7D4TA+ffoUf/3rX2M0GpUlWGx+YQHNHpCVrL0cvsse+2Ogh/fALEC9sQwse3ez2ewB4ImoHxWLAVi2v/yXKJnupm9qHomT+kw3z+cLxonJyiFZGEMvq3L/O6PYABdjy/Mx3NPptOSXWAa2trZib28vdnZ2Cl2KR49yJNGKd02n0xgOhy0PxJ4l9eX0PxsNvEfk8ccff4zRaBQ//fRTfPr0qXiY3W43dnd3o2maspcAGd9mmbzD2zIg6oLMUVePR2a12MbVK2mck2SdRb0xFl56mBX6Y6DuS5SaNxnx0HHy9ejkiCg6gYx5h2Kzt59BLs+LaJ93wQ9bZW9sbETTtE+prfXZfD4vcyvPwRrwWl1djTdv3kS/349vvvmmJA6yAsZL6ZC9q6ur+O677+Lf/u3fysoB9mmZTqclHEYbrTeZN+7LZWBwGdDhfyfIop9pb+3aiIVMw4DAULuOeaWdwT7bz5sJwxY9pfwqZiBPZIcJ+L72YyVTo7scb7UiRWANNPIzTT1xX2YhspeXP8+gomY4awaVe4hlefkgyBZ2gDZjSEajUQwGg8IQmBWovaOmRLPX+ljb/Fl+du5D34cAOqZei0n6WZTfg0J9rGTjZLmsAQYbCRQsMuiJ7d/uL9cpe6+5Hw0CM+sESPM4AIT5nmx4v8v1z2AEBcnukywXvL6+jrOzsxgOh8Xgux0OUeUYMp9jRGoeac1rzACY3zUmIX+fx8p/myb3Xg7omZps/F5ld9n1eb7SThv+rEtclukZvuMHObPs+/6ntGtZ/3Y6nbJbK/kC/J1ZAQDhaDQqRxMDYsl1MfDxPOO72m6Nj+n7p8pE1oXLbIvDtxhwnELmD31tfcF9OWyMPvibMwNZKGrKJ6IdBmDCZeSXUT5HtdIpDHSmOzzwvJPJHdHO1KTj+v1+yzswdZMFMmfcu+0gbWd6ZtRMe7e2tspyra+//jq2trbKBi7O8D47OyvHZ3LIDTFjKyVTPZ7sWdmyEUwNTHhyW+n52YwNh83U4mQeAxsXrqevAGM+PveliutuVJ2/4/cyuXDuBRv7wHLt7OzE5uZmMTBe0se7AMEGlfbW83h6eW3TNCV3YHNzs2T+5yWNrFgAgM5mszIGtJUYLwDU3zH3yCu4uLiIH3/8sewTwL3kJ3Ci4Ww2K6sS2DSIduDdMHfynM5A1X3AGCDXvs+6iD6i7w28cuiRA9JY873MiLlPXrpkb9uOUo0hyCDTbIE/z31MyY6a+8jfMSdg0pxT4Lljg8bzGGODNTsaq6ursbe3F7u7u7G/vx+7u7uxublZWAGS62A9fvjhh/j555/jw4cP8fHjx8IG0B92Mu1tZxa4pguyg+j+y8C+01kcCpYBBvPA+RVeag6oOTk5aR0Q5+2L87u2t7cfzAdkn+2+n1KeDQb8vxvjpSv+sSeQvSzWWqPAnBjEM7OnxG/e7wniBA6UKJSQj4akE91JOdZSa2tEtLweCs/C0CMEW1tbJTyAcGCoOUubrTGvr68LgjXwcVstjKaLrBBns/vd7AAVNaNnQOGlWExcJ2FZ+GuG0glW7hcbtJcuue2WmZqXmv9Ghh3aur29LQl2EYt9/mF2oOwolisUAl5VxILe9hkFV1dXLZSP4R2NRmVXQCf92YDiUXkOYvi8xfX5+Xmr/YSIAAOci4HhpK1ra2st4857SXiyI8A1+VqDXvonG6HcZ/aeGCMMA+/1s2pemJdQZtbq9w4GIuoyGtHeijZfWwNGfnY2bg6t+Tsbep7LZ35XbQ+DZcAlAxPGjOQ+NhhixQx6fW1trczFm5ubOD4+Lsmt5+fnLXnL7zArxpwzGMh9nI1tTbfae3e+GKxJDXA5BIvsktuAMwUjmMdwNpuV0GDTLDZEsl3LGyI9Vp6dM2ClY+rFKBVhopjOyMKQ41OmNWpJgJ+j/7mOd2ZjlI2on49RpJ7ZC6atNiiAmY2Njfjqq69iY2Mj9vb2Ymtrq2w/y6FE8/m8ZGRzWhaAIC9FyQg1x4ncHgSFw14AAu4Lj4Xb7nci5DlxrTYxTDfSV45NobSXKeQvWWponjpn1Oyxz56C+yQzVSgkx015tpF/pq0BU/QXW5WS8GSPGpBnYJC9aww+yjLi4YY6ePeAFs8ngDnzm+NmWZ0QsTCyKDHCEQBh2koba3kCNrTEnwFc6A+HPxiHrFTzc0maRNGinOfzeemPDFgz8LDcLDNgX7rYiC0zWDXAn3WYr/c45z6o9UvW/9QFeccwecxr4ZfH+psfh8IYR5/4icGcTCZxcXER4/G4sKzeqI268U7nj/iAuGVOj21FjSnIfe++4z0ZQOV3OunWICwiCshl7P1eb1jmMXaezm8CBmqGyWCAypJQlIXVcTl7BAhTRpcIANQ6HUFjlwECd4r/N+LN1xgMNE3TOhAoe7gMgJfTsAPcn/70p0IXk1BIiGB/fz8mk/sTCYlpsWELm19k7473G6l76RlCBYAyUs4ewrIJmMfWMamax4SS5jp7uaBgEl/yblu/l2LK0EY9IlrLUvMEjoiWvHsesLbZ3jJ9BBgwO+XQgalC7oUZcKFe7KyWFTOeBAog08h4QDmMZPk2A8Xfzm5mJQzhEWTOu2TyHKhjqE68Om8ohnfO/V52i6wBOtAFzL0cbqCeljtkEbDaNE2LtXkMDLh/XqosA6MR7cPfMiu3jD3wZ3lDssdK7dnoLMYCAErisfs2s7/52dm4Mv8IB7PEFX1nz/nnn38uCdifPn0qDlYGihELOwTAzY6Wdau/y+AqAyMDS4y5HSSHea235/N5ofKxGwbW9Jt1FoDIfcp4GAzQP3/zMEEu9nCM3HOnca0HJWdsco07vkar1Eo28Bkde8A8sTLVn5Fh7sAMNigYQWe3OskLg4knBj3LskIvy8rvysDAbaSensyepJkey6WmZIyGLeC5n7JyyZ/VPOiXVKhZJj0xsxzweU2BRrT7inH1cj3T2Xmr3KxoItr5BAYK9mhMe2cvtcYYMX48w8wbBtLxXe7JwNGgaBnT5Oe6XaZKHQrxdW7b3t5euQYl6mdQn9lsVsIY9JW9LPrCq1hubm6iaZrWDoqAt8y01cpLy++yeYzus0wYIEQ8zB2ozVOPWwb/tbogT9npM8uQ589juqhWH4cf7CRSJpNJMfrsMshGbXmLYfrF/bYMgDw2960fa2xR1jN2dB06yXtqZIfEz4h4mPPifnQb7WT8Glb22WCACexNK+wBMXmXoVkbyFrIwPeYGqs9KyKK4kAxGz3Z48odmuOdFnLuh77kM7efusAAsIQDbwkvCGDD+e2np6dxcnISx8fH8enTp5LEQlt417I6Gr1yDUt8QKGm0EhaqwmEn5kngxWgwy2fCw/YIHK9/3+JUgMBEVG8Vk9IFGxWFDZcfEemPXPBoA9j0+12W1ul+vn2yPMyRsaPGH/EwrtyrgIyhnwieznMEBGtffZpC/dnBeUxp72EKGoe6vr6evHi0QMZVDM32Z774uIijo6OYnV1Nd69e9dKtsorEsyK2dD7b66JiHJiIixLRJQwB9t/X15ettq4THaeqkx/i0IfZEOKbrPeyjSyn0EfMdY1kLgMCPiZDkNtbGyU57vvYQks39lQZfaT99IG7AjyDDikL0ajUXz33XcxGAziX//1X+Pi4iLOzs4eJMRSLI8GF5779qitc6lzZjhrY+KxYu6SO+DEeEBqfq5ZM/rNvyMWh/the2vMAKvX0BlPKf+uTYc8wMu82RriycKQjaGVla+peckIqhFTDZnm2E/NS/O9GeXluvIdHe9NMBzHt1DABpAsmHcAy/XJfVKrgykpt61G8T8GCJZ9v6zkfs5o+fcUFsjeUE1mll3/2OcoO69T5zd/c40T/Xx/nj98nj+zIqVk8ObPABN+hpUVRsEeGLKTgXG+JnuUvsYefaZYDTqz52rjhoLOIBhw7P0DnHRleYd9I0xgtos56ATP2hj/mnnxJUuWY4eFzFZFPDTwWe6z3vbnudSuqz0vP9f6Ytn7+M4yhNzaCeFMFxiB0WhUErBrda7pV+rD75qHvqwvfo1MZLvG/56XT2Gqlo1rduieW89ffTYBXlDufFNFEYs4peNSFmJ7Lo5p1gy6G827+F0DD9QRz6UGEECxCJgVOV5vbhP3Eud59+5dbG5ulq2HWROLcru6umptiAEzYBSLV9U0TcsrslK10PLj/uO3QVHuk1zcVi/3yf3siZN/ssfMYTjE+ojPvVTxMidT7tQ5g6vcf7nfs7fKenxoZ+7hb4e8MgDheRhRPrdRRgaZH6B+7uMdngNsOGL58GZEZpVWVlbKccVWtsQbYQ2sLPNvtw9QgfGG2aDPT09PC3MxHo+j2+2WbVO9aoFC/11dXRXjnj1O2g4zgcF3zgAxYt9vAML/zEHmw0uuJshy6rloBrYW7iJmzLV4kpTs/dZ0BsXvNQOZv4tYMKtmNWgD11MnG0Dm1crKStlpMLOt5NIcHR3Fd999V5Zoj8fjmM8Xh4E5H4KxN9tacxqZ4/QNnyEDvnZZ0qX7ldUQ3IfcWb6t35knPIecCfexlyszFrBybJ9es7dPKc9iBtx4ez4UC6YblD3GzA5YEPK7ap5xviYPnL83A5AHFENlxUyne4mIPaXcDnaB8/GZzuTGCLEZxmAwKNmv0PvUHSXtHICMVpch3Br6zuNS678acFgmRDU0b5BAnzk5BuT7kkxBBrFPqUsGOfyuebs2fLnYUOWkPYNiZCcrZgxrxGJjH3JU7E3nFSQoZOQvIopC5F3sNdDtdgt45X6AgJW/+yF7eE7I4zeeu4+AbpqmJM1aZrif8+UdjgLoQOv7kBnrgKa534OBbHOcFcCAQS/JyZkyr83BlyzLZNW6wXrN8pkdq6xLbIRrz4+IqjxS7Hzw7sxW1IpBI/KdHS0vp/MPAOfy8rIwArA83uzNzAgAw2yT6+2/H2MV8v3LnuE2eskrczHn63g8ckg8r9DiOwM55jrsNKHK7CB/rvyqfQZQAEY5NmZujAWl9hx7HhRTlvYAKDXBcefU6oqCcakNbvbGa99HLNYqo6DzZLOR4FS3i4uLOD4+jvPz83IYjkGVN3Bx/xCKgOlwWSbQPKfW7y65r9xnNXD2uf73MjErqpcEAzl+aSo/09k2ohgYe4uWs9XV1djZ2SlMCJ8BBtmmlT7wu9wfviZvguPrPC9MiyMXtJHEP+T7KYbtp59+Ku+zZ58BkMGjw1GZBXG+i52G2WxWlLe9btrsbG+DAdrG+7KxyfW2/OVtjW0c/ByDnNqqo5coGZC6zTkHhuudt5UdJM6sgJmp9WGNIXhsFVYuOeaePW73d3ZGbPgjFgeHEV8/PT2N09PTkjQ4Ho9bmwZlx2R7e7s1hwCovCvX2/2MLqDeNfYl/++21Gyi/2aeLpOv6XRaNjer2U1sEM9iszgYbrZM/5svLXRFaEzegMeDwTW5E3xtRH2vfyaqs32zkfVnfkYWNO7xOn4r+Fqx4C6L4UBd+TlWbvaMyHjl9KyLi4tiNDxxaiddwU4QyniKp/IYOs9KJQta/mzZde576guFhUHyuu6X9LAAA9Bw9LnHizHI1By0fMTCa0UuNjY24uDgoCQHMQFJrOIZFCf7ZZnNnon7P4MzJxfymzaZZkfRGCgADiaTSWuXwvPz85hMJq2118uUiN+5bDtjezlW0FZ8zCEUmdmDWhjMoKpWN7/HzIvZPXul9k5regkW4SlL736rUnuvQ12wGFxLH3p5Jtcjn+wQyZbSGbibKaHQH/ldEQ/nd80xM+NSAzjLnoWMsSJkMBjE0dFRnJ6elnMlnJhnnU9bu91u2dYXh4vymDF3u5EdszD5dzbqzCP3C2GsZd67wYHtqxMozX4g2ziTzHfm93MYgl+1mgB0mAe25ll+zmjnz2vf1zzLWkzdEzsPEL+51vFxGwIENmfDZ6HBUHvwWIsdcT+Q7DJ4cXHROjozG9vHjLHb60G1x+fwRu5DK8Vcf97l37n//b2VZwZ8EW1PJY/XS4IB+qkG7Nw/bpuvJ+7ItT5i1OAAAAtT4IOLauOQvSwb7PyZ5cQKL/8YDOR2OOscpUHSlfemgI6tjVlmBRx/zYbYXpABqvWBDRu/a/OZ6/k8j2O+h3bmueb7/L/71H1Um1cvVbJhzRnyXkpKOwCq3uOe5OXM2pl5imh78ZkNqI2B5SDLu2W0Bjhq8+Lm5qbkVbFChpMyWYWV7YR/HPaiDg6t5XlWyyXI18xms9ZOtxlEZXvCeHlOLgOXjANOg+uYQyasIqiBLvRTZtw/V/7dSwtzsg8lAwGoZNOKfJ+XNUUsaMNlDbJHamFGmXnJhTuVe21Mjb4ioiSEOQyQ64eyN9plt8GIe3rr7Owsfvnllzg7O4tPnz7F1dVVK4mnBqT437+zAIHQEcYs3FZi3GO2g/ovo+5rzIu92gwGvHFOzdviGS8FCMwILANNOcTjbHzkg9+bm5vR7/djPr8/o8DGlGVQ6+vrsbOzUwy0Daez4h+rUwYDEW3qMQOYiGhtRV1bqeL/eQ8Kje9yyQYzG/qsSCOiLGuy0s9et2WW5Nkc260Z8wzqXB/3B3PtKWAge0/uv5csecwMqugrL0/G4EFBI6ewdtPpNAaDQUS0+9zGzLkVNX2dGUfPm2Xj4s28eJdtRwYOJAoeHx8XILC6uhpHR0dlCWFOmOYZzlvieQa6a2trrbnnHTINruivx8bGxpfPvCkXutn94/lqQOw8oGz3bOMAfrAc6HLuxy6RWPhUvfvs7Yhr/xsdPRYDyV4mgpuvecoARCziYzUll5mAGjWVP/tc8SBaKWfljJCR6AK9XMtAzUYz9xV1zYAm1yMbfxvzxxR8/jvXa1n9cgExP9aelwIDNWObjSGf1Qxsra+yLGfwELE4gMuHNWFk+Dt78Vk+syKOqANh6pWXOVo+8hhkWVk2PrV+MFuWZdDXPKUYZD71ev+d788A2H3rttTkIj/fz/m9Fbfd45F1BSW3+TH9YP1Wo79zf9buz78tt7WSmQjYVfR8r9drneNCm5a102xqBiqZlfIcoJ7L5rrf47+z8xMRD5zlDJayrOZSA+kGabV5i51BJp4qv7/61ELQFwkpNdo84uFg8T30lb0KD1jtPje6tkyM345Zs9GIUWCmpzI95g40FWQBa5qm7C/AMhiQOoktv/zyS3z33XcFEPDuLGTZs/d3GQgYWc/nixi3r6uBo5pXlCc53+f1vf7O1KsRPm3PYwUafyxH47cu2ai65D55LFZuj57s5a2trbJaxFtFs6+E35spdb+/9tvynceS75Blsw70eY6N5/G2B7TMq3NdGUPPAd4Dg+ZkWM41yOep53mWAX7WHzVdYPDtdxAmYV445l8Dzm5HTTm7j16yuL6Z8cn0MY6IPX6WwPp5j+kb+mt1dbWMpz37iPaGSBEPdxu1zHpb3mUhRBJLYdqYb9SDMFw+dyADYs7c6Ha7rZUpXOMQcMRilU3NgXQ/dDoPt/Im3GY7YmDOSaM5H8NyT2jZbUKn5rlzc3PzYDWCD1qKiLKsljnw1HDBv2vToYjnbXuYkeRj12cU99g9mW7PihRkmZd2+Z7aex+7bhkrEBFldymYARRUzRhlL+exfsjXuW6PIfRl3+X7jTStaGpAYhnQyH3herwUGFjmAebva15mvi5iQSHX5B4FaWrfijzXpdaHy4BdrS78bdnKYNAUuu/1+ObPH/MWc/1tkOgD90X2mnjXMnmoyWWtXbV357q6z60fch/62Xke/F6YgZru8Fjl9pked9Ls556bQYLHfdm7fP2yukY8BKB8lutDndknAYPrg8CWzY8Mlj2e/p93ex+NXGevzHFehouvyfWBLazJUZZdy2W2NZ5TMOqAYMI9+d4cwvhc+dUJhLzMjaBxTm6osQSdzmJ/dCM1Nx7qlfvymtaI9pIx6uU63d3dlcNeTKVRv7wEy8lgWdHXlATFSPnu7i4Gg0E5iIjtau2d1JRybcK58JlZDNrMoRzZ43K/ZWHk+mWKlO+zAjEzwEYmFCas91uAMchZvF+y1OLyWZHVJmU21N53giQsb3W7trZWPAdkofacbMj9nev3WLy7VpYBtJwMmA1G7bk1Q2mv3e/xsiZ/x2qSGpvEvVkmske+DETlfuPZbE5DTLV2b62NtYTJZVTvlyx53JALlkx2Op0HW5obBDg8mQ1M9oBr766xfbwjoi1n1M997qXnfMdvljaahfW1BoN+XwbtNn4Ri1wIdBHzlX6xTkP3O8ydn5n7lDEgDEgS7sbGRoul9TJGb3Llbfvz2MIEAFIy672/v1/2Bdne3o5erxebm5vFptKPs9kstre34/3793/7MAGlNpExKjVD6XtotAXFA503z1imeObzeelU73WQjR10kxVrRoi+L6/R9rtdsmfCNSiU8XhcNsQg5uUElhqK83PdhozG3c8AHuJnVhY26J7Uj7U/F/e/l0z6OfYUQcFG2k9JxPmtixVJLssAXsRDL8djh8yxwyWb3TRNU5RcpiPze/P/GYQs85h8jevqtmQjaKXr65cBjBoYyIbXn5uyRS68CxxgKj/H7/NzUZZmF7IBsnwyb5ftNLesGBTV5nR2aF6yuG4OvdFPmaXFGC3bZ4J5mqlv3uXfZlYNmpeBiPx9BoLo5iyXvtbPqMle1tHInul16k0IItfJMuV+zPog6xDo/clkcSpnt9stS2WRp5pTzDj5AC7Gw/2dGcqmuU9eJjn58PAwer1ebG1tFTBAHzEX8j4Lj5Vfvc+AOybHmD1wy+h5nlUDDu6QiPbWrBQme61udHY2vNmwejMZC4g3c/DnVqwgQkIBs9ksTk9Po9vtxvHxcRwdHZV1sBkE5Lrmdz2WHFJTTE4yof4oRMbA52K7//Juge4risFRLeEtZ40b7JEfYirrS5fHwEAutGNZDN3ygZKZTqdxdXVV+rzGqLg8Bjwey2uo3Ze/fwww5HtqdbDSfoqitxx7fnhuZpD+uec8BortcaH08fy43wc6eZldbTyzE2NPkPe+ZJgg9xcyRy6UGQIMPwAMZiUiSk5TppnN9mQmtram3vM6J0RnStoyhE525ntmdhmD9fX1sjzX4HsZk0n96Qd+LI829GZEMkDmd96Cm++wGXa4YAA5grgGYKw7ckiBku0o/eV68Y7V1dXY2Ngo488cgJ2Fjc3HoD9W/t0HFeVG5I6mYabaTNdbmLIB4tmgL39v4cgZl9mLjahTULzfYYamWSQGZqXG90wgkqSGw2GhoSIiPn78GJ8+fYrLy8uyAxTGsGZkQLF8580mrJhdHwtopriywK2trRX0SB3df4AB76+QlZAVicFZbUtX2gKFbnD2EmVZotSya3NbKLSD8ycAgtNpe/9993OtZC+JMWuaptC6tTrm+7gmA9VlQPJz5XNgoNZ3Bv0RC7kCXGKUIx4uWfU7DCxdD+5z/cjJ4dmEA+fzeSuZik1Xcr6O578p5YiFsmdTmJeUW7fdevT6+rrFeK6vr5ewlLe7dQKeWRoYPPKpDAj8P7ohYrFXR20lTETbicpeL+NkIxqxAB2cOcH86vf7sbOzU4xhxGJXPYOYrBM3NzfL3h5eURMRsbu7W7xljKf1rfWft0Jm/PlNONCAnzZlWcEJM2NFX7nPzWgYNHle0WdsWuTVS01zz0CwvTjPwz49xQmK+HduR0yHZmVrw1R7hgez9n3+7ecaaVJqntSyZ5vG9Ofz+bwg1VosraZYETaAAPdhBBngmhKtKd5lsW2XjGSzZ5N/auPg+oOgl73X92ejbgo4183/vzQYqLUlYiEnKCsmmRWjiz83mLJiqcle7X/LOGNf88Jq7chAYtk1jz3HVG/tObme/j+Dj/yc3N/5mlobDV5rMmulb9aFfgfY53lUa5/fbdDtZxv0/l7CBBEL2fMukLnk+R8RxdPP3nDE4mhshwsYDxhFgwvqkY0Zq6ocIqL/fYSv+3o2m8XW1lYxwp1Op+zXYkePeekNf5iDAPHt7e1C07t/ACrOY7I8+Tf5A3ZG3Z92tCj+27JpWTRzxbsc4/d3mR1wof3kKyGf0+n9/hGELQEDhDCeUp4MBnK8j0HgJCYEDQNIxzjGb6X3uQlWUyr2uBgYdyqC9zmK93MekxWlqfbafbe3tzEYDKJpmjg/P4+IKMlUbE2baSP6kzohhI6hUWcba9pS6yu334KU2Rquq7EAOXejppQZW3YB8+dMYPcj3peR8ZcumTGJaMci8UbY3AQPC0NGvzjmiCJjcppdsozyroi6XGZ2Km8xWgNpBjHZqOe/fQ+FDYFIssvlMfDC3wZBEe0EPDN+NZYle3fQxhiRGmhzSAa5siHAaHke0eePgWFoVa6hHd6U5iXBQHZaWHvPevuI+lp0xggv05vfUDB89B1yTxw6om2o/MwMOJhDhC8YC+YRB7lZ51ku7BnX2uLTCFkyeHl5WRK2Z7NZvH37Nvr9fln6i6PWNE3rFFmzfzWDG/Ew1JmTMMniR95ms1lLPpEh5NNOE8/jMC02oyMRkfe7bjkEe3V1VcLQLLWkXdYhz8nVetYRxo99V/MQ8ne51CbZUzyWX1vP/I6s7Gv1yt5aVlQMakQ8GDiurzEMRr3+fFn98+TInlhtDGxMaptfPKYoH3u/n5H7J7cj04IvWWzE+N/GLXvVNa/WMlMbA65dBto+11+1ex77vOa515617PPnvOtz39Xk4XN9kkvWFzUP30yT51N+X76vBnL4YWyYExn0/V6KvU2viKoxHwab2VFDJwDMDOBYkWG9wTzG0OR3AOicTc99EY9vlOUxXMZUcq/Bp38wsP6fz2ASuN/JpdYB7mP3r5lAilcNYcRZQp71iUEFbcKzZy8C3889Hi8+p10AQkID7qvsUDy1PBkMkCThAxPcee78PBhuTMQi2SgbKAuqaVd7WgxSjcqveURZKSEE0EWgyBooWFtbi36/32o/MUbvgmVP3P2S42qeEOvr663T1ExrZSrN/Zz/tpeFR+QtXe2xua5GkXlTCtN/jEve2hMKkcNmmqZp7bg3mUxKfK5pmmclsvwWpab4+bxpmsJy8Znld1mCkMMk2RD7M8f/8HIISTBm2ZumZAWejaLnUp78y4yvafRloILPrLxrzAb5ADmZrDYXHdKiOEbta8xeMM/Ie+EZbHpm78h96JhuBnB+f65nra9fqmSAarnD6/WS13wv8oFRzic4wo7MZrMCAthIzSV7nZZ95gd5DLe3tw92CDR4y/1pT53/MyvjJco5KZR3/fjjj2V+RkTrOmSYtlrmLOO5Xva4a3aJ99vgu/+XtTWHoSxv/G9mGdYhg9T5vH0IYNZtzwGyTwYDOaZZQ6JPKTUK0M/IRtANdsP8veuRldVTvJosjDwDg+1QhGkyK0CUk5Eyz7aS8+/a/tPZWLk++W//bwHxuy1wNQ+Y77OCznXK39FOdqDk2Z6kNmQZlHzJ8phXyudOxrFnaLmAbkUeah6M38nzrXCIW66urlYVW/ZEPWaZqnxMTj7XH5mifYyhqPUdcxLD8JRSU1a8L3tUNuBOgvOObPRjp9NpLa/NoMqKvuYF5nY9pf1fuuQ62HOt1Zm5aJ0SEQ+Ag8Mh6C0fisN9jqfnFQNmKvCUAf81sJlBQU7gM7j13LMT5nogf6zoyTF8fgNg7GVTn2XFc7PmJJg1sFGnz2v61s/0++2oAc74nzNQrH/zfKqFdZ9TngwGeFFGRd3u/ZnRKysrsb29/WCC01GOt4Iw2ZSEYjRIo/JaYzqiNuDL1rQ7OzN3VtM0pR5GlPw/Go1aHgVxOhvd7BVa8fuAH+rCtWyVyeTb3Nx8AIJsxBkHt9uTgjhf7bAXe/Y8x0t9ahPCMTx7Ae4P2uiwB7G5jY2NkitQi8t9yZKNu/s5T1o+96qOiAU7ZCWcAWQGo9njaZqmFdNkXrieWYHmd9S8M8Yxe/y5To/1j+vt355fBpBuEx6nt2TOQNJ1swIzg7a+vh69Xq94p1CoTdOUvjI4wivL+sl96fZkYPC5tr80EMj9HPHQiUGHuD3ZUHJfBlrOBeM7+tZGjmtms1lhVGEAx+Nxy8h675OsV2pOZKbAbbjNMmfwncetlteVZd+ym43osmfnccigPYessg3z/bWciNwftq3IrJeLm9HIz3rMWfhceRYYoHhgut373ZBWV1ej3++3Ek5IUJrPFztAWYl6Wc98Pi9LTPjfFHi3u9jV0J4v7/JP9lS8xMOC5qVD7jTaQJKKJxB0DfExBsDFnmBtvaqpXX9P8k1GsW6b9xvnPsfzItpGnMkL4GGCQcMuM9RNs6Bq7+7uWkvoap42Ats0TaEYTY/nPbZfohgMPLbhVcTDPfIx4gYTjymPmjzZ63FoKk9s/20wYWXOsjmUbj43wPdTP4PXxwBCfr83k2IeZgaI8cWAGJxwXaY1IxbMk+c2DMD6+noLfNJngFjPYbLYbTBqYCDLQ81Lo90vDQQi6t5rNl52PiiPySX9ZGfBz7Hepv/9XGSC013Pz89b564sYytq9aGNHjMDAuwGcXlCbNgS2vQ5EJA9aTsAteT4bLxzn3oO5TBxzfu37Gfno5ZgazDA3HNdPmfofw2Y/VX7DOAJbG9vx/7+fvzhD38oGyCw3jdi4V1Np9NCc+TJGrHoXE98TwIG3s+z4X9M+HJnMgjj8TjG43FVSKjD9fX1g8NWOCiCutnbMLq2ssp1wcvBUEN1Umw0stHKxQohnxpoY5OBU0R7CSTF9WAtN5tX2Ij6HVbiEfEAXGSP7CWK+96Tv6ZsM2irKcNl4+GJjpK10l3mqda8iFyysvIcynSmn2Nw4Hrmz/wejEvOYrYCzIbe/et2+W/LDvMke705p4F13aaq89xnVYD7aDqdtupSGzdT45ZpA57HxuS3LpZFz93pdFqy6SMWW7NnvVmTmYhFXJ6/uSezAl5C6/oABjKFnfW32xHxeIJo/rHceiVCDVTmdzzl78w487l/+5oaQFw2b/k8zxO/O4PX3EfOV/KcqL3P/2fd89Ty7COM+dna2op3797FN998E//8z/8cu7u78S//8i8tVOdJORwOHyCuTHVmxOv7ETze7wRAnyk/nU6rJ89x39raWnS73RgMBnFxcdFSBBlZX11dxXA4jG63W7xdwAinZ+Gl2eDyLuca5EFhaRAekI2EgQ+Agl0OQewoCAy1vTP6jmxVntU0TXkXByhBeUcsEjgp3MvmFR4/CuNjheXldvamX1Kh8hvFkrcCXTaRXHee4zZ7AkcsDJxlmQltkJCRfTa2tfrnOWUAkM9BcJ38fwYjtTpQV29Xy/cOAdgDsgxk0JE9LwrhgIj7EBeGxbHjlZWV6Pf7cXd3F8PhsCxjdt1RqtYdfj+JrdmbJMS1uroaOzs7xcgxv2FdMuX7JQsGD51GX97e3sbJyUmsrKzEYDBohf3cN8w/nBjk2ctKa8xqpuZ5JsmBNmg4M1km/dtMZc0R9LUuvJcwFMvxzDbmdz42Vn4H92W9nZ0j17kWUs1G3PMgh1lrIY9sG7Ltq/VNTVe4jr85GKAYrTKhTAvmBmPQcmY9YAAK3vH0iGgpPXsVGD4obN6BQa6BAerNNpfX19cldOA9vt2p0OKdTqe1FteTBWVCfd0/ABOe5UJ/eLcrK+lM9eYdAC1sWfCssGuomGtsXLLXZoHMysGFz32fvRe+zytHXrrU2lu7pvY3ZZmn4HHMhikrm1qdfsvy2Dv8ndvw1OJ7a16SDTafPxZTzvOJZzymGJcBqmV19fNrQOmlAOxTSg0YUn/nBXju2us34OJah2NtTAwunNtlo1Yz7LXfTwUDj3nhf6vi99d05b93/K1nanLrv5fpGP9+KtD5VXWd/56l/bW8ltfyWl7La3ktv3n5fRzU/Vpey2t5La/ltbyWFyuvYOC1vJbX8lpey2v5Oy+vYOC1vJbX8lpey2v5Oy+vYOC1vJbX8lpey2v5Oy+vYOC1vJbX8lpey2v5Oy+vYOC1vJbX8lpey2v5Oy+vYOC1vJbX8lpey2v5Oy+vYOC1vJbX8lpey2v5Oy+vYOC1vJbX8lpey2v5Oy//PyoRr008Ynj/AAAAAElFTkSuQmCC\n", "text/plain": ["<Figure size 640x480 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Plot 3 examples from the synthetic data\n", "fig, ax = plt.subplots(nrows=1, ncols=3)\n", "for image_n in range(3):\n", "    ax[image_n].imshow(syn_images[image_n, 0, :, :].cpu(), cmap=\"gray\")\n", "    ax[image_n].axis(\"off\")"]}, {"cell_type": "markdown", "id": "5ba4e62d", "metadata": {}, "source": ["# Compute MMD"]}, {"cell_type": "markdown", "id": "0fa01253", "metadata": {}, "source": ["Because the realism of the LDMs will depend on the realism of the autoencoder reconstructions, we will compute the MMD betweeen the original images and the reconstructed images to evaluate the performance of the autoencoder.\n", "\n", "MMD (Maximum Mean Discrepancy) is a distance metric used to measure the similarity between two probability distributions. This metric maps the samples from each distribution to a high-dimensional feature space and calculates the distance between the mean of the features of each distribution. A smaller MMD value indicates a better match between the real and generated distributions. It is often used in combination with other evaluation metrics to assess the performance of a generative model."]}, {"cell_type": "code", "execution_count": 18, "id": "e0d92309", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["MS-SSIM score: 0.8291 +- 0.0169\n"]}], "source": ["mmd_scores = []\n", "\n", "mmd = MMDMetric()\n", "\n", "for step, x in list(enumerate(val_loader)):\n", "    image = x[\"image\"].to(device)\n", "\n", "    with torch.no_grad():\n", "        image_recon = autoencoderkl.reconstruct(image)\n", "\n", "    mmd_scores.append(mmd(image, image_recon))\n", "\n", "mmd_scores = torch.stack(mmd_scores)\n", "print(f\"MS-SSIM score: {mmd_scores.mean().item():.4f} +- {mmd_scores.std().item():.4f}\")\n"]}, {"cell_type": "markdown", "id": "d98f914c", "metadata": {}, "source": ["# Compute MultiScaleSSIMMetric and SSIMMetric\n", "\n", "SSIM measures the similarity between two images based on three components: luminance, contrast, and structure. In addition, MS-SSIM is an extension of SSIM that computes the structural similarity measure at multiple scales. Both metrics can assume values between 0 and 1, where 1 indicates perfect similarity between the images.\n", "\n", "There are two ways to compute the MS-SSIM and SSIM, and in this notebook we will look at both ways:\n", "1. Use the reconstructions of the autoencoder and the real images. By using the metric this way we can assess the performance of the autoencoder.\n", "2. Compute the MS-SSIM and SSIM between pairs of synthetic images. This second way of computing the MS-SSIM can be used as a metric to evaluate the diversity of the synthetic images.\n", "\n"]}, {"cell_type": "markdown", "id": "bd139cb5", "metadata": {}, "source": ["In this section we will compute the MS-SSIM and SSIM Meteric between the real images and those reconstructed by the AutoencoderKL."]}, {"cell_type": "code", "execution_count": 19, "id": "eb2cd8a6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["MS-SSIM Metric: 0.0017757 +- 0.0110144\n", "SSIM Metric: -0.0090123 +- 0.0118101\n"]}], "source": ["ms_ssim_recon_scores = []\n", "ssim_recon_scores = []\n", "\n", "ms_ssim = MultiScaleSSIMMetric(spatial_dims=2, data_range=1.0, kernel_size=4)\n", "ssim = SSIMMetric(spatial_dims=2, data_range=1.0, kernel_size=4)\n", "\n", "for step, x in list(enumerate(val_loader)):\n", "    image = x[\"image\"].to(device)\n", "\n", "    with torch.no_grad():\n", "        image_recon = autoencoderkl.reconstruct(image)\n", "\n", "    ms_ssim_recon_scores.append(ms_ssim(image, image_recon))\n", "    ssim_recon_scores.append(ssim(image, image_recon))\n", "\n", "ms_ssim_recon_scores = torch.cat(ms_ssim_recon_scores, dim=0)\n", "ssim_recon_scores = torch.cat(ssim_recon_scores, dim=0)\n", "\n", "print(f\"MS-SSIM Metric: {ms_ssim_recon_scores.mean():.7f} +- {ms_ssim_recon_scores.std():.7f}\")\n", "print(f\"SSIM Metric: {ssim_recon_scores.mean():.7f} +- {ssim_recon_scores.std():.7f}\")"]}, {"cell_type": "markdown", "id": "30ad94fd", "metadata": {}, "source": ["Compute the SSIM and MS-SSIM between pairs of synthetic images, the results of the MS-SSIM and SSIM can be used to evaluate the diversity of the synthetic samples."]}, {"cell_type": "code", "execution_count": 20, "id": "7e189159", "metadata": {"lines_to_next_cell": 0}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 25/25 [00:12<00:00,  1.95it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["MS-SSIM Metric: 0.3235 +- 0.1347\n", "SSIM Metric: 0.1563 +- 0.0668\n"]}], "source": ["ms_ssim_scores = []\n", "ssim_scores = []\n", "\n", "# How many synthetic images we want to generate\n", "n_synthetic_images = 100\n", "\n", "# Generate some synthetic images using the defined model\n", "noise = torch.randn((n_synthetic_images, 1, 64, 64))\n", "noise = noise.to(device)\n", "scheduler.set_timesteps(num_inference_steps=25)\n", "\n", "with torch.no_grad():\n", "    syn_images = inferer.sample(input_noise=noise, diffusion_model=unet, scheduler=scheduler)\n", "\n", "    idx_pairs = list(combinations(range(n_synthetic_images), 2))\n", "    for idx_a, idx_b in idx_pairs:\n", "        ms_ssim_scores.append(ms_ssim(syn_images[[idx_a]], syn_images[[idx_b]]))\n", "        ssim_scores.append(ssim(syn_images[[idx_a]], syn_images[[idx_b]]))\n", "\n", "\n", "ms_ssim_scores = torch.cat(ms_ssim_scores, dim=0)\n", "ssim_scores = torch.cat(ssim_scores, dim=0)\n", "\n", "print(f\"MS-SSIM Metric: {ms_ssim_scores.mean():.4f} +- {ms_ssim_scores.std():.4f}\")\n", "print(f\"SSIM Metric: {ssim_scores.mean():.4f} +- {ssim_scores.std():.4f}\")"]}, {"cell_type": "markdown", "id": "bcd99f0d", "metadata": {}, "source": ["# Clean-up data"]}, {"cell_type": "code", "execution_count": 21, "id": "a2bd7167", "metadata": {}, "outputs": [], "source": ["if directory is None:\n", "    shutil.rmtree(root_dir)"]}], "metadata": {"jupytext": {"formats": "ipynb,py", "notebook_metadata_filter": "-all"}, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.15"}}, "nbformat": 4, "nbformat_minor": 5}