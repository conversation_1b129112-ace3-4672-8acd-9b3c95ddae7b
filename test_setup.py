"""
测试BUS到CEUS模型设置的简单脚本
用于验证所有组件是否正确工作
"""

import os
# 解决OpenMP库冲突问题
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

import torch
import numpy as np
from torch.utils.data import DataLoader

# 导入我们的模块
from bus_ceus_dataset import BUSCEUSDataset, create_data_loaders
from conditional_ddim_model import create_conditional_ddim_model
from metrics import MetricsCalculator
from visualization import create_comparison_grid

def test_dataset():
    """测试数据集加载"""
    print("Testing dataset loading...")
    
    try:
        # 创建测试数据集
        dataset = BUSCEUSDataset(
            bus_dir="train_mini/bus",
            ceus_dir="train_mini/ceus",
            image_size=(256, 256),
            augmentation=True,
            normalize=True
        )
        
        print(f"Dataset size: {len(dataset)}")
        
        # 测试数据加载
        sample = dataset[0]
        print(f"BUS image shape: {sample['bus'].shape}")
        print(f"CEUS image shape: {sample['ceus'].shape}")
        print(f"BUS image range: [{sample['bus'].min():.3f}, {sample['bus'].max():.3f}]")
        print(f"CEUS image range: [{sample['ceus'].min():.3f}, {sample['ceus'].max():.3f}]")
        
        # 测试数据加载器
        dataloader = DataLoader(dataset, batch_size=2, shuffle=True, num_workers=0)
        batch = next(iter(dataloader))
        print(f"Batch BUS shape: {batch['bus'].shape}")
        print(f"Batch CEUS shape: {batch['ceus'].shape}")
        
        print("✓ Dataset test passed!")
        return True
        
    except Exception as e:
        print(f"✗ Dataset test failed: {e}")
        return False


def test_model():
    """测试模型创建和前向传播"""
    print("\nTesting model creation...")
    
    try:
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"Using device: {device}")
        
        # 创建模型
        model, scheduler, inferer = create_conditional_ddim_model(
            image_size=(256, 256),
            in_channels=1,
            out_channels=1,
            num_train_timesteps=1000,
            device=device
        )
        
        print(f"Model parameters: {sum(p.numel() for p in model.parameters()):,}")
        
        # 测试前向传播
        batch_size = 2
        bus_images = torch.randn(batch_size, 1, 256, 256).to(device)
        ceus_images = torch.randn(batch_size, 1, 256, 256).to(device)
        
        # 测试训练模式的前向传播
        model.train()
        noise = torch.randn_like(ceus_images)
        timesteps = torch.randint(0, scheduler.num_train_timesteps, 
                                (batch_size,), device=device)
        noisy_images = scheduler.add_noise(ceus_images, noise, timesteps)
        
        # 模型预测
        noise_pred = model(
            torch.cat([noisy_images, bus_images], dim=1),
            timesteps
        )
        
        print(f"Noise prediction shape: {noise_pred.shape}")
        print(f"Expected shape: {noise.shape}")
        
        # 测试推理模式
        model.eval()
        with torch.no_grad():
            # 设置推理步数
            inferer.scheduler.set_timesteps(50)
            
            # 生成图像
            noise = torch.randn_like(ceus_images)
            generated_images, _ = inferer.sample(
                input_noise=noise,
                diffusion_model=model,
                condition=bus_images,
                verbose=False
            )
            
            print(f"Generated images shape: {generated_images.shape}")
        
        print("✓ Model test passed!")
        return True
        
    except Exception as e:
        print(f"✗ Model test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_metrics():
    """测试指标计算"""
    print("\nTesting metrics calculation...")
    
    try:
        # 创建测试图像
        batch_size = 4
        generated = torch.rand(batch_size, 1, 256, 256)
        target = torch.rand(batch_size, 1, 256, 256)
        
        # 测试指标计算
        metrics_calc = MetricsCalculator(data_range=1.0)
        metrics_calc.update(generated, target)
        metrics = metrics_calc.compute()
        
        print("Computed metrics:")
        for key, value in metrics.items():
            print(f"  {key.upper()}: {value:.4f}")
        
        print("✓ Metrics test passed!")
        return True
        
    except Exception as e:
        print(f"✗ Metrics test failed: {e}")
        return False


def test_visualization():
    """测试可视化功能"""
    print("\nTesting visualization...")
    
    try:
        # 创建测试图像
        batch_size = 4
        bus_images = torch.rand(batch_size, 1, 256, 256)
        target_images = torch.rand(batch_size, 1, 256, 256)
        generated_images = torch.rand(batch_size, 1, 256, 256)
        
        # 测试对比图像创建
        fig = create_comparison_grid(
            bus_images, target_images, generated_images,
            num_samples=2, figsize=(10, 6)
        )
        
        # 保存测试图像
        os.makedirs("test_output", exist_ok=True)
        fig.savefig("test_output/test_comparison.png")
        
        print("✓ Visualization test passed!")
        print("  Test comparison image saved to test_output/test_comparison.png")
        return True
        
    except Exception as e:
        print(f"✗ Visualization test failed: {e}")
        return False


def main():
    """主测试函数"""
    print("BUS to CEUS Model Setup Test")
    print("=" * 50)
    
    # 检查数据目录
    if not os.path.exists("train_mini/bus") or not os.path.exists("train_mini/ceus"):
        print("⚠️  Warning: Data directories not found!")
        print("   Please ensure train_mini/bus and train_mini/ceus directories exist")
        print("   Creating dummy data for testing...")
        
        # 创建虚拟数据用于测试
        os.makedirs("train_mini/bus", exist_ok=True)
        os.makedirs("train_mini/ceus", exist_ok=True)
        
        # 创建一些虚拟图像文件
        import PIL.Image
        for i in range(3):
            # 创建虚拟图像
            dummy_img = PIL.Image.fromarray(np.random.randint(0, 255, (256, 256), dtype=np.uint8), mode='L')
            dummy_img.save(f"train_mini/bus/test_{i:03d}_bus_0.png")
            dummy_img.save(f"train_mini/ceus/test_{i:03d}_ceus_0.png")
        
        print("   Dummy data created for testing")
    
    # 运行所有测试
    tests = [
        ("Dataset", test_dataset),
        ("Model", test_model),
        ("Metrics", test_metrics),
        ("Visualization", test_visualization)
    ]
    
    results = []
    for test_name, test_func in tests:
        success = test_func()
        results.append((test_name, success))
    
    # 总结结果
    print("\n" + "=" * 50)
    print("Test Results Summary:")
    print("-" * 30)
    
    all_passed = True
    for test_name, success in results:
        status = "✓ PASSED" if success else "✗ FAILED"
        print(f"{test_name:15s}: {status}")
        if not success:
            all_passed = False
    
    print("-" * 30)
    if all_passed:
        print("🎉 All tests passed! The setup is ready for training.")
    else:
        print("❌ Some tests failed. Please check the error messages above.")
    
    return all_passed


if __name__ == "__main__":
    main()
