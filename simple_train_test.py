"""
简化的训练测试脚本
"""

import os
# 解决OpenMP库冲突问题
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

import torch
import torch.nn as nn
from torch.utils.data import DataLoader
from tqdm import tqdm

# 导入我们的模块
from bus_ceus_dataset import BUSCEUSDataset
from conditional_ddim_model import create_conditional_ddim_model
from metrics import calculate_psnr, calculate_ssim, calculate_mae, calculate_mse

def simple_train_test():
    """简化的训练测试"""
    print("Simple Training Test")
    print("=" * 50)
    
    # 设置设备
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"Using device: {device}")
    
    try:
        # 创建数据集
        print("Creating dataset...")
        dataset = BUSCEUSDataset(
            bus_dir="train_mini/bus",
            ceus_dir="train_mini/ceus",
            image_size=(256, 256),
            augmentation=False,  # 关闭增强以加快速度
            normalize=True
        )
        
        # 创建数据加载器
        dataloader = DataLoader(
            dataset, 
            batch_size=2, 
            shuffle=True, 
            num_workers=0  # 避免多进程问题
        )
        
        print(f"Dataset size: {len(dataset)}")
        print(f"Number of batches: {len(dataloader)}")
        
        # 创建模型
        print("Creating model...")
        model, scheduler, inferer = create_conditional_ddim_model(
            device=device,
            in_channels=1,
            out_channels=1,
            num_train_timesteps=1000
        )
        
        print(f"Model parameters: {sum(p.numel() for p in model.parameters()):,}")
        
        # 创建优化器
        optimizer = torch.optim.Adam(model.parameters(), lr=1e-4)
        
        # 测试一个训练步骤
        print("Testing training step...")
        model.train()
        
        # 获取一个批次
        batch = next(iter(dataloader))
        bus_images = batch["bus"].to(device)
        ceus_images = batch["ceus"].to(device)
        
        print(f"BUS images shape: {bus_images.shape}")
        print(f"CEUS images shape: {ceus_images.shape}")
        
        # 前向传播
        optimizer.zero_grad()
        
        # 添加噪声
        noise = torch.randn_like(ceus_images)
        timesteps = torch.randint(0, scheduler.num_train_timesteps, (ceus_images.shape[0],), device=device)
        
        # 使用调度器添加噪声
        noisy_images = scheduler.add_noise(ceus_images, noise, timesteps)
        
        # 模型预测
        noise_pred = model(
            x=noisy_images,
            timesteps=timesteps,
            condition=bus_images
        )
        
        # 计算损失
        loss = nn.MSELoss()(noise_pred, noise)
        
        print(f"Training loss: {loss.item():.6f}")
        
        # 反向传播
        loss.backward()
        optimizer.step()
        
        print("✅ Training step successful!")
        
        # 测试几个训练步骤
        print("Testing multiple training steps...")
        for i, batch in enumerate(dataloader):
            if i >= 3:  # 只测试3个批次
                break
                
            bus_images = batch["bus"].to(device)
            ceus_images = batch["ceus"].to(device)
            
            optimizer.zero_grad()
            
            # 添加噪声
            noise = torch.randn_like(ceus_images)
            timesteps = torch.randint(0, scheduler.num_train_timesteps, (ceus_images.shape[0],), device=device)
            noisy_images = scheduler.add_noise(ceus_images, noise, timesteps)
            
            # 模型预测
            noise_pred = model(
                x=noisy_images,
                timesteps=timesteps,
                condition=bus_images
            )
            
            # 计算损失
            loss = nn.MSELoss()(noise_pred, noise)
            
            # 反向传播
            loss.backward()
            optimizer.step()
            
            print(f"Batch {i+1}: Loss = {loss.item():.6f}")
        
        print("✅ Multiple training steps successful!")
        
        # 测试推理（简化版本）
        print("Testing simple inference...")
        model.eval()
        with torch.no_grad():
            # 使用第一个批次进行推理测试
            batch = next(iter(dataloader))
            bus_images = batch["bus"].to(device)
            ceus_images = batch["ceus"].to(device)
            
            # 创建随机噪声作为起始点
            noise = torch.randn_like(ceus_images)
            
            # 简单的一步去噪测试
            timesteps = torch.full((ceus_images.shape[0],), 999, device=device)
            noise_pred = model(
                x=noise,
                timesteps=timesteps,
                condition=bus_images
            )
            
            print(f"Noise prediction shape: {noise_pred.shape}")
            print("✅ Simple inference successful!")
        
        # 测试指标计算
        print("Testing metrics calculation...")
        with torch.no_grad():
            # 使用真实图像和预测图像计算指标
            fake_generated = torch.clamp(ceus_images + 0.1 * torch.randn_like(ceus_images), -1, 1)

            # 将图像范围从[-1,1]转换到[0,1]
            real_norm = (ceus_images + 1) / 2
            fake_norm = (fake_generated + 1) / 2

            psnr = calculate_psnr(fake_norm.cpu(), real_norm.cpu())
            ssim = calculate_ssim(fake_norm.cpu(), real_norm.cpu())
            mae = calculate_mae(fake_norm.cpu(), real_norm.cpu())
            mse = calculate_mse(fake_norm.cpu(), real_norm.cpu())

            print(f"Test metrics:")
            print(f"  PSNR: {psnr.mean().item():.4f}")
            print(f"  SSIM: {ssim.mean().item():.4f}")
            print(f"  MAE: {mae.mean().item():.4f}")
            print(f"  MSE: {mse.mean().item():.4f}")
            print("✅ Metrics calculation successful!")
        
        print("\n🎉 All tests passed! The system is ready for training.")
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    success = simple_train_test()
    if success:
        print("\n✅ System is ready for full training!")
        print("You can now run:")
        print("python train_bus_to_ceus.py --bus_dir train_mini/bus --ceus_dir train_mini/ceus --epochs 50 --batch_size 4")
    else:
        print("\n❌ System needs debugging before training.")


if __name__ == "__main__":
    main()
