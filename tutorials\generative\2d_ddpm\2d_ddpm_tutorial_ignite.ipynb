{"cells": [{"cell_type": "code", "execution_count": null, "id": "f5cbf8da", "metadata": {}, "outputs": [], "source": ["# Copyright (c) MONAI Consortium\n", "# Licensed under the Apache License, Version 2.0 (the \"License\");\n", "# you may not use this file except in compliance with the License.\n", "# You may obtain a copy of the License at\n", "#     http://www.apache.org/licenses/LICENSE-2.0\n", "# Unless required by applicable law or agreed to in writing, software\n", "# distributed under the License is distributed on an \"AS IS\" BASIS,\n", "# WITHOUT WAR<PERSON><PERSON>IES OR CONDITIONS OF ANY KIND, either express or implied.\n", "# See the License for the specific language governing permissions and\n", "# limitations under the License."]}, {"cell_type": "markdown", "id": "53c8dfc0", "metadata": {}, "source": ["# Denoising Diffusion Probabilistic Models with MedNIST Dataset\n", "\n", "This tutorial illustrates how to use MONAI for training a denoising diffusion probabilistic model (DDPM)[1] to create\n", "synthetic 2D images.\n", "\n", "[1] - <PERSON> et al. \"Denoising Diffusion Probabilistic Models\" https://arxiv.org/abs/2006.11239\n", "\n", "\n", "## Setup environment"]}, {"cell_type": "code", "execution_count": null, "id": "04629260", "metadata": {}, "outputs": [], "source": ["!python -c \"import monai\" || pip install -q \"monai-weekly[tqdm]\"\n", "!python -c \"import ignite\" || pip install -q pytorch-ignite\n", "!python -c \"import matplotlib\" || pip install -q matplotlib\n", "%matplotlib inline"]}, {"cell_type": "markdown", "id": "2342bc75", "metadata": {}, "source": ["## Setup imports"]}, {"cell_type": "code", "execution_count": null, "id": "3382be3f", "metadata": {"jupyter": {"outputs_hidden": false}}, "outputs": [], "source": ["import os\n", "import shutil\n", "import tempfile\n", "\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "import torch\n", "from ignite.contrib.handlers import ProgressBar\n", "from monai import transforms\n", "from monai.apps import MedNISTDataset\n", "from monai.config import print_config\n", "from monai.data import CacheDataset, DataLoader\n", "from monai.engines import SupervisedEvaluator, SupervisedTrainer\n", "from monai.handlers import MeanAbsoluteError, MeanSquaredError, StatsHandler, ValidationHandler, from_engine\n", "from monai.utils import first, set_determinism\n", "\n", "from generative.inferers import DiffusionInferer\n", "from generative.engines import DiffusionPrepareBatch\n", "from generative.networks.nets import DiffusionModelUNet\n", "from generative.networks.schedulers import DDPMScheduler\n", "\n", "print_config()"]}, {"cell_type": "markdown", "id": "a8a5e41b", "metadata": {}, "source": ["## Setup data directory\n", "\n", "You can specify a directory with the MONAI_DATA_DIRECTORY environment variable.\n", "\n", "This allows you to save results and reuse downloads.\n", "\n", "If not specified a temporary directory will be used."]}, {"cell_type": "code", "execution_count": null, "id": "10e3e959", "metadata": {"jupyter": {"outputs_hidden": false}}, "outputs": [], "source": ["directory = os.environ.get(\"MONAI_DATA_DIRECTORY\")\n", "root_dir = tempfile.mkdtemp() if directory is None else directory\n", "print(root_dir)"]}, {"cell_type": "markdown", "id": "0732a4a1", "metadata": {}, "source": ["## Set deterministic training for reproducibility"]}, {"cell_type": "code", "execution_count": null, "id": "b430e0f4", "metadata": {"jupyter": {"outputs_hidden": false}}, "outputs": [], "source": ["set_determinism(0)"]}, {"cell_type": "markdown", "id": "1750d513", "metadata": {}, "source": ["## Setup MedNIST Dataset and training and validation dataloaders\n", "In this tutorial, we will train our models on the MedNIST dataset available on MONAI\n", "(https://docs.monai.io/en/stable/apps.html#monai.apps.MedNISTDataset). In order to train faster, we will select just\n", "one of the available classes (\"Hand\"), resulting in a training set with 7999 2D images."]}, {"cell_type": "code", "execution_count": null, "id": "b1355f26", "metadata": {"jupyter": {"outputs_hidden": false}}, "outputs": [], "source": ["train_data = MedNISTDataset(root_dir=root_dir, section=\"training\", download=True, progress=False, seed=0)\n", "train_datalist = [{\"image\": item[\"image\"]} for item in train_data.data if item[\"class_name\"] == \"Hand\"]"]}, {"cell_type": "markdown", "id": "658a9a07", "metadata": {}, "source": ["Here we use transforms to augment the training dataset:\n", "\n", "1. `LoadImaged` loads the hands images from files.\n", "1. `EnsureChannelFirstd` ensures the original data to construct \"channel first\" shape.\n", "1. `ScaleIntensityRanged` extracts intensity range [0, 255] and scales to [0, 1].\n", "1. `RandAffined` efficiently performs rotate, scale, shear, translate, etc. together based on PyTorch affine transform."]}, {"cell_type": "code", "execution_count": null, "id": "c97cb55d", "metadata": {"jupyter": {"outputs_hidden": false}}, "outputs": [], "source": ["train_transforms = transforms.Compose(\n", "    [\n", "        transforms.LoadImaged(keys=[\"image\"]),\n", "        transforms.EnsureChannelFirstd(keys=[\"image\"]),\n", "        transforms.ScaleIntensityRanged(keys=[\"image\"], a_min=0.0, a_max=255.0, b_min=0.0, b_max=1.0, clip=True),\n", "        transforms.RandA<PERSON>ed(\n", "            keys=[\"image\"],\n", "            rotate_range=[(-np.pi / 36, np.pi / 36), (-np.pi / 36, np.pi / 36)],\n", "            translate_range=[(-1, 1), (-1, 1)],\n", "            scale_range=[(-0.05, 0.05), (-0.05, 0.05)],\n", "            spatial_size=[64, 64],\n", "            padding_mode=\"zeros\",\n", "            prob=0.5,\n", "        ),\n", "    ]\n", ")\n", "train_ds = CacheDataset(data=train_datalist, transform=train_transforms)\n", "train_loader = DataLoader(train_ds, batch_size=8, shuffle=True, num_workers=4, persistent_workers=True)"]}, {"cell_type": "code", "execution_count": null, "id": "6afd0f79", "metadata": {"jupyter": {"outputs_hidden": false}}, "outputs": [], "source": ["val_data = MedNISTDataset(root_dir=root_dir, section=\"validation\", download=True, progress=False, seed=0)\n", "val_datalist = [{\"image\": item[\"image\"]} for item in val_data.data if item[\"class_name\"] == \"Hand\"]\n", "val_transforms = transforms.Compose(\n", "    [\n", "        transforms.LoadImaged(keys=[\"image\"]),\n", "        transforms.EnsureChannelFirstd(keys=[\"image\"]),\n", "        transforms.ScaleIntensityRanged(keys=[\"image\"], a_min=0.0, a_max=255.0, b_min=0.0, b_max=1.0, clip=True),\n", "    ]\n", ")\n", "val_ds = CacheDataset(data=val_datalist, transform=val_transforms)\n", "val_loader = DataLoader(val_ds, batch_size=8, shuffle=False, num_workers=4, persistent_workers=True)"]}, {"cell_type": "markdown", "id": "021956eb", "metadata": {}, "source": ["### Visualisation of the training images"]}, {"cell_type": "code", "execution_count": null, "id": "469e4f76", "metadata": {"jupyter": {"outputs_hidden": false}}, "outputs": [], "source": ["check_data = first(train_loader)\n", "print(f\"batch shape: {check_data['image'].shape}\")\n", "image_visualisation = torch.cat(\n", "    [check_data[\"image\"][0, 0], check_data[\"image\"][1, 0], check_data[\"image\"][2, 0], check_data[\"image\"][3, 0]], dim=1\n", ")\n", "plt.figure(\"training images\", (12, 6))\n", "plt.imshow(image_visualisation, vmin=0, vmax=1, cmap=\"gray\")\n", "plt.axis(\"off\")\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "id": "31f564f8", "metadata": {}, "source": ["### Define network, scheduler, optimizer, and inferer\n", "At this step, we instantiate the MONAI components to create a DDPM, the UNET, the noise scheduler, and the inferer used for training and sampling. We are using\n", "the original DDPM scheduler containing 1000 timesteps in its Markov chain, and a 2D UNET with attention mechanisms\n", "in the 2nd and 3rd levels, each with 1 attention head."]}, {"cell_type": "code", "execution_count": null, "id": "4b6a775c", "metadata": {"jupyter": {"outputs_hidden": false}}, "outputs": [], "source": ["device = torch.device(\"cuda\")\n", "\n", "model = DiffusionModelUNet(\n", "    spatial_dims=2,\n", "    in_channels=1,\n", "    out_channels=1,\n", "    num_channels=(64, 128, 128),\n", "    attention_levels=(False, True, True),\n", "    num_res_blocks=1,\n", "    num_head_channels=(0, 128, 128),\n", ")\n", "model.to(device)\n", "\n", "num_train_timesteps = 1000\n", "scheduler = DDPMScheduler(num_train_timesteps=num_train_timesteps)\n", "\n", "optimizer = torch.optim.Adam(params=model.parameters(), lr=2.5e-5)\n", "\n", "inferer = DiffusionInferer(scheduler)"]}, {"cell_type": "markdown", "id": "62021a53", "metadata": {}, "source": ["### Model training\n", "Here, we are training our model for 75 epochs (training time: ~50 minutes)."]}, {"cell_type": "code", "execution_count": null, "id": "25498b74", "metadata": {"jupyter": {"outputs_hidden": false}, "lines_to_next_cell": 0}, "outputs": [], "source": ["n_epochs = 75\n", "val_interval = 5\n", "\n", "val_handlers = [StatsHandler(name=\"train_log\", output_transform=lambda x: None)]\n", "\n", "evaluator = SupervisedEvaluator(\n", "    device=device,\n", "    val_data_loader=val_loader,\n", "    network=model,\n", "    inferer=inferer,\n", "    prepare_batch=DiffusionPrepareBatch(num_train_timesteps=num_train_timesteps),\n", "    key_val_metric={\"val_mean_abs_error\": MeanAbsoluteError(output_transform=from_engine([\"pred\", \"label\"]))},\n", "    val_handlers=val_handlers,\n", ")\n", "\n", "\n", "train_handlers = [\n", "    ValidationHandler(validator=evaluator, interval=val_interval, epoch_level=True),\n", "    # StatsHandler(name=\"train_log\", tag_name=\"train_loss\", output_transform=from_engine([\"loss\"], first=True)),\n", "]\n", "\n", "trainer = SupervisedTrainer(\n", "    device=device,\n", "    max_epochs=n_epochs,\n", "    train_data_loader=train_loader,\n", "    network=model,\n", "    optimizer=optimizer,\n", "    loss_function=torch.nn.MSELoss(),\n", "    inferer=inferer,\n", "    prepare_batch=DiffusionPrepareBatch(num_train_timesteps=num_train_timesteps),\n", "    key_train_metric={\"train_acc\": MeanSquaredError(output_transform=from_engine([\"pred\", \"label\"]))},\n", "    train_handlers=train_handlers,\n", ")\n", "ProgressBar(\n", "    persist=True, bar_format=\"[{n_fmt}/{total_fmt}] {percentage:3.0f}%|{postfix} [{elapsed}<{remaining}]\"\n", ").attach(trainer, output_transform=from_engine([\"loss\"]))\n", "\n", "\n", "trainer.run()"]}, {"cell_type": "markdown", "id": "dd72e309", "metadata": {}, "source": ["### Plotting sampling process along DDPM's Markov chain"]}, {"cell_type": "code", "execution_count": null, "id": "c5ceb634", "metadata": {"jupyter": {"outputs_hidden": false}}, "outputs": [], "source": ["model.eval()\n", "noise = torch.randn((1, 1, 64, 64))\n", "noise = noise.to(device)\n", "scheduler.set_timesteps(num_inference_steps=1000)\n", "image, intermediates = inferer.sample(\n", "    input_noise=noise, diffusion_model=model, scheduler=scheduler, save_intermediates=True, intermediate_steps=100\n", ")\n", "\n", "chain = torch.cat(intermediates, dim=-1)\n", "\n", "plt.style.use(\"default\")\n", "plt.imshow(chain[0, 0].cpu(), vmin=0, vmax=1, cmap=\"gray\")\n", "plt.tight_layout()\n", "plt.axis(\"off\")\n", "plt.show()"]}, {"cell_type": "markdown", "id": "aeb3ccd4", "metadata": {}, "source": ["### Cleanup data directory\n", "\n", "Remove directory if a temporary was used."]}, {"cell_type": "code", "execution_count": null, "id": "babfbfd1", "metadata": {"tags": []}, "outputs": [], "source": ["if directory is None:\n", "    shutil.rmtree(root_dir)"]}], "metadata": {"jupytext": {"formats": "ipynb,py:percent"}, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.6"}}, "nbformat": 4, "nbformat_minor": 5}